<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="82a7eea7-5706-4bcf-abb2-3247d3a5e397" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/config/comment.api.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config/comment.http.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config/topic.api.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config/topic.http.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pagesSub/switch/add-topic.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pagesSub/switch/comment-detail.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pagesSub/switch/comment.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pagesSub/switch/topic-list.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/biaoqingbao.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/blacklist.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/chat-1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/chat.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/copy.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/delete.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/jianpan.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/more.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/no-messages.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/null.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/right.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/send.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/topic-title.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/static/icon/warn.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/tabbar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/components/tabbar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/tabbar_bf.vue" beforeDir="false" afterPath="$PROJECT_DIR$/components/tabbar_bf.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/uview-ui/components/u-tabs-swiper/u-tabs-swiper.vue" beforeDir="false" afterPath="$PROJECT_DIR$/components/uview-ui/components/u-tabs-swiper/u-tabs-swiper.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/uview-ui/libs/function/sys.js" beforeDir="false" afterPath="$PROJECT_DIR$/components/uview-ui/libs/function/sys.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/uview-ui/libs/mixin/mixin.js" beforeDir="false" afterPath="$PROJECT_DIR$/components/uview-ui/libs/mixin/mixin.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/http.api.js" beforeDir="false" afterPath="$PROJECT_DIR$/config/http.api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/main.js" beforeDir="false" afterPath="$PROJECT_DIR$/main.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/.package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/.package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/CONTRIBUTING.md" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/CONTRIBUTING.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/LICENSE" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/LICENSE" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/aes.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/aes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/blowfish.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/blowfish.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/bower.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/bower.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/cipher-core.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/cipher-core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/core.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/crypto-js.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/crypto-js.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/enc-base64.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/enc-base64.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/enc-base64url.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/enc-base64url.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/enc-hex.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/enc-hex.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/enc-latin1.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/enc-latin1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/enc-utf16.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/enc-utf16.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/enc-utf8.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/enc-utf8.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/evpkdf.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/evpkdf.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/format-hex.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/format-hex.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/format-openssl.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/format-openssl.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-md5.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-md5.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-ripemd160.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-ripemd160.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha1.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha224.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha224.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha256.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha256.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha3.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha384.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha384.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha512.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac-sha512.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/hmac.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/hmac.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/lib-typedarrays.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/lib-typedarrays.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/md5.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/md5.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/mode-cfb.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/mode-cfb.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/mode-ctr-gladman.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/mode-ctr-gladman.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/mode-ctr.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/mode-ctr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/mode-ecb.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/mode-ecb.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/mode-ofb.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/mode-ofb.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pad-ansix923.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pad-ansix923.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pad-iso10126.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pad-iso10126.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pad-iso97971.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pad-iso97971.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pad-nopadding.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pad-nopadding.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pad-pkcs7.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pad-pkcs7.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pad-zeropadding.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pad-zeropadding.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/pbkdf2.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/pbkdf2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/rabbit-legacy.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/rabbit-legacy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/rabbit.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/rabbit.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/rc4.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/rc4.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/ripemd160.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/ripemd160.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/sha1.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/sha1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/sha224.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/sha224.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/sha256.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/sha256.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/sha3.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/sha3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/sha384.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/sha384.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/sha512.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/sha512.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/tripledes.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/tripledes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/crypto-js/x64-core.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/crypto-js/x64-core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/tslib/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/tslib/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/.eslintignore" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/.eslintignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/LICENSE" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/LICENSE" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/build.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/build.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/prepareNightly.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/prepareNightly.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/prepublish.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/prepublish.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/processLib.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/processLib.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/progress.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/progress.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/build/transformImport.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/build/transformImport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/dist/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/dist/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/dist/zrender.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/dist/zrender.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/index.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/index.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/Element.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/Element.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/Element.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/Element.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/Handler.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/Handler.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/Handler.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/Handler.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/PainterBase.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/PainterBase.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/PainterBase.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/PainterBase.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/Storage.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/Storage.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/Storage.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/Storage.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/all.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/all.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/all.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/all.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animation.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animation.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animation.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animator.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animator.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animator.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Animator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Clip.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Clip.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Clip.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/Clip.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/cubicEasing.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/cubicEasing.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/cubicEasing.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/cubicEasing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/easing.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/easing.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/easing.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/easing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/requestAnimationFrame.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/requestAnimationFrame.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/animation/requestAnimationFrame.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/animation/requestAnimationFrame.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Layer.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Layer.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Layer.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Layer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Painter.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Painter.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Painter.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/Painter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/canvas.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/canvas.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/canvas.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/canvas.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/dashStyle.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/dashStyle.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/dashStyle.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/dashStyle.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/graphic.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/graphic.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/graphic.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/graphic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/helper.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/helper.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/helper.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/canvas/helper.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/config.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/config.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/arc.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/arc.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/arc.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/arc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/cubic.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/cubic.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/cubic.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/cubic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/line.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/line.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/line.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/line.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/path.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/path.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/path.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/path.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/polygon.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/polygon.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/polygon.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/polygon.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/quadratic.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/quadratic.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/quadratic.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/quadratic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/text.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/text.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/text.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/text.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/util.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/util.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/util.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/util.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/windingLine.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/windingLine.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/contain/windingLine.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/contain/windingLine.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/BoundingRect.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/BoundingRect.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/BoundingRect.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/BoundingRect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/Eventful.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/Eventful.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/Eventful.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/Eventful.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/GestureMgr.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/GestureMgr.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/GestureMgr.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/GestureMgr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/LRU.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/LRU.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/LRU.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/LRU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/OrientedBoundingRect.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/OrientedBoundingRect.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/OrientedBoundingRect.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/OrientedBoundingRect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/PathProxy.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/PathProxy.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/PathProxy.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/PathProxy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/Point.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/Point.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/Point.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/Point.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/Transformable.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/Transformable.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/Transformable.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/Transformable.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/WeakMap.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/WeakMap.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/WeakMap.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/WeakMap.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/arrayDiff.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/arrayDiff.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/arrayDiff.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/arrayDiff.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/bbox.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/bbox.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/bbox.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/bbox.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/curve.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/curve.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/curve.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/curve.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/dom.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/dom.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/dom.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/dom.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/env.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/env.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/env.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/env.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/event.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/event.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/event.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/event.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/fourPointsTransform.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/fourPointsTransform.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/fourPointsTransform.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/fourPointsTransform.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/matrix.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/matrix.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/matrix.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/matrix.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/platform.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/platform.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/platform.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/platform.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/timsort.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/timsort.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/timsort.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/timsort.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/types.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/types.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/types.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/types.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/util.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/util.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/util.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/util.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/vector.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/vector.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/core/vector.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/core/vector.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/debug/showDebugDirtyRect.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/debug/showDebugDirtyRect.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/debug/showDebugDirtyRect.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/debug/showDebugDirtyRect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/dom/HandlerProxy.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/dom/HandlerProxy.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/dom/HandlerProxy.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/dom/HandlerProxy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/export.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/export.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/export.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/export.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/CompoundPath.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/CompoundPath.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/CompoundPath.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/CompoundPath.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Displayable.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Displayable.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Displayable.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Displayable.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Gradient.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Gradient.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Gradient.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Gradient.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Group.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Group.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Group.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Group.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Image.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Image.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Image.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Image.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/IncrementalDisplayable.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/IncrementalDisplayable.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/IncrementalDisplayable.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/IncrementalDisplayable.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/LinearGradient.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/LinearGradient.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/LinearGradient.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/LinearGradient.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Path.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Path.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Path.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Path.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Pattern.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Pattern.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Pattern.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Pattern.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/RadialGradient.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/RadialGradient.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/RadialGradient.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/RadialGradient.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/TSpan.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/TSpan.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/TSpan.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/TSpan.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Text.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Text.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Text.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/Text.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/constants.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/constants.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/constants.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/constants.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/image.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/image.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/image.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/image.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/parseText.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/parseText.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/parseText.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/parseText.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/poly.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/poly.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/poly.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/poly.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundRect.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundRect.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundRect.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundRect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundSector.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundSector.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundSector.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/roundSector.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothBezier.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothBezier.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothBezier.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothBezier.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothSpline.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothSpline.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothSpline.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/smoothSpline.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/subPixelOptimize.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/subPixelOptimize.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/subPixelOptimize.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/helper/subPixelOptimize.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Arc.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Arc.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Arc.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Arc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/BezierCurve.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/BezierCurve.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/BezierCurve.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/BezierCurve.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Circle.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Circle.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Circle.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Circle.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Droplet.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Droplet.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Droplet.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Droplet.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ellipse.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ellipse.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ellipse.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ellipse.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Heart.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Heart.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Heart.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Heart.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Isogon.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Isogon.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Isogon.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Isogon.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Line.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Line.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Line.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Line.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polygon.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polygon.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polygon.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polygon.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polyline.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polyline.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polyline.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Polyline.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rect.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rect.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rect.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ring.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ring.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ring.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Ring.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rose.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rose.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rose.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Rose.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Sector.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Sector.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Sector.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Sector.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Star.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Star.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Star.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Star.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Trochoid.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Trochoid.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Trochoid.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/graphic/shape/Trochoid.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/mixin/Draggable.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/mixin/Draggable.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/mixin/Draggable.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/mixin/Draggable.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/Painter.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/Painter.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/Painter.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/Painter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/graphic.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/graphic.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/graphic.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/graphic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ClippathManager.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ClippathManager.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ClippathManager.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ClippathManager.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/Definable.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/Definable.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/Definable.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/Definable.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/GradientManager.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/GradientManager.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/GradientManager.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/GradientManager.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/PatternManager.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/PatternManager.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/PatternManager.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/PatternManager.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ShadowManager.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ShadowManager.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ShadowManager.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/helper/ShadowManager.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/svg-legacy.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/svg-legacy.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/svg-legacy.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg-legacy/svg-legacy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/Painter.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/Painter.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/Painter.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/Painter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/SVGPathRebuilder.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/SVGPathRebuilder.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/SVGPathRebuilder.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/SVGPathRebuilder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/core.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/core.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/core.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssAnimation.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssAnimation.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssAnimation.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssAnimation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssClassId.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssClassId.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssClassId.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssClassId.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssEmphasis.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssEmphasis.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssEmphasis.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/cssEmphasis.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/domapi.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/domapi.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/domapi.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/domapi.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/graphic.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/graphic.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/graphic.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/graphic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/helper.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/helper.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/helper.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/helper.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/mapStyleToAttrs.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/mapStyleToAttrs.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/mapStyleToAttrs.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/mapStyleToAttrs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/patch.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/patch.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/patch.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/patch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/svg.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/svg.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/svg/svg.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/svg/svg.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/color.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/color.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/color.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/color.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/convertPath.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/convertPath.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/convertPath.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/convertPath.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/dividePath.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/dividePath.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/dividePath.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/dividePath.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/morphPath.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/morphPath.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/morphPath.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/morphPath.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseSVG.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseSVG.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseSVG.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseSVG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseXML.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseXML.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseXML.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/parseXML.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/path.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/path.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/path.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/path.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/transformPath.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/transformPath.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/tool/transformPath.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/tool/transformPath.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/zrender.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/zrender.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/lib/zrender.js" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/lib/zrender.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/package.README.md" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/package.README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/.eslintrc.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/.eslintrc.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/Element.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/Element.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/Handler.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/Handler.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/PainterBase.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/PainterBase.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/Storage.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/Storage.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/all.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/all.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/animation/Animation.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/animation/Animation.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/animation/Animator.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/animation/Animator.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/animation/Clip.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/animation/Clip.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/animation/cubicEasing.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/animation/cubicEasing.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/animation/easing.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/animation/easing.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/animation/requestAnimationFrame.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/animation/requestAnimationFrame.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/canvas/Layer.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/canvas/Layer.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/canvas/Painter.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/canvas/Painter.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/canvas/canvas.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/canvas/canvas.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/canvas/dashStyle.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/canvas/dashStyle.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/canvas/graphic.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/canvas/graphic.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/canvas/helper.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/canvas/helper.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/arc.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/arc.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/cubic.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/cubic.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/line.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/line.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/path.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/path.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/polygon.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/polygon.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/quadratic.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/quadratic.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/text.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/text.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/util.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/util.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/contain/windingLine.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/contain/windingLine.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/BoundingRect.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/BoundingRect.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/Eventful.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/Eventful.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/GestureMgr.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/GestureMgr.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/LRU.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/LRU.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/OrientedBoundingRect.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/OrientedBoundingRect.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/PathProxy.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/PathProxy.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/Point.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/Point.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/Transformable.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/Transformable.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/WeakMap.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/WeakMap.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/arrayDiff.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/arrayDiff.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/bbox.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/bbox.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/curve.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/curve.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/dom.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/dom.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/env.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/env.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/event.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/event.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/fourPointsTransform.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/fourPointsTransform.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/matrix.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/matrix.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/platform.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/platform.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/timsort.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/timsort.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/types.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/types.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/util.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/util.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/core/vector.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/core/vector.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/debug/showDebugDirtyRect.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/debug/showDebugDirtyRect.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/dom/HandlerProxy.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/dom/HandlerProxy.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/export.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/export.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/global.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/global.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/CompoundPath.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/CompoundPath.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Displayable.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Displayable.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Gradient.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Gradient.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Group.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Group.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Image.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Image.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/IncrementalDisplayable.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/IncrementalDisplayable.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/LinearGradient.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/LinearGradient.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Path.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Path.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Pattern.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Pattern.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/RadialGradient.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/RadialGradient.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/TSpan.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/TSpan.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Text.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/Text.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/constants.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/constants.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/image.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/image.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/parseText.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/parseText.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/poly.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/poly.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/roundRect.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/roundRect.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/roundSector.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/roundSector.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/smoothBezier.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/smoothBezier.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/smoothSpline.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/smoothSpline.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/subPixelOptimize.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/helper/subPixelOptimize.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Arc.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Arc.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/BezierCurve.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/BezierCurve.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Circle.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Circle.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Droplet.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Droplet.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Ellipse.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Ellipse.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Heart.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Heart.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Isogon.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Isogon.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Line.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Line.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Polygon.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Polygon.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Polyline.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Polyline.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Rect.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Rect.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Ring.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Ring.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Rose.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Rose.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Sector.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Sector.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Star.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Star.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Trochoid.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/graphic/shape/Trochoid.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/mixin/Draggable.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/mixin/Draggable.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/Painter.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/Painter.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/graphic.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/graphic.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/ClippathManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/ClippathManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/Definable.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/Definable.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/GradientManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/GradientManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/PatternManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/PatternManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/ShadowManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/helper/ShadowManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/svg-legacy.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg-legacy/svg-legacy.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/Painter.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/Painter.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/SVGPathRebuilder.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/SVGPathRebuilder.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/core.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/core.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/cssAnimation.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/cssAnimation.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/cssClassId.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/cssClassId.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/cssEmphasis.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/cssEmphasis.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/domapi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/domapi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/graphic.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/graphic.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/helper.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/helper.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/mapStyleToAttrs.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/mapStyleToAttrs.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/patch.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/patch.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/svg/svg.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/svg/svg.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/color.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/color.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/convertPath.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/convertPath.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/dividePath.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/dividePath.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/morphPath.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/morphPath.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/parseSVG.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/parseSVG.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/parseXML.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/parseXML.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/path.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/path.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/tool/transformPath.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/tool/transformPath.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/src/zrender.ts" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/src/zrender.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/node_modules/zrender/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/node_modules/zrender/tsconfig.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pagesSub/switch/vote.vue" beforeDir="false" afterPath="$PROJECT_DIR$/pagesSub/switch/vote.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/styles/style.css" beforeDir="false" afterPath="$PROJECT_DIR$/styles/style.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/changelog.md" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/changelog.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uni-icons.uvue" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uni-icons.uvue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uni-icons.vue" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uni-icons.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uniicons.css" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uniicons.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uniicons_file.ts" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uniicons_file.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uniicons_file_vue.js" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/components/uni-icons/uniicons_file_vue.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-icons/readme.md" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-icons/readme.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/changelog.md" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/changelog.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/index.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/index.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/readme.md" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/readme.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/index.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/index.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_border.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_border.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_color.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_color.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_radius.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_radius.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_space.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_space.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_styles.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_styles.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_text.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_text.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_variables.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/setting/_variables.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/styles/tools/functions.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/styles/tools/functions.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/theme.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/theme.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uni_modules/uni-scss/variables.scss" beforeDir="false" afterPath="$PROJECT_DIR$/uni_modules/uni-scss/variables.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.automator/app-plus/.automator.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.automator/mp-alipay/.automator.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/common/main.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/common/main.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/common/runtime.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/common/runtime.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/PreviewCard.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/PreviewCard.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/mescroll-uni/components/mescroll-empty.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/mescroll-uni/components/mescroll-empty.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/mescroll-uni/components/mescroll-top.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/mescroll-uni/components/mescroll-top.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/mescroll-uni/mescroll-body.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/mescroll-uni/mescroll-body.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/open-one-box/open-one-box.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/open-one-box/open-one-box.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/open-several-box/open-several-box.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/tabbar.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/tabbar.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uniapp-zaudio/zaudio.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-badge/u-badge.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-column-notice/u-column-notice.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-icon/u-icon.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-icon/u-icon.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-loading/u-loading.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-loading/u-loading.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-mask/u-mask.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-mask/u-mask.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-notice-bar/u-notice-bar.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-popup/u-popup.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-popup/u-popup.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-row-notice/u-row-notice.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-sticky/u-sticky.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-switch/u-switch.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-switch/u-switch.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/components/uview-ui/components/u-tabbar/u-tabbar.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/Schedule.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/Schedule.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/Schedulexq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/Schedulexq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/confirmOrder.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/confirmOrder.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/search.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/search.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/searchResults.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/Schedule/searchResults.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/buy.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/buy.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/cardsPayment.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/cardsPayment.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/cardsSuccess.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/cardsSuccess.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/confirmOrder.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/confirmOrder.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/coursePackage.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/coursePackage.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/lessonPackagexq.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/myCoursexq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/myCoursexq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/orderPayment.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/orderPayment.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/success.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/success.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/teacherDetails.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/coursePackage/teacherDetails.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/membershipCard.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/membershipCard.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/confirmOrder.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/confirmOrder.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/pointsMall.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/pointsMall.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/productDetails.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/productDetails.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/search.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/search.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/searchResults.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/searchResults.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/success.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/pointsMall/success.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/selectStores.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/selectStores.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/specification.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/buy/specification.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/foxDetail.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/foxDetail.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/service.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/service.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/signing.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/signing.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/storesDetail.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/storesDetail.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/switchStores.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/switchStores.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/teacherDetail.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/teacherDetail.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index1/index1.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index1/index1.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/forgetPass.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/login.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/login.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/mobile_login.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/register.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/xieYi.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/xieYi.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/add_address.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/add_address.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/address.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/address.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/couponbag.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/couponbag.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/editinformation.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/editinformation.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/integral.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/integral.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/invitation.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/invitation.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/leave/leave.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/leave/leave.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/leave/leaveCard.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/leave/leaveCard.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/leave/leaveLists.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/leave/leaveLists.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/lessonPackage/lessonPackage.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/lessonPackage/lessonPackage.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/memberCard/myMemberCard.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/memberCard/myMemberCard.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/memberCard/myMemberCardxq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/memberCard/myMemberCardxq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/messageCenter.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/messageCenter.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/mine.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/mine.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/myCourse/myCourse.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/myCourse/myCourse.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/myCourse/myCoursexq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/myCourse/myCoursexq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/order/logistics.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/order/logistics.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/order/order.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/order/order.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/ranking.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/ranking.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/settings/feedback.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/settings/feedback.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/settings/settings.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/settings/settings.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/tzgl.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/tzgl.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/tzglxq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/tzglxq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/monthUserReport.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/monthUserReport.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/userReport.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/userReport.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/weeksUserReport.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/weeksUserReport.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/yearsUserReport.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/userReport/yearsUserReport.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/confirmOrder.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/confirmOrder.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/dengji.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/dengji.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/edit_skxx.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/edit_skxx.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/hb_confirmOrders.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/hb_confirmOrders.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/prizedraw.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/prizedraw.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/selectStores.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/selectStores.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/success.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/success.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/winningrecord.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/winningrecord.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/winningrecordxq.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/prizedraw/winningrecordxq.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/webView/webView.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pages/webView/webView.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/demo/demo.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/demo/demo.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/index/fox_introduce.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/index/store_detail.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/mine/questFeed.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/mine/questFeed_record.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/setting/chang_pass.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/setting/changeName.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/setting/setting.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/switch/vote.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/pagesSub/switch/vote.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappchooselocation.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniapperror.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappes6.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappopenlocation.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniapppicker.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappquill.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappquillimageresize.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappscan.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappsuccess.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/__uniappview.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/app-config-service.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/app-config.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/app-service.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/app-view.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/assets/uniicons.2579c7da.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/1Mr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/1Mr_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/Mr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/Mr_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/Mr_x2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/addr_del.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/addr_edit.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/addr_kong.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/bak.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/cjxz-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/cjxz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/delAll.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/dzxz-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/dzxz-11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/dzxz-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/dzxz-3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/dzxz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon12.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon14.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon15.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon17.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon18-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon19.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon23.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon23.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon24.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon25.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon26-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon26.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon27.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon28-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon28.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon29.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon30.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon31.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon33-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon33.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon34.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon35.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon36.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon37.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon38.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon46-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon46-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon46.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon47.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon48.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon49-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon50.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon51.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon52-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon52-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon52-3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon52-4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon53.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon54.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon56.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon57.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon58.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon59.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon60.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon61.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon62.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon67.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon68.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon69.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon70.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon71.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon72.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon73.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon74.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon75.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon76.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon77.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon78.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon81.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon82.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon83-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon83-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon83-3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon83.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon84-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon84.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon85-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon85.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon86.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon87.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/icon9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/index_concat_kf.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/index_help_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/index_notice.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/index_share.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/index_shop_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/introduce_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/jt1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/loginbj.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/popup-icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/popup_close.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/right_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/search.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/store_map_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/toux.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/toux1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/wusj.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/wusj_bf.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/xz-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/xz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/yj1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/images/yj2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/box_bgi.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_big1-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_big1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_big2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_buy.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_buy_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_fox.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_fox1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_fox_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_home.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_home_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_mine.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_mine_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_need.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_need_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_order.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_order_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_schedule.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/tab_schedule_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/static/tabbar/xjt.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/view.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/app-plus/view.umd.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/.mini-ide/compileMode.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/.mini-ide/project-ide.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/app.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/app.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/app.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/assets/uniicons.2579c7da.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/common/main.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/common/main.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/common/runtime.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/common/vendor.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-empty.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-empty.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-empty.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-empty.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-top.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-top.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-top.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/components/mescroll-top.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/mescroll-body.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/mescroll-body.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/mescroll-body.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/mescroll-uni/mescroll-body.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/tabbar.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/tabbar.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/tabbar.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/tabbar.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-icon/u-icon.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-icon/u-icon.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-icon/u-icon.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-icon/u-icon.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-loading/u-loading.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-loading/u-loading.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-loading/u-loading.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-loading/u-loading.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-mask/u-mask.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-mask/u-mask.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-mask/u-mask.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-mask/u-mask.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-navbar/u-navbar.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-navbar/u-navbar.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-navbar/u-navbar.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-navbar/u-navbar.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-popup/u-popup.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-popup/u-popup.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-popup/u-popup.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-popup/u-popup.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-swiper/u-swiper.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-swiper/u-swiper.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-swiper/u-swiper.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-swiper/u-swiper.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-switch/u-switch.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-switch/u-switch.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-switch/u-switch.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/components/uview-ui/components/u-switch/u-switch.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/mini.project.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedule.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedule.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedule.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedule.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedulexq.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedulexq.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedulexq.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/Schedulexq.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/confirmOrder.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/confirmOrder.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/confirmOrder.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/confirmOrder.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/search.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/search.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/search.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/search.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/searchResults.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/searchResults.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/searchResults.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/Schedule/searchResults.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/buy.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/buy.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/buy.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/buy.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsPayment.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsPayment.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsPayment.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsPayment.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsSuccess.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsSuccess.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsSuccess.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/cardsSuccess.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/confirmOrder.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/confirmOrder.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/confirmOrder.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/confirmOrder.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/coursePackage.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/coursePackage.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/coursePackage.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/coursePackage.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/myCoursexq.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/myCoursexq.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/myCoursexq.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/myCoursexq.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/orderPayment.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/orderPayment.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/orderPayment.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/orderPayment.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/success.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/success.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/success.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/success.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/teacherDetails.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/teacherDetails.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/teacherDetails.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/coursePackage/teacherDetails.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/membershipCard.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/membershipCard.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/membershipCard.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/membershipCard.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/confirmOrder.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/confirmOrder.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/confirmOrder.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/confirmOrder.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/pointsMall.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/pointsMall.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/pointsMall.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/pointsMall.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/productDetails.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/productDetails.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/productDetails.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/productDetails.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/search.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/search.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/search.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/search.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/searchResults.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/searchResults.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/searchResults.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/searchResults.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/success.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/success.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/success.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/pointsMall/success.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/selectStores.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/selectStores.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/selectStores.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/buy/selectStores.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/foxDetail.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/foxDetail.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/foxDetail.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/foxDetail.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/index.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/index.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/index.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/service.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/service.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/service.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/service.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/storesDetail.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/storesDetail.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/storesDetail.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/storesDetail.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/switchStores.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/switchStores.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/switchStores.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/switchStores.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/teacherDetail.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/teacherDetail.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/teacherDetail.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index/teacherDetail.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index1/index1.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index1/index1.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/index1/index1.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/login.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/login.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/login.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/login.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/xieYi.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/xieYi.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/xieYi.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/login/xieYi.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/add_address.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/add_address.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/add_address.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/add_address.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/address.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/address.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/address.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/address.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/couponbag.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/couponbag.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/couponbag.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/couponbag.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/editinformation.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/editinformation.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/editinformation.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/editinformation.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/integral.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/integral.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/integral.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/integral.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/invitation.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/invitation.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/invitation.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/invitation.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leave.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leave.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leave.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leave.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveCard.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveCard.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveCard.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveLists.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveLists.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveLists.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/leave/leaveLists.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackage.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackage.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackage.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackage.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackagexq.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackagexq.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackagexq.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/lessonPackage/lessonPackagexq.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/memberCard/myMemberCard.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/memberCard/myMemberCard.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/memberCard/myMemberCard.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/memberCard/myMemberCardxq.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/memberCard/myMemberCardxq.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/memberCard/myMemberCardxq.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/messageCenter.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/messageCenter.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/messageCenter.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/messageCenter.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/mine.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/mine.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/mine.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/mine.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCourse.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCourse.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCourse.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCoursexq.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCoursexq.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCoursexq.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/myCourse/myCoursexq.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/logistics.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/logistics.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/logistics.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/logistics.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/order.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/order.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/order/order.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/ranking.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/ranking.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/ranking.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/ranking.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/feedback.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/feedback.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/feedback.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/feedback.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/settings.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/settings.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/settings.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/settings/settings.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/monthUserReport.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/monthUserReport.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/monthUserReport.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/monthUserReport.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/weeksUserReport.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/weeksUserReport.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/weeksUserReport.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/weeksUserReport.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/yearsUserReport.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/yearsUserReport.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/yearsUserReport.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/mine/userReport/yearsUserReport.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/webView/webView.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/webView/webView.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/pages/webView/webView.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/plugin-wrapper.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/plugin-wrapper.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/plugin-wrapper.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/1Mr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/1Mr_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/1index_concat_kf.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/1index_share.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/Mr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/Mr_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/Mr_x2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/addr_del.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/addr_edit.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/addr_kong.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/delAll.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/dzxz-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/dzxz-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/dzxz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon11.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon12.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon13.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon14.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon15.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon16.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon17.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon18-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon18.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon19.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon20.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon21.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon22.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon23.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon23.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon24.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon25.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon26.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon27.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon28.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon29.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon30.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon31.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon32.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon33.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon34.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon35.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon36.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon37.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon46-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon46-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon46.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon47.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon48.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon49-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon50.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon51.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon52-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon52-2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon52.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon53.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon54.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon55.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon56.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon57.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon58.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon59.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon8.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/icon9.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_concat_kf.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_fox_js.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_fox_lsjs.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_fox_mdjs.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_help_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_notice.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_share.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/index_shop_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/introduce_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/popup-icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/popup_close.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/right_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/search.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/store_map_icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/toux.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/toux1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/wusj.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/xz-1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/xz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/yj1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/images/yj2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/login/login_text.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/login/login_top_bgi.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/box_bgi.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_big1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_big2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_buy.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_buy_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_fox.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_fox_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_home.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_home_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_mine.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_mine_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_need.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_need_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_order.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_order_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_schedule.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/static/tabbar/tab_schedule_x.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-icons/components/uni-icons/uni-icons.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-icons/components/uni-icons/uni-icons.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-icons/components/uni-icons/uni-icons.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-icons/components/uni-icons/uni-icons.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.acss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.axml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-alipay/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/app.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/app.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/app.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/app.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/main.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/main.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/main.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/main.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/runtime.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/runtime.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/vendor.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/common/vendor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/PreviewCard.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/PreviewCard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/PreviewCard.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/PreviewCard.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/PreviewCard.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/PreviewCard.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-empty.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-empty.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-empty.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-empty.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-empty.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-empty.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-top.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-top.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-top.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-top.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-top.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/components/mescroll-top.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.wxml" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.wxml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/mescroll-uni/mescroll-body.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/open-one-box/open-one-box.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/open-one-box/open-one-box.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/open-one-box/open-one-box.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/open-one-box/open-one-box.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/open-one-box/open-one-box.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/open-one-box/open-one-box.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.wxml" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.wxml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/tabbar.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-icon/u-icon.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-icon/u-icon.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-icon/u-icon.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-icon/u-icon.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-icon/u-icon.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-icon/u-icon.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-loading/u-loading.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-loading/u-loading.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-loading/u-loading.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-loading/u-loading.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-loading/u-loading.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-loading/u-loading.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-mask/u-mask.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-mask/u-mask.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-mask/u-mask.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-mask/u-mask.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-mask/u-mask.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-mask/u-mask.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-navbar/u-navbar.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-popup/u-popup.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-popup/u-popup.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-popup/u-popup.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-popup/u-popup.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-popup/u-popup.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-popup/u-popup.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-swiper/u-swiper.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-switch/u-switch.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-switch/u-switch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-switch/u-switch.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-switch/u-switch.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-switch/u-switch.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/components/uview-ui/components/u-switch/u-switch.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedule.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedule.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedule.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedule.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedule.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedule.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedulexq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedulexq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedulexq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedulexq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedulexq.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/Schedulexq.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/confirmOrder.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/confirmOrder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/confirmOrder.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/confirmOrder.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/confirmOrder.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/confirmOrder.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/search.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/search.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/search.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/search.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/search.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/search.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/searchResults.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/searchResults.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/searchResults.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/searchResults.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/searchResults.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/Schedule/searchResults.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/buy.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/buy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/buy.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/buy.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/buy.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/buy.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsPayment.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsPayment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsPayment.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsPayment.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsPayment.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsPayment.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsSuccess.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsSuccess.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsSuccess.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsSuccess.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsSuccess.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/cardsSuccess.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/confirmOrder.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/confirmOrder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/confirmOrder.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/confirmOrder.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/confirmOrder.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/confirmOrder.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/coursePackage.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/coursePackage.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/coursePackage.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/coursePackage.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/coursePackage.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/coursePackage.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/myCoursexq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/myCoursexq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/myCoursexq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/myCoursexq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/myCoursexq.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/myCoursexq.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/orderPayment.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/orderPayment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/orderPayment.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/orderPayment.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/orderPayment.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/orderPayment.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/success.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/success.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/success.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/success.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/success.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/success.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/teacherDetails.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/teacherDetails.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/teacherDetails.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/teacherDetails.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/teacherDetails.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/coursePackage/teacherDetails.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/membershipCard.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/membershipCard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/membershipCard.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/membershipCard.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/membershipCard.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/membershipCard.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/confirmOrder.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/confirmOrder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/confirmOrder.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/confirmOrder.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/confirmOrder.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/confirmOrder.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/pointsMall.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/pointsMall.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/pointsMall.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/pointsMall.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/pointsMall.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/pointsMall.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/productDetails.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/productDetails.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/productDetails.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/productDetails.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/productDetails.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/productDetails.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/search.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/search.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/search.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/search.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/search.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/search.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/searchResults.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/searchResults.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/searchResults.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/searchResults.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/searchResults.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/searchResults.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/success.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/success.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/success.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/success.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/success.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/pointsMall/success.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/selectStores.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/selectStores.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/selectStores.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/selectStores.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/selectStores.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/selectStores.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/specification.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/specification.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/specification.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/specification.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/specification.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/buy/specification.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/foxDetail.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/foxDetail.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/foxDetail.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/foxDetail.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/foxDetail.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/foxDetail.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/service.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/service.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/service.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/service.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/service.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/service.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/signing.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/signing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/signing.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/signing.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/signing.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/signing.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/storesDetail.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/storesDetail.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/storesDetail.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/storesDetail.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/storesDetail.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/storesDetail.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/switchStores.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/switchStores.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/switchStores.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/switchStores.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/switchStores.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/switchStores.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/teacherDetail.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/teacherDetail.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/teacherDetail.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/teacherDetail.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/teacherDetail.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/index/teacherDetail.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/login.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/login.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/login.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/login.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/login.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/login.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/xieYi.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/xieYi.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/xieYi.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/xieYi.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/xieYi.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/login/xieYi.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/add_address.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/add_address.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/add_address.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/add_address.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/add_address.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/add_address.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/address.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/address.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/address.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/address.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/address.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/address.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/couponbag.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/couponbag.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/couponbag.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/couponbag.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/couponbag.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/couponbag.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/editinformation.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/editinformation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/editinformation.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/editinformation.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/editinformation.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/editinformation.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/integral.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/integral.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/integral.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/integral.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/integral.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/integral.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/invitation.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/invitation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/invitation.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/invitation.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/invitation.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/invitation.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leave.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leave.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leave.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leave.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leave.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leave.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveCard.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveCard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveCard.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveCard.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveLists.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveLists.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveLists.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveLists.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveLists.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/leave/leaveLists.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackage.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackage.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackage.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackage.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackage.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackage.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/lessonPackage/lessonPackagexq.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCard.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCard.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCard.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCardxq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCardxq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCardxq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/memberCard/myMemberCardxq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/messageCenter.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/messageCenter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/messageCenter.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/messageCenter.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/messageCenter.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/messageCenter.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/mine.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/mine.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/mine.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/mine.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/mine.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/mine.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCourse.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCourse.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCourse.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCourse.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCourse.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCourse.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCoursexq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCoursexq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCoursexq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCoursexq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCoursexq.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/myCourse/myCoursexq.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/logistics.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/logistics.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/logistics.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/logistics.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/logistics.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/logistics.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/order.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/order.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/order.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/order/order.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/ranking.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/ranking.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/ranking.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/ranking.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/ranking.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/ranking.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/feedback.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/feedback.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/feedback.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/feedback.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/feedback.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/feedback.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/settings.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/settings.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/settings.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/settings/settings.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzgl.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzgl.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzgl.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzgl.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzgl.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzgl.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzglxq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzglxq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzglxq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzglxq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzglxq.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/tzglxq.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/monthUserReport.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/monthUserReport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/monthUserReport.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/monthUserReport.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/monthUserReport.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/monthUserReport.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/userReport.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/userReport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/userReport.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/userReport.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/userReport.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/userReport.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/weeksUserReport.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/weeksUserReport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/weeksUserReport.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/weeksUserReport.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/weeksUserReport.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/weeksUserReport.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/yearsUserReport.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/yearsUserReport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/yearsUserReport.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/yearsUserReport.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/yearsUserReport.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/mine/userReport/yearsUserReport.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/confirmOrder.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/confirmOrder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/confirmOrder.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/confirmOrder.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/confirmOrder.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/confirmOrder.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/dengji.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/dengji.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/dengji.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/dengji.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/dengji.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/dengji.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/edit_skxx.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/edit_skxx.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/edit_skxx.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/edit_skxx.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/edit_skxx.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/edit_skxx.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/hb_confirmOrders.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/hb_confirmOrders.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/hb_confirmOrders.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/hb_confirmOrders.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/hb_confirmOrders.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/hb_confirmOrders.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/prizedraw.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/prizedraw.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/prizedraw.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/prizedraw.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/prizedraw.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/prizedraw.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/selectStores.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/selectStores.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/selectStores.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/selectStores.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/selectStores.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/selectStores.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/success.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/success.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/success.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/success.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/success.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/success.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecord.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecord.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecord.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecord.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecord.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecord.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecordxq.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecordxq.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecordxq.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecordxq.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecordxq.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/prizedraw/winningrecordxq.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/webView/webView.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/webView/webView.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/webView/webView.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pages/webView/webView.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/demo/demo.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/demo/demo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/demo/demo.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/demo/demo.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/demo/demo.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/demo/demo.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.wxml" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.wxml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/pagesSub/switch/vote.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/project.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/project.config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/project.private.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/project.private.config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/jp-signature/components/jp-signature/jp-signature.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar-item.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/calendar.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.js" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.json" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/unpackage/dist/dev/mp-weixin/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/utils.js" beforeDir="false" afterPath="$PROJECT_DIR$/utils/utils.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yi6nHh0KuM3SIb8kCICXVkbPSI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Project/fox/用户端/fox-dance-user-terminal&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\DevolopmentTools\\WebStorm 2024.1.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Project\fox\用户端\fox-dance-user-terminal" />
      <recent name="D:\Project\fox\用户端\fox-dance-user-terminal\components" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Project\fox\用户端\fox-dance-user-terminal\static\icon" />
      <recent name="D:\Project\fox\用户端\fox-dance-user-terminal\static" />
      <recent name="D:\Project\fox\用户端\fox-dance-user-terminal\pagesSub\switch" />
      <recent name="D:\Project\fox\用户端\fox-dance-user-terminal\config" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-WS-241.18034.50" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="82a7eea7-5706-4bcf-abb2-3247d3a5e397" name="Changes" comment="" />
      <created>1750305211406</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750305211406</updated>
      <workItem from="1750305212436" duration="6422000" />
      <workItem from="1751509560756" duration="15000" />
      <workItem from="1752050483993" duration="692000" />
      <workItem from="1752120853338" duration="1959000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>