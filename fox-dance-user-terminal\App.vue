
<script>
	import{
		config
	} from '@/config/http.achieve.js'
	export default {
		onLaunch: function() {
			uni.hideTabBar()
			console.log('App Launch')
			uni.removeStorageSync('pageIndex')
			uni.removeStorageSync('pageZiliao')
			
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods:{
		
		}
	}
</script>

<style lang="scss">


	@import "@/components/uview-ui/index.scss";
	@import "@/styles/base.scss";
	@import "@/styles/common.scss";
	@import "@/styles/style.css";
	@import "@/styles/style_fz.css";
	@import '@/styles/animate.min.css';
	page{
		background-color: #f6f6f6;
	}
	image{
		display: block;
	}
	
	
	/*每个页面公共css */
	// 使用时仅需设置 宽高 圆角 和字号
	.btn {
		background-color: #131315;
		font-weight: 500;
		color: #FFFFFF;
		text-align: center;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		z-index: 11;
	}
	
	
	.inputRow {
		height: 110rpx;
		box-sizing: border-box;
		padding: 0 40rpx;
		background-color: #fff;
		border-bottom: 2rpx solid #f8f8f8;
	
		.laber {
			line-height: 110rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
	
			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 14rpx;
				margin-left: 0;
			}
		}
	
		input {
			flex: 1;
			height: 100%;
			text-align: right;
			font-size: 30rpx;
			font-weight: 500;
			line-height: 110rpx;
		}
	
		image {
			width: 16rpx;
			height: 28rpx;
			margin-left: 24rpx;
			margin-top: 4rpx;
		}
	
		text {
			margin-left: 24rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
		}
	
		.switch {
			width: 113rpx;
			height: 58rpx;
		}
	
		textarea {
			width: 100%;
			height: 130rpx;
			font-size: 30rpx;
		}
	}
	
	
	//晨阳设计图 二次确认弹窗常用样式  参考 /pages/setting/setting 退出登录弹窗
	.prompt {
		width: 600rpx;
		height: 340rpx;
		background: #FFFFFF;
		box-shadow: 0 0 10rpx 0 rgba(228, 239, 244, 0.6);
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		.prompt_t {
			box-sizing: border-box;
			padding: 22rpx 0 0 25rpx;
			display: flex;
			align-items: center;
	
			image {
				display: block;
				width: 33rpx;
				height: 33rpx;
				margin-right: 24rpx;
			}
	
			.prompt_t_text {
				font-size: 26rpx;
				font-weight: 400;
				color: #E93B3D;
				line-height: 36rpx;
			}
		}
	
		.prompt_c {
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
			padding: 0 40rpx;
			text-align: center
		}
	
		.prompt_d {
			height: 113rpx;
			display: flex;
			border-top: 1rpx solid #d5d5d5;
	
			.prompt_d_l {
				width: 50%;
				border-right: 1rpx solid #d5d5d5;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #8D8D8D;
				line-height: 113rpx;
			}
	
			.prompt_d_r {
				text-align: center;
				width: 50%;
				font-size: 32rpx;
				font-weight: 500;
				color: #000;
				line-height: 113rpx;
			}
		}
	}

</style>

