let apis = {
  // #ifdef H5
  vConsole: false,
  // #endif

  isTest: '0', // 运行环境


  // baseUrl: 'https://danceadmin.xinzhiyukeji.cn', // 接口地址
  baseUrl: 'https://admin.foxdance.com.cn', // 接口地址
  kefu_baseUrl: 'https://dancekefu.xinzhiyukeji.cn', // socket客服接口地址
  htym_baseUrl: 'https://contract.foxdance.com.cn', // 合同域名
  vote_baseUrl: 'https://vote.foxdance.com.cn', // 投票域名

  // https://mb.xinzhiyukeji.cn/admin.php/index/login


  // baseUrl: 'https://cpc-zm-test.mideazy.com', // 接口地址
  baseFile: '', // 文件地址
  baseWeb: '', // 前端地址
}


export default {
  // #ifndef VUE3
  install(Vue) {
    Vue.prototype.$api = apis
  },
  // #endif

  // #ifdef VUE3
  install(app, ops) {
    app.config.globalProperties.$api = apis
  },
  // #endif

  apis,
  
  // 2.全局 登录页面 配置
  	// 由于有些页面 可能使用navto/relaunch 所以只返回页面路径，具体页面页面自行处理跳转方法
  	getLoginPage() {
  		var lgPage = "";
  		// #ifdef APP-PLUS
  		lgPage = "/pages/login/wxLogin/wxLogin";
  		// #endif
  		// #ifdef MP-WEIXIN
  		lgPage = "/pages/login/login/login";
  		// #endif
  		// #ifdef H5
  		lgPage = "/pages/login/h5Login/h5Login";
  		// #endif
  		return lgPage
  	},
  
  	// 3.全局首页面
  	getHomePage: "/pages/home/<USER>/index"
}
