/**
 * 店铺相关API接口
 */

import apis from './http.api.js'

// 获取投票系统的基础URL
const VOTE_BASE_URL = apis.apis.vote_baseUrl || 'https://vote.foxdance.com.cn'

/**
 * 店铺专用HTTP请求函数
 * @param {Object} options - 请求配置
 * @returns {Promise} 请求Promise对象
 */
function storeRequest(options) {
  const {
    url,
    method = 'GET',
    data,
    params
  } = options

  // 获取token
  const token = uni.getStorageSync('bausertoken') || uni.getStorageSync('token')

  // 设置请求头
  const header = {
    'Content-Type': 'application/json',
    'server': 1
  }

  // 添加token到请求头
  if (token) {
    header['bausertoken'] = token
  }

  // 完整URL
  let fullUrl = `${VOTE_BASE_URL}${url}`

  // 处理GET请求参数
  if (method.toUpperCase() === 'GET' && params) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')

    if (queryString) {
      fullUrl = `${fullUrl}?${queryString}`
    }
  }

  console.log('🏪 店铺API请求:', {
    method,
    url: fullUrl,
    header,
    data: data || params
  })

  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method: method.toUpperCase(),
      header,
      data: method.toUpperCase() === 'GET' ? undefined : data,
      success: (res) => {
        console.log('🏪 店铺API响应:', res)

        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          console.error('🏪 店铺API请求失败:', res)
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        console.error('🏪 店铺API网络错误:', err)
        reject(err)
      }
    })
  })
}

/**
 * 获取所有店铺名称
 */
export function getStoreNames() {
  return storeRequest({
    url: '/api/store/names',
    method: 'GET'
  })
}

/**
 * 获取所有有效店铺信息
 */
export function getStoreList() {
  return storeRequest({
    url: '/api/store/list',
    method: 'GET'
  })
}

/**
 * 根据ID获取店铺信息
 * @param {number} id 店铺ID
 */
export function getStoreById(id) {
  return storeRequest({
    url: `/api/store/${id}`,
    method: 'GET'
  })
}
