/**
 * 店铺相关API接口
 */

import { request } from '@/utils/request'

/**
 * 获取所有店铺名称
 */
export function getStoreNames() {
  return request({
    url: '/api/store/names',
    method: 'GET'
  })
}

/**
 * 获取所有有效店铺信息
 */
export function getStoreList() {
  return request({
    url: '/api/store/list',
    method: 'GET'
  })
}

/**
 * 根据ID获取店铺信息
 * @param {number} id 店铺ID
 */
export function getStoreById(id) {
  return request({
    url: `/api/store/${id}`,
    method: 'GET'
  })
}
