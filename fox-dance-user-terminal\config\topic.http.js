// 话题功能专用HTTP请求函数

// 话题功能API服务的基础路径 https://vote.foxdance.com.cn  http://localhost:8101
const BASE_URL = 'http://localhost:8101';

/**
 * 处理响应结果
 * @param {Object} result - 响应结果
 * @param {Function} resolve - Promise成功回调
 * @param {Function} reject - Promise失败回调
 */
function handleResponse(result, resolve, reject) {
  if (result.code === 0) {
    // 请求成功
    resolve(result);
  } else if (result.code === 409 || result.code === 303) {
    // 登录失效
    uni.removeStorageSync('token');
    uni.removeStorageSync('userid');
    uni.setStorageSync('tokenwx', 1);
    
    uni.showModal({
      title: '提示',
      content: '登录超时，请重新登录',
      confirmText: '去登录',
      success: function(res) {
        if (res.confirm) {
          uni.reLaunch({
            url: '/pages/login/login'
          });
          resolve(result);
        } else {
          reject(result);
        }
      }
    });
  } else {
    // 其他错误
    if (result.message) {
      uni.showToast({
        title: result.message,
        icon: "none"
      });
    }
    reject(result);
  }
}

/**
 * 话题专用HTTP请求函数
 * @param {Object} options - 请求配置
 * @returns {Promise} 请求Promise对象
 */
export default function $topicHttp(options) {
  const {
    url,
    data,
    method = 'post',
    contentType = 'application/json'
  } = options;

  // 获取token
  const token = uni.getStorageSync('token');
  
  // 设置请求头
  const header = {
    'Content-Type': contentType,
    'server': 1,
    'bausertoken': token
  };
  
  // 完整URL
  let fullUrl = `${BASE_URL}${url}`;
  
  // 处理GET请求参数
  if (method.toLowerCase() === 'get') {
    const params = options.params || data || {};
    if (Object.keys(params).length > 0) {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      
      fullUrl = `${fullUrl}?${queryString}`;
      console.log('GET请求完整URL:', fullUrl);
    }
  }
  
  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      data: method.toLowerCase() !== 'get' ? data : {}, // GET请求不传data
      method: method,
      header: header,
      success: function(res) {
        handleResponse(res.data, resolve, reject);
      },
      fail: function(e) {
        handleResponse({
          code: -1,
          message: '网络请求失败'
        }, resolve, reject);
      }
    });
  });
} 