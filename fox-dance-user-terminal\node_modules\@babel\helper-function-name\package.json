{"name": "@babel/helper-function-name", "version": "7.24.7", "description": "Helper function to change the property 'name' of every function", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-function-name"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-function-name", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/template": "^7.24.7", "@babel/types": "^7.24.7"}, "devDependencies": {"@babel/traverse": "^7.24.7"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}