{"version": 3, "sources": ["../src/ExplorerBase.ts"], "names": ["ExplorerBase", "constructor", "options", "cache", "loadCache", "Map", "searchCache", "config", "validateConfig", "clearLoadCache", "clear", "clearSearchCache", "clearCaches", "searchPlaces", "for<PERSON>ach", "place", "loader<PERSON>ey", "path", "extname", "loader", "loaders", "Error", "getExtensionDescription", "shouldSearchStopWithResult", "result", "isEmpty", "ignoreEmptySearchPlaces", "nextDirectoryToSearch", "currentDir", "currentResult", "nextDir", "nextDirUp", "stopDir", "loadPackageProp", "filepath", "content", "parsed<PERSON><PERSON><PERSON>", "loadJson", "packagePropValue", "packageProp", "getLoaderEntryForFile", "basename", "bind", "loadedContentToCosmiconfigResult", "loadedContent", "undefined", "validate<PERSON>ile<PERSON><PERSON>", "dir", "dirname", "ext"], "mappings": ";;;;;;;;AAAA;;AACA;;AACA;;;;AAUA,MAAMA,YAAN,CAAoE;AAK3DC,EAAAA,WAAW,CAACC,OAAD,EAAa;AAC7B,QAAIA,OAAO,CAACC,KAAR,KAAkB,IAAtB,EAA4B;AAC1B,WAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB;AACA,WAAKC,WAAL,GAAmB,IAAID,GAAJ,EAAnB;AACD;;AAED,SAAKE,MAAL,GAAcL,OAAd;AACA,SAAKM,cAAL;AACD;;AAEMC,EAAAA,cAAc,GAAS;AAC5B,QAAI,KAAKL,SAAT,EAAoB;AAClB,WAAKA,SAAL,CAAeM,KAAf;AACD;AACF;;AAEMC,EAAAA,gBAAgB,GAAS;AAC9B,QAAI,KAAKL,WAAT,EAAsB;AACpB,WAAKA,WAAL,CAAiBI,KAAjB;AACD;AACF;;AAEME,EAAAA,WAAW,GAAS;AACzB,SAAKH,cAAL;AACA,SAAKE,gBAAL;AACD;;AAEOH,EAAAA,cAAc,GAAS;AAC7B,UAAMD,MAAM,GAAG,KAAKA,MAApB;AAEAA,IAAAA,MAAM,CAACM,YAAP,CAAoBC,OAApB,CAA6BC,KAAD,IAAiB;AAC3C,YAAMC,SAAS,GAAGC,cAAKC,OAAL,CAAaH,KAAb,KAAuB,OAAzC;AACA,YAAMI,MAAM,GAAGZ,MAAM,CAACa,OAAP,CAAeJ,SAAf,CAAf;;AACA,UAAI,CAACG,MAAL,EAAa;AACX,cAAM,IAAIE,KAAJ,CACH,2BAA0BC,uBAAuB,CAChDP,KADgD,CAEhD,2BAA0BA,KAAM,cAH9B,CAAN;AAKD;;AAED,UAAI,OAAOI,MAAP,KAAkB,UAAtB,EAAkC;AAChC,cAAM,IAAIE,KAAJ,CACH,cAAaC,uBAAuB,CACnCP,KADmC,CAEnC,uCAAsC,OAAOI,MAAO,6BAA4BJ,KAAM,cAHpF,CAAN;AAKD;AACF,KAlBD;AAmBD;;AAESQ,EAAAA,0BAA0B,CAACC,MAAD,EAAqC;AACvE,QAAIA,MAAM,KAAK,IAAf,EAAqB,OAAO,KAAP;AACrB,QAAIA,MAAM,CAACC,OAAP,IAAkB,KAAKlB,MAAL,CAAYmB,uBAAlC,EAA2D,OAAO,KAAP;AAC3D,WAAO,IAAP;AACD;;AAESC,EAAAA,qBAAqB,CAC7BC,UAD6B,EAE7BC,aAF6B,EAGd;AACf,QAAI,KAAKN,0BAAL,CAAgCM,aAAhC,CAAJ,EAAoD;AAClD,aAAO,IAAP;AACD;;AACD,UAAMC,OAAO,GAAGC,SAAS,CAACH,UAAD,CAAzB;;AACA,QAAIE,OAAO,KAAKF,UAAZ,IAA0BA,UAAU,KAAK,KAAKrB,MAAL,CAAYyB,OAAzD,EAAkE;AAChE,aAAO,IAAP;AACD;;AACD,WAAOF,OAAP;AACD;;AAEOG,EAAAA,eAAe,CAACC,QAAD,EAAmBC,OAAnB,EAA6C;AAClE,UAAMC,aAAa,GAAGhB,iBAAQiB,QAAR,CAAiBH,QAAjB,EAA2BC,OAA3B,CAAtB;;AACA,UAAMG,gBAAgB,GAAG,0CACvBF,aADuB,EAEvB,KAAK7B,MAAL,CAAYgC,WAFW,CAAzB;AAIA,WAAOD,gBAAgB,IAAI,IAA3B;AACD;;AAESE,EAAAA,qBAAqB,CAACN,QAAD,EAA2B;AACxD,QAAIjB,cAAKwB,QAAL,CAAcP,QAAd,MAA4B,cAAhC,EAAgD;AAC9C,YAAMf,MAAM,GAAG,KAAKc,eAAL,CAAqBS,IAArB,CAA0B,IAA1B,CAAf;AACA,aAAOvB,MAAP;AACD;;AAED,UAAMH,SAAS,GAAGC,cAAKC,OAAL,CAAagB,QAAb,KAA0B,OAA5C;AAEA,UAAMf,MAAM,GAAG,KAAKZ,MAAL,CAAYa,OAAZ,CAAoBJ,SAApB,CAAf;;AAEA,QAAI,CAACG,MAAL,EAAa;AACX,YAAM,IAAIE,KAAJ,CACH,2BAA0BC,uBAAuB,CAACY,QAAD,CAAW,EADzD,CAAN;AAGD;;AAED,WAAOf,MAAP;AACD;;AAESwB,EAAAA,gCAAgC,CACxCT,QADwC,EAExCU,aAFwC,EAGrB;AACnB,QAAIA,aAAa,KAAK,IAAtB,EAA4B;AAC1B,aAAO,IAAP;AACD;;AACD,QAAIA,aAAa,KAAKC,SAAtB,EAAiC;AAC/B,aAAO;AAAEX,QAAAA,QAAF;AAAY3B,QAAAA,MAAM,EAAEsC,SAApB;AAA+BpB,QAAAA,OAAO,EAAE;AAAxC,OAAP;AACD;;AACD,WAAO;AAAElB,MAAAA,MAAM,EAAEqC,aAAV;AAAyBV,MAAAA;AAAzB,KAAP;AACD;;AAESY,EAAAA,gBAAgB,CAACZ,QAAD,EAAyB;AACjD,QAAI,CAACA,QAAL,EAAe;AACb,YAAM,IAAIb,KAAJ,CAAU,mCAAV,CAAN;AACD;AACF;;AAzHiE;;;;AA4HpE,SAASU,SAAT,CAAmBgB,GAAnB,EAAwC;AACtC,SAAO9B,cAAK+B,OAAL,CAAaD,GAAb,CAAP;AACD;;AAED,SAASzB,uBAAT,CAAiCY,QAAjC,EAA2D;AACzD,QAAMe,GAAG,GAAGhC,cAAKC,OAAL,CAAagB,QAAb,CAAZ;;AACA,SAAOe,GAAG,GAAI,cAAaA,GAAI,GAArB,GAA0B,0BAApC;AACD", "sourcesContent": ["import path from 'path';\nimport { loaders } from './loaders';\nimport { getPropertyByPath } from './getPropertyByPath';\nimport {\n  CosmiconfigResult,\n  ExplorerOptions,\n  ExplorerOptionsSync,\n  Cache,\n  LoadedFileContent,\n} from './types';\nimport { Loader } from './index';\n\nclass ExplorerBase<T extends ExplorerOptions | ExplorerOptionsSync> {\n  protected readonly loadCache?: Cache;\n  protected readonly searchCache?: Cache;\n  protected readonly config: T;\n\n  public constructor(options: T) {\n    if (options.cache === true) {\n      this.loadCache = new Map();\n      this.searchCache = new Map();\n    }\n\n    this.config = options;\n    this.validateConfig();\n  }\n\n  public clearLoadCache(): void {\n    if (this.loadCache) {\n      this.loadCache.clear();\n    }\n  }\n\n  public clearSearchCache(): void {\n    if (this.searchCache) {\n      this.searchCache.clear();\n    }\n  }\n\n  public clearCaches(): void {\n    this.clearLoadCache();\n    this.clearSearchCache();\n  }\n\n  private validateConfig(): void {\n    const config = this.config;\n\n    config.searchPlaces.forEach((place): void => {\n      const loaderKey = path.extname(place) || 'noExt';\n      const loader = config.loaders[loaderKey];\n      if (!loader) {\n        throw new Error(\n          `No loader specified for ${getExtensionDescription(\n            place,\n          )}, so searchPlaces item \"${place}\" is invalid`,\n        );\n      }\n\n      if (typeof loader !== 'function') {\n        throw new Error(\n          `loader for ${getExtensionDescription(\n            place,\n          )} is not a function (type provided: \"${typeof loader}\"), so searchPlaces item \"${place}\" is invalid`,\n        );\n      }\n    });\n  }\n\n  protected shouldSearchStopWithResult(result: CosmiconfigResult): boolean {\n    if (result === null) return false;\n    if (result.isEmpty && this.config.ignoreEmptySearchPlaces) return false;\n    return true;\n  }\n\n  protected nextDirectoryToSearch(\n    currentDir: string,\n    currentResult: CosmiconfigResult,\n  ): string | null {\n    if (this.shouldSearchStopWithResult(currentResult)) {\n      return null;\n    }\n    const nextDir = nextDirUp(currentDir);\n    if (nextDir === currentDir || currentDir === this.config.stopDir) {\n      return null;\n    }\n    return nextDir;\n  }\n\n  private loadPackageProp(filepath: string, content: string): unknown {\n    const parsedContent = loaders.loadJson(filepath, content);\n    const packagePropValue = getPropertyByPath(\n      parsedContent,\n      this.config.packageProp,\n    );\n    return packagePropValue || null;\n  }\n\n  protected getLoaderEntryForFile(filepath: string): Loader {\n    if (path.basename(filepath) === 'package.json') {\n      const loader = this.loadPackageProp.bind(this);\n      return loader;\n    }\n\n    const loaderKey = path.extname(filepath) || 'noExt';\n\n    const loader = this.config.loaders[loaderKey];\n\n    if (!loader) {\n      throw new Error(\n        `No loader specified for ${getExtensionDescription(filepath)}`,\n      );\n    }\n\n    return loader;\n  }\n\n  protected loadedContentToCosmiconfigResult(\n    filepath: string,\n    loadedContent: LoadedFileContent,\n  ): CosmiconfigResult {\n    if (loadedContent === null) {\n      return null;\n    }\n    if (loadedContent === undefined) {\n      return { filepath, config: undefined, isEmpty: true };\n    }\n    return { config: loadedContent, filepath };\n  }\n\n  protected validateFilePath(filepath: string): void {\n    if (!filepath) {\n      throw new Error('load must pass a non-empty string');\n    }\n  }\n}\n\nfunction nextDirUp(dir: string): string {\n  return path.dirname(dir);\n}\n\nfunction getExtensionDescription(filepath: string): string {\n  const ext = path.extname(filepath);\n  return ext ? `extension \"${ext}\"` : 'files without extensions';\n}\n\nexport { ExplorerBase, getExtensionDescription };\n"], "file": "ExplorerBase.js"}