{"name": "detective", "description": "find all require() calls by walking the AST", "version": "5.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": "bin/detective.js", "dependencies": {"acorn-node": "^1.8.2", "defined": "^1.0.0", "minimist": "^1.2.6"}, "devDependencies": {"tap": "^10.7.3"}, "engines": {"node": ">=0.8.0"}, "keywords": ["analyze", "ast", "require", "source"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/browserify/detective.git"}, "scripts": {"test": "tap test/*.js"}}