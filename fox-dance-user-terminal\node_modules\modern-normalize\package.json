{"name": "modern-normalize", "version": "1.1.0", "description": "Normalize browsers' default style", "license": "MIT", "repository": "sindresorhus/modern-normalize", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "main": "modern-normalize.css", "style": "modern-normalize.css", "engines": {"node": ">=6"}, "scripts": {"test": "npm run test:stylelint", "test:all": "npm run test:stylelint && npm run test:chrome && npm run test:firefox && npm run test:safari", "test:stylelint": "stylelint modern-normalize.css", "test:chrome": "testcafe chrome:headless test/acceptance/chrome --app \"http-server . --silent\"", "test:firefox": "testcafe firefox:headless test/acceptance/firefox --app \"http-server . --silent\"", "test:safari": "testcafe safari test/acceptance/safari --app \"http-server . --silent\"", "version": "replace-in-files --regex='v\\d+\\.\\d+\\.\\d+' --replacement=v$npm_package_version modern-normalize.css && git add ."}, "files": ["modern-normalize.css"], "keywords": ["normalize", "css", "reset", "browser", "style"], "devDependencies": {"http-server": "^0.12.0", "replace-in-files-cli": "^0.3.0", "stylelint": "^12.0.0", "stylelint-config-xo": "^0.16.0", "testcafe": "^1.6.1"}, "stylelint": {"extends": "stylelint-config-xo", "rules": {"no-duplicate-selectors": null, "no-descending-specificity": null, "property-no-vendor-prefix": null}}}