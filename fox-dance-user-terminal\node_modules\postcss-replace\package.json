{"name": "postcss-replace", "version": "2.0.1", "author": "Gridonic <<EMAIL>>", "license": "MIT", "keywords": ["postcss", "css", "postcss-plugin", "replace", "strings"], "main": "index.js", "homepage": "https://github.com/gridonic/postcss-replace#readme", "description": "PostCSS plugin for replacing strings.", "bugs": {"url": "https://github.com/gridonic/postcss-replace/issues"}, "engines": {"node": "^12 || ^14 || >=16"}, "scripts": {"coverall": "cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "coverage": "jest --coverage", "test": "npm run test:unit && npm run test:mutate", "test:unit": "jest", "test:mutate": "stryker run"}, "dependencies": {"kind-of": "^6.0.3", "object-path": "^0.11.8"}, "peerDependencies": {"postcss": "^8.4"}, "devDependencies": {"@stryker-mutator/core": "^6.4.1", "@stryker-mutator/jest-runner": "^6.4.1", "coveralls": "^3.1.1", "jest": "^29.5.0", "jest-cli": "^29.5.0", "postcss": "^8.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/gridonic/postcss-replace.git"}}