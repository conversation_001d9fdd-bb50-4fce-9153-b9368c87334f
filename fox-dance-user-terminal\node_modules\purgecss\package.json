{"name": "purgecss", "version": "4.1.3", "description": "Remove unused css selectors", "author": "Ffloriel", "homepage": "https://purgecss.com", "keywords": ["optimize", "optimization", "remove", "unused", "css", "html", "rules", "purge", "uncss", "purify"], "license": "MIT", "main": "lib/purgecss.js", "module": "./lib/purgecss.esm.js", "types": "./lib/purgecss.d.ts", "bin": {"purgecss": "bin/purgecss.js"}, "directories": {"lib": "lib", "test": "__tests__"}, "files": ["bin", "lib"], "repository": {"type": "git", "url": "git+https://github.com/FullHuman/purgecss.git"}, "scripts": {"test": "echo \"Error: run tests from root\" && exit 1"}, "dependencies": {"commander": "^8.0.0", "glob": "^7.1.7", "postcss": "^8.3.5", "postcss-selector-parser": "^6.0.6"}, "devDependencies": {"@types/glob": "^7.1.1"}, "bugs": {"url": "https://github.com/FullHuman/purgecss/issues"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "37e5053a446880d12fd2f55abfc362c3dac9c49c"}