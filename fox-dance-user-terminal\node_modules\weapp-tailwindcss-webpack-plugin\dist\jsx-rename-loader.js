'use strict';

var loaderUtils = require('loader-utils');
var parser = require('@babel/parser');
var traverse = require('@babel/traverse');
var generate = require('@babel/generator');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var traverse__default = /*#__PURE__*/_interopDefaultLegacy(traverse);
var generate__default = /*#__PURE__*/_interopDefaultLegacy(generate);

function jsxHandler(rawSource, replacer) {
    const ast = parser.parse(rawSource, {
        sourceType: 'unambiguous'
    });
    traverse__default["default"](ast, {
        enter(path) {
            replacer(path);
        },
        noScope: true
    });
    //, {
    // sourceMaps: true
    // sourceFileName
    // }
    return generate__default["default"](ast);
}

function loader(content) {
    this.cacheable && this.cacheable();
    // if (/src\\pages/.test(this.resource)) {
    //   console.log(this.resource)
    //   debugger
    // }
    // ignore node_modules
    // webpack 4 and 5 -> this.version === 2
    // @ts-ignore
    const config = loaderUtils.getOptions(this);
    // if (config.isVue) {
    //   console.log(this.resource)
    //   debugger
    // }
    const { code } = jsxHandler(content, config.replacer);
    return code;
}

module.exports = loader;
