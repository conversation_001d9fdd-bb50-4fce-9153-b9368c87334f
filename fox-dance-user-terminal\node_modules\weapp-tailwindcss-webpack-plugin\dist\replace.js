const SYMBOL_TABLE = {
    BACKQUOTE: '`',
    TILDE: '~',
    EXCLAM: '!',
    AT: '@',
    NUMBERSIGN: '#',
    DOLLAR: '$',
    PERCENT: '%',
    CARET: '^',
    AMPERSAND: '&',
    ASTERISK: '*',
    PARENLEFT: '(',
    PARENRIGHT: ')',
    MINUS: '-',
    UNDERSCORE: '_',
    EQUAL: '=',
    PLUS: '+',
    BRACKETLEFT: '[',
    BRACELEFT: '{',
    BRACKETRIGHT: ']',
    BRACERIGHT: '}',
    SEMICOLON: ';',
    COLON: ':',
    QUOTE: "'",
    DOUBLEQUOTE: '"',
    BACKSLASH: '\\',
    BAR: '|',
    COMMA: ',',
    LESS: '<',
    PERIOD: '.',
    GREATER: '>',
    SLASH: '/',
    QUESTION: '?',
    SPACE: '',
    DOT: '.',
    HASH: '#'
};
const MappingChars2String = {
    [SYMBOL_TABLE.BRACKETLEFT]: '_l_',
    [SYMBOL_TABLE.BRACKETRIGHT]: '_r_',
    [SYMBOL_TABLE.PARENLEFT]: '_p_',
    [SYMBOL_TABLE.PARENRIGHT]: '_q_',
    [SYMBOL_TABLE.HASH]: '_h_',
    [SYMBOL_TABLE.EXCLAM]: '_i_',
    [SYMBOL_TABLE.SLASH]: '_div_',
    [SYMBOL_TABLE.DOT]: '_dot_',
    [SYMBOL_TABLE.COLON]: '_c_',
    [SYMBOL_TABLE.PERCENT]: '_pct_',
    [SYMBOL_TABLE.COMMA]: '_d_',
    [SYMBOL_TABLE.QUOTE]: '_y_'
};

function replaceWxml(original, keepEOL = false) {
    const res = original
        .replace(/\[/g, MappingChars2String['['])
        .replace(/\]/g, MappingChars2String[']'])
        .replace(/\(/g, MappingChars2String['('])
        .replace(/\)/g, MappingChars2String[')'])
        .replace(/#/g, MappingChars2String['#']) // hex
        .replace(/!/g, MappingChars2String['!']) // css !important
        .replace(/\//g, MappingChars2String['/'])
        .replace(/\./g, MappingChars2String['.'])
        .replace(/:/g, MappingChars2String[':'])
        // https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/8
        .replace(/%/g, MappingChars2String['%'])
        .replace(/,/g, MappingChars2String[','])
        .replace(/'/g, MappingChars2String["'"]);
    if (keepEOL) {
        return res;
    }
    return (res
        // 去除无用换行符和空格
        .replace(/[\r\n]+/g, ''));
}

// css 中，要多加一个 '\' 来转义
function cssSelectorReplacer(selector) {
    return (selector
        .replace(/\\\[/g, MappingChars2String['[']) // \[
        .replace(/\\\]/g, MappingChars2String[']']) // \]
        .replace(/\\\(/g, MappingChars2String['(']) // \(
        .replace(/\\\)/g, MappingChars2String[')']) // \)
        .replace(/\\#/g, MappingChars2String['#']) // \# : hex
        .replace(/\\!/g, MappingChars2String['!']) // \! : !important
        .replace(/\\\//g, MappingChars2String['/']) // \/ : w-1/2 -> width:50%
        .replace(/\\\./g, MappingChars2String['.']) // \. : w-1.5
        .replace(/\\:/g, MappingChars2String[':']) // colon for screen
        // https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/8
        .replace(/\\%/g, MappingChars2String['%'])
        // .replace(/\\,/g, '_d_')
        .replace(/\\2c /g, MappingChars2String[','])
        .replace(/\\'/g, MappingChars2String["'"]));
}

export { cssSelectorReplacer as replaceCss, replaceWxml as replaceJs };
