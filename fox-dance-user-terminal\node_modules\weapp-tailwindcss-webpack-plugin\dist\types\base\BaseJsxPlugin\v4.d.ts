import type { AppType, TaroUserDefinedOptions } from "../../types";
import type { Compiler } from 'webpack4';
import type { IBaseWebpackPlugin } from "../../interface";
/**
 * @issue https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/5
 */
export declare class BaseJsxWebpackPluginV4 implements IBaseWebpackPlugin {
    options: Required<TaroUserDefinedOptions>;
    appType: AppType;
    static NS: string;
    constructor(options: TaroUserDefinedOptions | undefined, appType: AppType);
    apply(compiler: Compiler): void;
}
