import type { AppType, UserDefinedOptions } from "../../types";
import type { Compiler } from 'webpack';
import type { IBaseWebpackPlugin } from "../../interface";
/**
 * @issue https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/2
 */
export declare class BaseJsxWebpackPluginV5 implements IBaseWebpackPlugin {
    options: Required<UserDefinedOptions>;
    appType: AppType;
    static NS: string;
    constructor(options: UserDefinedOptions | undefined, appType: AppType);
    apply(compiler: Compiler): void;
}
