import type { UserDefinedOptions, AppType } from "../../types";
import type { Compiler } from 'webpack4';
import type { IBaseWebpackPlugin } from "../../interface";
export declare class BaseTemplateWebpackPluginV4 implements IBaseWebpackPlugin {
    options: Required<UserDefinedOptions>;
    appType: AppType;
    constructor(options: UserDefinedOptions | undefined, appType: AppType);
    apply(compiler: Compiler): void;
}
