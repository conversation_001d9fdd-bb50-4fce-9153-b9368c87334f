import type { AppType, UserDefinedOptions } from "../../types";
import type { Compiler } from 'webpack';
import type { IBaseWebpackPlugin } from "../../interface";
/**
 * @issue https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/6
 */
export declare class BaseTemplateWebpackPluginV5 implements IBaseWebpackPlugin {
    options: Required<UserDefinedOptions>;
    appType: AppType;
    constructor(options: UserDefinedOptions | undefined, appType: AppType);
    apply(compiler: Compiler): void;
}
