import type { UserDefinedOptions } from './types';
export declare const defaultOptions: Required<UserDefinedOptions>;
export declare function getOptions<T = UserDefinedOptions>(options: T): T extends Required<UserDefinedOptions> ? T : Omit<T, keyof T & keyof UserDefinedOptions> & Omit<Required<UserDefinedOptions>, keyof T & keyof UserDefinedOptions> & { -readonly [Key in keyof T & keyof UserDefinedOptions]: T[Key] extends void | null | undefined ? Required<UserDefinedOptions>[Key] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key] : Required<UserDefinedOptions>[Key] extends void | null | undefined ? T[Key] : T[Key] extends void | null | undefined ? Required<UserDefinedOptions>[Key] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key] : Required<UserDefinedOptions>[Key] extends void | null | undefined ? T[Key] : T[Key] extends any[] ? Required<UserDefinedOptions>[Key] extends any[] ? T[Key] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key] : T[Key] | Required<UserDefinedOptions>[Key] : Required<UserDefinedOptions>[Key] | T[Key] : T[Key] extends Function ? Required<UserDefinedOptions>[Key] | T[Key] : T[Key] extends RegExp ? Required<UserDefinedOptions>[Key] | T[Key] : T[Key] extends Promise<any> ? Required<UserDefinedOptions>[Key] | T[Key] : Required<UserDefinedOptions>[Key] extends Function ? T[Key] | Required<UserDefinedOptions>[Key] : Required<UserDefinedOptions>[Key] extends RegExp ? T[Key] | Required<UserDefinedOptions>[Key] : Required<UserDefinedOptions>[Key] extends Promise<any> ? T[Key] | Required<UserDefinedOptions>[Key] : T[Key] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key] extends Required<UserDefinedOptions>[Key] ? Required<UserDefinedOptions>[Key] & T[Key] : Omit<T[Key], keyof T[Key] & keyof Required<UserDefinedOptions>[Key]> & Omit<Required<UserDefinedOptions>[Key], keyof T[Key] & keyof Required<UserDefinedOptions>[Key]> & { -readonly [Key_1 in keyof T[Key] & keyof Required<UserDefinedOptions>[Key]]: T[Key][Key_1] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1] : Required<UserDefinedOptions>[Key][Key_1] extends void | null | undefined ? T[Key][Key_1] : T[Key][Key_1] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1] : Required<UserDefinedOptions>[Key][Key_1] extends void | null | undefined ? T[Key][Key_1] : T[Key][Key_1] extends any[] ? Required<UserDefinedOptions>[Key][Key_1] extends any[] ? T[Key][Key_1] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1] : T[Key][Key_1] | Required<UserDefinedOptions>[Key][Key_1] : Required<UserDefinedOptions>[Key][Key_1] | T[Key][Key_1] : T[Key][Key_1] extends Function ? Required<UserDefinedOptions>[Key][Key_1] | T[Key][Key_1] : T[Key][Key_1] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1] | T[Key][Key_1] : T[Key][Key_1] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1] | T[Key][Key_1] : Required<UserDefinedOptions>[Key][Key_1] extends Function ? T[Key][Key_1] | Required<UserDefinedOptions>[Key][Key_1] : Required<UserDefinedOptions>[Key][Key_1] extends RegExp ? T[Key][Key_1] | Required<UserDefinedOptions>[Key][Key_1] : Required<UserDefinedOptions>[Key][Key_1] extends Promise<any> ? T[Key][Key_1] | Required<UserDefinedOptions>[Key][Key_1] : T[Key][Key_1] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1] extends Required<UserDefinedOptions>[Key][Key_1] ? Required<UserDefinedOptions>[Key][Key_1] & T[Key][Key_1] : Omit<T[Key][Key_1], keyof T[Key][Key_1] & keyof Required<UserDefinedOptions>[Key][Key_1]> & Omit<Required<UserDefinedOptions>[Key][Key_1], keyof T[Key][Key_1] & keyof Required<UserDefinedOptions>[Key][Key_1]> & { -readonly [Key_2 in keyof T[Key][Key_1] & keyof Required<UserDefinedOptions>[Key][Key_1]]: T[Key][Key_1][Key_2] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2] : Required<UserDefinedOptions>[Key][Key_1][Key_2] extends void | null | undefined ? T[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2] : Required<UserDefinedOptions>[Key][Key_1][Key_2] extends void | null | undefined ? T[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2] extends any[] ? T[Key][Key_1][Key_2] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] | Required<UserDefinedOptions>[Key][Key_1][Key_2] : Required<UserDefinedOptions>[Key][Key_1][Key_2] | T[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2] | T[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2] | T[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2] | T[Key][Key_1][Key_2] : Required<UserDefinedOptions>[Key][Key_1][Key_2] extends Function ? T[Key][Key_1][Key_2] | Required<UserDefinedOptions>[Key][Key_1][Key_2] : Required<UserDefinedOptions>[Key][Key_1][Key_2] extends RegExp ? T[Key][Key_1][Key_2] | Required<UserDefinedOptions>[Key][Key_1][Key_2] : Required<UserDefinedOptions>[Key][Key_1][Key_2] extends Promise<any> ? T[Key][Key_1][Key_2] | Required<UserDefinedOptions>[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2] extends Required<UserDefinedOptions>[Key][Key_1][Key_2] ? Required<UserDefinedOptions>[Key][Key_1][Key_2] & T[Key][Key_1][Key_2] : Omit<T[Key][Key_1][Key_2], keyof T[Key][Key_1][Key_2] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2], keyof T[Key][Key_1][Key_2] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2]> & { -readonly [Key_3 in keyof T[Key][Key_1][Key_2] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2]]: T[Key][Key_1][Key_2][Key_3] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends any[] ? T[Key][Key_1][Key_2][Key_3] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] | T[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] | T[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] | T[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] | T[Key][Key_1][Key_2][Key_3] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends Function ? T[Key][Key_1][Key_2][Key_3] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends RegExp ? T[Key][Key_1][Key_2][Key_3] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] & T[Key][Key_1][Key_2][Key_3] : Omit<T[Key][Key_1][Key_2][Key_3], keyof T[Key][Key_1][Key_2][Key_3] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3], keyof T[Key][Key_1][Key_2][Key_3] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3]> & { -readonly [Key_4 in keyof T[Key][Key_1][Key_2][Key_3] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3]]: T[Key][Key_1][Key_2][Key_3][Key_4] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] | T[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] | T[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] | T[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] | T[Key][Key_1][Key_2][Key_3][Key_4] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3][Key_4] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] & T[Key][Key_1][Key_2][Key_3][Key_4] : Omit<T[Key][Key_1][Key_2][Key_3][Key_4], keyof T[Key][Key_1][Key_2][Key_3][Key_4] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4], keyof T[Key][Key_1][Key_2][Key_3][Key_4] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4]> & { -readonly [Key_5 in keyof T[Key][Key_1][Key_2][Key_3][Key_4] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4]]: T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] & T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : Omit<T[Key][Key_1][Key_2][Key_3][Key_4][Key_5], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5]> & { -readonly [Key_6 in keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5]]: T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] & T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : Omit<T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6]> & { -readonly [Key_7 in keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6]]: T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] & T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : Omit<T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7]> & { -readonly [Key_8 in keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7]]: T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] & T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : Omit<T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8]> & { -readonly [Key_9 in keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8]]: T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] extends Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] & T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : Omit<T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9]> & Omit<Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9], keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9]> & { -readonly [Key_10 in keyof T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] & keyof Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9]]: T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends void | null | undefined ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends void | null | undefined ? void | null | undefined : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends void | null | undefined ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends any[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends any[] ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends (infer DestinationType)[] ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends (infer SourceType)[] ? (DestinationType | SourceType)[] : DestinationType[] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends Function ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends RegExp ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends Promise<any> ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends Function ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends RegExp ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends Promise<any> ? T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] extends {
    [x: string]: any;
    [x: number]: any;
    [x: symbol]: any;
} ? any : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9][Key_10]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8][Key_9]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7][Key_8]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6][Key_7]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5][Key_6]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] : T[Key][Key_1][Key_2][Key_3][Key_4][Key_5] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4][Key_5]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4] | T[Key][Key_1][Key_2][Key_3][Key_4] : T[Key][Key_1][Key_2][Key_3][Key_4] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3][Key_4]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3] | T[Key][Key_1][Key_2][Key_3] : T[Key][Key_1][Key_2][Key_3] | Required<UserDefinedOptions>[Key][Key_1][Key_2][Key_3]; } : Required<UserDefinedOptions>[Key][Key_1][Key_2] | T[Key][Key_1][Key_2] : T[Key][Key_1][Key_2] | Required<UserDefinedOptions>[Key][Key_1][Key_2]; } : Required<UserDefinedOptions>[Key][Key_1] | T[Key][Key_1] : T[Key][Key_1] | Required<UserDefinedOptions>[Key][Key_1]; } : Required<UserDefinedOptions>[Key] | T[Key] : T[Key] | Required<UserDefinedOptions>[Key]; };
