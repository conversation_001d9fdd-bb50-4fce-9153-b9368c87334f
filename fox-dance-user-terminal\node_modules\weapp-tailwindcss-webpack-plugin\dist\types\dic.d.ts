export declare const SYMBOL_TABLE: {
    readonly BACKQUOTE: "`";
    readonly TILDE: "~";
    readonly EXCLAM: "!";
    readonly AT: "@";
    readonly NUMBERSIGN: "#";
    readonly DOLLAR: "$";
    readonly PERCENT: "%";
    readonly CARET: "^";
    readonly AMPERSAND: "&";
    readonly ASTERISK: "*";
    readonly PARENLEFT: "(";
    readonly PARENRIGHT: ")";
    readonly MINUS: "-";
    readonly UNDERSCORE: "_";
    readonly EQUAL: "=";
    readonly PLUS: "+";
    readonly BRACKETLEFT: "[";
    readonly BRACELEFT: "{";
    readonly BRACKETRIGHT: "]";
    readonly BRACERIGHT: "}";
    readonly SEMICOLON: ";";
    readonly COLON: ":";
    readonly QUOTE: "'";
    readonly DOUBLEQUOTE: "\"";
    readonly BACKSLASH: "\\";
    readonly BAR: "|";
    readonly COMMA: ",";
    readonly LESS: "<";
    readonly PERIOD: ".";
    readonly GREATER: ">";
    readonly SLASH: "/";
    readonly QUESTION: "?";
    readonly SPACE: "";
    readonly DOT: ".";
    readonly HASH: "#";
};
export declare const MappingChars2String: {
    readonly "[": "_l_";
    readonly "]": "_r_";
    readonly "(": "_p_";
    readonly ")": "_q_";
    readonly "#": "_h_";
    readonly "!": "_i_";
    readonly "/": "_div_";
    readonly ".": "_dot_";
    readonly ":": "_c_";
    readonly "%": "_pct_";
    readonly ",": "_d_";
    readonly "'": "_y_";
};
