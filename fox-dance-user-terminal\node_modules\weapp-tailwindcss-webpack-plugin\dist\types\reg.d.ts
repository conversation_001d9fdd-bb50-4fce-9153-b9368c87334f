export declare const classRegexp: RegExp;
export declare const vueTemplateClassRegexp: RegExp;
export declare const tagRegexp: RegExp;
export declare const tagWithClassRegexp: RegExp;
export declare const doubleQuoteRegexp: RegExp;
export declare const variableRegExp: RegExp;
export declare const wxmlAllowClassCharsRegExp: RegExp;
export declare function createWxmlAllowClassCharsRegExp(): RegExp;
export declare function classStringReplace(str: string, replacement: (substring: string, ...args: any[]) => string): string;
export declare function tagStringReplace(str: string, replacement: (substring: string, ...args: any[]) => string): string;
export declare function doubleQuoteStringReplace(str: string, replacement: (substring: string, ...args: any[]) => string): string;
export declare function variableMatch(original: string): RegExpExecArray | null;
