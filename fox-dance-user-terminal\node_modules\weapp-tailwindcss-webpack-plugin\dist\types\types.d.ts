import type { InjectPreflight } from './postcss/preflight';
import type { Rule } from 'postcss';
export declare type AppType = 'uni-app' | 'taro' | 'remax' | 'rax' | 'native' | 'kbone' | 'mpx' | undefined;
export declare type CssPresetProps = 'box-sizing' | 'border-width' | 'border-style' | 'border-color';
export declare type CssPreflightOptions = {
    [key: CssPresetProps | string]: string | false;
} | false;
declare type RequiredStyleHandlerOptions = {
    isMainChunk: boolean;
    cssInjectPreflight: InjectPreflight;
};
export declare type CustomRuleCallback = (node: Rule, options: Readonly<RequiredStyleHandlerOptions>) => void;
export declare type StyleHandlerOptions = {
    customRuleCallback?: CustomRuleCallback;
} & RequiredStyleHandlerOptions;
export interface UserDefinedOptions {
    /**
     * wxml/ttml 这类的 ml 的匹配方法
     */
    htmlMatcher?: (name: string) => boolean;
    /**
     * wxss/jxss/ttss 这类的 ss 的匹配方法
     */
    cssMatcher?: (name: string) => boolean;
    /**
     * 用于处理js
     */
    jsMatcher?: (name: string) => boolean;
    /**
     * tailwind jit main chunk 的匹配方法
     * 用于处理原始变量和替换不兼容选择器
     */
    mainCssChunkMatcher?: (name: string, appType: AppType) => boolean;
    /**
     * @issue https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/7
     * 用于处理 postcss 的预设
     */
    cssPreflight?: CssPreflightOptions;
    /**
     * 用于自定义处理 css 的回调函数
     */
    customRuleCallback?: CustomRuleCallback;
    /**
     * @description plugin apply 初调用
     */
    onLoad?: () => void;
    /**
     * @description 开始处理时调用
     */
    onStart?: () => void;
    /**
     * @description 匹配成功并修改文件内容后调用
     */
    onUpdate?: (filename: string) => void;
    /**
     * @description 结束处理时调用
     */
    onEnd?: () => void;
}
export declare type InternalPostcssOptions = Pick<UserDefinedOptions, 'cssMatcher' | 'mainCssChunkMatcher' | 'cssPreflight'>;
export interface TaroUserDefinedOptions extends UserDefinedOptions {
    framework: 'react' | 'vue' | 'vue3' | string;
}
export interface RawSource {
    start: number;
    end: number;
    raw: string;
    source?: string;
}
export {};
