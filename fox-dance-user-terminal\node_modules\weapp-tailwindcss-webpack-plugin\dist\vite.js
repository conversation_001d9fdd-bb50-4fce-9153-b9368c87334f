'use strict';

var parser = require('@babel/parser');
var traverse = require('@babel/traverse');
var generate = require('@babel/generator');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var traverse__default = /*#__PURE__*/_interopDefaultLegacy(traverse);
var generate__default = /*#__PURE__*/_interopDefaultLegacy(generate);

function isObject(val) {
  return val !== null && typeof val === "object";
}
function _defu(baseObj, defaults, namespace = ".", merger) {
  if (!isObject(defaults)) {
    return _defu(baseObj, {}, namespace, merger);
  }
  const obj = Object.assign({}, defaults);
  for (const key in baseObj) {
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const val = baseObj[key];
    if (val === null || val === void 0) {
      continue;
    }
    if (merger && merger(obj, key, val, namespace)) {
      continue;
    }
    if (Array.isArray(val) && Array.isArray(obj[key])) {
      obj[key] = val.concat(obj[key]);
    } else if (isObject(val) && isObject(obj[key])) {
      obj[key] = _defu(val, obj[key], (namespace ? `${namespace}.` : "") + key.toString(), merger);
    } else {
      obj[key] = val;
    }
  }
  return obj;
}
function createDefu(merger) {
  return (...args) => args.reduce((p, c) => _defu(p, c, "", merger), {});
}
const defu = createDefu();

const noop = () => { };
const defaultOptions = {
    cssMatcher: (file) => /.+\.(?:wx|ac|jx|tt|q|c)ss$/.test(file),
    htmlMatcher: (file) => /.+\.(?:(?:(?:wx|ax|jx|ks|tt|q)ml)|swan)$/.test(file),
    jsMatcher: (file) => {
        if (file.includes('node_modules')) {
            return false;
        }
        return /.+\.[jt]sx?$/.test(file);
    },
    mainCssChunkMatcher: (file, appType) => {
        switch (appType) {
            case 'uni-app': {
                return /^common\/main/.test(file);
            }
            case 'mpx': {
                return /^app/.test(file);
            }
            case 'taro': {
                return /^app/.test(file);
            }
            case 'remax': {
                return /^app/.test(file);
            }
            case 'rax': {
                return /^bundle/.test(file);
            }
            case 'native': {
                return /^app/.test(file);
            }
            case 'kbone': {
                return /^(?:common\/)?miniprogram-app/.test(file);
            }
            default: {
                return true;
            }
        }
    },
    cssPreflight: {
        'box-sizing': 'border-box',
        'border-width': '0',
        'border-style': 'solid',
        'border-color': 'currentColor'
    },
    customRuleCallback: noop,
    onLoad: noop,
    onStart: noop,
    onEnd: noop,
    onUpdate: noop
};
function getOptions(options) {
    return defu(options, defaultOptions);
}

const SYMBOL_TABLE = {
    BACKQUOTE: '`',
    TILDE: '~',
    EXCLAM: '!',
    AT: '@',
    NUMBERSIGN: '#',
    DOLLAR: '$',
    PERCENT: '%',
    CARET: '^',
    AMPERSAND: '&',
    ASTERISK: '*',
    PARENLEFT: '(',
    PARENRIGHT: ')',
    MINUS: '-',
    UNDERSCORE: '_',
    EQUAL: '=',
    PLUS: '+',
    BRACKETLEFT: '[',
    BRACELEFT: '{',
    BRACKETRIGHT: ']',
    BRACERIGHT: '}',
    SEMICOLON: ';',
    COLON: ':',
    QUOTE: "'",
    DOUBLEQUOTE: '"',
    BACKSLASH: '\\',
    BAR: '|',
    COMMA: ',',
    LESS: '<',
    PERIOD: '.',
    GREATER: '>',
    SLASH: '/',
    QUESTION: '?',
    SPACE: '',
    DOT: '.',
    HASH: '#'
};
const MappingChars2String = {
    [SYMBOL_TABLE.BRACKETLEFT]: '_l_',
    [SYMBOL_TABLE.BRACKETRIGHT]: '_r_',
    [SYMBOL_TABLE.PARENLEFT]: '_p_',
    [SYMBOL_TABLE.PARENRIGHT]: '_q_',
    [SYMBOL_TABLE.HASH]: '_h_',
    [SYMBOL_TABLE.EXCLAM]: '_i_',
    [SYMBOL_TABLE.SLASH]: '_div_',
    [SYMBOL_TABLE.DOT]: '_dot_',
    [SYMBOL_TABLE.COLON]: '_c_',
    [SYMBOL_TABLE.PERCENT]: '_pct_',
    [SYMBOL_TABLE.COMMA]: '_d_',
    [SYMBOL_TABLE.QUOTE]: '_y_'
};

function replaceWxml(original, keepEOL = false) {
    const res = original
        .replace(/\[/g, MappingChars2String['['])
        .replace(/\]/g, MappingChars2String[']'])
        .replace(/\(/g, MappingChars2String['('])
        .replace(/\)/g, MappingChars2String[')'])
        .replace(/#/g, MappingChars2String['#']) // hex
        .replace(/!/g, MappingChars2String['!']) // css !important
        .replace(/\//g, MappingChars2String['/'])
        .replace(/\./g, MappingChars2String['.'])
        .replace(/:/g, MappingChars2String[':'])
        // https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/8
        .replace(/%/g, MappingChars2String['%'])
        .replace(/,/g, MappingChars2String[','])
        .replace(/'/g, MappingChars2String["'"]);
    if (keepEOL) {
        return res;
    }
    return (res
        // 去除无用换行符和空格
        .replace(/[\r\n]+/g, ''));
}

const tagWithClassRegexp = /<([a-z][-a-z]*[a-z]*)\s+[^>]*?(?:class="([^"]*)")[^>]*?\/?>/g;
const variableRegExp = /{{(.*?)}}/gs;
function variableMatch(original) {
    return variableRegExp.exec(original);
}

// import * as wxml from '@icebreakers/wxml'
function generateCode(match) {
    const ast = parser.parseExpression(match);
    traverse__default["default"](ast, {
        StringLiteral(path) {
            path.node.value = replaceWxml(path.node.value);
        },
        noScope: true
    });
    const { code } = generate__default["default"](ast, {
        compact: true,
        minified: true,
        jsescOption: {
            quotes: 'single'
        }
    });
    return code;
}
function templeteReplacer(original) {
    let match = variableMatch(original);
    const sources = [];
    while (match !== null) {
        // 过滤空字符串
        // if (match[1].trim().length) {
        sources.push({
            start: match.index,
            end: variableRegExp.lastIndex,
            raw: match[1]
        });
        match = variableMatch(original);
    }
    if (sources.length) {
        const resultArray = [];
        let p = 0;
        for (let i = 0; i < sources.length; i++) {
            const m = sources[i];
            // 匹配前值
            resultArray.push(replaceWxml(original.slice(p, m.start), true));
            p = m.start;
            // 匹配后值
            if (m.raw.trim().length) {
                const code = generateCode(m.raw);
                m.source = `{{${code}}}`;
            }
            else {
                m.source = '';
            }
            resultArray.push(m.source);
            p = m.end;
            // 匹配最终尾部值
            if (i === sources.length - 1) {
                resultArray.push(replaceWxml(original.slice(m.end), true));
            }
        }
        return resultArray.filter((x) => x).join('');
    }
    else {
        return replaceWxml(original);
    }
}
function templeteHandler(rawSource) {
    return rawSource.replace(tagWithClassRegexp, (m, tagName, className) => {
        return m.replace(className, templeteReplacer(className));
    });
}

// import renamePostcssPlugin from '../postcss/plugin'
// import type { Plugin as PostcssPlugin } from 'postcss'
// import postcssrc from 'postcss-load-config'
// https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/3
function ViteWeappTailwindcssPlugin(options = {}) {
    const { htmlMatcher // cssMatcher, mainCssChunkMatcher
     } = getOptions(options);
    return {
        name: 'som:vite-plugin-uni-app-weapp-tailwindcss',
        // config (config) {},
        generateBundle(opt, bundle, isWrite) {
            const entries = Object.entries(bundle);
            for (let i = 0; i < entries.length; i++) {
                const [file, originalSource] = entries[i];
                if (htmlMatcher(file)) {
                    if (originalSource.type === 'asset') {
                        originalSource.source = templeteHandler(originalSource.source.toString());
                    }
                }
            }
        }
    };
}

module.exports = ViteWeappTailwindcssPlugin;
