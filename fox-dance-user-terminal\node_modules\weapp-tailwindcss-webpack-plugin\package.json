{"name": "weapp-tailwindcss-webpack-plugin", "version": "1.6.0", "description": "把tailwindcss jit引擎，带给小程序开发者们\nbring tailwindcss jit engine to our miniprogram developers!", "main": "dist/index.js", "types": "dist/types/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/types/index.d.ts", "node": {"require": "./dist/index.js"}, "require": "./dist/index.js"}, "./replace": {"types": "./dist/types/replace.d.ts", "import": "./dist/replace.js"}, "./postcss": {"types": "./dist/types/postcss/plugin.d.ts", "require": "./dist/postcss.js"}, "./vite": {"types": "./dist/types/framework/vite/index.d.ts", "require": "./dist/vite.js"}}, "scripts": {"dev": "yarn dts && cross-env NODE_ENV=development rollup -cw", "build": "yarn clean && cross-env NODE_ENV=production rollup -c && yarn dts", "build:tsc": "cross-env NODE_ENV=development tsc --build tsconfig.json", "dts": "ttsc --emitDeclarationOnly -p tsconfig.dts.json", "test": "jest", "init:rename": "node scripts/init/rename.js", "clean": "node scripts/clean.js", "debug:web": "cd demo/web && yarn dev", "raw": "ts-node scripts/raw"}, "repository": {"type": "git", "url": "git+https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin.git"}, "files": ["dist", "postcss.*", "replace.*", "vite.*"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "keywords": ["webpack", "webpack-plugin", "weapp", "postcss", "taro", "uni-app", "tailwindcss", "jit", "tailwindcss jit", "mp", "mini", "miniprogram", "weapp-tailwindcss"], "author": "SonOfMagic <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues"}, "homepage": "https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin#readme", "devDependencies": {"@babel/types": "^7.17.0", "@rollup/plugin-alias": "^3.1.8", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.2.1", "@rollup/plugin-typescript": "^8.3.2", "@tsconfig/recommended": "^1.0.1", "@types/esm": "^3.2.0", "@types/jest": "^27.5.1", "@types/loader-utils": "^2.0.3", "@types/node": "^17.0.38", "@types/tailwindcss": "^3.0.9", "@types/webpack": "^5.28.0", "@types/webpack-sources": "^3.2.0", "@types/webpack4": "npm:@types/webpack@4", "@typescript-eslint/eslint-plugin": "^5.27.0", "@typescript-eslint/parser": "^5.27.0", "autoprefixer": "^10.4.5", "cross-env": "^7.0.3", "defu": "^6.0.0", "del": "^6.1.1", "eslint": "8.16.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-n": "^15.2.0", "eslint-plugin-promise": "6.0.0", "esm": "^3.2.25", "execa": "5", "jest": "^27.5.1", "memfs": "^3.4.4", "postcss": "^8.4.14", "postcss-load-config": "^4.0.1", "postcss-loader": "^7.0.0", "postcss-rem-to-responsive-pixel": "^5.1.1", "prettier": "^2.6.2", "rollup": "^2.75.4", "rollup-plugin-terser": "^7.0.2", "simple-functional-loader": "^1.2.1", "tailwindcss": "^3.0.24", "ts-jest": "^27.1.4", "ts-node": "^10.8.0", "tslib": "^2.4.0", "ttypescript": "^1.5.13", "typescript": "^4.7.2", "typescript-transform-paths": "^3.3.1", "vite": "^2.9.6", "webpack": "^5.70.0", "webpack4": "npm:webpack@webpack-4"}, "dependencies": {"@babel/generator": "7.17.9", "@babel/parser": "7.17.9", "@babel/traverse": "7.17.9", "loader-utils": "^2.0.2", "webpack-sources": "^3.2.3"}}