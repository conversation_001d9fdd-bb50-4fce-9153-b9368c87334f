<template>
  <view class="store-list-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="title">店铺列表</view>
      <view class="subtitle">选择您喜欢的舞蹈工作室</view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
    </view>

    <!-- 店铺网格布局 -->
    <view v-else class="store-grid">
      <view 
        v-for="(storeName, index) in storeNames" 
        :key="index"
        class="store-card"
        @click="handleStoreClick(storeName, index)"
      >
        <view class="store-card-content">
          <view class="store-icon">
            <text class="icon">🏪</text>
          </view>
          <view class="store-name">{{ storeName }}</view>
          <view class="store-status">营业中</view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="storeNames.length === 0" class="empty-state">
        <view class="empty-icon">🏪</view>
        <view class="empty-text">暂无店铺信息</view>
        <view class="empty-desc">请稍后再试</view>
      </view>
    </view>

    <!-- 刷新按钮 -->
    <view class="refresh-container">
      <button 
        class="refresh-btn" 
        @click="refreshStoreList"
        :disabled="loading"
      >
        <text class="refresh-icon">🔄</text>
        <text class="refresh-text">{{ loading ? '加载中...' : '刷新列表' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getStoreNames } from '@/api/store'

export default {
  name: 'StoreList',
  data() {
    return {
      storeNames: [], // 店铺名称列表
      loading: false, // 加载状态
      loadingText: {
        contentdown: '下拉刷新',
        contentrefresh: '正在刷新...',
        contentnomore: '没有更多数据'
      }
    }
  },
  
  onLoad() {
    console.log('📱 店铺列表页面加载')
    this.loadStoreNames()
  },
  
  onShow() {
    console.log('📱 店铺列表页面显示')
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    console.log('🔄 触发下拉刷新')
    this.refreshStoreList()
  },
  
  methods: {
    /**
     * 加载店铺名称列表
     */
    async loadStoreNames() {
      try {
        this.loading = true
        console.log('🏪 开始加载店铺名称列表')
        
        const response = await getStoreNames()
        console.log('📊 店铺名称API响应:', response)
        
        if (response.code === 0 && response.data) {
          this.storeNames = response.data
          console.log('✅ 成功加载店铺名称 - 数量:', this.storeNames.length)
          
          // 显示成功提示
          if (this.storeNames.length > 0) {
            uni.showToast({
              title: `加载成功，共${this.storeNames.length}家店铺`,
              icon: 'success',
              duration: 2000
            })
          }
        } else {
          console.error('❌ 店铺名称API返回错误:', response.message)
          uni.showToast({
            title: response.message || '加载失败',
            icon: 'error',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('❌ 加载店铺名称失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'error',
          duration: 2000
        })
      } finally {
        this.loading = false
        // 停止下拉刷新
        uni.stopPullDownRefresh()
      }
    },
    
    /**
     * 刷新店铺列表
     */
    refreshStoreList() {
      console.log('🔄 刷新店铺列表')
      this.loadStoreNames()
    },
    
    /**
     * 处理店铺点击事件
     */
    handleStoreClick(storeName, index) {
      console.log('🏪 点击店铺:', storeName, '索引:', index)
      
      uni.showToast({
        title: `您选择了：${storeName}`,
        icon: 'success',
        duration: 2000
      })
      
      // 这里可以添加跳转到店铺详情页的逻辑
      // uni.navigateTo({
      //   url: `/pagesSub/store/store-detail?name=${encodeURIComponent(storeName)}&index=${index}`
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.store-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b87 0%, #a8727e 100%);
  padding: 40rpx 30rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 20rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    opacity: 0.8;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.store-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.store-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  }
}

.store-card-content {
  text-align: center;
  
  .store-icon {
    margin-bottom: 20rpx;
    
    .icon {
      font-size: 60rpx;
      display: block;
    }
  }
  
  .store-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 12rpx;
    line-height: 1.4;
    word-break: break-all;
  }
  
  .store-status {
    font-size: 24rpx;
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    display: inline-block;
  }
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 100rpx 40rpx;
  
  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }
  
  .empty-text {
    font-size: 36rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16rpx;
    font-weight: bold;
  }
  
  .empty-desc {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.7);
  }
}

.refresh-container {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  
  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
  
  &[disabled] {
    opacity: 0.6;
  }
  
  .refresh-icon {
    font-size: 32rpx;
    color: #ffffff;
  }
  
  .refresh-text {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: 500;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .store-grid {
    gap: 20rpx;
  }
  
  .store-card {
    padding: 30rpx 15rpx;
  }
  
  .store-card-content .store-name {
    font-size: 28rpx;
  }
}
</style>
