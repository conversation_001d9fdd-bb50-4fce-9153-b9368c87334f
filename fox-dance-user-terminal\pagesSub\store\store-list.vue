<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-box">
        <u-icon name="search" size="32" color="#999"></u-icon>
        <input type="text" v-model="searchKeyword" placeholder="搜索店铺" @confirm="handleSearch" @input="onSearchInput" />
        <view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
          <u-icon name="close" size="28" color="#999"></u-icon>
        </view>
      </view>
    </view>
    <view class="store-list-title">你想找哪家店的搭子？</view>

    <!-- 店铺列表 -->
    <view class="store-list">
      <view v-if="loading" class="loading">
        <u-loading mode="flower" size="50"></u-loading>
        <text class="loading-text">加载中...</text>
      </view>
      <block v-else-if="filteredStoreList.length > 0">
        <view
          v-for="(store, index) in filteredStoreList"
          :key="store.id || index"
          class="store-card"
          @click="() => { handleStoreClick(String(store.name), String(store.image), index) }"
        >
          <view class="store-card-content">
            <view class="store-icon">
              <image
                :src="getStoreImage(store)"
                class="icon"
                mode="aspectFit"
                @error="handleImageError"
              ></image>
            </view>
            <view class="store-name">{{ store.name }}</view>
          </view>
        </view>
      </block>
      <view v-else class="empty-list">
        <image src="/static/icon/null.png" mode="" class="empty-image"></image>
        <text class="empty-text">{{ searchKeyword ? '未找到相关店铺' : '暂无店铺信息' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getStoreNames, getStoreList } from '@/config/store'

export default {
  name: 'StoreList',
  data() {
    return {
      storeNames: [], // 店铺名称列表（保持兼容性）
      storeList: [], // 店铺完整信息列表
      loading: false, // 加载状态
      searchKeyword: '', // 搜索关键词
      sortBy: 'all', // 排序方式：all-全部店铺，nearby-附近店铺
    }
  },

  computed: {
    // 过滤后的店铺列表
    filteredStoreList() {
      let filtered = this.storeList;
      console.log('filteredStoreList', filtered)

      // 根据搜索关键词过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        filtered = filtered.filter(store =>
          store.name.toLowerCase().includes(keyword)
        );
      }

      // 根据排序方式过滤（这里可以扩展更多逻辑）
      if (this.sortBy === 'nearby') {
        // 这里可以添加基于位置的排序逻辑
        // 暂时返回所有店铺
      }

      return filtered;
    },

    // 保持兼容性：过滤后的店铺名称列表
    filteredStoreNames() {
      return this.filteredStoreList.map(store => store.name);
    }
  },

  onLoad() {
    console.log('📱 店铺列表页面加载')
    this.loadStoreNames()
  },

  onShow() {
    console.log('📱 店铺列表页面显示')
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('🔄 触发下拉刷新')
    this.refreshStoreList()
  },
  
  methods: {
    /**
     * 清除搜索
     */
    clearSearch() {
      this.searchKeyword = '';
    },

    /**
     * 搜索输入处理
     */
    onSearchInput(e) {
      // 实时处理输入，去除不必要的空格
      if (this.searchKeyword) {
        this.searchKeyword = this.searchKeyword.replace(/\s+/g, ' ');
      }
    },

    /**
     * 处理搜索
     */
    handleSearch() {
      console.log('🔍 搜索店铺:', this.searchKeyword);
      // 搜索逻辑已在computed中处理
    },

    /**
     * 切换排序方式
     */
    changeSort(sortType) {
      console.log('🔄 切换排序方式:', sortType);
      this.sortBy = sortType;
    },

    /**
     * 加载店铺列表
     */
    async loadStoreNames() {
      try {
        this.loading = true
        console.log('🏪 开始加载店铺列表')

        // 获取完整的店铺信息
        const response = await getStoreList()
        console.log('📊 店铺列表API响应:', response)

        if (response.code === 0 && response.data) {
          this.storeList = response.data
          // 为了保持兼容性，同时维护storeNames数组
          this.storeNames = response.data.map(store => store.name)
          console.log('✅ 成功加载店铺列表 - 数量:', this.storeList.length)

          // 显示成功提示
          if (this.storeList.length > 0) {
            uni.showToast({
              title: `加载成功，共${this.storeList.length}家店铺`,
              icon: 'success',
              duration: 2000
            })
          }
        } else {
          console.error('❌ 店铺列表API返回错误:', response.message)
          uni.showToast({
            title: response.message || '加载失败',
            icon: 'error',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('❌ 加载店铺列表失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'error',
          duration: 2000
        })
      } finally {
        this.loading = false
        // 停止下拉刷新
        uni.stopPullDownRefresh()
      }
    },

    /**
     * 刷新店铺列表
     */
    refreshStoreList() {
      console.log('🔄 刷新店铺列表')
      this.loadStoreNames()
    },

    /**
     * 获取店铺图片
     */
    getStoreImage(store) {
      // 如果店铺有image字段且不为空，使用店铺图片
      if (store.image && store.image.trim()) {
        // 如果是相对路径，需要拼接完整URL
        if (store.image.startsWith('/')) {
          // 这里可以根据实际情况配置图片服务器地址
          return `https://file.foxdance.com.cn${store.image}`;
        }
        // 如果已经是完整URL，直接返回
        return store.image;
      }

      // 如果没有图片，使用默认店铺图标
      return '/static/icon/店铺.png';
    },

    /**
     * 处理图片加载错误
     */
    handleImageError(e) {
      console.log('🖼️ 店铺图片加载失败，使用默认图标');
      // 图片加载失败时，替换为默认图标
      e.target.src = '/static/icon/店铺.png';
    },

    /**
     * 处理店铺点击事件
     */
    handleStoreClick(storeName, image, index) {
      console.log('🏪 点击店铺:', storeName, '类型:', typeof storeName, '图片：', image, '索引:', index);
      
      if (storeName === undefined || storeName === null) {
        // 如果storeName为undefined，从索引获取
        console.warn('⚠️ storeName为undefined，尝试从索引获取');
        const storeFromIndex = this.filteredStoreList[index];
        if (storeFromIndex && storeFromIndex.name) {
          storeName = storeFromIndex.name;
          console.log('🔄 已从索引恢复storeName:', storeName);
        }
      }

      // 根据店铺名称找到对应的店铺信息
      const store = this.storeList.find(s => s && s.name === storeName);
      if (!store) {
        console.error('❌ 未找到店铺信息:', storeName);
        uni.showToast({
          title: '店铺信息不存在',
          icon: 'error',
          duration: 2000
        });
        return;
      }

      console.log('🏪 找到店铺信息:', store);

      // 跳转到评论页面，传递店铺信息
      uni.navigateTo({
        url: `/pagesSub/switch/comment?storeId=${store.id}&storeName=${encodeURIComponent(storeName)}&storeImage=${'https://file.foxdance.com.cn' + image}&content_type=store`
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 0 24rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
  min-height: 100vh;
}

.store-list-title {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
  text-align: center;
}

.search-bar {
  padding: 24rpx 0;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 238, 248, 0.95);
  backdrop-filter: blur(20rpx);

  .search-box {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: 32rpx;
    padding: 24rpx 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &:focus-within {
      box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
      border-color: rgba(255, 107, 135, 0.3);
      transform: translateY(-2rpx);
    }

    .u-icon {
      margin-right: 16rpx;
      color: #ff6b87;
    }

    input {
      flex: 1;
      font-size: 30rpx;
      color: #4a4a4a;
      font-weight: 400;
      letter-spacing: 0.3rpx;
    }

    .clear-icon {
      margin-left: 10rpx;
      padding: 10rpx;
      opacity: 0.7;
      transition: all 0.2s ease;

      &:active {
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }
}

.filter-bar {
  padding: 20rpx 0 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .sort-options {
    .van-tabs {
      position: relative;
      display: flex;
      justify-content: center;
      -webkit-tap-highlight-color: transparent;

      &__wrap {
        overflow: hidden;
        position: relative;
        padding: 0;
        border-radius: 48rpx;
      }

      &__nav {
        position: relative;
        display: flex;
        background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
        height: 96rpx;
        border-radius: 48rpx;
        user-select: none;
        box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
        padding: 8rpx;
        border: 1rpx solid rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20rpx);
      }
    }

    .van-tab {
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40rpx;
      min-width: 180rpx;
      height: 80rpx;
      margin: 0 6rpx;
      -webkit-tap-highlight-color: transparent;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &__text {
        font-size: 30rpx;
        color: #8a8a8a;
        line-height: 1.2;
        padding: 0 24rpx;
        font-weight: 500;
        transition: all 0.3s ease;
        letter-spacing: 0.5rpx;
      }

      &--active {
        background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
        box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
        transform: translateY(-2rpx) scale(1.02);

        .van-tab__text {
          color: #ffffff;
          font-weight: 600;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
          transform: scale(1.05);
        }
      }
    }
  }
}

.store-list {
  padding-bottom: 48rpx;
  display: flex;
  flex-wrap: wrap;

  .loading {
    padding: 120rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-text {
      margin-top: 32rpx;
      font-size: 30rpx;
      color: #ff6b87;
      font-weight: 500;
      letter-spacing: 0.5rpx;
    }
  }

  .empty-list {
    padding: 160rpx 40rpx;
    display: flex;
    //flex-direction: column;
    //align-items: center;
    //justify-content: center;
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
    border-radius: 32rpx;
    margin: 24rpx 0;

    .empty-image {
      width: 280rpx;
      height: 280rpx;
      opacity: 0.6;
      border-radius: 24rpx;
    }

    .empty-text {
      margin-top: 40rpx;
      font-size: 36rpx;
      background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: 600;
      letter-spacing: 0.5rpx;
    }
  }

  .store-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 32rpx;
    padding: 30rpx 20rpx 25rpx 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20rpx);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 48%;

    &:active {
      transform: translateY(-4rpx) scale(0.98);
      box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
    }

    // 单数则增加margin-right:5rpx
    &:nth-child(odd) {
      margin-right: 4%;
    }

    .store-card-content {
      display: flex;
      align-items: center;

      .store-icon {
        margin-right: 16rpx;
        //padding-bottom: 15rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        border-radius: 12rpx;
        background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
        overflow: hidden;

        .icon {
          display: block;
          width: 50rpx;
          height: 50rpx;
          border-radius: 8rpx;
          object-fit: cover;
          transition: all 0.3s ease;
        }
      }

      .store-name {
        flex: 1;
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        //margin-bottom: 8rpx;
        line-height: 1.4;
        letter-spacing: 0.3rpx;
      }

      .store-status {
        font-size: 24rpx;
        color: #52c41a;
        background: rgba(82, 196, 26, 0.1);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        margin-left: 16rpx;
      }
    }
  }
}
</style>
