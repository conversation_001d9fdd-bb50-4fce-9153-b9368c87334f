# 共通样式文件使用说明

## 📁 文件结构
```
pagesSub/styles/
├── common.scss          # 主要共通样式文件
└── README.md           # 使用说明文档
```

## 🎨 样式分类

### 1. 主题色彩变量
- `$primary-gradient`: 主要渐变色 (#ff6b87 → #ff8e53)
- `$secondary-gradient`: 次要渐变色
- `$success-gradient`: 成功状态渐变色
- `$background-gradient`: 背景渐变色

### 2. 容器和布局
- `.container`: 页面主容器
- `.no-highlight`: 移除点击高亮效果

### 3. 卡片样式
- `.card`: 基础卡片样式
- `.xiaohongshu-card`: 小红书风格卡片
- `.topic-card`: 话题卡片样式
- `.store-card`: 店铺卡片样式

### 4. 按钮样式
- `.btn-primary`: 主要按钮样式
- `.floating-btn`: 浮动按钮样式

### 5. 搜索和筛选
- `.search-bar`: 搜索栏样式
- `.filter-bar`: 筛选栏样式

### 6. 状态样式
- `.loading`: 加载状态
- `.empty-list`: 空状态样式

### 7. 表单样式
- `.form-item`: 表单项容器
- `.input`: 输入框样式
- `.textarea`: 文本域样式

### 8. 文字排版
- `.page-title`: 页面标题
- `.section-title`: 章节标题
- `.text`: 正文文字
- `.gradient-text`: 渐变文字效果

### 9. 动画效果
- `fadeInUp`: 淡入上移动画
- `successPulse`: 成功脉冲动画
- `shimmer`: 闪光动画

### 10. 特效样式
- `.glass-effect`: 毛玻璃效果
- `.floating-shadow`: 悬浮阴影效果

### 11. 用户界面元素
- `.user-avatar`: 用户头像样式
- `.store-icon`: 店铺图标样式

## 🔧 使用方法

### 在Vue文件中引入
```scss
<style lang="scss" scoped>
@import '@/pagesSub/styles/common.scss';

// 你的自定义样式
.custom-style {
  // ...
}
</style>
```

### 使用示例

#### 1. 基础页面容器
```vue
<template>
  <view class="container">
    <!-- 页面内容 -->
  </view>
</template>
```

#### 2. 小红书风格卡片
```vue
<template>
  <view class="xiaohongshu-card">
    <!-- 卡片内容 -->
  </view>
</template>
```

#### 3. 主要按钮
```vue
<template>
  <button class="btn-primary">
    确认
  </button>
</template>
```

#### 4. 搜索栏
```vue
<template>
  <view class="search-bar">
    <view class="search-box">
      <u-icon name="search" class="u-icon"></u-icon>
      <input placeholder="搜索..." />
    </view>
  </view>
</template>
```

#### 5. 加载状态
```vue
<template>
  <view class="loading">
    <u-loading mode="flower" size="50"></u-loading>
    <text class="loading-text">加载中...</text>
  </view>
</template>
```

## 🎯 设计原则

### 1. Xiaohongshu风格
- 使用温暖的粉色渐变色彩
- 大圆角设计语言 (24rpx-32rpx)
- 毛玻璃效果和柔和阴影
- 现代化的卡片布局

### 2. 微信小程序兼容性
- 避免使用不兼容的CSS属性
- 提供降级方案
- 优化性能，减少复杂动画

### 3. 响应式设计
- 使用rpx单位适配不同屏幕
- 提供小屏幕适配样式
- 保持良好的触摸体验

## ⚠️ 注意事项

### 1. 性能优化
- 避免过度使用backdrop-filter
- 使用transform和opacity进行动画
- 启用GPU加速 (transform: translateZ(0))

### 2. 兼容性处理
- 渐变文字提供降级方案
- 毛玻璃效果提供备用样式
- 测试在微信开发者工具中的效果

### 3. 维护建议
- 新增样式前先检查是否已存在类似样式
- 保持命名规范的一致性
- 及时更新文档说明

## 🔄 更新记录

### v1.0.0 (2025-01-10)
- 初始版本发布
- 提取switch和store目录的共通样式
- 建立Xiaohongshu风格设计语言
- 添加微信小程序兼容性处理
