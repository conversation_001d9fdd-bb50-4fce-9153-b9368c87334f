/* ========================================
   共通样式文件 - Xiaohongshu风格设计语言
   适用于pagesSub/switch/和pagesSub/store/目录
   ======================================== */

/* ========================================
   1. 主题色彩变量 - Xiaohongshu风格
   ======================================== */
$primary-gradient: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
$secondary-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
$success-gradient: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
$background-gradient: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);

/* 基础颜色 */
$background-color: #f8f9fa;
$card-background: #ffffff;
$text-primary: #2d3436;
$text-secondary: #636e72;
$text-light: #b2bec3;
$border-color: #e9ecef;

/* 阴影效果 */
$shadow-light: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
$shadow-medium: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
$shadow-card: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
$shadow-floating: 0 12rpx 48rpx rgba(255, 107, 135, 0.4);

/* 动画变量 */
$transition-fast: 0.2s ease-out;
$transition-medium: 0.3s ease-out;
$transition-slow: 0.5s ease-out;
$transition-cubic: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

/* ========================================
   2. 容器和布局样式
   ======================================== */
.container {
  padding: 0 24rpx;
  background: $background-gradient;
  min-height: 100vh;
}

/* 移除点击高亮效果 */
.no-highlight {
  -webkit-tap-highlight-color: transparent;
}

/* ========================================
   3. 卡片基础样式
   ======================================== */
.card {
  background: $card-background;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: $shadow-light;
  overflow: hidden;
  transition: transform $transition-fast, box-shadow $transition-fast;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  }
}

/* 小红书风格卡片 */
.xiaohongshu-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: $shadow-card;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all $transition-cubic;
  transform: translateZ(0);
  will-change: transform;

  &:active {
    transform: translateY(-2rpx) scale(0.98);
    box-shadow: 0 12rpx 36rpx rgba(255, 105, 135, 0.15);
  }
}

/* 话题卡片样式 */
.topic-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  }
}

/* 店铺卡片样式 */
.store-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 30rpx 20rpx 25rpx 20rpx;
  margin-bottom: 24rpx;
  box-shadow: $shadow-card;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  transition: all $transition-cubic;
  width: 48%;

  &:active {
    transform: translateY(-4rpx) scale(0.98);
    box-shadow: 0 16rpx 48rpx rgba(255, 105, 135, 0.2);
  }
}

/* ========================================
   4. 按钮样式
   ======================================== */
.btn-primary {
  background: $primary-gradient;
  color: white;
  font-size: 32rpx;
  border: none;
  border-radius: 44rpx;
  font-weight: 600;
  box-shadow: $shadow-medium;
  transition: all $transition-fast;
  position: relative;
  overflow: hidden;

  &:not(.btn-disabled):active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 15rpx rgba(255, 107, 135, 0.4);
  }

  &.btn-disabled {
    background: #e9ecef;
    color: $text-light;
    box-shadow: none;
  }

  &.btn-success {
    background: $success-gradient;
    animation: successPulse 0.6s ease-out;
  }
}

/* 浮动按钮 */
.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: $primary-gradient;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $shadow-floating;
  z-index: 999;
  transition: all $transition-cubic;
  border: 3rpx solid rgba(255, 255, 255, 0.8);

  &:active {
    transform: scale(0.9);
    box-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.4);
  }

  .icon {
    font-size: 68rpx;
    color: #ffffff;
    font-weight: bold;
    line-height: 1;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
}

/* ========================================
   5. 搜索栏样式
   ======================================== */
.search-bar {
  padding: 24rpx 0;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 238, 248, 0.95);
  backdrop-filter: blur(20rpx);

  .search-box {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: 32rpx;
    padding: 24rpx 32rpx;
    box-shadow: $shadow-card;
    border: 1rpx solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &:focus-within {
      box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
      border-color: rgba(255, 107, 135, 0.3);
      transform: translateY(-2rpx);
    }

    .u-icon {
      margin-right: 16rpx;
      color: #ff6b87;
    }

    input {
      flex: 1;
      font-size: 30rpx;
      color: #4a4a4a;
      font-weight: 400;
      letter-spacing: 0.3rpx;
    }

    .clear-icon {
      margin-left: 10rpx;
      padding: 10rpx;
      opacity: 0.7;
      transition: all 0.2s ease;

      &:active {
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }
}

/* ========================================
   6. 筛选栏样式
   ======================================== */
.filter-bar {
  padding: 20rpx 0 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .sort-options {
    .van-tabs {
      position: relative;
      display: flex;
      justify-content: center;
      -webkit-tap-highlight-color: transparent;

      &__wrap {
        overflow: hidden;
        position: relative;
        padding: 0;
        border-radius: 48rpx;
      }

      &__nav {
        position: relative;
        display: flex;
        background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
        height: 96rpx;
        border-radius: 48rpx;
        user-select: none;
        box-shadow: $shadow-card;
        padding: 8rpx;
        border: 1rpx solid rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20rpx);
      }
    }

    .van-tab {
      cursor: pointer;
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      color: #8a8a8a;
      font-weight: 500;
      border-radius: 40rpx;
      transition: all $transition-medium;
      font-size: 28rpx;
      height: 80rpx;
      letter-spacing: 0.3rpx;

      &:active {
        background: rgba(255, 107, 135, 0.1);
        transform: scale(0.95);
      }

      &--active {
        background: $primary-gradient;
        box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
        transform: translateY(-2rpx) scale(1.02);

        .van-tab__text {
          color: #ffffff;
          font-weight: 600;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
          transform: scale(1.05);
        }
      }
    }
  }
}

/* ========================================
   7. 加载状态样式
   ======================================== */
.loading {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .loading-text {
    margin-top: 32rpx;
    font-size: 30rpx;
    color: #ff6b87;
    font-weight: 500;
    letter-spacing: 0.5rpx;
  }
}

/* 空状态样式 */
.empty-list {
  padding: 160rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
  border-radius: 32rpx;
  margin: 24rpx 0;

  .empty-image {
    width: 280rpx;
    height: 280rpx;
    opacity: 0.6;
    border-radius: 24rpx;
  }

  .empty-text {
    margin-top: 40rpx;
    font-size: 36rpx;
    background: $primary-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
    letter-spacing: 0.5rpx;
  }
}

/* ========================================
   8. 表单和输入框样式
   ======================================== */
.form-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: $shadow-card;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all $transition-medium;
  animation: fadeInUp 0.6s ease-out;

  &:focus-within {
    box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
    border-color: rgba(255, 107, 135, 0.3);
    transform: translateY(-4rpx);
  }

  .form-label {
    font-size: 28rpx;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 20rpx;
    display: block;
    letter-spacing: 0.3rpx;
  }

  .input {
    width: 100%;
    font-size: 30rpx;
    color: #4a4a4a;
    background: transparent;
    border: none;
    outline: none;
    font-weight: 400;
    letter-spacing: 0.3rpx;
    line-height: 1.6;

    &::placeholder {
      color: #b2bec3;
      font-weight: 400;
    }
  }

  .textarea {
    width: 92%;
    height: 360rpx;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10rpx);
    border-radius: 24rpx;
    padding: 28rpx;
    font-size: 30rpx;
    color: #4a4a4a;
    border: 1rpx solid rgba(255, 107, 135, 0.2);
    box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);
    transition: all 0.3s ease;
    line-height: 1.7;
    letter-spacing: 0.3rpx;

    &:focus {
      border-color: rgba(255, 107, 135, 0.5);
      box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);
      transform: translateY(-2rpx);
    }
  }
}

/* ========================================
   9. 文字和排版样式
   ======================================== */
.page-title {
  font-size: 30rpx;
  font-weight: 600;
  text-align: center;
  color: #ff6b87;
  background: $primary-gradient;
  letter-spacing: 0.5rpx;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

.section-title {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
  text-align: center;
}

.text {
  font-size: 30rpx;
  line-height: 1.8;
  margin-bottom: 20rpx;
  color: #4a4a4a;
  word-break: break-all;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}

.expand-btn {
  color: #ff6b87;
  font-size: 26rpx;
  display: inline-block;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;

  &:active {
    background: rgba(255, 107, 135, 0.2);
    transform: scale(0.95);
  }
}

/* 渐变文字效果 - 微信小程序兼容版本 */
.gradient-text {
  color: #ff6b87;
  background: $primary-gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

/* ========================================
   10. 动画效果
   ======================================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes progressSlide {
  from {
    width: 0;
  }
}

/* ========================================
   11. 特效样式
   ======================================== */
/* 毛玻璃效果 */
.glass-effect {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

/* 悬浮阴影效果 */
.floating-shadow {
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);
  transition: box-shadow 0.3s ease;
}

/* 性能优化的毛玻璃效果 */
.glass-effect-optimized {
  background: rgba(255, 255, 255, 0.9);
}

/* ========================================
   12. 用户头像和图标样式
   ======================================== */
.user-avatar {
  position: relative;
  margin-right: 28rpx;

  .avatar-image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 3rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.2);
  }

  .level-badge {
    position: absolute;
    bottom: -4rpx;
    right: -4rpx;
    min-width: 32rpx;
    height: 32rpx;
    border-radius: 16rpx;
    font-size: 18rpx;
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid white;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
    padding: 0 8rpx;
  }
}

.store-icon {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: $primary-gradient;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
}

/* ========================================
   13. 响应式和适配样式
   ======================================== */
/* 小屏幕适配 */
@media screen and (max-width: 750rpx) {
  .container {
    padding: 0 16rpx;
  }

  .xiaohongshu-card {
    padding: 24rpx;
  }

  .form-item {
    padding: 32rpx 24rpx;
  }
}
