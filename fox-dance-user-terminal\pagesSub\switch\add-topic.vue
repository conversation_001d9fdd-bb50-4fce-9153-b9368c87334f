<template>
	<view class="container">
		<uni-forms ref="form" :model="formData" :rules="rules" validate-trigger="submit" err-show-type="toast">
			<view class="form-item">
				<text class="label">话题标题</text>
				<uni-forms-item name="title">
					<input class="input" type="text" v-model="formData.title" placeholder="请输入话题标题" />
				</uni-forms-item>
			</view>
			
			<view class="form-item">
				<text class="label">话题描述</text>
				<uni-forms-item name="description">
					<textarea class="textarea" v-model="formData.description" placeholder="请输入话题描述" />
				</uni-forms-item>
			</view>

			<!-- 图片上传区域 -->
			<view class="form-item">
				<text class="label">话题图片 <text class="label-tip">（最多9张，第一张将作为封面）</text></text>
				<view class="image-upload-container">
					<!-- 已上传的图片列表 -->
					<view class="image-list">
						<view
							class="image-item"
							v-for="(image, index) in uploadedImages"
							:key="index"
							@longpress="showImageOptions(index)"
						>
							<image
								class="uploaded-image"
								:src="processImageUrl(image)"
								mode="aspectFill"
								@tap="previewImage(index)"
								@error="handleImageLoadError(index)"
								:lazy-load="true"
							></image>
							<view class="image-delete" @tap="deleteImage(index)">
								<text class="delete-icon">×</text>
							</view>
							<view v-if="index === 0" class="cover-badge">封面</view>
						</view>

						<!-- 添加图片按钮 -->
						<view
							class="add-image-btn"
							v-if="uploadedImages.length < 9"
							@tap="chooseImages"
						>
							<text class="add-icon">+</text>
							<text class="add-text">添加图片</text>
						</view>
					</view>

					<!-- 上传进度提示 -->
					<view v-if="uploading" class="upload-progress">
						<u-loading mode="flower" size="30" color="#667eea"></u-loading>
						<text class="progress-text">正在上传图片...</text>
					</view>
				</view>
			</view>

			<view class="submit-btn">
				<button class="btn" type="primary" @click="submitForm" :disabled="submitting">{{ submitting ? '提交中...' : '发布话题' }}</button>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import topicApi from '@/config/topic.api.js';
	import { upImg } from '@/config/http.achieve.js';
	// 引入uni-forms组件
	import { uniForms, uniFormsItem } from '@dcloudio/uni-ui';
	
	export default {
		components: {
			uniForms,
			uniFormsItem
		},
		data() {
			return {
				userId: '',
				submitting: false,
				uploading: false, // 图片上传状态
				uploadedImages: [], // 已上传的图片URL数组
				formData: {
					title: '',
					description: ''
				},
				rules: {
					title: {
						rules: [
							{
								required: true,
								errorMessage: '请输入话题标题'
							},
							{
								minLength: 3,
								maxLength: 100,
								errorMessage: '标题长度在3-100个字符之间'
							}
						]
					},
					description: {
						rules: [
							{
								required: true,
								errorMessage: '请输入话题描述'
							},
							{
								minLength: 5,
								maxLength: 200,
								errorMessage: '描述长度在5-200个字符之间'
							}
						]
					}
				}
			}
		},
		onLoad() {
			// 获取用户ID
			this.userId = uni.getStorageSync('userid') || '18';
		},
		methods: {
			submitForm() {
				if (this.submitting) return;
				
				this.$refs.form.validate().then(res => {
					this.submitting = true;
					
					uni.showLoading({
						title: '发布中...'
					});
					
					// 构建请求数据
					const requestData = {
						userId: this.userId,
						title: this.formData.title,
						description: this.formData.description,
						topicImages: this.uploadedImages // 添加图片数组
					};
					
					// 调用API创建话题
					topicApi.addTopic(requestData).then(res => {
						console.log('创建话题API返回数据:', JSON.stringify(res));
						if (res.code === 0) {
							uni.hideLoading();
							uni.showToast({
								title: '发布成功',
								icon: 'success'
							});
							
							// 返回上一页并刷新
							setTimeout(() => {
								// 返回话题列表页面并刷新
								const pages = getCurrentPages();
								const prevPage = pages[pages.length - 2]; // 上一页
								
								// 通知上一页刷新
								if (prevPage && prevPage.$vm) {
									// 调用上一页的刷新方法
									prevPage.$vm.fetchTopicList(true);
								}
								
								uni.navigateBack();
							}, 1000);
						} else {
							// 处理不同的错误情况
							let errorMessage = '发布失败';
							if (res.code === 40300) {
								errorMessage = '只有特定用户可以创建话题';
							} else if (res.code === 40001) {
								if (res.message.includes('标题过长')) {
									errorMessage = '标题过长';
								} else {
									errorMessage = '标题或描述不能为空';
								}
							} else {
								errorMessage = res.message || '发布失败';
							}
							
							uni.hideLoading();
							uni.showToast({
								title: errorMessage,
								icon: 'none'
							});
						}
					}).catch(err => {
						console.error('创建话题失败:', err);
						uni.hideLoading();
						uni.showToast({
							title: '网络请求错误',
							icon: 'none'
						});
					}).finally(() => {
						this.submitting = false;
					});
				}).catch(err => {
					console.log('表单验证错误:', err);
				});
			},

			// 选择图片
			chooseImages() {
				console.log('🔥 开始选择图片');

				if (this.uploading) {
					uni.showToast({
						title: '正在上传中，请稍候',
						icon: 'none'
					});
					return;
				}

				const remainingCount = 9 - this.uploadedImages.length;
				console.log('🔥 当前已上传图片数量:', this.uploadedImages.length, '剩余可上传:', remainingCount);

				if (remainingCount <= 0) {
					uni.showToast({
						title: '最多只能上传9张图片',
						icon: 'none'
					});
					return;
				}

				uni.chooseImage({
					count: remainingCount,
					sizeType: ['compressed'], // 使用压缩图片
					sourceType: ['album', 'camera'],
					success: (res) => {
						console.log('🔥 选择图片成功:', res.tempFilePaths);
						console.log('🔥 选择的图片数量:', res.tempFilePaths.length);
						this.uploadImages(res.tempFilePaths);
					},
					fail: (error) => {
						console.error('❌ 选择图片失败:', error);
						uni.showToast({
							title: '选择图片失败: ' + (error.errMsg || '未知错误'),
							icon: 'none'
						});
					}
				});
			},

			// 上传图片到COS
			async uploadImages(tempFilePaths) {
				if (!tempFilePaths || tempFilePaths.length === 0) return;

				this.uploading = true;
				uni.showLoading({
					title: '上传图片中...'
				});

				console.log('🔥 开始上传图片，数量:', tempFilePaths.length);

				try {
					// 逐个上传图片，避免并发问题
					let successCount = 0;
					let failCount = 0;

					for (let i = 0; i < tempFilePaths.length; i++) {
						const filePath = tempFilePaths[i];
						console.log(`🔥 上传第${i + 1}张图片:`, filePath);

						try {
							// 使用新的图片上传API
							const result = await topicApi.uploadImage(filePath, 'file', { driver: 'cos' });
							console.log(`🔥 第${i + 1}张图片上传结果:`, result);

							if (result.code === 1 && result.data && result.data.file && result.data.file.url) {
								const imageUrl = result.data.file.url;

								// 验证图片URL的可访问性
								await this.validateImageUrl(imageUrl);

								this.uploadedImages.push(imageUrl);
								successCount++;
								console.log('✅ 图片上传成功:', imageUrl);
							} else {
								failCount++;
								console.error('❌ 图片上传失败，响应格式错误:', result);
							}
						} catch (uploadError) {
							failCount++;
							console.error(`❌ 第${i + 1}张图片上传异常:`, uploadError);
						}
					}

					uni.hideLoading();

					if (successCount > 0) {
						uni.showToast({
							title: `成功上传${successCount}张图片${failCount > 0 ? `，${failCount}张失败` : ''}`,
							icon: successCount === tempFilePaths.length ? 'success' : 'none'
						});
					} else {
						uni.showToast({
							title: '图片上传失败，请重试',
							icon: 'none'
						});
					}

				} catch (error) {
					console.error('❌ 图片上传异常:', error);
					uni.hideLoading();
					uni.showToast({
						title: '图片上传失败: ' + (error.message || '未知错误'),
						icon: 'none'
					});
				} finally {
					this.uploading = false;
				}
			},

			// 删除图片
			deleteImage(index) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这张图片吗？',
					success: (res) => {
						if (res.confirm) {
							this.uploadedImages.splice(index, 1);
							console.log('删除图片，当前图片数量:', this.uploadedImages.length);
						}
					}
				});
			},

			// 预览图片
			previewImage(index) {
				console.log('🔥 开始预览图片，索引:', index, '图片数组:', this.uploadedImages);

				if (!this.uploadedImages || this.uploadedImages.length === 0) {
					uni.showToast({
						title: '没有可预览的图片',
						icon: 'none'
					});
					return;
				}

				if (index < 0 || index >= this.uploadedImages.length) {
					uni.showToast({
						title: '图片索引错误',
						icon: 'none'
					});
					return;
				}

				// 验证图片URL的有效性
				const currentImageUrl = this.uploadedImages[index];
				console.log('🔥 当前预览图片URL:', currentImageUrl);

				if (!currentImageUrl || typeof currentImageUrl !== 'string') {
					uni.showToast({
						title: '图片URL无效',
						icon: 'none'
					});
					return;
				}

				// 处理图片URL，确保可访问性
				const processedUrls = this.uploadedImages.map(url => {
					return this.processImageUrl(url);
				});

				const processedCurrentUrl = processedUrls[index];
				console.log('🔥 处理后的图片URL数组:', processedUrls);
				console.log('🔥 当前预览URL:', processedCurrentUrl);

				uni.previewImage({
					current: processedCurrentUrl,  // 修复：使用图片URL而不是索引
					urls: processedUrls,           // 修复：使用处理后的URL数组
					success: () => {
						console.log('✅ 图片预览成功');
					},
					fail: (error) => {
						console.error('❌ 图片预览失败:', error);
						uni.showToast({
							title: '图片预览失败: ' + (error.errMsg || '未知错误'),
							icon: 'none'
						});
					}
				});
			},

			// 显示图片操作选项
			showImageOptions(index) {
				const itemList = ['预览图片', '删除图片'];
				if (index > 0) {
					itemList.unshift('设为封面');
				}

				uni.showActionSheet({
					itemList: itemList,
					success: (res) => {
						if (itemList[res.tapIndex] === '设为封面') {
							this.setAsCover(index);
						} else if (itemList[res.tapIndex] === '预览图片') {
							this.previewImage(index);
						} else if (itemList[res.tapIndex] === '删除图片') {
							this.deleteImage(index);
						}
					}
				});
			},

			// 设为封面（移动到第一位）
			setAsCover(index) {
				if (index === 0) return;

				const image = this.uploadedImages.splice(index, 1)[0];
				this.uploadedImages.unshift(image);

				uni.showToast({
					title: '已设为封面',
					icon: 'success'
				});
			},

			// 处理图片URL，确保可访问性
			processImageUrl(url) {
				if (!url) return '';

				// 如果URL是相对路径，转换为绝对路径
				if (url.startsWith('/')) {
					return 'https://file.foxdance.com.cn' + url;
				}

				// 如果URL不包含协议，添加https
				if (!url.startsWith('http://') && !url.startsWith('https://')) {
					return 'https://' + url;
				}

				return url;
			},

			// 处理图片加载错误
			handleImageLoadError(index) {
				console.error('❌ 图片加载失败，索引:', index, '图片URL:', this.uploadedImages[index]);
				uni.showToast({
					title: '图片加载失败',
					icon: 'none'
				});
			},

			// 验证图片URL的可访问性
			async validateImageUrl(imageUrl) {
				return new Promise((resolve, reject) => {
					// 在微信小程序中，我们可以通过创建一个临时的image组件来验证URL
					// 但这里我们简化处理，主要检查URL格式
					if (!imageUrl || typeof imageUrl !== 'string') {
						reject(new Error('图片URL格式错误'));
						return;
					}

					// 检查URL是否为有效格式
					const urlPattern = /^(https?:\/\/|\/)/;
					if (!urlPattern.test(imageUrl)) {
						reject(new Error('图片URL格式不正确'));
						return;
					}

					console.log('✅ 图片URL验证通过:', imageUrl);
					resolve(imageUrl);
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
@import '@/pagesSub/styles/common.scss';

	.form-item {
		margin-bottom: 48rpx;
		border-radius: 32rpx;
		padding: 32rpx;
		box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
		border: 1rpx solid rgba(255, 255, 255, 0.8);

		.label {
			display: block;
			font-size: 32rpx;
			background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			font-weight: 600;
			margin-bottom: 28rpx;
			letter-spacing: 0.5rpx;

			.label-tip {
				font-size: 26rpx;
				color: #8a8a8a;
				font-weight: 400;
				opacity: 0.8;
			}
		}

		.input {
			width: 92%;
			height: 104rpx;
			background: rgba(255, 255, 255, 0.9);
			backdrop-filter: blur(10rpx);
			border-radius: 24rpx;
			padding: 0 28rpx;
			font-size: 30rpx;
			color: #4a4a4a;
			border: 1rpx solid rgba(255, 107, 135, 0.2);
			box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);
			transition: all 0.3s ease;
			letter-spacing: 0.3rpx;

			&:focus {
				border-color: rgba(255, 107, 135, 0.5);
				box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);
				transform: translateY(-2rpx);
			}
		}

		.textarea {
			width: 92%;
			height: 360rpx;
			background: rgba(255, 255, 255, 0.9);
			backdrop-filter: blur(10rpx);
			border-radius: 24rpx;
			padding: 28rpx;
			font-size: 30rpx;
			color: #4a4a4a;
			border: 1rpx solid rgba(255, 107, 135, 0.2);
			box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);
			transition: all 0.3s ease;
			line-height: 1.7;
			letter-spacing: 0.3rpx;

			&:focus {
				border-color: rgba(255, 107, 135, 0.5);
				box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);
				transform: translateY(-2rpx);
			}
		}
	}

	// 图片上传样式 - 小红书风格
	.image-upload-container {
		.image-list {
			display: flex;
			flex-wrap: wrap;
			gap: 24rpx;
		}

		.image-item {
			position: relative;
			width: 200rpx;
			height: 200rpx;
			border-radius: 24rpx;
			overflow: hidden;
			background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
			border: 2rpx solid rgba(255, 107, 135, 0.2);
			box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.1);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.95);
			}

			.uploaded-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.3s ease;
			}

			.image-delete {
				position: absolute;
				top: 12rpx;
				right: 12rpx;
				width: 44rpx;
				height: 44rpx;
				background: rgba(255, 107, 135, 0.9);
				backdrop-filter: blur(10rpx);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.3);

				.delete-icon {
					color: #ffffff;
					font-size: 26rpx;
					font-weight: bold;
				}
			}

			.cover-badge {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
				color: #ffffff;
				font-size: 22rpx;
				text-align: center;
				padding: 10rpx 0;
				font-weight: 600;
				letter-spacing: 0.5rpx;
			}
		}

		.add-image-btn {
			width: 200rpx;
			height: 200rpx;
			border: 2rpx dashed rgba(255, 107, 135, 0.4);
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
			transition: all 0.3s ease;

			&:active {
				background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
				border-color: rgba(255, 107, 135, 0.6);
				transform: scale(0.95);
			}

			.add-icon {
				font-size: 52rpx;
				color: #ff6b87;
				margin-bottom: 12rpx;
				font-weight: bold;
			}

			.add-text {
				font-size: 26rpx;
				color: #ff6b87;
				font-weight: 500;
				letter-spacing: 0.3rpx;
			}
		}

		.upload-progress {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 24rpx;
			padding: 24rpx;
			background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
			border-radius: 20rpx;
			border: 1rpx solid rgba(255, 107, 135, 0.1);

			.progress-text {
				margin-left: 20rpx;
				font-size: 28rpx;
				color: #ff6b87;
				font-weight: 500;
				letter-spacing: 0.3rpx;
			}
		}
	}

	.submit-btn {
		margin-top: 80rpx;

		.btn {
			background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
			color: #ffffff;
			font-weight: 600;
			border: none;
			border-radius: 32rpx;
			height: 104rpx;
			font-size: 34rpx;
			box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.3);
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			letter-spacing: 1rpx;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

			&:active {
				transform: translateY(2rpx) scale(0.98);
				box-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.3);
			}

			&[disabled] {
				background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
				color: #94a3b8;
				box-shadow: none;
				transform: none;
			}
		}
	}

	/* 小红书风格动画效果 */
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.form-item {
		animation: fadeInUp 0.6s ease-out;
	}

	/* 毛玻璃效果 */
	.glass-effect {
		backdrop-filter: blur(20rpx);
		-webkit-backdrop-filter: blur(20rpx);
	}
</style>