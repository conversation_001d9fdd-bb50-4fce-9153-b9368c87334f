<template>
  <view class="comment-detail">

    <!-- 评论详情 -->
    <scroll-view
      ref="replyScrollView"
      scroll-y
      class="comment-container"
      @scrolltolower="loadMoreReplies"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
      :style="{ height: scrollViewHeight }"
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      :scroll-with-animation="true">
      <view class="safe-area-inset"></view>
      <view v-if="loading" class="loading">
        <u-loading mode="flower" size="50"></u-loading>
      </view>
      <block v-else>
        <!-- 主评论 -->
        <view class="main-comment" id="main-comment">
          <view class="user-info">
            <view class="avatar-wrap">
              <image :src="comment.user.avatar" mode="aspectFill" :lazy-load="true" class="avatar"></image>
            </view>
            <view class="user-meta">
              <view class="nickname">
                {{ comment.user.nickname }}
                <view v-if="comment.user.level >= 0" class="user-level"
                  :style="{ backgroundColor: getLevelColor(comment.user.level) }">Lv{{ comment.user.level }}</view>
              </view>
              <view class="time">{{ formatTime(comment.created_at) }}</view>
            </view>
            <view class="like-btn" @tap="likeComment">
              <u-icon :name="comment.is_liked ? 'heart-fill' : 'heart'" :color="comment.is_liked ? '#f56c6c' : '#999'"
                size="28"></u-icon>
              <text>{{ comment.likes }}</text>
            </view>
          </view>
          <view class="content">
            <text>{{ showFullContent ? comment.content : (comment.content.length > 100 ? comment.content.slice(0, 100) +
              '...' : comment.content) }}</text>
            <view v-if="comment.content.length > 100" class="expand-btn" @tap="toggleContent">
              {{ showFullContent ? '收起' : '展开' }}
            </view>
          </view>
          <view class="actions">
            <!-- <view class="reply-btn" @tap="replyToMain">
              <uni-icons type="chatbubble" color="#999" size="18"></uni-icons>
              <text>回复</text>
            </view> -->
            <!-- 添加删除按钮，仅当当前用户是评论者时显示 -->
            <view v-if="isCommentOwner" class="delete-btn" @tap="showDeleteCommentConfirm">
              <u-icon name="trash" color="#999" size="28"></u-icon>
              <!-- <text>删除</text> -->
            </view>
          </view>
        </view>

        <!-- 回复列表 -->
        <view class="replies-container">
          <view class="replies-header">
            <text>回复 ({{ replyCount }})</text>
            <view class="sort-options">
              <view class="van-tabs">
                <view class="van-tabs__wrap">
                  <view class="van-tabs__nav">
                    <view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'hot' }"
                      @tap="changeSort('hot')">
                      <view class="van-tab__text">最热</view>
                    </view>
                    <view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'new' }"
                      @tap="changeSort('new')">
                      <view class="van-tab__text">最新</view>
                    </view>
                    <view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'my' }"
                      @tap="changeSort('my')">
                      <view class="van-tab__text">我的</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="reply-list">
            <view v-if="!(replies.length > 0)" class="empty-replies">
              <image src="/static/icon/no-messages.png" mode="" class="empty-image"></image>
              <!-- <view slot="message">还没有人回复，快来抢沙发吧~</view> -->
            </view>
            <view v-else class="reply-item" v-for="(reply, index) in replies" :key="index" :id="`reply-${index}`">
              <view class="reply-user-info">
                <image :src="reply.user.avatar" mode="aspectFill" :lazy-load="true" class="reply-avatar"></image>
                <view class="reply-user-meta">
                  <view class="reply-nickname">
                    {{ reply.user.nickname }}
                    <text v-if="reply.reply_to" class="reply-text">回复</text>
                    <text v-if="reply.reply_to" class="reply-to">{{ reply.reply_to.nickname }}</text>
                  </view>
                  <view class="reply-time">{{ formatTime(reply.created_at) }}</view>
                </view>
                <view class="reply-like" @tap="likeReply(reply, index)">
                  <u-icon :name="reply.is_liked ? 'heart-fill' : 'heart'" :color="reply.is_liked ? '#f56c6c' : '#999'"
                    size="24"></u-icon>
                  <text>{{ reply.likes }}</text>
                </view>
              </view>
              <view class="reply-content" @tap="replyToComment(reply)">
                <text>{{ reply.showFullContent ? reply.content : (reply.content.length > 100 ? reply.content.slice(0,
                  100) + '...' : reply.content) }}</text>
                <view v-if="reply.content.length > 100" class="expand-btn" @tap.stop="toggleReplyContent(reply, index)">
                  {{ reply.showFullContent ? '收起' : '展开' }}
                </view>
              </view>
              <view class="reply-actions">
                <view class="reply-reply" @tap="replyToComment(reply)">
                  <image src="/static/icon/chat.png" mode="aspectFill"></image>
                  <!-- <text>回复</text> -->
                </view>
                <!-- 添加回复删除按钮，仅当当前用户是回复者时显示 -->
                <!-- <view v-if="isReplyOwner(reply)" class="reply-delete" @tap="showDeleteReplyConfirm(reply, index)">
                  <u-icon name="trash" color="#999" size="24"></u-icon>
                  <text>删除</text>
                </view> -->
                <view class="more-btn" @tap.stop="showMoreOptions(reply)">
                  <image src="/static/icon/more.png" mode="aspectFill"></image>
                </view>
              </view>
            </view>

            <!-- 加载更多状态 - 使用骨架屏优化 -->
            <view v-if="pagination.loading" class="loading-more-skeleton">
              <!-- 显示回复骨架屏而不是loading图标 -->
              <reply-skeleton v-for="n in 4" :key="n"></reply-skeleton>
            </view>
            <view v-else-if="!pagination.hasMore && replies.length > 0" class="no-more">
              <text>没有更多回复了</text>
            </view>
          </view>
        </view>
      </block>
    </scroll-view>

    <!-- 蒙版层：回复状态或键盘弹出时显示 -->
    <view v-if="isReplying || isKeyboardShow" class="mask-layer"
      @tap="isReplying ? cancelReply() : hideMaskAndKeyboard()"></view>

    <!-- 底部输入框 -->
    <view class="input-container" :style="{ bottom: inputContainerBottom + 'px' }">
      <comment-input v-model="replyText" :placeholder="inputPlaceholder" :use-image-button="true" @send="sendReply"
        ref="commentInput" @focus="onInputFocus" @blur="onInputBlur" />
    </view>

    <!-- 更多操作弹窗 -->
    <u-popup v-model="showMorePopup" mode="bottom" border-radius="30">
      <view class="action-popup">
        <view class="action-item reply" @tap="replyFromMore">
          <view class="action-icon">
            <image src="/static/icon/chat-1.png" mode="aspectFill"></image>
          </view>
          <text>回复</text>
        </view>
        <view class="action-item copy" @tap="copyComment">
          <view class="action-icon">
            <image src="/static/icon/copy.png" mode="aspectFill"></image>
          </view>
          <text>复制</text>
        </view>
        <view v-if="currentMoreComment && isReplyOwner(currentMoreComment)" class="action-item report block"
          @tap="deleteReply">
          <view class="action-icon">
            <image src="/static/icon/delete.png" mode="aspectFill"></image>
          </view>
          <text>删除</text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import commentApi from '@/config/comment.api.js'
import topicApi from '@/config/topic.api.js'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import CommentInput from '../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue'
import ReplySkeleton from './components/ReplySkeleton.vue'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export default {
  components: {
    CommentInput,
    ReplySkeleton
  },
  data() {
    return {
      commentId: null,
      userId: '',
      loading: true,
      isRefreshing: false,
      loadingMore: false,
      hasMore: true,
      page: 1,
      limit: 10,
      comment: {
        id: '',
        content: '',
        created_at: '',
        likes: 0,
        is_liked: false,
        user: {
          id: '',
          nickname: '',
          avatar: '',
          level: 0
        }
      },
      replies: [],
      replyCount: 0,
      replyText: '',
      currentReplyTo: null,
      inputPlaceholder: '发表您的评论...',
      sortBy: 'hot',
      scrollViewHeight: 'calc(90vh - 110rpx)',
      showFullContent: false,
      showMorePopup: false, // 更多操作弹窗
      currentMoreComment: null, // 当前操作的评论
      isReplying: false, // 是否处于回复状态
      keyboardHeight: 0, // 键盘高度
      inputContainerBottom: 0, // 输入框容器底部距离
      isKeyboardShow: false, // 键盘是否显示
      scrollTop: 0, // scroll-view的滚动位置
      scrollIntoView: '', // scroll-view的滚动到指定元素
      // 分页相关数据
      pagination: {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      },
      isLoadingMore: false, // 是否正在加载更多
      loadingText: '加载中...' // 加载提示文本
    }
  },
  computed: {
    // 判断当前用户是否是评论所有者
    isCommentOwner() {
      console.log(this.comment.user, this.userId)
      return this.comment.user && this.userId && String(this.comment.user.id) == String(this.userId);
    }
  },
  onLoad(options) {
    if (options.id) {
      // 修复：将commentId转换为Long兼容的数字类型
      this.commentId = Number(options.id);

      // 修复：将userId转换为Long兼容的数字类型
      const userIdStr = options.userId || uni.getStorageSync('userid');
      this.userId = Number(userIdStr);
      this.fetchCommentDetail();
      this.setScrollViewHeight();
      this.setupKeyboardListener();
    } else {
      uni.showToast({
        title: '评论ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },

  // 添加生命周期钩子来管理键盘监听
  onShow() {
    // 页面显示时，确保只有一个有效的键盘监听
    uni.offKeyboardHeightChange(); // 先移除可能存在的监听
    this.setupKeyboardListener();
  },

  onHide() {
    // 页面隐藏时移除监听
    uni.offKeyboardHeightChange();
  },

  onUnload() {
    // 页面卸载时移除监听
    uni.offKeyboardHeightChange();
  },
  methods: {
    // 兼容性时间戳函数 - 替代performance.now()
    getTimestamp() {
      // 微信小程序环境使用Date.now()
      if (typeof performance !== 'undefined' && performance.now) {
        return performance.now();
      }
      return Date.now();
    },

    // 设置键盘高度监听器
    setupKeyboardListener() {
      // #ifdef MP-WEIXIN
      uni.onKeyboardHeightChange(res => {
        this.keyboardHeight = res.height;
        this.isKeyboardShow = res.height > 0;

        if (res.height > 0) {
          // 键盘弹出，调整输入框位置
          this.inputContainerBottom = res.height;
        } else {
          // 键盘收起，恢复输入框位置
          this.inputContainerBottom = 0;
        }
      });
    },

    // 输入框获取焦点
    onInputFocus(e) {
      console.log('输入框获取焦点');
      this.isKeyboardShow = true;

      // 微信小程序中，键盘弹出时的额外处理
      // #ifdef MP-WEIXIN
      // 延时获取键盘高度，因为键盘弹出需要时间
      setTimeout(() => {
        if (this.keyboardHeight === 0) {
          // 如果监听器没有获取到键盘高度，使用默认值
          this.keyboardHeight = 280; // 微信小程序默认键盘高度
          this.inputContainerBottom = this.keyboardHeight;
        }
      }, 300);
      // #endif
    },

    // 输入框失去焦点
    onInputBlur(e) {
      console.log('输入框失去焦点');
      this.isKeyboardShow = false;

      // 延时重置，确保键盘完全收起
      setTimeout(() => {
        if (!this.isKeyboardShow) {
          this.keyboardHeight = 0;
          this.inputContainerBottom = 0;
        }
      }, 100);
    },

    // 隐藏蒙版层并收起键盘
    hideMaskAndKeyboard() {
      console.log('点击蒙版层，收起键盘');

      // 让输入框失去焦点
      if (this.$refs.commentInput) {
        this.$refs.commentInput.blur();
      }

      // 强制隐藏键盘
      uni.hideKeyboard();

      // 重置键盘状态
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
      this.inputContainerBottom = 0;
    },

    // 取消回复
    cancelReply() {
      // 重置回复状态和相关数据
      this.isReplying = false;
      this.currentReplyTo = null;
      this.replyText = '';
      this.inputPlaceholder = '发表您的评论...';
      // 确保键盘收起
      uni.hideKeyboard();
      // 模拟失去焦点，确保下次可以重新获取焦点
      if (this.$refs.commentInput) {
        this.$refs.commentInput.$emit('blur');
      }
    },
    // 判断当前用户是否是回复所有者
    isReplyOwner(reply) {
      return reply && reply.user && this.userId && String(reply.user.id) == String(this.userId);
    },

    // 显示删除评论确认框
    showDeleteCommentConfirm() {
      uni.showModal({
        title: '删除评论',
        content: '确认要删除这条评论吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: res => {
          if (res.confirm) {
            this.deleteComment();
          }
        }
      });
    },
    // 显示更多操作弹窗
    showMoreOptions(item) {
      this.currentMoreComment = item;
      this.showMorePopup = true;
    },

    // 从更多操作弹窗中点击回复
    replyFromMore() {
      if (this.currentMoreComment) {
        this.showMorePopup = false;
        // 等待更多操作弹窗关闭后再打开回复弹窗
        setTimeout(() => {
          // 先确保重置状态
          if (this.$refs.commentInput) {
            this.$refs.commentInput.autoFocus = false;
          }

          // 设置回复状态
          this.replyToComment(this.currentMoreComment);
          this.isReplying = true;

          // 再次延时确保输入框聚焦，因为replyToComment中的聚焦可能因为弹窗动画而失效
          setTimeout(() => {
            if (this.$refs.commentInput) {
              this.$refs.commentInput.focus();
            }
          }, 150);
        }, 300);
      }
    },

    // 复制评论内容
    copyComment() {
      if (!this.currentMoreComment) return;

      uni.setClipboardData({
        data: this.currentMoreComment.content,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
          this.showMorePopup = false;
        }
      });
    },

    // 删除回复
    deleteReply() {
      if (!this.currentMoreComment) return;

      uni.showModal({
        title: '删除回复',
        content: '确认要删除这条回复吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: res => {
          if (res.confirm) {
            // 调用API删除回复
            commentApi.deleteReply(Number(this.currentMoreComment.id), {
              userId: this.userId
            }).then(res => {
              console.log('删除回复API返回数据:', JSON.stringify(res));
              if (res.code === 0) {
                // 从列表中移除已删除的回复
                const index = this.replies.findIndex(item => item.id == this.currentMoreComment.id);
                if (index > -1) {
                  this.replies.splice(index, 1);
                }
                // 减少回复计数
                this.replyCount--;

                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });

                // 关闭弹窗
                this.showMorePopup = false;
              } else {
                uni.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('删除回复失败:', err);
              uni.showToast({
                title: '网络请求错误',
                icon: 'none'
              });
            });
          }
        }
      });
    },

    // 删除评论
    deleteComment() {
      console.log('删除评论ID:', this.commentId, '类型:', typeof this.commentId);
      commentApi.deleteComment(Number(this.commentId), {
        userId: this.userId
      }).then(res => {
        console.log('删除评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });

          // 删除成功后返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.message || '删除失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('删除评论失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },

    // 显示删除回复确认框
    showDeleteReplyConfirm(reply, index) {
      uni.showModal({
        title: '删除回复',
        content: '确认要删除这条回复吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: res => {
          if (res.confirm) {
            this.deleteReply(reply, index);
          }
        }
      });
    },

    // 删除回复
    deleteReply(reply, index) {
      console.log('删除回复:', reply, index);
      commentApi.deleteReply(Number(reply.id), {
        userId: this.userId
      }).then(res => {
        console.log('删除回复API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 从列表中移除已删除的回复
          this.replies.splice(index, 1);
          // 减少回复计数
          this.replyCount--;

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.message || '删除失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('删除回复失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },

    setScrollViewHeight() {
      const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 25;
      const inputBoxHeight = 110; // 底部输入框高度（rpx转px）
      const filterBarHeight = 100; // 筛选栏高度（rpx转px）

      // 修改计算方式，不再减去输入框高度，因为输入框现在是固定定位
      const scrollHeight = `calc(90vh - ${statusBarHeight}px - 20rpx)`;
      this.scrollViewHeight = scrollHeight;
      console.log('设置滚动视图高度:', scrollHeight);
    },
    goBack() {
      uni.navigateBack();
    },
    fetchCommentDetail() {
      this.loading = true;

      // 调用API获取评论详情
      commentApi.getCommentDetail(this.commentId, {
        userId: this.userId,
        sort: this.sortBy,
        current: 1,
        pageSize: 1 // 只获取评论信息，不包含回复
      }).then(res => {
        console.log('评论详情API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 设置评论详情
          const commentData = res.data.comment || {};
          console.log('原始评论数据:', JSON.stringify(commentData));

          // 转换字段名
          commentData.created_at = commentData.createdAt;
          commentData.is_liked = commentData.isLiked;
          commentData.reply_count = commentData.replyCount;

          // 处理用户头像为空的情况
          if (!commentData.user.avatar) {
            commentData.user.avatar = '/static/images/toux.png';
          }

          console.log('处理后的评论数据:', JSON.stringify(commentData));

          this.comment = commentData;
          this.replyCount = commentData.replyCount || 0;

          // 获取评论回复列表
          this.fetchRepliesOnly();

          // 设置loading为false，因为主评论已经加载完成
          this.loading = false;
        } else {
          uni.showToast({
            title: res.message || '获取评论详情失败',
            icon: 'none'
          });

          // 失败后返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      }).catch(err => {
        console.error('获取评论详情失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });

        // 失败后返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      });
    },
    fetchReplies() {
      if (this.page === 1) {
        // 第一页不显示加载更多
        this.loading = true;
      } else {
        this.loadingMore = true;
      }

      // 调用API获取回复列表
      commentApi.getCommentDetail(this.commentId, {
        userId: this.userId,
        sort: this.sortBy,
        current: this.page,
        pageSize: this.limit
      }).then(res => {
        console.log('评论回复API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          const data = res.data;

          // 获取回复列表，可能在replies.items或replies.records中
          const replyItems = data.replies.items || [];

          console.log('原始回复列表:', JSON.stringify(replyItems));
          if (replyItems.length === 0) {
            this.replies = [];
            console.log('replies.length=', this.replies.length)
            return;
          }

          // 转换回复列表中的字段名
          replyItems.forEach(reply => {
            reply.created_at = reply.createdAt;
            reply.is_liked = reply.isLiked;
            // 添加控制内容展开/收起的属性
            reply.showFullContent = false;

            // 处理replyTo字段
            if (reply.replyTo) {
              reply.reply_to = {
                id: reply.replyTo.id,
                nickname: reply.replyTo.nickname
              };
            } else {
              reply.reply_to = undefined;
            }

            // 处理用户头像为空的情况
            if (!reply.user.avatar) {
              reply.user.avatar = '/static/images/toux.png';
            }
          });

          console.log('处理后的回复列表:', JSON.stringify(replyItems));

          if (this.page === 1) {
            // 第一页直接替换
            this.replies = replyItems;
            this.replyCount = (data.replies && data.replies.total) || 0;
          } else {
            // 加载更多，追加数据
            this.replies = [...this.replies, ...replyItems];
          }

          // 是否还有更多数据
          this.hasMore = data.replies && data.replies.has_more;
          if (this.hasMore) {
            this.page++;
          }
        } else {
          uni.showToast({
            title: res.message || '获取回复列表失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('获取回复列表失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      }).finally(() => {
        this.loading = false;
        this.loadingMore = false;
        this.isRefreshing = false;
      });
    },
    // 懒加载更多回复（优化版）
    loadMoreReplies() {
      console.log('🔄 触发回复懒加载');

      // 防抖处理，避免重复请求
      if (this.pagination.loading || !this.pagination.hasMore) {
        console.log('⚠️ 回复正在加载或已无更多数据，跳过请求');
        return;
      }

      // 性能优化：增强防抖处理，减少低端设备的请求频率
      const now = Date.now();
      const lastRequestTime = this.lastRequestTime || 0;
      if (now - lastRequestTime < 600) { // 增加到600ms，减少低端设备的负担
        console.log('⚠️ 请求过于频繁，跳过回复懒加载');
        return;
      }
      this.lastRequestTime = now;

      // 设置加载状态
      this.pagination.loading = true;
      this.loadingText = '加载更多回复...';

      // 计算下一页页码
      const nextPage = this.pagination.page + 1;
      const startTime = this.getTimestamp();

      console.log(`📄 回复当前页码: ${this.pagination.page}, 请求页码: ${nextPage}`);

      // 使用正确的API方法获取回复分页数据
      // 修复：使用getCommentDetail而不是getCommentReplies，因为后者可能不存在
      const params = {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        sort: this.sortBy,
        current: nextPage,  // 使用current而不是page
        pageSize: this.pagination.pageSize
      };

      console.log('📋 回复分页请求参数:', JSON.stringify(params));

      // 调用API获取更多回复 - 修复数据类型为Long兼容
      commentApi.getCommentDetail(Number(this.commentId), params).then(res => {
        const endTime = this.getTimestamp();
        const loadTime = endTime - startTime;

        console.log(`✅ 回复分页API返回，耗时: ${loadTime.toFixed(2)}ms`);

        if (res.code === 0) {
          const data = res.data;

          // 性能优化：减少日志输出
          console.log('📊 回复分页数据概览:', {
            repliesCount: data.replies && data.replies.items ? data.replies.items.length : 0,
            total: data.replies ? data.replies.total : 0,
            hasMore: data.replies ? data.replies.hasMore : false
          });

          // 获取回复列表，处理不同的数据结构（优化版）
          let rawReplies = [];
          if (data.replies) {
            if (data.replies.items && Array.isArray(data.replies.items)) {
              rawReplies = data.replies.items;
            } else if (data.replies.records && Array.isArray(data.replies.records)) {
              rawReplies = data.replies.records;
            } else if (Array.isArray(data.replies)) {
              rawReplies = data.replies;
            }
          }

          const newReplies = this.processReplyDataOptimized(rawReplies);

          if (newReplies && newReplies.length > 0) {
            // 检查是否有重复数据（优化版）
            const existingIds = new Set(this.replies.map(reply => reply.id));
            const filteredReplies = newReplies.filter(reply => !existingIds.has(reply.id));

            console.log(`🔄 回复去重: 原始${newReplies.length}条，去重后${filteredReplies.length}条`);

            if (filteredReplies.length > 0) {
              // 追加新回复到列表（优化：使用concat）
              this.replies = this.replies.concat(filteredReplies);

              // 更新分页信息
              this.pagination.page = nextPage;

              console.log(`✅ 回复加载成功，页码: ${nextPage}，新增: ${filteredReplies.length}条`);
            }

            // 检查是否还有更多数据
            if (data.replies && data.replies.hasMore === false || newReplies.length < this.pagination.pageSize) {
              this.pagination.hasMore = false;
              console.log('🔚 回复已加载完毕');
            }
          } else {
            // 没有更多数据
            this.pagination.hasMore = false;
            console.log('🔚 回复无更多数据');
          }
        } else {
          // API返回错误
          console.error('❌ 回复API返回错误:', res.message);
          uni.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('❌ 回复懒加载失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      }).finally(() => {
        // 重置加载状态
        this.pagination.loading = false;
        this.loadingText = '加载中...';
        console.log('🔄 回复加载状态重置');
      });
    },
    onRefresh() {
      this.isRefreshing = true;

      // 重置分页状态
      this.pagination = {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      };

      // 下拉刷新时仅刷新回复列表
      this.fetchRepliesOnly();
      setTimeout(() => {
        this.isRefreshing = false;
      }, 500);
    },
    likeComment() {
      // 点赞/取消点赞主评论
      const action = this.comment.is_liked ? 'unlike' : 'like';

      commentApi.likeComment(Number(this.comment.id), {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        action
      }).then(res => {
        console.log('点赞主评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 更新评论点赞状态和数量
          this.comment.is_liked = res.data.isLiked || res.data.is_liked;
          this.comment.likes = res.data.likes;
        } else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('点赞操作失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    likeReply(reply, index) {
      // 点赞/取消点赞回复
      const action = reply.is_liked ? 'unlike' : 'like';

      commentApi.likeReply(Number(reply.id), {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        action
      }).then(res => {
        console.log('点赞回复API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 更新回复点赞状态和数量
          this.replies[index].is_liked = res.data.isLiked || res.data.is_liked;
          this.replies[index].likes = res.data.likes;
        } else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('点赞回复操作失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    replyToMain() {
      // 回复主评论
      // 先重置当前状态
      if (this.$refs.commentInput) {
        this.$refs.commentInput.autoFocus = false;
      }

      this.currentReplyTo = null;
      this.inputPlaceholder = '发表您的评论...';
      this.isReplying = true; // 设置为回复状态

      // 自动聚焦输入框 - 使用延时确保在DOM更新后执行
      setTimeout(() => {
        if (this.$refs.commentInput) {
          this.$refs.commentInput.focus();
        }
      }, 150);
    },
    replyToComment(reply) {
      // 回复某条回复
      // 先重置当前状态
      if (this.$refs.commentInput) {
        this.$refs.commentInput.autoFocus = false;
      }

      this.currentReplyTo = reply;
      this.isReplying = true; // 设置为回复状态
      console.log('replyToComment:', JSON.stringify(reply));
      console.log('currentReplyTo:', JSON.stringify(this.currentReplyTo));

      // 确保reply.user存在且能访问
      if (reply && reply.user) {
        console.log('回复用户信息:', JSON.stringify(reply.user));
        this.inputPlaceholder = `@ ${reply.user.nickname}:`;
      } else {
        console.log('回复用户信息不存在');
        this.inputPlaceholder = '回复评论...';
      }

      // 自动聚焦输入框 - 使用延时确保在DOM更新后执行
      setTimeout(() => {
        if (this.$refs.commentInput) {
          this.$refs.commentInput.focus();
        }
      }, 150);
    },
    sendReply() {
      if (this.replyText.length > 1000) {
        uni.showToast({
          title: '评论字数不能超过1000字',
          icon: 'none'
        });
        return;
      }

      if (!this.replyText.trim()) return;

      const replyData = {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        commentId: Number(this.commentId), // 使用Number确保Long兼容性
        content: this.replyText.trim(),
        replyToId: this.currentReplyTo ? Number(this.currentReplyTo.userId) : null // 使用Number确保Long兼容性
      };

      console.log('🚀 发送回复数据:', JSON.stringify(replyData));
      console.log('📊 回复数据类型检查:', {
        userId: typeof replyData.userId,
        userIdValue: replyData.userId,
        commentId: typeof replyData.commentId,
        commentIdValue: replyData.commentId,
        replyToId: typeof replyData.replyToId,
        replyToIdValue: replyData.replyToId,
        content: typeof replyData.content
      });

      commentApi.replyComment(replyData.commentId, replyData).then(res => {
        console.log('发送回复API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          uni.showToast({
            title: '回复成功',
            icon: 'success'
          });

          // 清空输入框
          if (this.$refs.commentInput) {
            this.$refs.commentInput.clear();
          } else {
            this.replyText = '';
          }

          // 重置回复状态
          this.currentReplyTo = null;
          this.inputPlaceholder = '发表您的评论...';
          this.isReplying = false;

          // 刷新回复列表 - 重置分页状态
          this.pagination = {
            page: 1,
            pageSize: 10,
            hasMore: true,
            loading: false
          };
          this.fetchRepliesOnly();
        } else {
          uni.showToast({
            title: res.message || '回复失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('发送回复失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    changeSort(type) {
      // 不再添加触觉反馈

      if (this.sortBy === type) return;
      this.sortBy = type;

      // 重置分页状态
      this.pagination = {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: true
      };

      // 切换排序方式，只重新加载回复列表，不刷新整个页面
      this.loading = false; // 确保不显示整页加载状态
      this.fetchRepliesOnly();
    },

    // 只获取回复列表，不重新加载整个页面
    fetchRepliesOnly() {
      console.log('🔄 开始获取回复列表，当前页码:', this.pagination.page);

      // 调用API获取回复列表 - 修复数据类型为Long兼容
      commentApi.getCommentDetail(Number(this.commentId), {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        sort: this.sortBy,
        current: this.pagination.page,  // 使用当前页码，不是page+1
        pageSize: this.pagination.pageSize
      }).then(res => {
        console.log('✅ 评论回复API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          const data = res.data;

          // 获取回复列表，处理不同的数据结构
          let replyItems = [];
          if (data.replies) {
            if (data.replies.items && Array.isArray(data.replies.items)) {
              replyItems = data.replies.items;
            } else if (data.replies.records && Array.isArray(data.replies.records)) {
              replyItems = data.replies.records;
            } else if (Array.isArray(data.replies)) {
              replyItems = data.replies;
            }
          }

          console.log('📊 原始回复列表数量:', replyItems.length);

          // 使用processReplyData处理回复数据
          const processedReplies = this.processReplyData(replyItems);

          if (this.pagination.page === 1) {
            // 第一页，直接替换
            this.replies = processedReplies;
            console.log('📝 第一页回复数据已替换，数量:', processedReplies.length);
          } else {
            // 后续页，追加到现有列表
            const existingIds = this.replies.map(reply => reply.id);
            const filteredReplies = processedReplies.filter(reply => !existingIds.includes(reply.id));
            this.replies = [...this.replies, ...filteredReplies];
            console.log('📝 后续页回复数据已追加，去重后数量:', filteredReplies.length);
          }

          // 更新回复总数
          this.replyCount = (data.replies && data.replies.total) || 0;

          // 检查是否还有更多数据
          if (processedReplies.length < this.pagination.pageSize) {
            this.pagination.hasMore = false;
            console.log('🔚 回复数据已加载完毕');
          } else {
            this.pagination.hasMore = true;
            console.log('📄 还有更多回复数据');
          }

          console.log(`✅ 回复列表加载成功，当前页:${this.pagination.page}，总数:${this.replies.length}条`);
        } else {
          console.error('❌ 获取回复列表API错误:', res.message);
          uni.showToast({
            title: res.message || '获取回复列表失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('❌ 获取回复列表失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      }).finally(() => {
        this.pagination.loading = false;
        console.log('🔄 回复列表加载状态重置');
      });
    },
    formatTime(timeString) {
      if (!timeString) return '';
      return dayjs(timeString).fromNow();
    },
    getLevelColor(level) {
      const colors = {
        0: '#cccbc8', // 灰色
        1: '#c6ffe6',
        2: '#61bc84', // 绿色
        3: '#4d648d',
        4: '#1F3A5F',
        5: '#9c27b0',
        6: '#6c35de',
        7: '#ffd299', // 橙色
        8: '#FF7F50', // 红色
        9: '#f35d74', // 紫色
        10: '#bb2649' // 粉色
      };
      return colors[level] || '#8dc63f';
    },
    // 切换评论内容的展开/收起状态
    toggleContent() {
      const wasExpanded = this.showFullContent;
      this.showFullContent = !this.showFullContent;

      // 如果是从展开状态收起，则滚动到主评论顶部
      if (wasExpanded) {
        this.scrollToMainComment();
      }
    },

    // 切换回复内容的展开/收起状态
    toggleReplyContent(reply, index) {
      const wasExpanded = reply.showFullContent;

      // Vue无法直接检测到通过索引设置的数组变化，需要使用Vue.set或$set方法
      if (!reply.showFullContent) {
        this.$set(reply, 'showFullContent', true);
      } else {
        this.$set(reply, 'showFullContent', false);
      }

      // 如果是从展开状态收起，则滚动到回复顶部
      if (wasExpanded) {
        this.scrollToReply(index);
      }
    },

    // 滚动到主评论顶部位置
    scrollToMainComment() {
      console.log('🎯 开始滚动到主评论');

      // 方法1: 使用scrollIntoView
      this.scrollToElementByScrollIntoView('main-comment');

      // 方法2: 备用方案
      setTimeout(() => {
        this.scrollToElementByScrollTop('main-comment');
      }, 100);
    },

    // 滚动到指定回复的顶部位置
    scrollToReply(index) {
      const replyId = `reply-${index}`;
      console.log(`🎯 开始滚动到回复 - ${replyId}`);

      // 方法1: 使用scrollIntoView
      this.scrollToElementByScrollIntoView(replyId);

      // 方法2: 备用方案
      setTimeout(() => {
        this.scrollToElementByScrollTop(replyId);
      }, 100);
    },

    // 方法1: 使用scrollIntoView滚动到元素
    scrollToElementByScrollIntoView(elementId) {
      console.log(`📍 使用scrollIntoView滚动到 - ${elementId}`);

      this.$nextTick(() => {
        setTimeout(() => {
          // 设置scroll-into-view属性
          this.scrollIntoView = elementId;

          // 清除scrollIntoView，避免影响后续滚动
          setTimeout(() => {
            this.scrollIntoView = '';
          }, 500);

          console.log(`✅ scrollIntoView设置成功 - ${elementId}`);
        }, 150);
      });
    },

    // 方法2: 使用scroll-top属性滚动到元素
    scrollToElementByScrollTop(elementId) {
      console.log(`📍 使用scroll-top滚动到 - ${elementId}`);

      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);

        query.select('.comment-container').boundingClientRect();
        query.select(`#${elementId}`).boundingClientRect();

        query.exec((res) => {
          console.log(`📊 scroll-top查询结果 - ${elementId}:`, res);

          if (res && res.length >= 2) {
            const scrollViewRect = res[0];
            const elementRect = res[1];

            if (scrollViewRect && elementRect) {
              const relativeTop = elementRect.top - scrollViewRect.top;
              const topOffset = elementId === 'main-comment' ? 60 : 80;
              const targetScrollTop = Math.max(0, relativeTop - topOffset);

              console.log(`📐 scroll-top计算 - ${elementId}:`, {
                scrollViewTop: scrollViewRect.top,
                elementTop: elementRect.top,
                relativeTop: relativeTop,
                targetScrollTop: targetScrollTop
              });

              // 强制更新scrollTop
              this.scrollTop = 0;
              this.$nextTick(() => {
                this.scrollTop = targetScrollTop;
                console.log(`✅ scroll-top设置成功 - ${elementId}, 位置: ${targetScrollTop}`);
              });
            }
          }
        });
      });
    },

    // 调试方法：检查DOM元素是否存在
    debugScrollElements(elementId = 'main-comment') {
      console.log(`🔍 调试滚动元素 - ${elementId}`);

      const query = uni.createSelectorQuery().in(this);
      query.select(`#${elementId}`).boundingClientRect();
      query.select('.comment-container').boundingClientRect();

      query.exec((res) => {
        console.log('🔍 调试结果:', {
          elementId: elementId,
          targetElement: res[0],
          scrollViewElement: res[1],
          hasTarget: !!res[0],
          hasScrollView: !!res[1]
        });
      });
    },

    // 处理回复数据
    processReplyData(replies) {
      if (!replies || !Array.isArray(replies)) {
        console.warn('回复数据为空或格式错误');
        return [];
      }

      console.log(`处理回复数据，回复数量:`, replies.length);

      return replies.map(reply => {
        if (!reply) return null;

        // 转换字段名
        reply.created_at = reply.createdAt || reply.created_at || new Date().toISOString();
        reply.is_liked = reply.isLiked || reply.is_liked || false;
        reply.likes = reply.likes || 0;

        // 确保user对象存在
        if (!reply.user) {
          reply.user = {
            id: 0,
            nickname: '未知用户',
            avatar: '/static/images/toux.png',
            level: 0
          };
        } else {
          // 处理用户头像为空的情况
          if (!reply.user.avatar) {
            reply.user.avatar = '/static/images/toux.png';
          }

          // 确保其他用户字段存在
          reply.user.nickname = reply.user.nickname || '未知用户';
          reply.user.level = reply.user.level || 0;
        }

        // 确保reply_to存在
        if (reply.replyTo) {
          reply.reply_to = reply.replyTo;
        }

        return reply;
      }).filter(reply => reply !== null);
    },

    // 优化的回复数据处理方法
    processReplyDataOptimized(replies) {
      const startTime = this.getTimestamp();

      if (!replies || !Array.isArray(replies)) {
        console.warn('⚠️ 回复数据为空或格式错误');
        return [];
      }

      console.log(`🔄 开始处理回复数据，数量: ${replies.length}`);

      const processedReplies = replies.map(reply => {
        if (!reply) return null;

        // 优化：减少对象创建和属性复制
        const processedReply = {
          ...reply,
          created_at: reply.createdAt || reply.created_at || new Date().toISOString(),
          is_liked: reply.isLiked || reply.is_liked || false,
          likes: reply.likes || 0,
          showFullContent: false
        };

        // 确保user对象存在
        if (!processedReply.user) {
          processedReply.user = {
            id: 0,
            nickname: '未知用户',
            avatar: '/static/images/toux.png',
            level: 0
          };
        } else {
          // 处理用户头像为空的情况
          if (!processedReply.user.avatar) {
            processedReply.user.avatar = '/static/images/toux.png';
          }
          processedReply.user.nickname = processedReply.user.nickname || '未知用户';
          processedReply.user.level = processedReply.user.level || 0;
        }

        // 确保reply_to存在
        if (reply.replyTo) {
          processedReply.reply_to = reply.replyTo;
        }

        return processedReply;
      }).filter(reply => reply !== null);

      const endTime = this.getTimestamp();
      console.log(`✅ 回复数据处理完成，耗时: ${(endTime - startTime).toFixed(2)}ms，处理数量: ${processedReplies.length}`);

      return processedReplies;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/pagesSub/styles/common.scss';

.comment-detail {
  display: flex;
  flex-direction: column;
  height: 94vh;
  background: $background-gradient;
}

.filter-bar {
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
}

.back-btn {
  padding: 10rpx;
}

.page-title {
  flex: 1;
  font-size: 30rpx; /* 优化：从34rpx减小到30rpx，更符合移动端标准 */
  font-weight: 600;
  text-align: center;
  margin-right: 44rpx;
  /* 为了居中，右侧留出与左侧返回按钮相同的空间 */
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  letter-spacing: 0.5rpx;

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

.comment-container {
  flex: 1;
  position: relative;
  padding: 24rpx;
  width: auto;
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;

  text {
    margin-top: 32rpx;
    font-size: 30rpx;
    color: #ff6b87;
    font-weight: 500;
    letter-spacing: 0.5rpx;
  }
}

.main-comment {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 6rpx 24rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 优化过渡效果，只对transform进行过渡 */
  transition: transform 0.2s ease;
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;

  .user-info {
    display: flex;
    margin-bottom: 24rpx;

    .avatar-wrap {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 28rpx;
      border: 3rpx solid rgba(255, 107, 135, 0.2);
      box-shadow: 0 6rpx 20rpx rgba(255, 107, 135, 0.15);
      /* 优化过渡效果，只对transform进行过渡 */
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .avatar {
        width: 100%;
        height: 100%;
      }
    }

    .user-meta {
      flex: 1;

      .nickname {
        font-size: 28rpx; /* 优化：从32rpx减小到28rpx，用户名更精致 */
        font-weight: 600;
        /* 备用颜色方案，确保在微信小程序中显示 */
        color: #4a4a4a;
        background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
        display: flex;
        align-items: center;
        letter-spacing: 0.3rpx;

        /* 渐变文字效果（如果支持） */
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        /* 微信小程序兼容性处理 */
        @supports not (-webkit-background-clip: text) {
          color: #4a4a4a !important;
          background: none;
        }

        .user-level {
          font-size: 22rpx;
          color: #ffffff !important;
          background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
          border-radius: 16rpx;
          padding: 6rpx 16rpx;
          margin-left: 16rpx;
          font-weight: 600;
          box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
          letter-spacing: 0.5rpx;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
          /* 确保文字显示，移除可能导致兼容性问题的属性 */
          -webkit-text-fill-color: initial;
          -webkit-background-clip: initial;
          background-clip: initial;
        }
      }

      .time {
        font-size: 26rpx;
        color: #8a8a8a;
        margin-top: 12rpx;
        font-weight: 400;
        opacity: 0.8;
      }
    }

    .like-btn {
      display: flex;
      align-items: center;
      padding: 12rpx 20rpx;
      border-radius: 32rpx;
      background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
      border: 1rpx solid rgba(255, 107, 135, 0.2);
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);
      align-self: flex-start;
      margin-top: 8rpx;

      &:active {
        background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);
        transform: scale(0.95);
        box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);
      }

      text {
        font-size: 26rpx;
        color: #ff6b87;
        margin-left: 10rpx;
        font-weight: 600;
        letter-spacing: 0.3rpx;
      }

      /* 添加心形图标的样式 */
      :deep(.u-icon__icon) {

        &.uicon-heart-fill,
        &.uicon-heart {
          font-weight: bold;
          transform: scale(1.1);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        &.uicon-heart-fill {
          /* 移除复杂的心跳动画和阴影效果，提升性能 */
          color: #f56c6c;
        }
      }
    }
  }

  .content {
    font-size: 30rpx; /* 优化：从32rpx减小到30rpx，正文更符合移动端标准 */
    line-height: 1.8;
    color: #4a4a4a;
    margin-bottom: 28rpx;
    word-break: break-all;
    font-weight: 400;
    letter-spacing: 0.3rpx;

    .expand-btn {
      color: #ff6b87;
      font-size: 28rpx;
      display: inline-block;
      font-weight: 600;
      padding: 6rpx 12rpx;
      border-radius: 16rpx;
      background: rgba(255, 107, 135, 0.1);
      transition: all 0.3s ease;
      letter-spacing: 0.3rpx;

      &:active {
        background: rgba(255, 107, 135, 0.2);
        transform: scale(0.95);
      }
    }
  }

  .actions {
    display: flex;
    margin-top: 24rpx;

    .reply-btn,
    .delete-btn {
      display: flex;
      align-items: center;
      margin-right: 32rpx;
      padding: 12rpx 20rpx;
      border-radius: 28rpx;
      background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
      border: 1rpx solid rgba(255, 107, 135, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);

      &:active {
        background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
        transform: scale(0.95);
      }

      text {
        font-size: 26rpx;
        color: #ff6b87;
        margin-left: 10rpx;
        font-weight: 600;
        letter-spacing: 0.3rpx;
      }

      /* 添加图标的样式 */
      :deep(.u-icon__icon) {

        &.uicon-message-circle,
        &.uicon-trash {
          transform: scale(1.1);
          transition: all 0.3s ease;
          color: #ff6b87;
        }
      }
    }
  }
}

.replies-container {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 6rpx 24rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
  position: relative;
  overflow: hidden;

  .replies-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    padding-bottom: 24rpx;
    border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);

    text {
      font-size: 28rpx; /* 优化：从32rpx减小到28rpx，回复标题更精致 */
      font-weight: 600;
      /* 备用颜色方案，确保在微信小程序中显示 */
      color: #ff6b87;
      background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
      letter-spacing: 0.5rpx;

      /* 渐变文字效果（如果支持） */
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      /* 微信小程序兼容性处理 */
      @supports not (-webkit-background-clip: text) {
        color: #ff6b87 !important;
        background: none;
      }
    }

    .sort-options {
      .van-tabs {
        position: relative;
        display: flex;
        justify-content: flex-end;
        -webkit-tap-highlight-color: transparent; // 移除整个标签区域的点击高亮效果

        &__wrap {
          overflow: hidden;
          position: relative;
          padding: 0;
        }

        &__nav {
          position: relative;
          display: flex;
          background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
          backdrop-filter: blur(20rpx);
          height: 72rpx;
          border-radius: 36rpx;
          user-select: none;
          box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
          padding: 6rpx;
          border: 1rpx solid rgba(255, 255, 255, 0.8);
        }
      }

      .van-tab {
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 26rpx;
        min-width: 80rpx;
        margin: 0 2rpx;
        -webkit-tap-highlight-color: transparent;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &__text {
          font-size: 24rpx;
          color: #8a8a8a;
          line-height: 1.2;
          padding: 0 16rpx;
          font-weight: 500;
          transition: all 0.3s ease;
          letter-spacing: 0.3rpx;
        }

        &--active {
          background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
          box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
          transform: translateY(-2rpx) scale(1.02);

          .van-tab__text {
            color: #ffffff;
            font-weight: 600;
            text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
            transform: scale(1.05);
          }
        }
      }
    }
  }
}

.reply-list {
  .empty-image {
    width: 462rpx;
    height: 256rpx;
    margin: 0 auto;
  }

  .empty-replies {
    padding: 80rpx 0;
    text-align: center;

    .empty-text {
      font-size: 32rpx; /* 优化：从36rpx减小到32rpx，空状态文字更合理 */
      /* 备用颜色方案，确保在微信小程序中显示 */
      color: #ff6b87;
      background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
      font-weight: 600;
      margin-top: 24rpx;
      letter-spacing: 0.5rpx;

      /* 渐变文字效果（如果支持） */
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      /* 微信小程序兼容性处理 */
      @supports not (-webkit-background-clip: text) {
        color: #ff6b87 !important;
        background: none;
      }
    }

    .empty-subtext {
      font-size: 28rpx;
      color: #8a8a8a;
      margin-top: 16rpx;
      font-weight: 400;
      opacity: 0.8;
    }
  }

  .reply-item {
    padding: 28rpx 0;
    border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    .reply-user-info {
      display: flex;
      margin-bottom: 20rpx;

      .reply-avatar {
        width: 72rpx;
        height: 72rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        border: 2rpx solid rgba(255, 107, 135, 0.2);
        box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }
      }

      .reply-user-meta {
        flex: 1;

        .reply-nickname {
          font-size: 26rpx; /* 优化：从28rpx减小到26rpx，回复用户名更精致 */
          font-weight: 600;
          /* 备用颜色方案，确保在微信小程序中显示 */
          color: #4a4a4a;
          background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          letter-spacing: 0.3rpx;

          /* 渐变文字效果（如果支持） */
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;

          /* 微信小程序兼容性处理 */
          @supports not (-webkit-background-clip: text) {
            color: #4a4a4a !important;
            background: none;
          }

          .reply-text {
            color: #8a8a8a;
            font-weight: normal;
            margin: 0 10rpx;
            font-size: 26rpx;
          }

          .reply-to {
            /* 备用颜色方案，确保在微信小程序中显示 */
            color: #ff6b87 !important;
            font-weight: 600;
            background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);

            /* 渐变文字效果（如果支持） */
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            /* 微信小程序兼容性处理 */
            @supports not (-webkit-background-clip: text) {
              color: #ff6b87 !important;
              background: none;
            }
          }
        }

        .reply-time {
          font-size: 24rpx;
          color: #8a8a8a;
          margin-top: 12rpx;
          font-weight: 400;
          opacity: 0.8;
        }
      }

      .reply-like {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        border-radius: 24rpx;
        background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
        border: 1rpx solid rgba(255, 107, 135, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        align-self: flex-start;
        margin-top: 8rpx;
        box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);

        &:active {
          background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);
          transform: scale(0.95);
          box-shadow: 0 1rpx 4rpx rgba(255, 107, 135, 0.15);
        }

        text {
          font-size: 24rpx;
          color: #ff6b87;
          margin-left: 8rpx;
          font-weight: 600;
          letter-spacing: 0.3rpx;
        }

        /* 添加心形图标的样式 */
        :deep(.u-icon__icon) {

          &.uicon-heart-fill,
          &.uicon-heart {
            font-weight: bold;
            transform: scale(1.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }

          &.uicon-heart-fill {
            /* 移除复杂的心跳动画和阴影效果，提升性能 */
            color: #f56c6c;
          }
        }
      }
    }

    .reply-content {
      font-size: 28rpx; /* 优化：从30rpx减小到28rpx，回复内容更精致 */
      line-height: 1.8;
      color: #4a4a4a;
      margin-bottom: 16rpx;
      padding-left: 96rpx;
      word-break: break-all;
      cursor: pointer;
      font-weight: 400;
      letter-spacing: 0.3rpx;

      /* 添加触摸反馈效果 */
      &:active {
        background-color: rgba(255, 107, 135, 0.05);
        border-radius: 12rpx;
      }

      text {
        display: block;
      }

      .expand-btn {
        color: #ff6b87;
        font-size: 26rpx;
        display: inline-block;
        font-weight: 600;
        margin-top: 12rpx;
        padding: 6rpx 12rpx;
        border-radius: 16rpx;
        background: rgba(255, 107, 135, 0.1);
        transition: all 0.3s ease;
        letter-spacing: 0.3rpx;

        &:active {
          background: rgba(255, 107, 135, 0.2);
          transform: scale(0.95);
        }
      }
    }

    .reply-actions {
      display: flex;
      margin-top: 10rpx;
      justify-content: space-between;

      .reply-reply {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  margin-left: 84rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);


        image {
          width: 28rpx;
          height: 28rpx;
        }

  &:active {
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
    transform: scale(0.95);
  }

        text {
          font-size: 22rpx;
          color: #667eea;
          margin-left: 8rpx;
          font-weight: 500;
        }

        /* 添加回复图标的样式 */
        :deep(.u-icon__icon) {
          &.uicon-message-circle {
            transform: scale(1.1);
            transition: all 0.3s ease;
          }
        }
      }

      // .reply-delete {
      //   display: flex;
      //   align-items: center;
      //   justify-content: flex-end;
      //   margin-left: 20rpx;
      //   padding: 4rpx 10rpx;
      // }
      .more-btn {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);

  &:active {
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
    transform: scale(0.9);
  }

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 48rpx 0;

    text {
      margin-left: 24rpx;
      font-size: 30rpx;
      color: #ff6b87;
      font-weight: 500;
      letter-spacing: 0.5rpx;
    }
  }

  .no-more {
    text-align: center;
    padding: 48rpx 0;

    text {
      font-size: 30rpx;
      color: #b0b0b0;
      font-weight: 500;
      letter-spacing: 0.5rpx;
    }
  }
}

.input-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 -8rpx 32rpx rgba(255, 105, 135, 0.08);
  border-top: 1rpx solid rgba(255, 105, 135, 0.1);
  transition: bottom 0.3s ease-in-out;
}

/* 蒙版层样式 */
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  opacity: 0;
  animation: maskFadeIn 0.3s ease-out forwards;
}

/* 更多操作弹窗样式 - 小红书风格 */
.action-popup {
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  backdrop-filter: blur(20rpx);
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  padding: 32rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  margin: 0 24rpx;
  background: #ffffff;
  transition: all 0.3s ease;

  &:active {
    background: #f8fafc;
    transform: scale(0.98);
  }

  .action-icon {
    width: 44rpx;
    height: 44rpx;
    margin: 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 100%;
      height: 100%;
    }
  }

  /* 第一个action-item的样式 */
  &.reply {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
  }

  /* 第二个action-item的样式 */
  &.copy {
    margin-bottom: 24rpx;
    border-bottom-left-radius: 24rpx;
    border-bottom-right-radius: 24rpx;
  }

  /* 第三个action-item的样式 */
  &.report {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
  }

  /* 最后一个action-item的样式 */
  &.block {
    border-bottom-left-radius: 24rpx;
    border-bottom-right-radius: 24rpx;
  }

  text {
    font-size: 28rpx;
    color: #334155;
    font-weight: 500;
  }
}

.action-cancel {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-top: 20rpx;
  border-radius: 20rpx;

  text {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  &:active {
    background-color: #f5f5f5;
  }
}

/* 加载更多状态样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  .loading-text {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #667eea;
  }
}

/* 回复骨架屏加载状态样式 */
.loading-more-skeleton {
  padding: 0 32rpx;

  /* 骨架屏淡入动画 */
  animation: replySkeletonFadeIn 0.3s ease-out;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  text {
    font-size: 28rpx;
    color: #94a3b8;
  }
}

/* 蒙版层淡入动画 */
@keyframes maskFadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/* 小红书风格动画效果 */
@keyframes heartBeat {
  0% {
    transform: scale(1.2);
  }

  14% {
    transform: scale(1.4);
  }

  28% {
    transform: scale(1.2);
  }

  42% {
    transform: scale(1.4);
  }

  70% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1.2);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* 性能优化：移除入场动画，避免滚动时重复触发导致卡顿 */

/* 渐变文字效果 - 微信小程序兼容版本 */
.gradient-text {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

/* 性能优化：移除消耗性能的毛玻璃效果 */
.glass-effect {
  /* backdrop-filter: blur(20rpx); */
  /* -webkit-backdrop-filter: blur(20rpx); */
  background: rgba(255, 255, 255, 0.9);
}

/* 悬浮阴影效果 */
.floating-shadow {
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);
  transition: box-shadow 0.3s ease;
}

.floating-shadow:hover {
  box-shadow: 0 12rpx 48rpx rgba(255, 105, 135, 0.2);
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .main-comment {
    padding: 32rpx;
  }

  .replies-container {
    padding: 32rpx;
  }

  .reply-content {
    font-size: 26rpx; /* 小屏幕优化：进一步减小回复内容字体 */
  }
}

/* 回复骨架屏动画 */
@keyframes replySkeletonFadeIn {
  from {
    opacity: 0;
    transform: translateY(15rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>