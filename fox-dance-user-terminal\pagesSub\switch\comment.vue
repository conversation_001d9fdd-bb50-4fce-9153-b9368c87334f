<template>
  <view class="comment-page">
    <!-- 整页滚动容器 -->
    <scroll-view
      ref="commentScrollView"
      scroll-y
      class="page-scroll-view"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
      @scrolltolower="loadMoreComments"
      :lower-threshold="100"
      :style="{ height: pageHeight }"
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      :scroll-with-animation="true">

      <!-- 话题图片轮播 -->
      <view class="topic-images-container" v-if="topicInfo && topicInfo.topicImages && topicInfo.topicImages.length > 0">
        <swiper
          class="topic-images-swiper"
          :indicator-dots="topicInfo.topicImages.length > 1"
          :autoplay="false"
          :circular="true"
          indicator-color="rgba(255, 255, 255, 0.5)"
          indicator-active-color="#ffffff"
        >
        <swiper-item v-for="(image, index) in topicInfo.topicImages" :key="index">
          <image
            class="topic-image"
            :src="processImageUrl(image)"
            mode="aspectFill"
            @error="handleTopicImageError(index)"
            :lazy-load="true"
            @tap="previewTopicImage(index)"
          ></image>
        </swiper-item>
      </swiper>
    </view>

    <!-- 话题信息区域 -->
    <view class="topic-info-section" v-if="topicInfo">
      <view class="topic-header">
        <view class="topic-title">{{ topicInfo.title }}</view>
        <view class="topic-desc" v-if="topicInfo.description">{{ topicInfo.description }}</view>
        <view class="topic-meta">
          <text class="participants">{{ topicInfo.commentUserCount || 0 }}人参与此话题</text>
          <text class="create-time" v-if="topicInfo.createTime">· {{ formatTime(topicInfo.createTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 店铺信息区域 -->
    <view class="store-info-section" v-if="storeInfo">
      <view class="store-header">
        <view class="store-icon">
          <image :src="storeInfo.storeImage" class="icon" mode="aspectFit"></image>
        </view>
        <view class="store-content">
          <view class="store-title">{{ storeInfo.title }}</view>
          <view class="store-desc" v-if="storeInfo.description">{{ storeInfo.description }}</view>
        </view>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <!-- 左侧评论数量 -->
      <view class="comment-count-section">
        <text class="comment-count-text">评论({{ getCurrentFilterTotal() }})</text>
      </view>

      <!-- 右侧筛选标签 -->
      <view class="filter-tabs-section">
        <view class="van-tabs">
          <view class="van-tabs__wrap">
            <view class="van-tabs__nav">
              <view class="van-tab" :class="{ 'van-tab--active': activeFilter === 'hot' }" @tap="changeFilter('hot')">
                <view class="van-tab__text">最热</view>
              </view>
              <view class="van-tab" :class="{ 'van-tab--active': activeFilter === 'new' }" @tap="changeFilter('new')">
                <view class="van-tab__text">最新</view>
              </view>
              <view class="van-tab" :class="{ 'van-tab--active': activeFilter === 'my' }" @tap="changeFilter('my')">
                <view class="van-tab__text">我的</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 评论列表容器 -->
    <view class="comment-list-container">
      <!-- 当前激活的评论列表 -->
      <view class="comment-list" v-if="activeFilter === 'hot'">
          <view v-if="loading && activeFilter === 'hot'" class="loading">
            <u-loading mode="flower" size="50" color="#667eea"></u-loading>
            <view class="loading-text">正在加载热门评论...</view>
          </view>
          <view v-else-if="commentListHot.length == 0" class="empty-tip">
            <image src="/static/icon/null.png" mode="" class="empty-image"></image>
            <view class="empty-text">暂无热门评论</view>
            <view class="empty-subtext">快来发表第一条评论吧~</view>
            <view class="empty-action" @tap="focusInput">
              <text>立即评论</text>
            </view>
          </view>
          <block v-else>
            <view class="comment-item" v-for="(item, index) in commentListHot" :key="index" :id="`comment-hot-${index}`" @tap="goToDetail(item)">
              <view class="user-avatar">
                <image
                  :src="processImageUrl(item.user.avatar)"
                  mode="aspectFill"
                  :lazy-load="true"
                  @error="handleImageLoadError(item.user.avatar, '用户头像')"
                ></image>
              </view>
              <view class="comment-content">
                <view class="user-info-row">
                  <view class="user-info">
                    <view class="user-name">
                      {{ item.user.nickname }}
                      <view v-if="item.user.level >= 0" class="user-level"
                        :style="{ backgroundColor: getLevelColor(item.user.level) }">Lv.{{ item.user.level }}</view>
                    </view>
                    <view class="time">{{ formatTime(item.created_at) }}</view>
                  </view>
                  <view class="like-btn" @tap.stop="likeComment(item, index, 'hot')">
                    <u-icon :name="item.is_liked ? 'heart-fill' : 'heart'" :color="item.is_liked ? '#f56c6c' : '#999'"
                      size="28"></u-icon>
                    <text>{{ item.likes }}</text>
                  </view>
                </view>
                <view class="text">
                  <text>{{ item.showFullContent ? item.content : (item.content.length > 100 ? item.content.slice(0, 100) +
                    '...' : item.content) }}</text>
                  <view v-if="item.content.length > 100" class="expand-btn" @tap.stop="toggleContent(item, index, 'hot')">
                    {{ item.showFullContent ? '收起' : '展开' }}
                  </view>
                </view>
                <view class="actions">
                  <view class="reply-btn" @tap.stop="replyComment(item)">
                    <image src="/static/icon/chat.png" mode="aspectFill"></image>
                    <text>回复</text>
                  </view>
                  <view class="more-btn" @tap.stop="showMoreOptions(item)">
                    <image src="/static/icon/more.png" mode="aspectFill"></image>
                  </view>
                </view>
                <view v-if="item.replies && item.replies.length > 0" class="reply-preview">
                  <view class="reply-item" v-for="(reply, rIndex) in item.replies.slice(0, 2)" :key="rIndex">
                    <text class="reply-nickname">{{ reply.user && reply.user.nickname }}</text>
                    <text v-if="reply.reply_to" class="reply-to">@{{ reply.reply_to.nickname }}</text>
                    <text class="reply-content">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>
                  </view>
                  <view v-if="item.reply_count > 2" class="view-more" @tap.stop="goToDetail(item)">
                    查看全部{{ item.reply_count }}条回复 >
                  </view>
                </view>
              </view>
            </view>

            <!-- 加载更多状态 - 使用骨架屏优化 -->
            <view v-if="pagination.hot.loading" class="loading-more-skeleton">
              <!-- 显示骨架屏而不是loading图标 -->
              <comment-skeleton v-for="n in 3" :key="n"></comment-skeleton>
            </view>
            <view v-else-if="!pagination.hot.hasMore && commentListHot.length > 0" class="no-more">
              <text>没有更多评论了</text>
            </view>

            <!-- 底部留白区域 -->
            <view class="bottom-space"></view>
        </block>
      </view>

      <!-- 最新评论列表 -->
      <view class="comment-list" v-if="activeFilter === 'new'">
        <view v-if="loading && activeFilter === 'new'" class="loading">
          <u-loading mode="flower" size="50" color="#ff6b87"></u-loading>
          <view class="loading-text">正在加载最新评论...</view>
        </view>
          <view v-else-if="commentListNew.length == 0" class="empty-tip">
            <image src="/static/icon/null.png" mode="" class="empty-image"></image>
            <view class="empty-text">暂无最新评论</view>
            <view class="empty-subtext">快来发表第一条评论吧~</view>
            <view class="empty-action" @tap="focusInput">
              <text>立即评论</text>
            </view>
          </view>
          <block v-else>
            <view class="comment-item" v-for="(item, index) in commentListNew" :key="index" :id="`comment-new-${index}`" @tap="goToDetail(item)">
              <view class="user-avatar">
                <image
                  :src="processImageUrl(item.user.avatar)"
                  mode="aspectFill"
                  :lazy-load="true"
                  @error="handleImageLoadError(item.user.avatar, '用户头像')"
                ></image>
              </view>
              <view class="comment-content">
                <view class="user-info-row">
                  <view class="user-info">
                    <view class="user-name">
                      {{ item.user.nickname }}
                      <view v-if="item.user.level >= 0" class="user-level"
                        :style="{ backgroundColor: getLevelColor(item.user.level) }">Lv{{ item.user.level }}</view>
                    </view>
                    <view class="time">{{ formatTime(item.created_at) }}</view>
                  </view>
                  <view class="like-btn" @tap.stop="likeComment(item, index, 'new')">
                    <u-icon :name="item.is_liked ? 'heart-fill' : 'heart'" :color="item.is_liked ? '#f56c6c' : '#999'"
                      size="28"></u-icon>
                    <text>{{ item.likes }}</text>
                  </view>
                </view>
                <view class="text">
                  <text>{{ item.showFullContent ? item.content : (item.content.length > 100 ? item.content.slice(0, 100) +
                    '...' : item.content) }}</text>
                  <view v-if="item.content.length > 100" class="expand-btn" @tap.stop="toggleContent(item, index, 'new')">
                    {{ item.showFullContent ? '收起' : '展开' }}
                  </view>
                </view>
                <view class="actions">
                  <view class="reply-btn" @tap.stop="replyComment(item)">
                    <image src="/static/icon/chat.png" mode="aspectFill"></image>
                    <text>回复</text>
                  </view>
                  <view class="more-btn" @tap.stop="showMoreOptions(item)">
                    <image src="/static/icon/more.png" mode="aspectFill"></image>
                  </view>
                </view>
                <view v-if="item.replies && item.replies.length > 0" class="reply-preview">
                  <view class="reply-item" v-for="(reply, rIndex) in item.replies.slice(0, 2)" :key="rIndex">
                    <text class="reply-nickname">{{ reply.user && reply.user.nickname }}</text>
                    <text v-if="reply.reply_to" class="reply-to">@{{ reply.reply_to.nickname }}</text>
                    <text class="reply-content">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>
                  </view>
                  <view v-if="item.reply_count > 2" class="view-more" @tap.stop="goToDetail(item)">
                    查看全部{{ item.reply_count }}条回复 >
                  </view>
                </view>
              </view>
            </view>

            <!-- 加载更多状态 - 使用骨架屏优化 -->
            <view v-if="pagination.new.loading" class="loading-more-skeleton">
              <!-- 显示骨架屏而不是loading图标 -->
              <comment-skeleton v-for="n in 3" :key="n"></comment-skeleton>
            </view>
            <view v-else-if="!pagination.new.hasMore && commentListNew.length > 0" class="no-more">
              <text>没有更多评论了</text>
            </view>

        </block>
      </view>

      <!-- 我的评论列表 -->
      <view class="comment-list" v-if="activeFilter === 'my'">
        <view v-if="loading && activeFilter === 'my'" class="loading">
          <u-loading mode="flower" size="50" color="#ff6b87"></u-loading>
          <view class="loading-text">正在加载我的评论...</view>
        </view>
          <view v-else-if="commentListMy.length == 0 || commentListMy === null" class="empty-tip">
            <image src="/static/icon/null.png" mode="" class="empty-image"></image>
            <view class="empty-text">您还没有发表过评论</view>
            <view class="empty-subtext">快来参与互动吧~</view>
            <view class="empty-action" @tap="focusInput">
              <text>立即评论</text>
            </view>
          </view>
          <block v-else>
            <view class="comment-item" v-for="(item, index) in commentListMy" :key="index" :id="`comment-my-${index}`" @tap="goToDetail(item)">
              <view class="user-avatar">
                <image
                  :src="processImageUrl(item.user.avatar)"
                  mode="aspectFill"
                  :lazy-load="true"
                  @error="handleImageLoadError(item.user.avatar, '用户头像')"
                ></image>
              </view>
              <view class="comment-content">
                <view class="user-info-row">
                  <view class="user-info">
                    <view class="user-name">
                      {{ item.user.nickname }}
                      <view v-if="item.user.level >= 0" class="user-level"
                        :style="{ backgroundColor: getLevelColor(item.user.level) }">Lv{{ item.user.level }}</view>
                    </view>
                    <view class="time">{{ formatTime(item.created_at) }}</view>
                  </view>
                  <view class="like-btn" @tap.stop="likeComment(item, index, 'my')">
                    <u-icon :name="item.is_liked ? 'heart-fill' : 'heart'" :color="item.is_liked ? '#f56c6c' : '#999'"
                      size="28"></u-icon>
                    <text>{{ item.likes }}</text>
                  </view>
                </view>
                <view class="text">
                  <text>{{ item.showFullContent ? item.content : (item.content.length > 100 ? item.content.slice(0, 100) +
                    '...' : item.content) }}</text>
                  <view v-if="item.content.length > 100" class="expand-btn" @tap.stop="toggleContent(item, index, 'my')">
                    {{ item.showFullContent ? '收起' : '展开' }}
                  </view>
                </view>
                <view class="actions">
                  <view class="reply-btn" @tap.stop="replyComment(item)">
                    <image src="/static/icon/chat.png" mode="aspectFill"></image>
                    <text>回复</text>
                  </view>
                  <view class="more-btn" @tap.stop="showMoreOptions(item)">
                    <image src="/static/icon/more.png" mode="aspectFill"></image>
                  </view>
                </view>
                <view v-if="item.replies && item.replies.length > 0" class="reply-preview">
                  <view class="reply-item" v-for="(reply, rIndex) in item.replies.slice(0, 2)" :key="rIndex">
                    <text class="reply-nickname">{{ reply.user && reply.user.nickname }}</text>
                    <text v-if="reply.reply_to" class="reply-to">@{{ reply.reply_to.nickname }}</text>
                    <text class="reply-content">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>
                  </view>
                  <view v-if="item.reply_count > 2" class="view-more" @tap.stop="goToDetail(item)">
                    查看全部{{ item.reply_count }}条回复 >
                  </view>
                </view>
              </view>
            </view>

            <!-- 加载更多状态 - 使用骨架屏优化 -->
            <view v-if="pagination.my.loading" class="loading-more-skeleton">
              <!-- 显示骨架屏而不是loading图标 -->
              <comment-skeleton v-for="n in 3" :key="n"></comment-skeleton>
            </view>
            <view v-else-if="!pagination.my.hasMore && commentListMy.length > 0" class="no-more">
              <text>没有更多评论了</text>
            </view>

        </block>
      </view>

      <!-- 底部留白区域 -->
      <view class="bottom-space"></view>
    </view>
    </scroll-view>

    <!-- 蒙版层 -->
    <view v-if="isKeyboardShow" class="mask-layer" @tap="hideMaskAndKeyboard"></view>

    <!-- 底部输入框 - 支持回复模式 -->
    <view class="input-container" :style="{ bottom: inputContainerBottom + 'px' }">
      <!-- 回复状态指示器 -->
      <view v-if="isReplyMode" class="reply-indicator">
        <view class="reply-info">
          <text class="reply-text">回复 @{{ currentReply && currentReply.user && currentReply.user.nickname ? currentReply.user.nickname : '用户' }}</text>
          <view class="cancel-reply-btn" @tap="cancelReplyMode">
            <text>✕</text>
          </view>
        </view>
      </view>

      <comment-input v-model="commentText" :placeholder="inputPlaceholder" :use-image-button="true" @send="sendComment"
        ref="mainCommentInput" @focus="onInputFocus" @blur="onInputBlur" />
    </view>

    <!-- 回复弹窗已移除，改为使用底部主输入框进行回复 -->

    <!-- 更多操作弹窗 -->
    <u-popup v-model="showMorePopup" mode="bottom" border-radius="30">
      <view class="action-popup">
        <view class="action-item reply" @tap="replyFromMore">
          <view class="action-icon">
            <image src="/static/icon/chat-1.png" mode="aspectFill"></image>
          </view>
          <text>回复</text>
        </view>
        <view class="action-item copy" @tap="copyComment">
          <view class="action-icon">
            <image src="/static/icon/copy.png" mode="aspectFill"></image>
          </view>
          <text>复制</text>
        </view>
        <view v-if="isCommentOwner(currentMoreComment)" class="action-item report block" @tap="deleteComment">
          <view class="action-icon">
            <image src="/static/icon/delete.png" mode="aspectFill"></image>
          </view>
          <text>删除</text>
        </view>
      </view>
    </u-popup>

  </view>
</template>

<script>
import commentApi from '@/config/comment.api.js'
import topicApi from '@/config/topic.api.js'
import { getStoreById } from '@/config/store'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import CommentInput from '../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue'
import CommentSkeleton from './components/CommentSkeleton.vue'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export default {
  components: {
    CommentInput,
    CommentSkeleton
  },
  data() {
    return {
      activeFilter: 'hot',
      commentList: [],
      commentText: '',
      loading: true,
      loadingMore: false, // 加载更多状态
      isRefreshing: false,
      page: 1,
      limit: 10,
      hasMore: true,
      showMorePopup: false, // 更多操作弹窗
      currentMoreComment: null, // 当前操作的评论
      // 回复状态管理
      isReplyMode: false, // 是否处于回复模式
      currentReply: null, // 当前被回复的评论
      inputPlaceholder: '说点什么...', // 输入框提示文字
      pageHeight: 'calc(100vh - 120rpx)', // 页面滚动容器高度
      contentId: '', // 内容ID
      contentType: '', // 内容类型
      topicId: '', // 话题ID
      storeId: '', // 店铺ID
      storeName: '', // 店铺名称
      storeImage: '',
      userId: '', // 用户ID
      topicInfo: null, // 话题信息（包含图片）
      storeInfo: null, // 店铺信息
      keyboardHeight: 0, // 键盘高度
      inputContainerBottom: 0, // 输入框容器底部距离
      isKeyboardShow: false, // 键盘是否显示
      totalComments: 0, // 评论总数
      // 各筛选条件的真实总数
      commentStats: {
        hotTotal: 0,    // 热门评论总数
        newTotal: 0,    // 最新评论总数
        myTotal: 0      // 我的评论总数
      },
      commentListHot: [],
      commentListNew: [],
      commentListMy: [],
      // 分页相关数据
      pagination: {
        hot: { page: 1, pageSize: 10, hasMore: true, loading: false },
        new: { page: 1, pageSize: 10, hasMore: true, loading: false },
        my: { page: 1, pageSize: 10, hasMore: true, loading: false }
      },
      isLoadingMore: false, // 是否正在加载更多
      loadingText: '加载中...', // 加载提示文本
      scrollTop: 0, // scroll-view的滚动位置
      scrollIntoView: '' // scroll-view的滚动到指定元素
    }
  },
  onLoad(options) {
    // 获取页面参数
    this.contentId = options.content_id || '';
    this.contentType = options.content_type || '';

    // 修复：将topicId转换为Long类型（使用Number确保兼容性）
    this.topicId = options.topicId ? Number(options.topicId) : null;

    // 新增：店铺相关参数
    this.storeId = options.storeId ? Number(options.storeId) : null;
    this.storeName = options.storeName ? decodeURIComponent(options.storeName) : null;
    this.storeImage = options.storeImage || null;


    // 如果没有传入内容ID或类型，使用默认值
    if (!this.contentId) this.contentId = 'default_content';
    if (!this.contentType) this.contentType = 'video';

    // 修复：将userId转换为Long类型（使用Number确保兼容性）
    const userIdStr = uni.getStorageSync('userid') || '222';
    this.userId = Number(userIdStr);

    console.log('📱 评论页面参数:', {
      contentId: this.contentId,
      contentType: this.contentType,
      topicId: this.topicId,
      storeId: this.storeId,
      storeName: this.storeName,
      userId: this.userId
    });

    // 如果是话题页面，获取话题信息
    if (this.topicId) {
      this.fetchTopicInfo();
    }

    // 如果是店铺页面，设置店铺信息
    if (this.storeId && this.storeName) {
      this.setupStoreInfo();
    }

    // 获取评论统计信息
    this.fetchCommentStats();

    // 获取各标签的评论列表
    this.fetchComments();
    this.setPageHeight();

    // 设置键盘监听
    this.setupKeyboardListener();

    // 将调试方法挂载到全局，方便控制台调用
    // #ifdef H5
    if (typeof window !== 'undefined') {
      window.debugKeyboard = () => this.debugKeyboardState();
      console.log('🔧 调试方法已挂载到全局: window.debugKeyboard()');
    }
    // #endif

    // 微信小程序环境下的调试方法
    // #ifdef MP-WEIXIN
    // 在微信小程序中，可以通过 getCurrentPages() 获取当前页面实例
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage) {
      currentPage.debugKeyboard = () => this.debugKeyboardState();
      console.log('🔧 调试方法已挂载到页面实例: getCurrentPages()[getCurrentPages().length-1].debugKeyboard()');
    }
    // #endif
  },
  
  // 在页面显示时启用键盘监听
  onShow() {
    // 确保只有一个有效的键盘监听
    uni.offKeyboardHeightChange(); // 先移除可能存在的监听
    this.setupKeyboardListener();
  },
  
  // 在页面隐藏时取消键盘监听
  onHide() {
    // 取消监听键盘高度变化
    uni.offKeyboardHeightChange();
    console.log('页面隐藏，取消键盘高度监听');
  },
  
  // 在页面卸载时取消键盘监听
  onUnload() {
    // 取消监听键盘高度变化
    uni.offKeyboardHeightChange();
    console.log('页面卸载，取消键盘高度监听');
  },
  methods: {
    // 兼容性时间戳函数 - 替代performance.now()
    getTimestamp() {
      // 微信小程序环境使用Date.now()
      if (typeof performance !== 'undefined' && performance.now) {
        return performance.now();
      }
      return Date.now();
    },

    /**
     * 设置店铺信息
     */
    setupStoreInfo() {
      console.log('🏪 设置店铺信息 - storeId:', this.storeId, 'storeName:', this.storeName, 'storeImage:', this.storeImage);

      // 构建店铺信息对象
      this.storeInfo = {
        id: this.storeId,
        name: this.storeName,
        title: `${this.storeName}找搭子`,
        description: '快来寻找你的搭子吧！',
        storeImage: this.storeImage,
        commentUserCount: 0, // 初始值，后续会通过API更新
        createTime: new Date().toISOString()
      };

      console.log('🏪 店铺信息设置完成:', this.storeInfo);

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: this.storeInfo.title
      });

      // 加载店铺评论
      this.fetchComments();
    },

    /**
     * 获取店铺评论列表
     */
    async fetchStoreComments(storeId, userId, filter, current, pageSize) {
      try {
        console.log('🏪 调用店铺评论API - storeId:', storeId, 'filter:', filter);

        // 构建请求URL和参数
        const baseUrl = this.$config?.apis?.vote_baseUrl || 'https://vote.foxdance.com.cn';
        const url = `${baseUrl}/api/comments/store/${storeId}`;

        const params = {
          userId: userId,
          filter: filter,
          current: current,
          pageSize: pageSize
        };

        console.log('🏪 店铺评论请求URL:', url);
        console.log('🏪 店铺评论请求参数:', params);

        // 发送请求
        const response = await new Promise((resolve, reject) => {
          uni.request({
            url: url,
            method: 'GET',
            data: params,
            header: {
              'Content-Type': 'application/json',
              'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
            },
            success: (res) => {
              console.log('🏪 店铺评论API响应:', res);
              if (res.statusCode === 200) {
                resolve(res.data);
              } else {
                reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
              }
            },
            fail: (err) => {
              console.error('🏪 店铺评论API请求失败:', err);
              reject(err);
            }
          });
        });

        return response;
      } catch (error) {
        console.error('🏪 获取店铺评论失败:', error);
        throw error;
      }
    },

    // 获取当前筛选类型下的评论数量（已加载的数量）
    getCurrentCommentCount() {
      switch (this.activeFilter) {
        case 'hot':
          return this.commentListHot.length;
        case 'new':
          return this.commentListNew.length;
        case 'my':
          return this.commentListMy.length;
        default:
          return 0;
      }
    },

    // 获取当前筛选条件对应的真实总数
    getCurrentFilterTotal() {
      switch (this.activeFilter) {
        case 'hot':
          return this.commentStats.hotTotal || 0;
        case 'new':
          return this.commentStats.newTotal || 0;
        case 'my':
          return this.commentStats.myTotal || 0;
        default:
          return 0;
      }
    },

    // 设置键盘高度监听器
    setupKeyboardListener() {
      // #ifdef MP-WEIXIN
      uni.onKeyboardHeightChange(res => {
        console.log('🎹 键盘高度变化:', res.height);
        console.log('📱 回复弹窗状态:', this.showReplyPopup);
        console.log('🎯 回复输入框焦点状态:', this.isReplyInputFocused);

        this.keyboardHeight = res.height;
        this.isKeyboardShow = res.height > 0;

        if (res.height > 0) {
          // 键盘弹出，调整主输入框位置
          this.inputContainerBottom = res.height;
          console.log('🔧 调整主输入框位置:', this.inputContainerBottom);
          if (this.isReplyMode) {
            console.log('� 当前处于回复模式');
          }
        } else {
          // 键盘收起，恢复主输入框位置
          this.inputContainerBottom = 0;
          console.log('📥 键盘收起，重置主输入框位置');
        }
      });
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序环境的兼容处理
      console.log('非微信小程序环境，使用默认键盘处理');
      // #endif

      console.log('键盘高度监听器已设置');
    },
    
    // 判断当前用户是否是评论所有者
    isCommentOwner(comment) {
      if (!comment || !comment.user) return false;
      return String(comment.user.id) == String(this.userId);
    },
    
    onInputFocus(e) {
      // 主输入框获取焦点，键盘弹出
      console.log('📝 主输入框获取焦点');
      if (this.isReplyMode) {
        console.log('💬 当前处于回复模式，回复用户:', this.currentReply && this.currentReply.user ? this.currentReply.user.nickname : '未知用户');
      }
      this.isKeyboardShow = true;

      // 微信小程序中，键盘弹出时的额外处理
      // #ifdef MP-WEIXIN
      // 延时获取键盘高度，因为键盘弹出需要时间
      setTimeout(() => {
        if (this.keyboardHeight === 0) {
          // 如果监听器没有获取到键盘高度，使用默认值
          this.keyboardHeight = 280; // 微信小程序默认键盘高度
          this.inputContainerBottom = this.keyboardHeight;
        }
      }, 300);
      // #endif
    },

    onInputBlur(e) {
      // 输入框失去焦点，键盘收起
      console.log('输入框失去焦点');
      this.isKeyboardShow = false;

      // 延时重置，确保键盘完全收起
      setTimeout(() => {
        if (!this.isKeyboardShow) {
          this.keyboardHeight = 0;
          this.inputContainerBottom = 0;
        }
      }, 100);
    },

    // 聚焦输入框
    focusInput() {
      if (this.$refs.mainCommentInput) {
        this.$refs.mainCommentInput.focus();
      }
    },

    // 隐藏蒙版层并收起键盘
    hideMaskAndKeyboard() {
      console.log('点击蒙版层，收起键盘');

      // 检查是否处于回复模式，如果是则取消回复
      if (this.isReplyMode) {
        console.log('🚫 检测到回复模式，取消回复');
        this.cancelReplyMode();
      }

      // 让输入框失去焦点
      if (this.$refs.mainCommentInput) {
        this.$refs.mainCommentInput.blur();
      }

      // 强制隐藏键盘
      uni.hideKeyboard();

      // 重置键盘状态
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
      this.inputContainerBottom = 0;
    },

    // 回复弹窗相关方法已移除，改为使用底部主输入框进行回复

    // 调试方法：检查当前键盘适配状态
    debugKeyboardState() {
      console.log('🔍 当前键盘适配状态:');
      console.log('  键盘高度:', this.keyboardHeight);
      console.log('  键盘显示状态:', this.isKeyboardShow);
      console.log('  回复弹窗显示:', this.showReplyPopup);
      console.log('  回复输入框焦点:', this.isReplyInputFocused);
      console.log('  回复弹窗底部距离:', this.replyPopupBottom);
      console.log('  主输入框底部距离:', this.inputContainerBottom);
    },
    setPageHeight() {
      const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 25;
      const inputBoxHeight = 120; // 底部输入框高度（rpx）

      // 计算页面滚动容器高度，减去输入框高度
      const pageHeight = `calc(100vh - ${inputBoxHeight}rpx)`;
      this.pageHeight = pageHeight;
      console.log('设置页面滚动高度:', pageHeight);
    },
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },

    changeFilter(type) {
      if (this.activeFilter === type) return;
      this.activeFilter = type;
      
      // 如果该类型的评论列表为空，则加载数据
      if (type === 'hot' && this.commentListHot.length === 0) {
        this.fetchCommentsByType('hot');
      } else if (type === 'new' && this.commentListNew.length === 0) {
        this.fetchCommentsByType('new');
      } else if (type === 'my' && this.commentListMy.length === 0) {
        this.fetchCommentsByType('my');
      }
    },
    
    // 获取所有类型的评论
    fetchComments() {
      // 初始化加载热门评论
      this.fetchCommentsByType('hot');
    },

    // 获取评论统计信息
    async fetchCommentStats() {
      if ((!this.topicId && !this.storeId) || !this.userId) {
        console.warn('⚠️ 缺少必要参数，无法获取评论统计');
        return;
      }

      try {
        console.log('🔢 开始获取评论统计信息...');

        let res;
        if (this.topicId) {
          // 话题评论统计
          res = await topicApi.getTopicCommentStats(this.topicId, this.userId);
        } else if (this.storeId) {
          // 店铺评论统计
          res = await this.fetchStoreCommentStats(this.storeId, this.userId);
        }

        if (res && res.code === 0) {
          this.commentStats = res.data;
          console.log('🔢 评论统计获取成功:', this.commentStats);
        } else {
          console.error('❌ 获取评论统计失败:', res?.message);
          // 使用默认值
          this.commentStats = {
            hotTotal: 0,
            newTotal: 0,
            myTotal: 0
          };
        }
      } catch (error) {
        console.error('❌ 获取评论统计异常:', error);
        // 使用默认值
        this.commentStats = {
          hotTotal: 0,
          newTotal: 0,
          myTotal: 0
        };
      }
    },

    /**
     * 获取店铺评论统计信息
     */
    async fetchStoreCommentStats(storeId, userId) {
      try {
        console.log('🏪 调用店铺评论统计API - storeId:', storeId);

        const baseUrl = this.$config?.apis?.vote_baseUrl || 'https://vote.foxdance.com.cn';
        const url = `${baseUrl}/api/comments/store/${storeId}/stats`;

        const params = {
          userId: userId
        };

        console.log('🏪 店铺评论统计请求URL:', url);
        console.log('🏪 店铺评论统计请求参数:', params);

        const response = await new Promise((resolve, reject) => {
          uni.request({
            url: url,
            method: 'GET',
            data: params,
            header: {
              'Content-Type': 'application/json',
              'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
            },
            success: (res) => {
              console.log('🏪 店铺评论统计API响应:', res);
              if (res.statusCode === 200) {
                resolve(res.data);
              } else {
                reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
              }
            },
            fail: (err) => {
              console.error('🏪 店铺评论统计API请求失败:', err);
              reject(err);
            }
          });
        });

        return response;
      } catch (error) {
        console.error('🏪 获取店铺评论统计失败:', error);
        throw error;
      }
    },

    // 根据类型获取评论
    fetchCommentsByType(type) {
      console.log(`请求${type}评论列表`);

      this.loading = true;

      // 重置分页状态
      this.pagination[type] = {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      };

      // 准备分页参数 - 修复参数名和数据类型
      const startTime = this.getTimestamp();
      const params = {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        contentId: this.contentId,
        contentType: this.contentType,
        filter: type,
        current: 1, // 修复：使用current而不是page
        pageSize: this.pagination[type].pageSize
      };

      console.log(`🚀 开始请求${type}评论列表，参数:`, params);

      // 判断是否为话题类型的评论
      if (this.topicId) {
        console.log(`🎯 检测到topicId: ${this.topicId}，使用话题API获取评论`);
        console.log(`📋 话题API调用参数:`, {
          topicId: Number(this.topicId),
          userId: Number(this.userId),
          filter: type,
          current: params.current,
          pageSize: params.pageSize
        });

        // 使用话题API获取评论 - 修复数据类型为Long兼容
        topicApi.getTopicComments(Number(this.topicId), Number(this.userId), type, params.current, params.pageSize).then(res => {
          const endTime = this.getTimestamp();
          const loadTime = endTime - startTime;

          console.log(`✅ 话题${type}评论列表API返回，耗时: ${loadTime.toFixed(2)}ms`);
          if (res.code === 0) {
            const data = res.data;

            // 性能优化：减少JSON序列化
            console.log(`📊 话题${type}评论列表数据概览:`, {
              total: data.total,
              commentsCount: data.comments ? data.comments.length : 0,
              hasMore: data.hasMore,
              current: data.current,
              pages: data.pages
            });

            // 处理评论数据（优化版）
            const processedData = this.processCommentDataOptimized(data);

            // 根据类型更新对应的评论列表
            switch (type) {
              case 'hot':
                this.commentListHot = processedData;
                break;
              case 'new':
                this.commentListNew = processedData;
                break;
              case 'my':
                this.commentListMy = processedData;
                break;
            }

            // 更新总评论数和分页信息
            this.totalComments = (data && data.total) || 0;
            this.pagination[type].hasMore = data.hasMore !== false;

            console.log(`🎯 话题${type}评论加载完成，总数: ${this.totalComments}, 当前显示: ${processedData.length}`);
          } else {
            // 请求失败，显示错误信息
            this.handleApiError(type, res.message || '获取评论失败');
          }
        }).catch(err => {
          console.error(`获取${type}评论列表失败:`, err);
          this.handleApiError(type, '网络请求错误');
        }).finally(() => {
          this.loading = false;
          this.isRefreshing = false;
        });
      } else if (this.storeId) {
        console.log(`🏪 检测到storeId: ${this.storeId}，使用店铺API获取评论`);
        console.log(`📋 店铺API调用参数:`, {
          storeId: Number(this.storeId),
          userId: Number(this.userId),
          filter: type,
          current: params.current,
          pageSize: params.pageSize
        });

        // 使用店铺API获取评论
        this.fetchStoreComments(Number(this.storeId), Number(this.userId), type, params.current, params.pageSize).then(res => {
          const endTime = this.getTimestamp();
          const loadTime = endTime - startTime;

          console.log(`✅ 店铺${type}评论列表API返回，耗时: ${loadTime.toFixed(2)}ms`);
          if (res.code === 0) {
            const data = res.data;

            console.log(`📊 店铺${type}评论列表数据概览:`, {
              total: data.total,
              commentsCount: data.comments ? data.comments.length : 0,
              hasMore: data.hasMore,
              current: data.current,
              pages: data.pages
            });

            // 处理评论数据
            const processedData = this.processCommentDataOptimized(data);

            // 根据类型更新对应的评论列表
            switch (type) {
              case 'hot':
                this.commentListHot = processedData;
                break;
              case 'new':
                this.commentListNew = processedData;
                break;
              case 'my':
                this.commentListMy = processedData;
                break;
            }

            // 更新总评论数和分页信息
            this.totalComments = (data && data.total) || 0;
            this.pagination[type].hasMore = data.hasMore !== false;

            console.log(`🎯 店铺${type}评论加载完成，总数: ${this.totalComments}, 当前显示: ${processedData.length}`);
          } else {
            this.handleApiError(type, res.message || '获取店铺评论失败');
          }
        }).catch(err => {
          console.error(`获取店铺${type}评论列表失败:`, err);
          this.handleApiError(type, '网络请求错误');
        }).finally(() => {
          this.loading = false;
          this.isRefreshing = false;
        });
      } else {
        // 使用普通评论API获取评论
        console.log('请求参数:', JSON.stringify(params));

        commentApi.getCommentList(params).then(res => {
          const endTime = this.getTimestamp();
          const loadTime = endTime - startTime;

          console.log(`✅ ${type}评论列表API返回，耗时: ${loadTime.toFixed(2)}ms`);

          if (res.code === 0) {
            const data = res.data;

            // 性能优化：减少JSON序列化
            console.log(`📊 ${type}评论列表数据概览:`, {
              total: data.total,
              commentsCount: data.comments ? data.comments.length : 0,
              hasMore: data.hasMore
            });

            // 处理评论数据（优化版）
            const processedData = this.processCommentDataOptimized(data);

            // 根据类型更新对应的评论列表
            switch (type) {
              case 'hot':
                this.commentListHot = processedData;
                break;
              case 'new':
                this.commentListNew = processedData;
                break;
              case 'my':
                this.commentListMy = processedData;
                break;
            }

            // 更新总评论数和分页信息
            this.totalComments = (data && data.total) || 0;
            this.pagination[type].hasMore = data.hasMore !== false;

            console.log(`🎯 ${type}评论加载完成，总数: ${this.totalComments}, 当前显示: ${processedData.length}`);
          } else {
            // 请求失败，显示错误信息
            this.handleApiError(type, res.message || '获取评论失败');
          }
        }).catch(err => {
          console.error(`获取${type}评论列表失败:`, err);
          this.handleApiError(type, '网络请求错误');
        }).finally(() => {
          this.loading = false;
          this.isRefreshing = false;
        });
      }
    },
    
    // 处理API错误
    handleApiError(type, message) {
      // 显示错误提示
      uni.showToast({
        title: message,
        icon: 'none'
      });
      
      // 确保评论列表不为undefined
      switch (type) {
        case 'hot':
          if (!this.commentListHot) this.commentListHot = [];
          break;
        case 'new':
          if (!this.commentListNew) this.commentListNew = [];
          break;
        case 'my':
          if (!this.commentListMy) this.commentListMy = [];
          break;
      }
      
      // 如果是刷新操作，提供重试选项
      if (this.isRefreshing) {
        setTimeout(() => {
          uni.showModal({
            title: '提示',
            content: '获取评论失败，是否重试？',
            confirmText: '重试',
            success: (res) => {
              if (res.confirm) {
                this.fetchCommentsByType(type);
              }
            }
          });
        }, 500);
      }
    },

    // 懒加载更多评论（优化版）
    loadMoreComments() {
      const type = this.activeFilter; // 使用当前激活的筛选类型
      console.log(`🔄 触发${type}评论懒加载`);

      // 防抖处理，避免重复请求
      if (this.pagination[type].loading || !this.pagination[type].hasMore) {
        console.log(`⚠️ ${type}评论正在加载或已无更多数据，跳过请求`);
        return;
      }

      // 性能优化：增强防抖处理，减少低端设备的请求频率
      const now = Date.now();
      const lastRequestTime = this.lastRequestTime || 0;
      if (now - lastRequestTime < 800) { // 增加到800ms，减少低端设备的负担
        console.log(`⚠️ 请求过于频繁，跳过${type}评论懒加载`);
        return;
      }
      this.lastRequestTime = now;

      // 设置加载状态
      this.pagination[type].loading = true;
      this.loadingText = '加载更多评论...';

      // 计算下一页页码
      const nextPage = this.pagination[type].page + 1;
      const startTime = this.getTimestamp();

      console.log(`📄 ${type}评论当前页码: ${this.pagination[type].page}, 请求页码: ${nextPage}`);

      // 准备分页参数 - 修复参数传递问题
      let apiCall;

      if (this.topicId) {
        // 话题评论API调用 - 修复数据类型为Long兼容
        console.log(`🎯 调用话题评论API: topicId=${this.topicId}, type=${type}, page=${nextPage}`);
        apiCall = topicApi.getTopicComments(Number(this.topicId), Number(this.userId), type, nextPage, this.pagination[type].pageSize);
      } else {
        // 普通评论API调用 - 修复分页
        // 参数和数据类型为Long兼容
        const params = {
          userId: Number(this.userId), // 使用Number确保Long兼容性
          contentId: this.contentId,
          contentType: this.contentType,
          filter: type,
          current: nextPage,  // 修复：使用current而不是page
          pageSize: this.pagination[type].pageSize
        };

        console.log(`📋 调用普通评论API，参数:`, JSON.stringify(params));
        apiCall = commentApi.getCommentList(params);
      }

      apiCall.then(res => {
        const endTime = this.getTimestamp();
        const loadTime = endTime - startTime;

        console.log(`✅ ${type}评论分页API返回，耗时: ${loadTime.toFixed(2)}ms`);

        if (res.code === 0) {
          const data = res.data;

          // 性能优化：减少日志输出
          console.log(`📊 ${type}评论分页数据概览:`, {
            commentsCount: data.comments ? data.comments.length : 0,
            total: data.total,
            hasMore: data.hasMore
          });

          // 处理不同的数据结构（优化版）
          let rawComments = [];
          if (data.comments && Array.isArray(data.comments)) {
            rawComments = data.comments;
          } else if (data.items && Array.isArray(data.items)) {
            rawComments = data.items;
          } else if (Array.isArray(data)) {
            rawComments = data;
          }

          const newComments = this.processCommentDataOptimized({ comments: rawComments });

          if (newComments && newComments.length > 0) {
            // 检查是否有重复数据（优化版）
            const existingIds = new Set(this.getExistingCommentIds(type));
            const filteredComments = newComments.filter(comment => !existingIds.has(comment.id));

            console.log(`� ${type}评论去重: 原始${newComments.length}条，去重后${filteredComments.length}条`);

            if (filteredComments.length > 0) {
              // 追加新评论到对应列表（优化：使用concat而不是展开运算符）
              switch (type) {
                case 'hot':
                  this.commentListHot = this.commentListHot.concat(filteredComments);
                  break;
                case 'new':
                  this.commentListNew = this.commentListNew.concat(filteredComments);
                  break;
                case 'my':
                  this.commentListMy = this.commentListMy.concat(filteredComments);
                  break;
              }

              // 更新分页信息
              this.pagination[type].page = nextPage;

              console.log(`✅ ${type}评论加载成功，页码: ${nextPage}，新增: ${filteredComments.length}条`);
            }

            // 检查是否还有更多数据
            if (data.hasMore === false || newComments.length < this.pagination[type].pageSize) {
              this.pagination[type].hasMore = false;
              console.log(`🔚 ${type}评论已加载完毕`);
            }
          } else {
            // 没有更多数据
            this.pagination[type].hasMore = false;
            console.log(`🔚 ${type}评论无更多数据`);
          }
        } else {
          // API返回错误
          console.error(`❌ ${type}评论API返回错误:`, res.message);
          uni.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error(`❌ ${type}评论懒加载失败:`, err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      }).finally(() => {
        // 重置加载状态
        this.pagination[type].loading = false;
        this.loadingText = '加载中...';
        console.log(`🔄 ${type}评论加载状态重置`);
      });
    },

    // 获取已存在的评论ID列表，用于去重
    getExistingCommentIds(type) {
      let existingComments = [];
      switch (type) {
        case 'hot':
          existingComments = this.commentListHot;
          break;
        case 'new':
          existingComments = this.commentListNew;
          break;
        case 'my':
          existingComments = this.commentListMy;
          break;
      }
      return existingComments.map(comment => comment.id);
    },

    // 优化的评论数据处理方法 - 增强渲染性能
    processCommentDataOptimized(data) {
      const startTime = this.getTimestamp();

      // 性能优化：减少批处理大小，避免滚动时卡顿
      const batchSize = 5; // 减少到5条评论，确保滚动流畅

      if (!data || !data.comments) {
        console.warn('⚠️ 评论数据为空或格式错误');
        return [];
      }

      const comments = Array.isArray(data.comments) ? data.comments : [];
      console.log(`� 开始处理评论数据，数量: ${comments.length}`);

      const processedComments = comments.map(comment => {
        if (!comment) return null;

        // 优化：减少对象创建和属性复制
        const processedComment = Object.assign({}, comment, {
          created_at: comment.createdAt || comment.created_at || new Date().toISOString(),
          is_liked: comment.isLiked || comment.is_liked || false,
          showFullContent: false
        });

        // 确保用户对象存在
        if (!processedComment.user) {
          processedComment.user = {
            id: 0,
            nickname: '未知用户',
            avatar: '/static/images/toux.png',
            level: 0
          };
        } else {
          // 处理用户头像为空的情况
          if (!processedComment.user.avatar) {
            processedComment.user.avatar = '/static/images/toux.png';
          }
          processedComment.user.nickname = processedComment.user.nickname || '未知用户';
          processedComment.user.level = processedComment.user.level || 0;
        }

        return processedComment;
      }).filter(comment => comment !== null);

      const endTime = this.getTimestamp();
      console.log(`✅ 评论数据处理完成，耗时: ${(endTime - startTime).toFixed(2)}ms，处理数量: ${processedComments.length}`);

      return processedComments;
    },



    onRefresh() {
      this.isRefreshing = true;

      // 重置当前激活标签的分页状态
      this.pagination[this.activeFilter] = {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      };

      // 刷新评论统计信息
      this.fetchCommentStats();

      // 刷新当前激活的评论列表
      this.fetchCommentsByType(this.activeFilter);
    },
    likeComment(item, index, type) {
      // 调用点赞/取消点赞API
      const action = item.is_liked ? 'unlike' : 'like';

      commentApi.likeComment(Number(item.id), {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        action
      }).then(res => {
        console.log('点赞评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 更新评论点赞状态和数量
          const updatedIsLiked = res.data.isLiked || res.data.is_liked;
          const updatedLikes = res.data.likes;
          
          // 根据类型更新对应的评论列表
          switch (type) {
            case 'hot':
              this.commentListHot[index].is_liked = updatedIsLiked;
              this.commentListHot[index].likes = updatedLikes;
              break;
            case 'new':
              this.commentListNew[index].is_liked = updatedIsLiked;
              this.commentListNew[index].likes = updatedLikes;
              break;
            case 'my':
              this.commentListMy[index].is_liked = updatedIsLiked;
              this.commentListMy[index].likes = updatedLikes;
              break;
          }
        } else {
          uni.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('点赞操作失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    goToDetail(item) {
      // 跳转到评论详情页面
      uni.navigateTo({
        url: `/pagesSub/switch/comment-detail?id=${item.id}&userId=${this.userId}`
      });
    },
    sendComment() {
      if (this.commentText.length > 1000) {
        uni.showToast({
          title: '评论字数不能超过1000字',
          icon: 'none'
        });
        return;
      }

      if (!this.commentText.trim()) return;

      // 根据当前状态决定是发送评论还是回复
      if (this.isReplyMode && this.currentReply) {
        console.log('📤 发送回复给用户:', this.currentReply.user.nickname);
        this.sendReply();
      } else {
        console.log('📤 发送普通评论');
        this.sendNormalComment();
      }
    },

    // 发送普通评论
    sendNormalComment() {
      // 判断是话题评论还是店铺评论
      if (this.topicId) {
        this.sendTopicComment();
      } else if (this.storeId) {
        this.sendStoreComment();
      } else {
        // 普通评论
        this.sendRegularComment();
      }
    },

    // 发送话题评论
    sendTopicComment() {
      const data = {
        userId: Number(this.userId),
        contentId: this.contentId,
        topicId: Number(this.topicId),
        content: this.commentText.trim()
      };

      console.log('🎯 发送话题评论请求数据:', JSON.stringify(data));

      commentApi.postComment(data).then(res => {
        console.log('发送话题评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          this.handleCommentSuccess();
        } else {
          this.handleCommentError(res.message || '话题评论失败');
        }
      }).catch(err => {
        console.error('发送话题评论失败:', err);
        this.handleCommentError('网络错误，请重试');
      });
    },

    // 发送店铺评论
    async sendStoreComment() {
      const data = {
        userId: Number(this.userId),
        storeId: Number(this.storeId),
        content: this.commentText.trim()
      };

      console.log('🏪 发送店铺评论请求数据:', JSON.stringify(data));

      try {
        const res = await this.postStoreComment(data);
        console.log('发送店铺评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          this.handleCommentSuccess();
        } else {
          this.handleCommentError(res.message || '店铺评论失败');
        }
      } catch (err) {
        console.error('发送店铺评论失败:', err);
        this.handleCommentError('网络错误，请重试');
      }
    },

    // 发送普通评论
    sendRegularComment() {
      const data = {
        userId: Number(this.userId),
        contentId: this.contentId,
        content: this.commentText.trim()
      };

      console.log('📝 发送普通评论请求数据:', JSON.stringify(data));

      commentApi.postComment(data).then(res => {
        console.log('发送普通评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          this.handleCommentSuccess();
        } else {
          this.handleCommentError(res.message || '评论失败');
        }
      }).catch(err => {
        console.error('发送普通评论失败:', err);
        this.handleCommentError('网络错误，请重试');
      });
    },

    // 处理评论成功
    handleCommentSuccess() {
      // 清空输入框并重置状态
      this.clearInputAndResetState();

      // 重新加载评论列表 - 发布后切换到"最新"标签
      this.fetchCommentsByType('new');
      this.activeFilter = 'new';
      this.currentTabIndex = 1;

      // 刷新评论统计
      this.fetchCommentStats();

      uni.showToast({
        title: '评论成功',
        icon: 'success'
      });
    },

    // 处理评论错误
    handleCommentError(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      });
    },

    // 发送店铺评论API调用
    async postStoreComment(data) {
      const baseUrl = this.$config?.apis?.vote_baseUrl || 'https://vote.foxdance.com.cn';
      const url = `${baseUrl}/api/comments/store`;

      return new Promise((resolve, reject) => {
        uni.request({
          url: url,
          method: 'POST',
          data: data,
          header: {
            'Content-Type': 'application/json',
            'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')
          },
          success: (res) => {
            console.log('🏪 店铺评论API响应:', res);
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
            }
          },
          fail: (err) => {
            console.error('🏪 店铺评论API请求失败:', err);
            reject(err);
          }
        });
      });
    },
    replyComment(item) {
      console.log('💬 进入回复模式，回复用户:', item.user.nickname);

      // 设置回复状态
      this.isReplyMode = true;
      this.currentReply = item;
      this.inputPlaceholder = `@${item.user.nickname}`;

      console.log('🔄 已设置回复模式，placeholder:', this.inputPlaceholder);

      // 聚焦主输入框
      this.$nextTick(() => {
        if (this.$refs.mainCommentInput) {
          this.$refs.mainCommentInput.focus();
          console.log('🎯 主输入框已聚焦，准备回复');
        } else {
          console.warn('⚠️ 主输入框引用不存在');
        }
      });
    },
    
    // 显示更多操作弹窗
    showMoreOptions(item) {
      this.currentMoreComment = item;
      this.showMorePopup = true;
    },
    
    // 从更多操作弹窗中点击回复
    replyFromMore() {
      if (this.currentMoreComment) {
        this.showMorePopup = false;
        // 等待更多操作弹窗关闭后再打开回复弹窗
        setTimeout(() => {
          this.replyComment(this.currentMoreComment);
        }, 300);
      }
    },
    
    // 复制评论内容
    copyComment() {
      if (!this.currentMoreComment) return;
      
      uni.setClipboardData({
        data: this.currentMoreComment.content,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
          this.showMorePopup = false;
        }
      });
    },
    
    // 删除评论
    deleteComment() {
      if (!this.currentMoreComment) return;
      
      uni.showModal({
        title: '删除评论',
        content: '确认要删除这条评论吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: res => {
          if (res.confirm) {
            // 调用API删除评论 - 修复数据类型为Long兼容
            commentApi.deleteComment(Number(this.currentMoreComment.id), {
              userId: Number(this.userId) // 使用Number确保Long兼容性
            }).then(res => {
              console.log('删除评论API返回数据:', JSON.stringify(res));
              if (res.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 关闭弹窗
                this.showMorePopup = false;
                
                // 从列表中移除已删除的评论
                const removeFromList = (list, commentId) => {
                  const index = list.findIndex(item => item.id == commentId);
                  if (index > -1) {
                    list.splice(index, 1);
                  }
                };
                
                // 根据当前标签页删除对应列表中的评论
                removeFromList(this.commentListHot, this.currentMoreComment.id);
                removeFromList(this.commentListNew, this.currentMoreComment.id);
                removeFromList(this.commentListMy, this.currentMoreComment.id);
                
                // 更新评论总数
                this.totalComments--;
              } else {
                uni.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('删除评论失败:', err);
              uni.showToast({
                title: '网络请求错误',
                icon: 'none'
              });
            });
          }
        }
      });
    },
    sendReply() {
      if (!this.commentText.trim() || !this.currentReply) return;

      // 调用回复评论API - 修复数据类型为Long兼容
      commentApi.replyComment(Number(this.currentReply.id), {
        userId: Number(this.userId), // 使用Number确保Long兼容性
        content: this.commentText.trim(),
        replyToId: null // 直接回复评论，不是回复其他回复
      }).then(res => {
        console.log('回复评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          // 回复成功
          // 清空输入框并重置回复状态
          this.clearInputAndResetState();

          // 重新加载当前标签页的评论列表
          this.fetchCommentsByType(this.activeFilter);
          
          uni.showToast({
            title: '回复成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: res.message || '回复失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('回复评论失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },

    // 清空输入框并重置回复状态
    clearInputAndResetState(clearInput = true) {
      // 根据参数决定是否清空输入框
      if (clearInput) {
        if (this.$refs.mainCommentInput) {
          this.$refs.mainCommentInput.clear();
        } else {
          this.commentText = '';
        }
        console.log('🔄 输入框已清空');
      } else {
        console.log('🔄 保留输入内容');
      }

      // 重置回复状态
      this.isReplyMode = false;
      this.currentReply = null;
      this.inputPlaceholder = '说点什么...';

      console.log('🔄 回复状态已重置');
    },

    // 取消回复模式
    cancelReplyMode(clearInput = false) {
      const wasInReplyMode = this.isReplyMode;
      const replyTargetName = this.currentReply && this.currentReply.user ? this.currentReply.user.nickname : '未知用户';
      const hasInputContent = this.commentText && this.commentText.trim();

      // 重置回复状态，但可以选择保留输入内容
      this.isReplyMode = false;
      this.currentReply = null;
      this.inputPlaceholder = '说点什么...';

      // 根据参数决定是否清空输入框
      if (clearInput) {
        if (this.$refs.mainCommentInput) {
          this.$refs.mainCommentInput.clear();
        } else {
          this.commentText = '';
        }
      }

      if (wasInReplyMode) {
        console.log(`❌ 已取消回复模式，原回复对象: ${replyTargetName}`);

        // 根据是否有输入内容给出不同的提示
        let toastTitle = '已取消回复';
        if (hasInputContent && !clearInput) {
          toastTitle = '已取消回复，内容转为评论';
        }

        // 给用户视觉反馈
        uni.showToast({
          title: toastTitle,
          icon: 'none',
          duration: 1500
        });
      }
    },

    formatTime(timeString) {
      if (!timeString) return '';
      return dayjs(timeString).fromNow();
    },

    // 获取话题信息
    async fetchTopicInfo() {
      if (!this.topicId) return;

      try {
        console.log('🎯 获取话题信息，topicId:', this.topicId, 'userId:', this.userId);
        const res = await topicApi.getTopicById(this.topicId, this.userId);

        console.log('🔍 API响应完整数据:', JSON.stringify(res, null, 2));

        if (res.code === 0 && res.data) {
          console.log('🔍 API响应data字段:', JSON.stringify(res.data, null, 2));
          console.log('🔍 topicImages字段类型:', typeof res.data.topicImages);
          console.log('🔍 topicImages字段值:', res.data.topicImages);
          console.log('🔍 topicImages是否为数组:', Array.isArray(res.data.topicImages));

          this.topicInfo = res.data;
          console.log('✅ 话题信息获取成功，赋值后的topicInfo:', this.topicInfo);
          console.log('✅ 赋值后的topicImages:', this.topicInfo.topicImages);

          // 如果话题有图片，记录详细日志并验证URL
          if (this.topicInfo.topicImages && this.topicInfo.topicImages.length > 0) {
            console.log('🖼️ 话题包含图片，数量:', this.topicInfo.topicImages.length);
            console.log('🖼️ 原始图片URL列表:', this.topicInfo.topicImages);

            // 验证和处理图片URL
            const processedImages = this.topicInfo.topicImages.map((url, index) => {
              const processedUrl = this.processImageUrl(url);
              console.log(`🖼️ 图片${index + 1}: ${url} -> ${processedUrl}`);
              return processedUrl;
            });

            // 更新处理后的图片URL（可选，保持原始数据不变）
            console.log('🖼️ 处理后图片URL列表:', processedImages);
          } else {
            console.warn('📷 话题不包含图片或topicImages为null/empty');
            console.warn('📷 topicImages详细信息:', {
              value: this.topicInfo.topicImages,
              type: typeof this.topicInfo.topicImages,
              isArray: Array.isArray(this.topicInfo.topicImages),
              length: this.topicInfo.topicImages ? this.topicInfo.topicImages.length : 'N/A'
            });
          }
        } else {
          console.warn('⚠️ 获取话题信息失败:', res.message);
          console.warn('⚠️ 完整响应:', res);
          uni.showToast({
            title: res.message || '获取话题信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('❌ 获取话题信息异常:', error);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      }
    },

    // 处理话题图片加载错误
    handleTopicImageError(index) {
      console.error('❌ 话题图片加载失败，索引:', index);
      if (this.topicInfo && this.topicInfo.topicImages && this.topicInfo.topicImages[index]) {
        const originalUrl = this.topicInfo.topicImages[index];
        const processedUrl = this.processImageUrl(originalUrl);
        console.error('❌ 原始URL:', originalUrl);
        console.error('❌ 处理后URL:', processedUrl);

        // 尝试使用备用图片或提示用户
        this.handleImageLoadError(processedUrl, '话题图片');
      }
    },

    // 预览话题图片
    previewTopicImage(index) {
      console.log('🔥 开始预览话题图片，索引:', index);

      if (!this.topicInfo || !this.topicInfo.topicImages || this.topicInfo.topicImages.length === 0) {
        uni.showToast({
          title: '没有可预览的图片',
          icon: 'none'
        });
        return;
      }

      if (index < 0 || index >= this.topicInfo.topicImages.length) {
        uni.showToast({
          title: '图片索引错误',
          icon: 'none'
        });
        return;
      }

      // 处理图片URL，确保可访问性
      const processedUrls = this.topicInfo.topicImages.map(url => {
        return this.processImageUrl(url);
      });

      const processedCurrentUrl = processedUrls[index];
      console.log('🔥 处理后的话题图片URL数组:', processedUrls);
      console.log('🔥 当前预览URL:', processedCurrentUrl);

      uni.previewImage({
        current: processedCurrentUrl,  // 修复：使用图片URL而不是索引
        urls: processedUrls,           // 修复：使用处理后的URL数组
        success: () => {
          console.log('✅ 话题图片预览成功');
        },
        fail: (error) => {
          console.error('❌ 话题图片预览失败:', error);
          uni.showToast({
            title: '图片预览失败: ' + (error.errMsg || '未知错误'),
            icon: 'none'
          });
        }
      });
    },

    getLevelColor(level) {
      const colors = {
        0: '#cccbc8', // 灰色
        1: '#c6ffe6',
        2: '#61bc84', // 绿色
        3: '#4d648d',
        4: '#1F3A5F',
        5: '#9c27b0',
        6: '#6c35de',
        7: '#ffd299', // 橙色
        8: '#FF7F50', // 红色
        9: '#f35d74', // 紫色
        10: '#bb2649' // 粉色
      };
      return colors[level] || '#8dc63f';
    },

    // 处理图片URL，确保可访问性
    processImageUrl(url) {
      if (!url) return '';

      console.log('🔥 处理图片URL:', url);

      // 如果URL是相对路径，转换为绝对路径
      if (url.startsWith('/')) {
        const processedUrl = 'https://file.foxdance.com.cn' + url;
        console.log('🔥 相对路径转换:', url, '->', processedUrl);
        return processedUrl;
      }

      // 如果URL不包含协议，添加https
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        const processedUrl = 'https://' + url;
        console.log('🔥 添加协议:', url, '->', processedUrl);
        return processedUrl;
      }

      console.log('🔥 URL无需处理:', url);
      return url;
    },

    // 处理图片加载错误
    handleImageLoadError(url, context = '图片') {
      console.error('❌ 图片加载失败:', context, url);
      uni.showToast({
        title: context + '加载失败',
        icon: 'none'
      });
    },
    // 切换评论内容的展开/收起状态
    toggleContent(item, index, type) {
      const wasExpanded = item.showFullContent;

      // 根据类型更新对应的评论列表
      switch (type) {
        case 'hot':
          this.$set(this.commentListHot[index], 'showFullContent', !item.showFullContent);
          break;
        case 'new':
          this.$set(this.commentListNew[index], 'showFullContent', !item.showFullContent);
          break;
        case 'my':
          this.$set(this.commentListMy[index], 'showFullContent', !item.showFullContent);
          break;
      }

      // 如果是从展开状态收起，则滚动到评论顶部
      if (wasExpanded) {
        this.scrollToComment(index, type);
      }
    },

    // 滚动到指定评论的顶部位置
    scrollToComment(index, type) {
      const commentId = `comment-${type}-${index}`;
      console.log(`🎯 开始滚动到评论 - ${commentId}, 索引: ${index}, 类型: ${type}`);

      // 优先使用scroll-top方法，因为它更可控
      setTimeout(() => {
        this.scrollToCommentByScrollTop(commentId);
      }, 200);

      // 如果scroll-top失败，再尝试scrollIntoView
      setTimeout(() => {
        // 检查scroll-top是否成功，如果scrollTop仍然很小，说明可能失败了
        if (this.scrollTop < 50) {
          console.log(`🔄 scroll-top可能失败，尝试scrollIntoView - ${commentId}`);
          this.scrollToCommentByScrollIntoView(commentId);
        }
      }, 600);
    },

    // 方法1: 使用scrollIntoView滚动到评论
    scrollToCommentByScrollIntoView(commentId) {
      console.log(`📍 使用scrollIntoView滚动到 - ${commentId}`);

      // 先验证目标元素是否存在
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select(`#${commentId}`).boundingClientRect((rect) => {
          if (rect) {
            console.log(`📍 找到目标元素 - ${commentId}:`, rect);

            // 设置scroll-into-view属性
            this.scrollIntoView = commentId;

            // 清除scrollIntoView，避免影响后续滚动
            setTimeout(() => {
              this.scrollIntoView = '';
              console.log(`✅ scrollIntoView设置成功 - ${commentId}`);
            }, 800); // 增加清除延时
          } else {
            console.warn(`⚠️ 未找到目标元素 - ${commentId}`);
            // 如果scrollIntoView失败，尝试使用scroll-top方法
            setTimeout(() => {
              this.scrollToCommentByScrollTop(commentId);
            }, 100);
          }
        }).exec();
      });
    },

    // 方法2: 使用scroll-top属性滚动到评论
    scrollToCommentByScrollTop(commentId) {
      console.log(`📍 使用scroll-top滚动到 - ${commentId}`);

      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);

        // 获取scroll-view容器和目标元素的位置信息
        query.select('.page-scroll-view').boundingClientRect();
        query.select(`#${commentId}`).boundingClientRect();
        // 同时获取话题信息区域的高度，用于更精确的计算
        query.select('.topic-info-section').boundingClientRect();
        // 获取当前scroll-view的滚动信息
        query.select('.page-scroll-view').scrollOffset();

        query.exec((res) => {
          console.log(`📊 scroll-top查询结果 - ${commentId}:`, res);

          if (res && res.length >= 3) {
            const scrollViewRect = res[0];  // scroll-view容器
            const commentRect = res[1];     // 目标评论元素
            const topicInfoRect = res[2];   // 话题信息区域
            const scrollInfo = res[3];      // 当前滚动信息

            if (scrollViewRect && commentRect) {
              // 获取当前真实的滚动位置
              const currentScrollTop = scrollInfo ? scrollInfo.scrollTop : (this.scrollTop || 0);

              // 计算评论元素在整个内容中的绝对位置
              const commentAbsoluteTop = currentScrollTop + (commentRect.top - scrollViewRect.top);

              // 设置合理的顶部偏移量
              const topOffset = 120;
              const targetScrollTop = Math.max(0, commentAbsoluteTop - topOffset);

              // 如果话题信息区域存在，记录其高度用于调试
              let topicInfoHeight = 0;
              if (topicInfoRect) {
                topicInfoHeight = topicInfoRect.height || 0;
                console.log(`📏 话题信息区域高度: ${topicInfoHeight}`);
              }

              console.log(`📐 scroll-top详细计算 - ${commentId}:`, {
                scrollViewTop: scrollViewRect.top,
                commentTop: commentRect.top,
                currentScrollTop: currentScrollTop,
                commentAbsoluteTop: commentAbsoluteTop,
                topicInfoHeight: topicInfoRect ? topicInfoRect.height : 0,
                topOffset: topOffset,
                targetScrollTop: targetScrollTop
              });

              // 使用更可靠的滚动方式
              if (targetScrollTop > 0) {
                // 先重置到一个不同的值，强制触发更新
                this.scrollTop = targetScrollTop + 1;
                this.$nextTick(() => {
                  this.scrollTop = targetScrollTop;
                  console.log(`✅ scroll-top设置成功 - ${commentId}, 位置: ${targetScrollTop}`);
                });
              } else {
                console.warn(`⚠️ 计算的滚动位置为0或负数 - ${commentId}, 位置: ${targetScrollTop}`);
                // 如果计算结果为0，尝试一个最小的滚动位置
                this.scrollTop = 50;
              }
            } else {
              console.warn(`⚠️ 获取元素位置失败 - scrollView: ${!!scrollViewRect}, comment: ${!!commentRect}`);
            }
          } else {
            console.warn(`⚠️ 查询结果不完整 - ${commentId}:`, res);
          }
        });
      });
    },

    // 调试方法：检查DOM元素是否存在
    debugScrollElements(index, type) {
      const commentId = `comment-${type}-${index}`;
      console.log(`🔍 调试滚动元素 - ${commentId}`);

      const query = uni.createSelectorQuery().in(this);
      query.select(`#${commentId}`).boundingClientRect();
      query.select('.page-scroll-view').boundingClientRect();

      query.exec((res) => {
        console.log('🔍 调试结果:', {
          commentId: commentId,
          commentElement: res[0],
          scrollViewElement: res[1],
          hasComment: !!res[0],
          hasScrollView: !!res[1]
        });
      });
    },

    // 测试方法：手动触发滚动
    testScroll(index = 0, type = 'hot') {
      console.log(`🧪 测试滚动功能 - ${type}-${index}`);

      // 先检查元素是否存在
      this.debugScrollElements(index, type);

      // 测试scrollIntoView
      const commentId = `comment-${type}-${index}`;
      setTimeout(() => {
        console.log(`🧪 设置scrollIntoView - ${commentId}`);
        this.scrollIntoView = commentId;

        setTimeout(() => {
          this.scrollIntoView = '';
          console.log(`🧪 测试完成 - ${commentId}`);
        }, 1000);
      }, 500);
    },

    // 强制滚动方法：用于测试
    forceScrollToComment(index, type) {
      const commentId = `comment-${type}-${index}`;
      console.log(`🚀 强制滚动到评论 - ${commentId}`);

      // 直接设置scrollIntoView，不使用延时
      this.scrollIntoView = commentId;

      // 同时尝试scroll-top方式
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select(`#${commentId}`).boundingClientRect((rect) => {
          if (rect) {
            console.log(`🚀 强制滚动 - 找到元素:`, rect);
            // 计算一个简单的滚动位置
            const targetScrollTop = Math.max(0, rect.top - 100);
            this.scrollTop = targetScrollTop;
            console.log(`🚀 强制滚动 - 设置scrollTop: ${targetScrollTop}`);
          } else {
            console.warn(`🚀 强制滚动 - 未找到元素: ${commentId}`);
          }
        }).exec();
      });
    },

    // 简单滚动测试：直接滚动到指定位置
    testScrollToPosition(position = 300) {
      console.log(`🧪 测试滚动到位置: ${position}`);
      this.scrollTop = position;
    },

    // 获取当前滚动状态
    getCurrentScrollStatus() {
      console.log(`📊 当前滚动状态:`, {
        scrollTop: this.scrollTop,
        scrollIntoView: this.scrollIntoView
      });

      // 获取实际的滚动位置
      const query = uni.createSelectorQuery().in(this);
      query.select('.page-scroll-view').scrollOffset((scrollInfo) => {
        console.log(`📊 实际滚动位置:`, scrollInfo);
      }).exec();
    },
    // 处理评论数据的方法
    processCommentData(data) {
      // 处理数据，转换字段名称以适配前端展示
      if (!data) {
        console.warn('评论数据为空');
        return [];
      }
      
      const commentsList = data.comments || [];
      console.log(`处理评论数据，评论数量:`, commentsList.length);
      
      if (commentsList.length > 0) {
        commentsList.forEach(item => {
          if (!item) return;
          
          // 转换字段名
          item.created_at = item.createdAt || new Date().toISOString();
          item.is_liked = item.isLiked || false;
          item.reply_count = item.replyCount || 0;
          item.likes = item.likes || 0;
          
          // 确保user对象存在
          if (!item.user) {
            item.user = {
              id: 0,
              nickname: '未知用户',
              avatar: '/static/images/toux.png',
              level: 0
            };
          } else {
            // 处理用户头像为空的情况
            if (!item.user.avatar) {
              item.user.avatar = '/static/images/toux.png';
            }
            
            // 确保其他用户字段存在
            item.user.nickname = item.user.nickname || '未知用户';
            item.user.level = item.user.level || 0;
          }
          
          // 确保replies字段存在
          if (!item.replies) {
            item.replies = [];
          } else if (item.replies.length > 0) {
            // 转换回复中的字段名
            item.replies.forEach(reply => {
              if (!reply) return;
              
              reply.created_at = reply.createdAt || new Date().toISOString();
              reply.is_liked = reply.isLiked || false;
              
              // 确保reply_to存在
              if (reply.replyTo) {
                reply.reply_to = reply.replyTo;
              }
              
              // 确保user对象存在
              if (!reply.user) {
                reply.user = {
                  id: 0,
                  nickname: '未知用户',
                  avatar: '/static/images/toux.png'
                };
              } else {
                // 处理用户头像为空的情况
                if (!reply.user.avatar) {
                  reply.user.avatar = '/static/images/toux.png';
                }
                
                // 确保其他用户字段存在
                reply.user.nickname = reply.user.nickname || '未知用户';
              }
            });
          }
        });
      }
      
      return commentsList;
    }
  }
}
</script>

<style lang="scss" scoped>
.comment-page {
  height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
  position: relative;
}

/* 页面滚动容器 */
.page-scroll-view {
  width: 94%;
  padding: 0 24rpx;
}

/* 话题图片轮播样式 - 小红书风格 */
.topic-images-container {
  position: relative;
  width: 100%;
  height: 520rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(255, 105, 135, 0.15);
  margin-top: 24rpx;
  background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.topic-images-swiper {
  width: 100%;
  height: 100%;
  border-radius: 32rpx;
}

.topic-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 32rpx;
}

.topic-image:active {
  transform: scale(0.98);
}

/* 话题信息区域样式 - 性能优化版本 */
.topic-info-section {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  margin: 24rpx 0;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
}

.topic-header {
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 245, 240, 0.9) 100%);
}

.topic-title {
  font-size: 32rpx; /* 优化：从38rpx减小到32rpx，更符合移动端标准 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  margin-bottom: 20rpx;
  line-height: 1.4;
  letter-spacing: 0.5rpx;

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

.topic-desc {
  font-size: 28rpx; /* 优化：从30rpx减小到28rpx，提升精致感 */
  color: #4a4a4a;
  line-height: 1.7;
  margin-bottom: 24rpx;
  letter-spacing: 0.3rpx;
}

.topic-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #8a8a8a;
  padding: 20rpx 0 0;
  border-top: 1rpx solid rgba(255, 105, 135, 0.1);

  .participants {
    /* 备用颜色方案，确保在微信小程序中显示 */
    color: #ff6b87 !important;
    font-weight: 600;
    background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);

    /* 渐变文字效果（如果支持） */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    /* 微信小程序兼容性处理 */
    @supports not (-webkit-background-clip: text) {
      color: #ff6b87 !important;
      background: none;
    }
  }

  .create-time {
    margin-left: 16rpx;
    opacity: 0.8;
  }
}

/* 店铺信息区域样式 */
.store-info-section {
  background: rgba(255, 255, 255, 0.95);
  margin: 24rpx 0;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  transform: translateZ(0);
  will-change: transform;
}

.store-header {
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 245, 240, 0.9) 100%);
  display: flex;
  // align-items: center;
}

.store-icon {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
}

.store-icon .icon {
  font-size: 40rpx;
    width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  color: #ffffff;
}

.store-content {
  flex: 1;
}

.store-title {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12rpx;
  line-height: 1.4;
  letter-spacing: 0.3rpx;
}

.store-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  letter-spacing: 0.2rpx;
}

.store-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #8a8a8a;
  letter-spacing: 0.2rpx;
}

.store-meta .participants {
  color: #ff6b87;
  font-weight: 500;
}

.store-meta .store-name {
  color: #8a8a8a;
  font-size: 26rpx;
  margin-left: 8rpx;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx; /* 减少垂直padding，与comment-detail.vue保持一致 */
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  margin: 0 0 24rpx 0;
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  left: 0;
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;
  right: 0;
  z-index: 10;
}

/* 评论数量区域样式 */
.comment-count-section {
  flex: 0 0 auto;
}

.comment-count-text {
  font-size: 28rpx; /* 优化：从30rpx减小到28rpx，更符合移动端标准 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  letter-spacing: 0.5rpx;

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

/* 筛选标签区域样式 */
.filter-tabs-section {
  flex: 0 0 auto;
}

.van-tabs__wrap {
  padding: 0;
  border-radius: 24rpx;
  overflow: hidden;
}

.van-tabs__nav {
  display: flex;
  justify-content: flex-end;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border-radius: 48rpx;
  padding: 6rpx; /* 减少内边距 */
  height: 70rpx; /* 减少高度 */
  box-shadow: inset 0 2rpx 8rpx rgba(255, 105, 135, 0.1);
  width: auto;
  min-width: 100rpx;
}

.van-tab {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 32rpx;
  margin: 0 4rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 58rpx; /* 减少高度 */
  min-width: 80rpx;
  padding: 0 14rpx; /* 减少水平内边距 */
}

.van-tab--active {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
  transform: translateY(-2rpx) scale(1.02);
}

.van-tab--active .van-tab__text {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.van-tab__text {
  color: #8a8a8a;
  font-size: 24rpx; /* 减小字体大小 */
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
  white-space: nowrap;
}



/* 评论列表容器 */
.comment-list-container {
  width: 100%;
}

.comment-list {
  width: 100%;
  padding: 0 0 32rpx 0;

  .empty-image {
    width: 280rpx;
    height: 280rpx;
    opacity: 0.6;
    border-radius: 24rpx;
  }
}

.comment-item {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  border-radius: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 优化过渡效果，只对transform进行过渡 */
  transition: transform 0.2s ease;
  width: 100%;
  position: relative;
  overflow: hidden;
  /* 启用GPU加速 */
  transform: translateZ(0);
  will-change: transform;

  &:active {
    transform: translateY(-2rpx) scale(0.98);
    box-shadow: 0 12rpx 36rpx rgba(255, 105, 135, 0.15);
  }
}

.user-avatar {
  position: relative;
  margin-right: 28rpx;
}

.user-avatar image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 135, 0.15);
  transition: all 0.3s ease;
}

.user-avatar:active image {
  transform: scale(0.95);
}

.comment-content {
  flex: 1;
}

.user-info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx; /* 优化：从30rpx减小到28rpx，更符合移动端用户名标准 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #4a4a4a;
  background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
  display: flex;
  align-items: center;
  letter-spacing: 0.3rpx;

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #4a4a4a !important;
    background: none;
  }
}

.user-level {
  font-size: 22rpx;
  color: #ffffff !important;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  margin-left: 16rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  /* 确保文字显示，移除可能导致兼容性问题的属性 */
  -webkit-text-fill-color: initial;
  -webkit-background-clip: initial;
  background-clip: initial;
}

.time {
  font-size: 24rpx;
  color: #8a8a8a;
  margin-top: 8rpx;
  font-weight: 400;
  opacity: 0.8;
}

.like-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);

  &:active {
    background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);
  }
}

.like-btn text {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

/* 心形图标样式 - 性能优化版本 */
.u-icon__icon.uicon-heart-fill,
.u-icon__icon.uicon-heart {
  font-weight: bold;
  transform: scale(1.1);
  /* 优化过渡效果，缩短时长并只对transform进行过渡 */
  transition: transform 0.2s ease;
  color: #ff6b87;
}

.u-icon__icon.uicon-heart-fill {
  /* 移除复杂的心跳动画，提升性能 */
  color: #ff6b87;
}

.text {
  font-size: 30rpx; /* 优化：从32rpx减小到30rpx，更符合移动端正文标准 */
  line-height: 1.8;
  margin-bottom: 20rpx;
  color: #4a4a4a;
  word-break: break-all;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}

.expand-btn {
  color: #ff6b87;
  font-size: 26rpx; /* 优化：从28rpx减小到26rpx，按钮文字更精致 */
  display: inline-block;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;

  &:active {
    background: rgba(255, 107, 135, 0.2);
    transform: scale(0.95);
  }
}

.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.reply-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);

  &:active {
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
    transform: scale(0.95);
  }
}

.reply-btn image {
  width: 36rpx;
  height: 36rpx;
  filter: hue-rotate(320deg) saturate(1.2);
}

.reply-btn text {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

.more-btn {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);

  &:active {
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
    transform: scale(0.9);
  }
}

.more-btn image {
  width: 100%;
  height: 100%;
  filter: hue-rotate(320deg) saturate(1.2);
}

.reply-preview {
  margin-top: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
  border-radius: 20rpx;
  padding: 28rpx;
  border: 1rpx solid rgba(255, 107, 135, 0.1);
  backdrop-filter: blur(10rpx);
}

.reply-item {
  font-size: 26rpx;
  margin-bottom: 16rpx;
  line-height: 1.7;
  color: #6a6a6a;
  letter-spacing: 0.3rpx;
}

.reply-nickname {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87 !important;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

.reply-to {
  color: #ff8e53;
  margin: 0 8rpx;
  font-weight: 600;
}

.reply-content {
  color: #4a4a4a;
  font-weight: 400;
}

.view-more {
  font-size: 26rpx;
  color: #ff6b87;
  text-align: right;
  margin-top: 16rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  display: inline-block;
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;

  &:active {
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
    transform: scale(0.95);
  }
}

/* 确保评论列表内容底部有足够空间 */
.comment-list-bottom-space {
  height: 160rpx; /* 比输入框高度大一些 */
}

/* 加载更多状态样式 - 小红书风格 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 0;

  .loading-text {
    margin-left: 24rpx;
    font-size: 30rpx;
    color: #ff6b87;
    font-weight: 500;
    letter-spacing: 0.5rpx;
  }
}

/* 骨架屏加载状态样式 */
.loading-more-skeleton {
  padding: 0;

  /* 骨架屏淡入动画 */
  animation: skeletonFadeIn 0.3s ease-out;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 0;

  text {
    font-size: 30rpx;
    color: #b0b0b0;
    font-weight: 500;
    letter-spacing: 0.5rpx;
  }
}

/* 评论列表的底部留白空间 */
.bottom-space {
  height: 200rpx;
  width: 100%;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;

  .loading-text {
    margin-top: 24rpx;
    font-size: 30rpx;
    color: #ff6b87;
    font-weight: 500;
    letter-spacing: 0.5rpx;
  }
}

.empty-tip {
  padding: 160rpx 40rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
  border-radius: 32rpx;
  margin: 24rpx;
}

.empty-text {
  font-size: 32rpx; /* 优化：从36rpx减小到32rpx，空状态文字更合理 */
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  margin-top: 32rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

.empty-subtext {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-top: 16rpx;
  font-weight: 400;
}

.empty-action {
  margin-top: 32rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #a8727e 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  }

  text {
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 600;
  }
}

/* 蒙版层样式 */
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  opacity: 0;
  animation: maskFadeIn 0.3s ease-out forwards;
}

.input-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background: #ffffff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid #f0f0f0;
  transition: bottom 0.3s ease-in-out;
}

/* 回复状态指示器样式 - 性能优化版本 */
.reply-indicator {
  background: rgba(255, 107, 135, 0.1);
  border-bottom: 1rpx solid rgba(255, 107, 135, 0.15);
  padding: 16rpx 32rpx;
  /* 移除复杂动画，使用简单的透明度过渡 */
  opacity: 1;
  transition: opacity 0.2s ease;
}

.reply-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reply-text {
  font-size: 28rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.3rpx;
  /* 小红书风格渐变文字 */
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

.cancel-reply-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 107, 135, 0.15);
  border-radius: 50%;
  font-size: 24rpx;
  color: #ff6b87;
  font-weight: bold;
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 107, 135, 0.25);
    transform: scale(0.9);
  }
}

/* 回复指示器入场动画 */
@keyframes replyIndicatorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reply-popup {
  padding: 30rpx;
  background-color: #fff;
  /* 确保弹窗能够平滑跟随键盘调整 - 使用transform */
  transition: transform 0.3s ease-in-out;
  /* 确保transform生效 */
  position: relative;
  z-index: 1;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.reply-header text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 自定义回复弹窗样式已移除，改为使用底部主输入框进行回复 */

/* 弹窗入场动画已移除，保留键盘适配过渡效果 */

/* 蒙版层淡入动画 */
@keyframes maskFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 添加心跳动画 */
@keyframes heartBeat {
  0% {
    transform: scale(1.1);
  }
  25% {
    transform: scale(1.4) rotate(-5deg);
  }
  50% {
    transform: scale(1.6) rotate(5deg);
  }
  75% {
    transform: scale(1.3) rotate(-2deg);
  }
  100% {
    transform: scale(1.1) rotate(0deg);
  }
}

/* 更多操作弹窗样式 */
.action-popup {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  padding: 32rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  margin: 0 24rpx;
  background: #ffffff;
  transition: all 0.3s ease;

  &:active {
    background: #f8fafc;
    transform: scale(0.98);
  }
}

.action-icon {
  width: 44rpx;
  height: 44rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon image {
  width: 100%;
  height: 100%;
}

/* 第一个action-item的样式 */
.action-item.reply {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
}

/* 第二个action-item的样式 */
.action-item.copy {
  margin-bottom: 24rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}

/* 第三个action-item的样式 */
.action-item.report {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
}

/* 最后一个action-item的样式 */
.action-item.block {
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}

.action-item text {
  font-size: 28rpx;
  color: #334155;
  font-weight: 500;
}

.action-cancel {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-top: 20rpx;
  border-radius: 20rpx;
}

.action-cancel text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.action-cancel:active {
  background-color: #f5f5f5;
}

/* 小红书风格动画效果 */
@keyframes heartBeat {
  0% {
    transform: scale(1.2);
  }
  14% {
    transform: scale(1.4);
  }
  28% {
    transform: scale(1.2);
  }
  42% {
    transform: scale(1.4);
  }
  70% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1.2);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 性能优化：移除评论项入场动画，避免滚动时重复触发导致卡顿 */

/* 骨架屏动画 */
@keyframes skeletonFadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 渐变文字效果 - 微信小程序兼容版本 */
.gradient-text {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);

  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* 微信小程序兼容性处理 */
  @supports not (-webkit-background-clip: text) {
    color: #ff6b87 !important;
    background: none;
  }
}

/* 毛玻璃效果 */
.glass-effect {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

/* 悬浮阴影效果 */
.floating-shadow {
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);
  transition: box-shadow 0.3s ease;
}

.floating-shadow:hover {
  box-shadow: 0 12rpx 48rpx rgba(255, 105, 135, 0.2);
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .comment-item {
    padding: 28rpx;
    margin-bottom: 16rpx;
  }

  .topic-title {
    font-size: 30rpx; /* 小屏幕优化：进一步减小标题字体 */
  }

  .text {
    font-size: 28rpx; /* 小屏幕优化：进一步减小正文字体 */
  }

  /* 小屏幕下筛选栏适配 */
  .filter-bar {
    padding: 16rpx 24rpx; /* 进一步减少小屏幕下的padding */
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx; /* 减少间距 */
  }

  .comment-count-text {
    font-size: 24rpx; /* 小屏幕优化：进一步减小评论数量字体 */
  }

  .van-tabs__nav {
    min-width: 240rpx;
    height: 60rpx; /* 小屏幕下进一步减少高度 */
    padding: 4rpx; /* 减少内边距 */
  }

  .van-tab {
    height: 52rpx; /* 小屏幕下减少标签高度 */
    padding: 0 12rpx; /* 减少水平内边距 */
  }

  .van-tab__text {
    font-size: 22rpx; /* 小屏幕下进一步减小字体 */
  }
}
</style>