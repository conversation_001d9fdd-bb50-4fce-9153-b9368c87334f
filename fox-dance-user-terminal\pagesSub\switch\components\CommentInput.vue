<template>
  <view class="comment-input-box">
    <view class="input-container">
              <textarea 
        v-model="inputText" 
        :placeholder="placeholder" 
        @input="onInput"
        @confirm="onSend"
        @focus="onFocus"
        @blur="onBlur"
        :maxlength="maxLength"
        :focus="autoFocus"
        :auto-blur="true"
        :show-confirm-bar="false"
        :cursor-spacing="10"
        class="input-textarea"
        :adjust-position="false"
        confirm-type="send"
        ref="textareaRef"
        :style="{ height: textareaHeight + 'px' }"
      />
      <!-- 隐藏的镜像div，用于测量文本高度 -->
      <view class="measure-box" ref="measureBox">{{inputText || placeholder}}</view>
      <template v-if="useImageButton">
        <!-- <image src="/static/icon/biaoqingbao.png" mode="aspectFill" class="biaoqingbao-btn-inside" @tap="showEmoji"></image> -->
        <image src="/static/icon/send.png" mode="aspectFill" class="send-btn-inside" @tap="onSend"></image>
      </template>
      <template v-else>
        <text class="text-btn-inside" :class="{'disabled': !inputText.trim()}" @tap="onSend">{{ buttonText }}</text>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CommentInput',
  props: {
    // 输入框提示文字
    placeholder: {
      type: String,
      default: '说点什么...'
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: '发送'
    },
    // 是否使用图片按钮
    useImageButton: {
      type: Boolean,
      default: false
    },
    // 最大字符数
    maxLength: {
      type: Number,
      default: 1000
    },
    // 初始值
    value: {
      type: String,
      default: ''
    },
    // 最小高度
    minHeight: {
      type: Number,
      default: 40
    },
    // 最大高度
    maxHeight: {
      type: Number,
      default: 120
    }
  },
  data() {
    return {
      inputText: this.value,
      textareaHeight: this.minHeight,
      baseLineHeight: 40, // 基础行高，单位px
      autoFocus: false // 控制自动获取焦点
    }
  },
  watch: {
    value(newVal) {
      this.inputText = newVal;
      this.$nextTick(() => {
        this.adjustHeight();
      });
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.adjustHeight();
    });
  },
  methods: {
    // 调整输入框高度
    adjustHeight() {
      // 使用选择器查询获取镜像div的高度
      const query = uni.createSelectorQuery().in(this);
      query.select('.measure-box').boundingClientRect(data => {
        if (data) {
          // 获取文本实际高度
          const scrollHeight = data.height;
          
          // 计算应该设置的高度（不小于最小高度，不大于最大高度）
          let newHeight = Math.max(this.minHeight, scrollHeight);
          newHeight = Math.min(newHeight, this.maxHeight);
          
          // 如果高度有变化，则更新
          if (this.textareaHeight !== newHeight) {
            this.textareaHeight = newHeight;
          }
        }
      }).exec();
    },
    
    onInput() {
      // 限制字符长度
      if (this.inputText.length > this.maxLength) {
        this.inputText = this.inputText.slice(0, this.maxLength);
        uni.showToast({
          title: `评论字数不能超过${this.maxLength}字`,
          icon: 'none'
        });
      }
      
      // 调整高度
      this.$nextTick(() => {
        this.adjustHeight();
      });
      
      // 向父组件发送输入内容
      this.$emit('input', this.inputText);
    },
    
    onSend() {
      if (!this.inputText.trim()) return;
      
      // 向父组件发送提交事件
      this.$emit('send', this.inputText);
    },
    // 清空输入框
    clear() {
      this.inputText = '';
      this.textareaHeight = this.minHeight;
      this.$emit('input', '');
    },
    // 设置焦点
    focus() {
      // 先重置再设置 autoFocus，确保每次都能触发 focus 变更
      this.autoFocus = false;

      // 使用 nextTick 确保视图已更新
      this.$nextTick(() => {
        this.autoFocus = true;

        // 微信小程序需要使用额外方法确保聚焦
        setTimeout(() => {
          // 微信小程序使用选择器进行聚焦
          // #ifdef MP-WEIXIN
          const query = uni.createSelectorQuery().in(this);
          query.select('.input-textarea').fields({
            properties: ['focus'],
            context: true
          }, res => {
            if (res && res.context) {
              res.context.focus({
                success: () => {
                  console.log('微信小程序设置焦点成功');
                },
                fail: (err) => {
                  console.error('微信小程序设置焦点失败:', err);
                  // 失败后尝试原生方法
                  const inputComponent = this.$refs.textareaRef;
                  if (inputComponent) {
                    inputComponent.focus();
                  }
                }
              });
            }
          }).exec();
          // #endif

          // H5、App 和其他平台
          // #ifndef MP-WEIXIN
          const inputComponent = this.$refs.textareaRef;
          if (inputComponent) {
            // 使用 focus() 方法
            inputComponent.focus();
          }
          // #endif
        }, 150);
      });
    },

    // 失去焦点
    blur() {
      // 重置autoFocus状态
      this.autoFocus = false;

      // 微信小程序使用选择器进行失焦
      // #ifdef MP-WEIXIN
      const query = uni.createSelectorQuery().in(this);
      query.select('.input-textarea').fields({
        properties: ['blur'],
        context: true
      }, res => {
        if (res && res.context) {
          res.context.blur({
            success: () => {
              console.log('微信小程序失去焦点成功');
            },
            fail: (err) => {
              console.error('微信小程序失去焦点失败:', err);
            }
          });
        }
      }).exec();
      // #endif

      // H5、App 和其他平台
      // #ifndef MP-WEIXIN
      const inputComponent = this.$refs.textareaRef;
      if (inputComponent) {
        inputComponent.blur();
      }
      // #endif
    },
    // 输入框获取焦点
    onFocus(e) {
      console.log('输入框获得焦点');
      this.$emit('focus', e);
    },
    // 输入框失去焦点
    onBlur(e) {
      this.$emit('blur', e);
      // 重置autoFocus，确保下次可以再次触发聚焦
      this.autoFocus = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.comment-input-box {
  display: flex;
  padding: 0 30rpx;
  background-color: #fff;
  //border-top: 1rpx solid #eee;
  //box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
  //border-radius: 20rpx;
  
  .input-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    
    .input-textarea {
      flex: 1;
      min-height: 80rpx;
      background: #f8fafc;
      border-radius: 24rpx;
      padding: 20rpx 120rpx 20rpx 30rpx;
      margin: 20rpx 0;
      font-size: 28rpx;
      border: 1rpx solid #e2e8f0;
      width: 100%;
      box-sizing: border-box;
      line-height: 40rpx;
      overflow-y: auto;
      color: #1e293b;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
        background: #ffffff;
      }
    }
    
    // 隐藏的镜像div，用于测量文本高度
    .measure-box {
      position: absolute;
      visibility: hidden;
      width: calc(100% - 150rpx); // 减去按钮和padding的空间
      font-size: 28rpx;
      line-height: 40rpx;
      padding: 20rpx 0;
      white-space: pre-wrap; // 保留换行符和空格
      word-wrap: break-word; // 允许长单词换行
      box-sizing: border-box;
      top: -9999px;
      left: -9999px;
      border: 1rpx solid transparent;
    }
    
    .send-btn-inside {
      position: absolute;
      right: 10rpx;
      top: 25rpx;
      width: 70rpx;
      height: 70rpx;
      border-radius: 30rpx;
      padding: 0;
      z-index: 2;
    }
    
    .text-btn-inside {
      position: absolute;
      right: 20rpx;
      top: 45rpx;
      font-size: 28rpx;
      color: #667eea;
      font-weight: 600;
      z-index: 2;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      background: rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;

      &:active {
        background: rgba(102, 126, 234, 0.2);
        transform: scale(0.95);
      }

      &.disabled {
        color: #94a3b8;
        background: rgba(148, 163, 184, 0.1);
      }
    }
  }
  .biaoqingbao-btn-inside {
    position: absolute;
    right: 80rpx;
    width: 55rpx;
    height: 55rpx;
    border-radius: 30rpx;
    margin-right: 10rpx;
  }
  
  // 移除原来的外部按钮样式
  // .send-btn {
  //   margin-left: 20rpx;
  //   
  //   &[disabled] {
  //     background: linear-gradient(135deg, #a0cfbb, #a0cfbb);
  //     color: #fff;
  //     box-shadow: none;
  //   }
  // }
  // 
  // button.send-btn {
  //   width: 140rpx;
  //   height: 80rpx;
  //   border-radius: 40rpx;
  //   font-size: 28rpx;
  //   padding: 0;
  //   line-height: 80rpx;
  //   background: linear-gradient(135deg, #4b8df8, #3b7ff2);
  //   color: #fff;
  //   box-shadow: 0 4rpx 8rpx rgba(75, 141, 248, 0.3);
  //   transition: all 0.3s;
  //   
  //   &:active {
  //     transform: translateY(2rpx);
  //     box-shadow: 0 2rpx 4rpx rgba(75, 141, 248, 0.3);
  //   }
  // }
  // 
  // image.send-btn {
  //   width: 80rpx;
  //   height: 80rpx;
  //   border-radius: 40rpx;
  //   padding: 0;
  //   line-height: 80rpx;
  // }
}
</style> 