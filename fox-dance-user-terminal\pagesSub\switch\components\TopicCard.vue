<template>
	<view class="topic-card" @click="goToDetail">
		<view class="topic-content" :class="{ 'no-image': !hasCoverImage }">
			<!-- 左侧内容区域 -->
			<view class="topic-left">
				<view class="topic-title">
					<image class="topic-icon" src="/static/icon/topic-title.png" mode="aspectFill"></image>
					<text class="title-text">{{ topic.title }}</text>
				</view>
				<view class="topic-desc">{{ topic.description }}</view>
				<view class="topic-stat">
					<view class="dot"></view>
					<text class="participants">{{ topic.participants }}人参与此话题</text>
					<text class="time" v-if="topic.createTime">· {{ formatTime(topic.createTime) }}</text>
					<image class="arrow" src="/static/icon/right.png" mode="aspectFill"></image>
				</view>
			</view>

			<!-- 右侧封面图区域（仅在有图片时显示） -->
			<view class="topic-right" v-if="hasCoverImage">
				<image
					class="cover-image"
					:src="topic.coverImage"
					mode="aspectFill"
					@error="handleImageError"
					:lazy-load="true"
				></image>
			</view>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import relativeTime from 'dayjs/plugin/relativeTime';
	import 'dayjs/locale/zh-cn';
	
	dayjs.extend(relativeTime);
	dayjs.locale('zh-cn');
	
	export default {
		name: 'TopicCard',
		props: {
			topic: {
				type: Object,
				required: true
			}
		},
		computed: {
			// 判断是否有封面图
			hasCoverImage() {
				return this.topic.coverImage &&
					   this.topic.coverImage.trim() !== '' &&
					   this.topic.coverImage !== 'null' &&
					   this.topic.coverImage !== 'undefined';
			}
		},
		methods: {
			goToDetail() {
				uni.navigateTo({
					url: '/pagesSub/switch/comment?topicId=' + this.topic.id + '&content_type=topic'
				});
			},
			formatTime(timeString) {
				if (!timeString) return '';
				return dayjs(timeString).fromNow();
			},
			handleImageError() {
				console.warn('话题封面图加载失败:', this.topic.coverImage);
				// 可以在这里设置默认图片或隐藏图片
			}
		}
	}
</script>

<style lang="scss" scoped>
	.topic-card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
		transition: all 0.3s ease;

		&:active {
			transform: translateY(-4rpx);
			box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
		}

		.topic-content {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			gap: 24rpx;

			// 无图片时的布局优化
			&.no-image {
				.topic-left {
					width: 100%;
					flex: none;

					.topic-desc {
						// 无图片时描述文字可以显示更多行
						-webkit-line-clamp: 4;
						max-height: 120rpx;
					}

					.topic-stat {
						margin-top: 24rpx; // 增加间距，让布局更舒适
					}
				}
			}

			.topic-left {
				flex: 1;
				min-width: 0; // 防止文字溢出
				
				.topic-title {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;
					
					.topic-icon {
						width: 45rpx;
						height: 45rpx;
						margin-right: 10rpx;
						border-radius: 50%;
					}
					
					.title-text {
						font-size: 32rpx;
						color: #333333;
						font-weight: bold;
						line-height: 1.3;
					}
				}
				
				.topic-desc {
					font-size: 26rpx;
					color: #64748b;
					margin-bottom: 20rpx;
					line-height: 1.6;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					word-break: break-all;
				}
				
				.topic-stat {
					display: flex;
					align-items: center;
					
					.dot {
						width: 10rpx;
						height: 10rpx;
						background-color: #ff6b87;
						border-radius: 50%;
						margin-right: 10rpx;
					}
					
					.participants {
						font-size: 24rpx;
						color: #999999;
					}
					
					.time {
						font-size: 24rpx;
						color: #999999;
						margin-left: 8rpx;
					}
					
					.arrow {
						width: 20rpx;
						height: 20rpx;
						margin-left: 4rpx;
					}
				}
			}

			// 右侧封面图区域
			.topic-right {
				width: 200rpx;
				height: 150rpx;
				flex-shrink: 0;
				border-radius: 12rpx;
				overflow: hidden;
				background: #f8fafc;
				border: 1rpx solid #e2e8f0;

				.cover-image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}
	}
</style> 