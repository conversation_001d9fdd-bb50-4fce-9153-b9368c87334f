<template>
	<view class="container">
		<view class="search-bar">
			<view class="search-box">
				<u-icon name="search" size="32" color="#999"></u-icon>
				<input type="text" v-model="searchKeyword" placeholder="搜索话题" @confirm="handleSearch" @input="onSearchInput" />
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<u-icon name="close" size="28" color="#999"></u-icon>
				</view>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="sort-options">
				<view class="van-tabs">
					<view class="van-tabs__wrap">
						<view class="van-tabs__nav">
							<view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'new' }" @tap="changeSort('new')">
								<view class="van-tab__text">新帖</view>
							</view>
							<view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'hot' }" @tap="changeSort('hot')">
								<view class="van-tab__text">热帖</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="topic-list">
			<view v-if="loading" class="loading">
				<u-loading mode="flower" size="50"></u-loading>
				<text class="loading-text">加载中...</text>
			</view>
			<block v-else-if="topicList.length > 0">
				<topic-card 
					v-for="(topic, index) in topicList" 
					:key="index" 
					:topic="topic"
				></topic-card>
			</block>
			<view v-else class="empty-list">
				<image src="/static/icon/null.png" mode="" class="empty-image"></image>
				<text class="empty-text">暂无话题</text>
			</view>
		</view>
		<u-loadmore v-if="topicList.length > 0" :status="status" @loadmore="loadMore" />
		
		<!-- 添加浮动按钮 -->
		<view class="add-button" @click="goToAddTopic" v-if="userId == '24840' || userId == '18'">
			<text class="plus-icon">+</text>
		</view>
	</view>
</template>

<script>
	import TopicCard from '../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue';
	import topicApi from '@/config/topic.api.js';
	
	export default {
		components: {
			TopicCard
		},
		data() {
			return {
				userId: '', // 用户ID，从storage获取
				searchKeyword: '',
				status: 'loading',
				loading: true,
				page: 1,
				pageSize: 10,
				sortBy: 'new', // 默认排序方式为新帖
				sortOrder: 'descend', // 默认降序
				hasMore: true,
				topicList: []
			};
		},
		onLoad() {
			// 获取用户ID
			this.userId = uni.getStorageSync('userid') || '18';
			this.fetchTopicList();
		},
		methods: {
			clearSearch() {
				this.searchKeyword = '';
				this.fetchTopicList(true);
			},
			onSearchInput(e) {
				// 实时处理输入，去除不必要的空格
				if (this.searchKeyword) {
					this.searchKeyword = this.searchKeyword.replace(/\s+/g, ' ');
				}
			},
			fetchTopicList(reset = false) {
				if (reset) {
					this.page = 1;
					this.topicList = [];
					this.hasMore = true;
				}
				
				if (!this.hasMore && !reset) {
					this.status = 'nomore';
					return;
				}
				
				this.loading = this.page === 1;
				this.status = 'loading';
				
				// 构建请求参数
				const params = {
					current: this.page,
					pageSize: this.pageSize,
					sortField: this.sortBy,
					sortOrder: this.sortOrder
				};
				
				// 添加搜索关键词 - 确保关键词不为空且使用正确的格式
				if (this.searchKeyword && this.searchKeyword.trim()) {
					// 直接传递原始关键词，让后端处理
					params.title = this.searchKeyword.trim();

				}
				
				// 调用API获取话题列表
				topicApi.getTopicList(params).then(res => {
					if (res.code === 0 && res.data) {
						const { records, current, pages } = res.data;
						
						// 处理数据，转换字段名
						const formattedRecords = records.map(item => ({
							id: item.id,
							title: item.title,
							description: item.description,
							participants: item.commentUserCount || 0,
							createTime: item.createTime,
							coverImage: item.coverImage, // 话题封面图
							topicImages: item.topicImages || [] // 话题图片数组
						}));
						
						// 追加或替换数据
						if (reset || this.page === 1) {
							this.topicList = formattedRecords;
						} else {
							this.topicList = [...this.topicList, ...formattedRecords];
						}
						
						// 更新分页状态
						this.hasMore = current < pages;
						this.page++;
						this.status = this.hasMore ? 'loadmore' : 'nomore';
						

					} else {
						uni.showToast({
							title: res.message || '获取数据失败',
							icon: 'none'
						});
						this.status = 'loadmore';
					}
				}).catch(() => {
					uni.showToast({
						title: '网络请求错误',
						icon: 'none'
					});
					this.status = 'loadmore';
				}).finally(() => {
					this.loading = false;
					uni.stopPullDownRefresh();
				});
			},
			loadMore() {
				if (!this.hasMore) return;
				this.fetchTopicList();
			},
			handleSearch() {
				this.searchKeyword = this.searchKeyword.trim();
				this.fetchTopicList(true);
			},
			goToAddTopic() {
				uni.navigateTo({
					url: '/pagesSub/switch/add-topic'
				});
			},
			changeSort(type) {
				if (this.sortBy === type) return;
				this.sortBy = type;
				
				// 切换排序方式，重新加载数据
				this.fetchTopicList(true);
			}
		},
    // 添加分享到朋友圈的方法
    onShareTimeline() {
      return {
        title: 'Fox Dance话题广场'
      }
    },

    // 分享给好友
    onShareAppMessage() {
      return {
        title: 'Fox话题广场，快来看看有没有你想讨论的话题吧！',
        path: '/pagesSub/switch/topic-list'
      }
    },
		// 下拉刷新
		onPullDownRefresh() {
			this.fetchTopicList(true);
		},
		// 触底加载更多
		onReachBottom() {
			if (this.status !== 'nomore') {
				this.loadMore();
			}
		}
	}
</script>

<style lang="scss" scoped>
@import '@/pagesSub/styles/common.scss';

// 搜索栏样式已移至common.scss
	
// 筛选栏样式已移至common.scss
	.filter-bar {
		.sort-options {
			.van-tab {
				min-width: 180rpx;
				margin: 0 6rpx;

				&__text {
					font-size: 30rpx;
					padding: 0 24rpx;
				}
			}
		}
	}
	
	.topic-list {
		padding-bottom: 48rpx;
		// 加载状态和空状态样式已移至common.scss
	}
	
	.add-button {
		position: fixed;
		right: 40rpx;
		bottom: 120rpx;
		width: 120rpx;
		height: 120rpx;
		background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.4);
		z-index: 999;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		border: 3rpx solid rgba(255, 255, 255, 0.8);

		&:active {
			transform: scale(0.9);
			box-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.4);
		}

		.plus-icon {
			font-size: 68rpx;
			color: #ffffff;
			font-weight: bold;
			line-height: 1;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		}
	}
</style>