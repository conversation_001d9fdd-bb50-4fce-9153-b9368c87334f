<template>
	<view class="container">
		<view class="search-bar">
			<view class="search-box">
				<u-icon name="search" size="32" color="#999"></u-icon>
				<input type="text" v-model="searchKeyword" placeholder="搜索话题" @confirm="handleSearch" @input="onSearchInput" />
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<u-icon name="close" size="28" color="#999"></u-icon>
				</view>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="sort-options">
				<view class="van-tabs">
					<view class="van-tabs__wrap">
						<view class="van-tabs__nav">
							<view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'new' }" @tap="changeSort('new')">
								<view class="van-tab__text">新帖</view>
							</view>
							<view class="van-tab no-highlight" :class="{ 'van-tab--active': sortBy === 'hot' }" @tap="changeSort('hot')">
								<view class="van-tab__text">热帖</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="topic-list">
			<view v-if="loading" class="loading">
				<u-loading mode="flower" size="50"></u-loading>
				<text class="loading-text">加载中...</text>
			</view>
			<block v-else-if="topicList.length > 0">
				<topic-card 
					v-for="(topic, index) in topicList" 
					:key="index" 
					:topic="topic"
				></topic-card>
			</block>
			<view v-else class="empty-list">
				<image src="/static/icon/null.png" mode="" class="empty-image"></image>
				<text class="empty-text">暂无话题</text>
			</view>
		</view>
		<u-loadmore v-if="topicList.length > 0" :status="status" @loadmore="loadMore" />
		
		<!-- 添加浮动按钮 -->
		<view class="add-button" @click="goToAddTopic" v-if="userId == '24840' || userId == '18'">
			<text class="plus-icon">+</text>
		</view>
	</view>
</template>

<script>
	import TopicCard from '../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue';
	import topicApi from '@/config/topic.api.js';
	
	export default {
		components: {
			TopicCard
		},
		data() {
			return {
				userId: '', // 用户ID，从storage获取
				searchKeyword: '',
				status: 'loading',
				loading: true,
				page: 1,
				pageSize: 10,
				sortBy: 'new', // 默认排序方式为新帖
				sortOrder: 'descend', // 默认降序
				hasMore: true,
				topicList: []
			};
		},
		onLoad() {
			// 获取用户ID
			this.userId = uni.getStorageSync('userid') || '18';
			this.fetchTopicList();
		},
		methods: {
			clearSearch() {
				this.searchKeyword = '';
				this.fetchTopicList(true);
			},
			onSearchInput(e) {
				// 实时处理输入，去除不必要的空格
				if (this.searchKeyword) {
					this.searchKeyword = this.searchKeyword.replace(/\s+/g, ' ');
				}
			},
			fetchTopicList(reset = false) {
				if (reset) {
					this.page = 1;
					this.topicList = [];
					this.hasMore = true;
				}
				
				if (!this.hasMore && !reset) {
					this.status = 'nomore';
					return;
				}
				
				this.loading = this.page === 1;
				this.status = 'loading';
				
				// 构建请求参数
				const params = {
					current: this.page,
					pageSize: this.pageSize,
					sortField: this.sortBy,
					sortOrder: this.sortOrder
				};
				
				// 添加搜索关键词 - 确保关键词不为空且使用正确的格式
				if (this.searchKeyword && this.searchKeyword.trim()) {
					// 直接传递原始关键词，让后端处理
					params.title = this.searchKeyword.trim();
					// 使用searchTitle参数名可能更准确
					// params.searchTitle = this.searchKeyword.trim();
					console.log('搜索关键词(未处理):', params.title);
				}
				
				// 调用API获取话题列表
				topicApi.getTopicList(params).then(res => {
					console.log('话题列表API返回数据:', JSON.stringify(res));
					if (res.code === 0 && res.data) {
						const { records, total, size, current, pages } = res.data;
						
						// 处理数据，转换字段名
						const formattedRecords = records.map(item => ({
							id: item.id,
							title: item.title,
							description: item.description,
							participants: item.commentUserCount || 0,
							createTime: item.createTime,
							coverImage: item.coverImage, // 话题封面图
							topicImages: item.topicImages || [] // 话题图片数组
						}));
						
						// 追加或替换数据
						if (reset || this.page === 1) {
							this.topicList = formattedRecords;
						} else {
							this.topicList = [...this.topicList, ...formattedRecords];
						}
						
						// 更新分页状态
						this.hasMore = current < pages;
						this.page++;
						this.status = this.hasMore ? 'loadmore' : 'nomore';
						
						// 记录搜索结果
						if (this.searchKeyword) {
							console.log(`搜索"${this.searchKeyword}"结果: 找到${formattedRecords.length}条记录，总计${total}条`);
						}
					} else {
						uni.showToast({
							title: res.message || '获取数据失败',
							icon: 'none'
						});
						this.status = 'loadmore';
					}
				}).catch(err => {
					console.error('获取话题列表失败:', err);
					uni.showToast({
						title: '网络请求错误',
						icon: 'none'
					});
					this.status = 'loadmore';
				}).finally(() => {
					this.loading = false;
					uni.stopPullDownRefresh();
				});
			},
			loadMore() {
				if (!this.hasMore) return;
				this.fetchTopicList();
			},
			handleSearch() {
				// 执行搜索前先去除关键词前后空格
				this.searchKeyword = this.searchKeyword.trim();
				console.log('执行搜索，关键词:', this.searchKeyword);
				
				// 查看实际发送的数据
				const params = {
					current: this.page,
					pageSize: this.pageSize,
					sortField: this.sortBy,
					sortOrder: this.sortOrder,
					title: this.searchKeyword.trim()
				};
				
				console.log('实际发送的搜索参数:', JSON.stringify(params));
				
				// 重置页码并重新加载数据
				this.fetchTopicList(true);
			},
			goToAddTopic() {
				uni.navigateTo({
					url: '/pagesSub/switch/add-topic'
				});
			},
			changeSort(type) {
				if (this.sortBy === type) return;
				this.sortBy = type;
				
				// 切换排序方式，重新加载数据
				this.fetchTopicList(true);
			}
		},
    // 添加分享到朋友圈的方法
    onShareTimeline() {
      return {
        title: 'Fox Dance话题广场'
      }
    },

    // 分享给好友
    onShareAppMessage() {
      return {
        title: 'Fox话题广场，快来看看有没有你想讨论的话题吧！',
        path: '/pagesSub/switch/topic-list'
      }
    },
		// 下拉刷新
		onPullDownRefresh() {
			this.fetchTopicList(true);
		},
		// 触底加载更多
		onReachBottom() {
			if (this.status !== 'nomore') {
				this.loadMore();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 0 24rpx;
		background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
		min-height: 100vh;
	}

	.search-bar {
		padding: 24rpx 0;
		position: sticky;
		top: 0;
		z-index: 100;
		background: rgba(255, 238, 248, 0.95);
		backdrop-filter: blur(20rpx);

		.search-box {
			display: flex;
			align-items: center;
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(20rpx);
			border-radius: 32rpx;
			padding: 24rpx 32rpx;
			box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
			border: 1rpx solid rgba(255, 255, 255, 0.8);
			transition: all 0.3s ease;

			&:focus-within {
				box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
				border-color: rgba(255, 107, 135, 0.3);
				transform: translateY(-2rpx);
			}

			.u-icon {
				margin-right: 16rpx;
				color: #ff6b87;
			}

			input {
				flex: 1;
				font-size: 30rpx;
				color: #4a4a4a;
				font-weight: 400;
				letter-spacing: 0.3rpx;
			}
			
			.clear-icon {
				margin-left: 10rpx;
				padding: 10rpx;
				opacity: 0.7;
				transition: all 0.2s ease;
				
				&:active {
					opacity: 1;
					transform: scale(1.1);
				}
			}
		}
	}
	
	.filter-bar {
		padding: 20rpx 0 32rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;

		.sort-options {
			.van-tabs {
				position: relative;
				display: flex;
				justify-content: center;
				-webkit-tap-highlight-color: transparent;

				&__wrap {
					overflow: hidden;
					position: relative;
					padding: 0;
					border-radius: 48rpx;
				}

				&__nav {
					position: relative;
					display: flex;
					background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
					height: 96rpx;
					border-radius: 48rpx;
					user-select: none;
					box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
					padding: 8rpx;
					border: 1rpx solid rgba(255, 255, 255, 0.8);
					backdrop-filter: blur(20rpx);
				}
			}

			.van-tab {
				cursor: pointer;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 40rpx;
				min-width: 180rpx;
				height: 80rpx;
				margin: 0 6rpx;
				-webkit-tap-highlight-color: transparent;
				transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

				&__text {
					font-size: 30rpx;
					color: #8a8a8a;
					line-height: 1.2;
					padding: 0 24rpx;
					font-weight: 500;
					transition: all 0.3s ease;
					letter-spacing: 0.5rpx;
				}

				&--active {
					background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
					box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
					transform: translateY(-2rpx) scale(1.02);

					.van-tab__text {
						color: #ffffff;
						font-weight: 600;
						text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
						transform: scale(1.05);
					}
				}
			}
		}
	}
	
	.topic-list {
		padding-bottom: 48rpx;

		.loading {
			padding: 120rpx 0;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.loading-text {
				margin-top: 32rpx;
				font-size: 30rpx;
				color: #ff6b87;
				font-weight: 500;
				letter-spacing: 0.5rpx;
			}
		}

		.empty-list {
			padding: 160rpx 40rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
			border-radius: 32rpx;
			margin: 24rpx 0;

			.empty-image {
				width: 280rpx;
				height: 280rpx;
				opacity: 0.6;
				border-radius: 24rpx;
			}

			.empty-text {
				margin-top: 40rpx;
				font-size: 36rpx;
				background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
				font-weight: 600;
				letter-spacing: 0.5rpx;
			}
		}
	}
	
	.add-button {
		position: fixed;
		right: 40rpx;
		bottom: 120rpx;
		width: 120rpx;
		height: 120rpx;
		background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.4);
		z-index: 999;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		border: 3rpx solid rgba(255, 255, 255, 0.8);

		&:active {
			transform: scale(0.9);
			box-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.4);
		}

		.plus-icon {
			font-size: 68rpx;
			color: #ffffff;
			font-weight: bold;
			line-height: 1;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		}
	}
</style>