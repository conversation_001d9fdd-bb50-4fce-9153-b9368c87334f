<template>
  <view class="vote-container">
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="vote-title">{{ voteTitle }}</view>
      <view class="vote-info">{{ voteInfo }}</view>
    </view>

    <!-- 线路选择卡片 -->
    <view class="card dropdown-card">
      <view class="card-header">
        <text class="card-title">选择地铁线路</text>
      </view>
      <picker @change="handleLineChange" :value="selectedLineIndex" :range="metroLines" range-key="lineName">
        <view class="picker">
          <text v-if="metroLines.length > 0" class="picker-text">{{ metroLines[selectedLineIndex].lineName }}</text>
          <text v-else class="picker-loading">加载中...</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 提示信息卡片 -->
    <view class="card notice-card">
      <view class="notice-content">
        <image src="/static/images/icon78.png" class="notice-icon"></image>
        <view class="notice-text">
          <text class="notice-title">投票须知</text>
          <text class="notice-desc">仅限广州地区用户参与投票，需要获取位置权限</text>
        </view>
      </view>
    </view>

    <!-- 投票列表 -->
    <view class="card vote-list-card" v-if="sortedStations.length > 0">
      <view class="card-header">
        <text class="card-title">站点排行榜</text>
        <text class="total-votes">总票数: {{ getTotalVotes() }}</text>
      </view>

      <view class="vote-list">
        <view
          v-for="(item, idx) in sortedStations"
          :key="item.station"
          class="vote-item"
          :class="{
            voted: isVotedStation(item.station),
            selected: selected !== -1 && currentStations[selected] === item.station,
            'rank-top': idx < 3
          }"
          @click="select(currentStations.indexOf(item.station))"
        >
          <!-- 排名标识 -->
          <view class="rank-badge" v-if="idx < 3">
            <text class="rank-number">{{ idx + 1 }}</text>
          </view>
          <view class="rank-number-normal" v-else>{{ idx + 1 }}</view>

          <!-- 站点信息 -->
          <view class="station-info">
            <text class="station-name">{{ item.station }}</text>
            <view class="vote-progress">
              <view class="vote-bar">
                <view
                  class="vote-bar-inner"
                  :style="{
                    width: getPercent(item.votes) + '%',
                    animationDelay: (idx * 0.1) + 's'
                  }"
                ></view>
              </view>
              <text class="vote-percentage">{{ getPercent(item.votes) }}%</text>
            </view>
          </view>

          <!-- 票数显示 -->
          <view class="vote-count-container">
            <text class="vote-count" :class="{ 'count-animate': animatingStations.includes(item.station) }">
              {{ item.votes }}
            </text>
            <text class="vote-unit">票</text>
          </view>

          <!-- 选中状态指示器 -->
          <view class="selection-indicator" v-if="selected !== -1 && currentStations[selected] === item.station">
            <text class="check-icon">✓</text>
          </view>
        </view>

        <!-- 更多按钮 -->
        <view
          v-if="isAllStationsSelected && displayMode !== 'all' && allStationsData && allStationsData.length > (displayMode === 'more' ? 30 : 15)"
          class="more-button"
          @click="toggleShowMoreStations"
        >
          <text class="more-text">{{ displayMode === 'more' ? '查看全部' : '查看更多' }}</text>
          <text class="more-icon" :class="{ 'icon-rotate': displayMode === 'more' }">▼</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="card loading-card" v-else>
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载站点信息中...</text>
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="bottom-section">
      <view class="vote-tip">{{ voteTips }}</view>

      <view class="vote-action">
        <button
          class="vote-btn"
          :class="{
            'btn-disabled': remainingVotes <= 0 || selected === -1,
            'btn-success': voteSuccess
          }"
          :disabled="remainingVotes <= 0 || selected === -1"
          @click="vote"
        >
          <text class="btn-text">{{ getBtnText() }}</text>
          <view class="btn-ripple" v-if="voteSuccess"></view>
        </button>

        <view class="remaining-votes">
          <text class="votes-label">剩余投票次数</text>
          <text class="votes-count">{{ remainingVotes }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 地铁线路信息
      metroLines: [],
      selectedLineIndex: 0, // 默认选择第一项，即"全部线路"
      selectedLineId: -1, // 默认选择ID为-1的线路，表示"全部线路"
      currentLineData: null,
      selected: -1,
      remainingVotes: 0, // 剩余投票次数
      voteRecords: [], // 存储从服务器获取的用户投票记录
      debugShown: false, // 调试标记
      locationAuth: false, // 位置授权状态
      // 投票基本信息
      voteTitle: '你希望下一家Fox新店开在哪里',
      voteInfo: '排名靠前的投票会看地铁附近是否有合适的场地才会最终确定开',
      voteTips: '每人仅限投票一次，fox会员可投两次',
      // 添加全部站点数据
      allStationsData: null,
      isAllStationsSelected: true, // 默认选中全部站点
      displayMode: 'initial', // 显示模式: initial(初始15条), more(30条), all(全部)
      // 新增UI状态
      voteSuccess: false, // 投票成功状态
      animatingStations: [], // 正在动画的站点列表
    }
  },

  computed: {
    // 计算当前选中线路的站点列表
    currentStations() {
      if (this.isAllStationsSelected) {
        return this.allStationsData ? this.allStationsData.map(item => item.station) : []
      }
      
      if (this.currentLineData) {
        return this.currentLineData.stations || []
      }
      return []
    },

    // 计算当前选中线路的投票数据
    currentVoteCounts() {
      if (this.isAllStationsSelected) {
        // 如果选择了全部，构建一个站点到投票数的映射对象
        const votesMap = {}
        if (this.allStationsData) {
          this.allStationsData.forEach(item => {
            votesMap[item.station] = item.votes
          })
        }
        return votesMap
      }
      
      if (this.currentLineData) {
        return this.currentLineData.voteCounts || {}
      }
      return {}
    },
    
    // 按投票数量排序的站点列表
    sortedStations() {
      if (this.isAllStationsSelected) {
        // 在全部线路视图中，根据displayMode决定展示多少条数据
        if (this.allStationsData && this.allStationsData.length > 0) {
          if (this.displayMode === 'initial' && this.allStationsData.length > 15) {
            return this.allStationsData.slice(0, 15) // 显示前15条
          } else if (this.displayMode === 'more' && this.allStationsData.length > 30) {
            return this.allStationsData.slice(0, 30) // 显示前30条
          }
        }
        return this.allStationsData || []
      }
      
      // 如果没有站点数据，返回空数组
      if (!this.currentStations.length) return []
      
      // 创建站点和投票数的组合数组
      const stationsWithVotes = this.currentStations.map(station => ({
        station: station,
        votes: this.currentVoteCounts[station] || 0
      }))
      
      // 按投票数降序排序
      return stationsWithVotes.sort((a, b) => b.votes - a.votes)
    }
  },

  mounted() {
    // 获取投票信息
    this.getVoteInfo(1)
    
    // 获取所有地铁线路
    this.getAllMetroLines()

    // 检查URL参数是否带有指定的线路ID
    const query = uni.getLaunchOptionsSync().query;
    if (query && query.lineId && query.lineId !== '-1') {
      this.selectedLineId = parseInt(query.lineId) || -1;
      this.isAllStationsSelected = false;
    }

    // 根据选择的线路ID决定加载哪种数据
    if (this.selectedLineId === -1) {
      // 加载全部站点排名
      this.buildAllStationsRanking();
    } else {
      // 加载特定线路
      this.getMetroLineById(this.selectedLineId);
    }

    // 检查用户是否登录
    if (this.checkLogin()) {
      // 获取用户剩余投票次数
      this.getRemainingVotes()

      // 获取用户投票记录
      this.getUserVoteRecords()
    }
    
    // 检查位置权限
    this.checkLocationPermission()
  },

  methods: {
    // 切换显示更多站点
    toggleShowMoreStations() {
      if (this.displayMode === 'initial') {
        // 从初始状态切换到显示更多
        this.displayMode = 'more'
      } else if (this.displayMode === 'more') {
        // 从显示更多切换到显示全部
        this.displayMode = 'all'
      }
    },
    
    // 获取投票信息
    async getVoteInfo(id) {
      const BASE_URL = this.getBaseUrl()
      try {
        const response = await uni.request({
          url: `${BASE_URL}/api/vote-info/${id}`,
          method: 'GET'
        })
        console.log('获取投票信息:', response)
        if (response.data && response.data.code === 0) {
          const voteInfo = response.data.data
          // 更新页面显示内容
          this.voteTitle = voteInfo.title || this.voteTitle
          this.voteInfo = voteInfo.info || this.voteInfo
          this.voteTips = voteInfo.tips || this.voteTips
        } else {
          console.error('获取投票信息失败:', response.data)
        }
      } catch (error) {
        console.error('获取投票信息异常:', error)
      }
    },

    // 根据环境配置API基础URL
    getBaseUrl() {
      // #ifdef MP-WEIXIN
      return 'https://vote.foxdance.com.cn' // 替换为实际的HTTPS域名
      // #endif

      // 非小程序环境使用本地开发地址
      return 'https://vote.foxdance.com.cn'
    },

    // 获取用户ID和token
    getUserInfo() {
      const token = uni.getStorageSync('token')
      const userId = uni.getStorageSync('userid')
      return {
        token,
        userId
      }
    },

    // 检查用户是否已登录
    checkLogin() {
      const { token } = this.getUserInfo()
      if (!token) {
        uni.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        })

        // 延迟跳转到登录页
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }, 1500)

        return false
      }
      return true
    },

    // 获取用户剩余投票次数
    async getRemainingVotes() {
      // 检查用户是否登录
      if (!this.checkLogin()) return

      const { userId } = this.getUserInfo()
      if (!userId) return

      const BASE_URL = this.getBaseUrl()
      try {
        const response = await uni.request({
          url: `${BASE_URL}/api/ba-user/remaining-votes/${userId}`,
          method: 'GET'
        })

        if (response.data && response.data.code === 0) {
          this.remainingVotes = response.data.data
        } else {
          console.log('userId:', userId)
          console.error('获取剩余投票次数失败:', response.data)
          this.remainingVotes = 0
        }
      } catch (error) {
        console.error('获取剩余投票次数异常:', error)
        this.remainingVotes = 0
      }
    },

    // 获取所有地铁线路
    async getAllMetroLines() {
      const BASE_URL = this.getBaseUrl()
      try {
        const response = await uni.request({
          url: `${BASE_URL}/api/metro-lines`,
          method: 'GET'
        })

        if (response.data && response.data.code === 0) {
          // 添加"全部"选项
          const allOption = {
            id: -1, // 使用一个特殊值表示全部
            lineName: '全部线路'
          }
          this.metroLines = [allOption, ...response.data.data]
          
          // 如果有已选择的线路ID，找到对应的索引
          if (this.selectedLineId !== -1) {
            const index = this.metroLines.findIndex(line => line.id === this.selectedLineId)
            if (index !== -1) {
              this.selectedLineIndex = index
              this.isAllStationsSelected = false
            }
          }
          
          // 获取所有站点的排名数据（无论是否默认选择全部）
          this.buildAllStationsRanking()
        } else {
          uni.showToast({
            title: '获取地铁线路失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '网络请求异常',
          icon: 'none'
        })
      }
    },

    // 根据ID获取地铁线路详情
    async getMetroLineById(id) {
      const BASE_URL = this.getBaseUrl()
      try {
        const response = await uni.request({
          url: `${BASE_URL}/api/metro-lines/${id}`,
          method: 'GET'
        })

        if (response.data && response.data.code === 0) {
          this.currentLineData = response.data.data
        } else {
          uni.showToast({
            title: '获取线路详情失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '网络请求异常',
          icon: 'none'
        })
      }
    },
    
    // 获取所有站点的排名数据
    async buildAllStationsRanking() {
      // 显示加载提示
      uni.showLoading({
        title: '处理数据...'
      })
      
      try {
        const BASE_URL = this.getBaseUrl()
        // 先获取所有线路
        const linesResponse = await uni.request({
          url: `${BASE_URL}/api/metro-lines`,
          method: 'GET'
        })
        
        if (linesResponse.data && linesResponse.data.code === 0) {
          const lines = linesResponse.data.data
          let allStations = []
          
          // 对每条线路获取详细信息
          for (const line of lines) {
            const lineDetailResponse = await uni.request({
              url: `${BASE_URL}/api/metro-lines/${line.id}`,
              method: 'GET'
            })
            
            if (lineDetailResponse.data && lineDetailResponse.data.code === 0) {
              const lineData = lineDetailResponse.data.data
              const stations = lineData.stations || []
              const voteCounts = lineData.voteCounts || {}
              
              // 将站点和对应投票数添加到总列表
              for (const station of stations) {
                const votes = voteCounts[station] || 0
                // 检查是否已存在同名站点
                const existingIndex = allStations.findIndex(s => s.station === station)
                
                if (existingIndex >= 0) {
                  // 如果存在，累加投票
                  allStations[existingIndex].votes += votes
                  // 记录该站点属于多条线路
                  if (!allStations[existingIndex].lines.includes(line.lineName)) {
                    allStations[existingIndex].lines.push(line.lineName)
                  }
                } else {
                  // 如果不存在，添加新站点
                  allStations.push({
                    station,
                    votes,
                    lines: [line.lineName]
                  })
                }
              }
            }
          }
          
          // 按投票数排序
          allStations.sort((a, b) => b.votes - a.votes)
          this.allStationsData = allStations
          console.log('手动构建的所有站点数据:', allStations)
        }
      } catch (error) {
        console.error('手动构建站点数据失败:', error)
      } finally {
        uni.hideLoading()
      }
    },

    // 处理线路选择变化
    handleLineChange(e) {
      this.selectedLineIndex = e.detail.value
      const selectedLine = this.metroLines[this.selectedLineIndex]
      
      if (selectedLine) {
        this.selectedLineId = selectedLine.id
        
        // 判断是否选择了"全部"选项
        if (selectedLine.id === -1) {
          this.isAllStationsSelected = true
          this.displayMode = 'initial' // 切换到全部线路时，重置为只显示前15个
          // 确保已加载全部站点数据
          if (!this.allStationsData || this.allStationsData.length === 0) {
            this.buildAllStationsRanking()
          }
        } else {
          this.isAllStationsSelected = false
          this.getMetroLineById(selectedLine.id)
        }

        // 重置选择状态
        this.selected = -1

        // 在线路切换后刷新投票记录
        this.$nextTick(() => {
          this.getUserVoteRecords()
        })
      }
    },

    select(idx) {
      if (this.remainingVotes <= 0) return
      this.selected = idx
    },

    // 初始投票方法
    async vote() {
      if (this.remainingVotes <= 0 || this.selected === -1) return
      
      // 如果选择的是全部站点视图，不允许投票
      if (this.isAllStationsSelected) {
        uni.showToast({
          title: '请先选择具体线路再投票',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 验证用户是否已登录
      if (!this.checkLogin()) return

      // 检查用户是否还有剩余投票次数
      if (this.remainingVotes <= 0) {
        uni.showToast({
          title: '您的投票次数已用完',
          icon: 'none',
          duration: 2000
        })
        return
      }

      const { token, userId } = this.getUserInfo()
      if (!userId) {
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
        return
      }

      const selectedStation = this.currentStations[this.selected]
      const BASE_URL = this.getBaseUrl()

      try {
        // 先检查位置权限状态
        const authStatus = await this.getLocationAuth()
        
        if (!authStatus) {
          // 如果没有权限，提示用户
          uni.showModal({
            title: '提示',
            content: '投票需要获取您的位置信息，是否前往设置页面授权？',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting()
              }
            }
          })
          return
        }
        
        // 获取用户位置
        uni.showLoading({
          title: '获取位置中...'
        })

        // 使用Promise包装getLocation
        const location = await new Promise((resolve, reject) => {
          uni.getLocation({
            type: 'gcj02', // 国内使用gcj02坐标系
            success: function(res) {
              resolve(res)
            },
            fail: function(err) {
              reject(err)
            }
          })
        }).catch(err => {
          console.error('获取位置失败:', err)
          uni.hideLoading()
          uni.showToast({
            title: '获取位置失败，请允许位置权限',
            icon: 'none',
            duration: 2000
          })
          throw new Error('获取位置失败')
        })

        uni.hideLoading()

        // 打印调试信息
        console.log('开始投票', {
          lineId: this.selectedLineId,
          station: selectedStation,
          userId,
          latitude: location.latitude,
          longitude: location.longitude
        })

        // 构建请求参数 - 使用基于位置的投票接口
        const requestUrl = `${BASE_URL}/api/metro-lines/${this.selectedLineId}/location-vote/${selectedStation}?latitude=${location.latitude}&longitude=${location.longitude}&userId=${userId}`


        // 调用投票接口
        const response = await uni.request({
          url: requestUrl,
          method: 'POST',
          header: {
            'bausertoken': token
          }
        })

        // 打印响应
        console.log('投票响应', response)

        if (response.data && response.data.code === 0) {
          // 投票成功，更新本地数据
          this.handleVoteSuccess(selectedStation)

          // 更新剩余投票次数
          this.remainingVotes--
        } else {
          // 处理错误情况
          this.handleVoteError(response.data)
        }
      } catch (error) {
        console.error('投票异常', error)
        if (error.message !== '获取位置失败') { // 避免重复显示错误提示
          uni.showToast({
            title: '网络请求异常',
            icon: 'none'
          })
        }
      }
    },

    // 检查位置授权状态
    async getLocationAuth() {
      return new Promise((resolve) => {
        uni.getSetting({
          success: (res) => {
            if (res.authSetting['scope.userLocation']) {
              resolve(true)
            } else {
              resolve(false)
            }
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    },

    // 处理投票成功
    handleVoteSuccess(selectedStation) {
      // 设置投票成功状态
      this.voteSuccess = true

      // 添加动画效果
      this.animatingStations.push(selectedStation)

      // 更新本地数据
      if (this.currentVoteCounts[selectedStation] !== undefined) {
        this.currentVoteCounts[selectedStation]++
      } else {
        this.currentVoteCounts[selectedStation] = 1
      }

      // 重置选择状态
      this.selected = -1

      // 更新投票记录（从服务器获取最新数据）
      this.getUserVoteRecords()

      // 立即刷新线路数据以获取最新票数
      setTimeout(() => {
        console.log('刷新线路数据以获取最新票数')
        this.getMetroLineById(this.selectedLineId)
        // 刷新全部站点排名数据
        this.buildAllStationsRanking()

        // 移除动画状态
        const index = this.animatingStations.indexOf(selectedStation)
        if (index > -1) {
          this.animatingStations.splice(index, 1)
        }
      }, 800)

      // 重置投票成功状态
      setTimeout(() => {
        this.voteSuccess = false
      }, 1000)

      uni.showToast({
        title: '投票成功',
        icon: 'success'
      })
    },

    // 处理投票错误
    handleVoteError(errorData) {
      let errorMsg = '投票失败'

      if (errorData) {
        // 输出更详细的错误信息用于调试
        console.error('投票失败详情:', errorData)
        console.error('错误信息内容:', errorData.message)

        // 直接显示原始错误信息
        errorMsg = errorData.message || '投票失败'

        // 检测特定错误类型并设置友好提示
        if (errorData.code === 50000) {
          // 记录实际消息内容以便调试
          if (errorData.message) {
            // 移除24小时投票限制的检查，因为该限制已取消
            // console.log('检查24小时内投票条件:', errorData.message.indexOf('已在24小时内对该站点投过票') !== -1)

            if (errorData.message.indexOf('仅限广州地区') !== -1) {
              errorMsg = '仅限广州地区用户参与投票'
            } else if (errorData.message.indexOf('异常投票行为') !== -1) {
              errorMsg = '检测到异常投票行为，请稍后再试'
            }
            // 注释掉24小时投票限制的处理，因为该限制已取消
            // else if (errorData.message.indexOf('已在24小时内对该站点投过票') !== -1) {
            //   errorMsg = '您已在24小时内对该站点投过票'
            // }
          }
        } else if (errorData.code === 40000) {
          errorMsg = errorData.message || '参数错误'
        }
      }

      uni.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 2000
      })
    },

    getPercent(count) {
      if (!this.currentVoteCounts) return 0

      // 计算当前所有站点的总票数
      const total = Object.values(this.currentVoteCounts).reduce((sum, count) => sum + Number(count), 0)

      // 防止除以零
      if (total === 0) return 0

      // 计算百分比
      return ((count / total) * 100).toFixed(1)
    },

    // 检查当前站点是否是已投票的站点 - 仅用于UI显示
    isVotedStation(stationName) {
      // 确保voteRecords是数组且不为空
      if (!this.voteRecords || !Array.isArray(this.voteRecords) || this.voteRecords.length === 0) {
        return false
      }

      // 打印调试信息
      if (!this.debugShown) {
        console.log('调试投票记录信息:')
        console.log('当前选择的线路ID:', this.selectedLineId, '类型:', typeof this.selectedLineId)
        console.log('投票记录:', this.voteRecords)
        this.debugShown = true
      }

      // 如果选择了"全部"视图，检查所有线路中是否有投过该站点
      if (this.isAllStationsSelected) {
        return this.voteRecords.some(record => record.stationName === stationName)
      }

      // 确保类型一致进行比较 - 将两边都转为字符串再比较
      return this.voteRecords.some(record => {
        // 类型转换再比较
        const recordLineId = String(record.metroLineId)
        const currentLineId = String(this.selectedLineId)

        return recordLineId === currentLineId && record.stationName === stationName
      })
    },

    // 获取用户投票记录
    async getUserVoteRecords() {
      // 检查用户是否登录
      const { userId } = this.getUserInfo()
      if (!userId) return

      const BASE_URL = this.getBaseUrl()
      try {
        uni.showLoading({
          title: '加载投票记录...'
        })

        const response = await uni.request({
          url: `${BASE_URL}/api/vote-records/user/${userId}`,
          method: 'GET'
        })

        uni.hideLoading()

        if (response.data && response.data.code === 0) {
          // 更新投票记录
          this.voteRecords = response.data.data || []
          console.log('获取到投票记录:', this.voteRecords)

          // 强制更新视图
          this.$forceUpdate()
        } else {
          console.error('获取投票记录失败:', response.data)
        }
      } catch (error) {
        uni.hideLoading()
        console.error('获取投票记录异常:', error)
      }
    },

    // 添加分享到朋友圈的方法
    onShareTimeline() {
      return {
        title: 'Fox Dance新店投票'
      }
    },
    
    // 分享给好友
    onShareAppMessage() {
      
      return {
        title: 'Fox Dance新店投票，快来投票吧！',
        path: '/pagesSub/switch/vote'
      }
    },

    // 添加检查位置权限的方法
    checkLocationPermission() {
      uni.getSetting({
        success: (res) => {
          // 更新位置授权状态
          this.locationAuth = !!res.authSetting['scope.userLocation']
          
          if (!res.authSetting['scope.userLocation']) {
            // 用户未授权位置信息
            uni.showModal({
              title: '提示',
              content: '投票需要获取您的位置信息，是否前往设置页面授权？',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      // 更新位置授权状态
                      this.locationAuth = !!settingRes.authSetting['scope.userLocation']
                      
                      if (settingRes.authSetting['scope.userLocation']) {
                        uni.showToast({
                          title: '授权成功',
                          icon: 'success'
                        })
                      } else {
                        uni.showToast({
                          title: '授权失败，无法参与投票',
                          icon: 'none'
                        })
                      }
                    }
                  })
                }
              }
            })
          }
        },
        fail: () => {
          this.locationAuth = false
        }
      })
    },
    
    // 添加请求位置权限的方法
    requestLocationPermission() {
      // 先尝试直接请求位置权限
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          this.locationAuth = true
          uni.showToast({
            title: '授权成功',
            icon: 'success'
          })
        },
        fail: () => {
          // 如果直接请求失败，打开设置页面
          uni.showModal({
            title: '提示',
            content: '需要位置权限才能参与投票，是否前往设置页面授权？',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    this.locationAuth = !!settingRes.authSetting['scope.userLocation']
                    if (this.locationAuth) {
                      uni.showToast({
                        title: '授权成功',
                        icon: 'success'
                      })
                    }
                  }
                })
              }
            }
          })
        }
      })
    },

    // 获取按钮文本
    getBtnText() {
      if (this.voteSuccess) {
        return '投票成功'
      }
      if (this.remainingVotes <= 0) {
        return '投票次数已用完'
      }
      if (this.selected === -1) {
        return '请选择站点'
      }
      return '立即投票'
    },

    // 获取总票数
    getTotalVotes() {
      if (!this.sortedStations || this.sortedStations.length === 0) {
        return 0
      }
      return this.sortedStations.reduce((total, station) => total + station.votes, 0)
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/pagesSub/styles/common.scss';

.vote-container {
  background: $background-color;
  min-height: 100vh;
  padding: 0 24rpx 120rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

// 头部区域
.header-section {
  padding: 40rpx 0 32rpx;
  text-align: center;
  // background: $primary-gradient;
  margin: 0 -24rpx 32rpx;
  border-radius: 0 0 32rpx 32rpx;
  color: $text-primary;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
  }
}

.vote-title {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}

.vote-info {
  font-size: 20rpx;
  opacity: 0.9;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

// 卡片基础样式已移至common.scss

.card-header {
  padding: 32rpx 32rpx 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid $border-color;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $text-primary;
}

.total-votes {
  font-size: 24rpx;
  color: $text-secondary;
  background: linear-gradient(135deg, #ff6b87, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

// 下拉选择器
.dropdown-card {
  .picker {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 32rpx;
    min-height: 80rpx;
  }

  .picker-text {
    font-size: 32rpx;
    color: $text-primary;
    font-weight: 500;
  }

  .picker-loading {
    font-size: 32rpx;
    color: $text-light;
  }

  .picker-arrow {
    color: $text-secondary;
    font-size: 28rpx;
    transition: transform $transition-fast;
  }
}

// 提示卡片
.notice-card {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 20%, #ff7675 100%);

  .notice-content {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
  }

  .notice-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
    border-radius: 50%;
  }

  .notice-text {
    flex: 1;
  }

  .notice-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: white;
    margin-bottom: 4rpx;
  }

  .notice-desc {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
  }
}

// 投票列表
.vote-list-card {
  .vote-list {
    padding: 0;
    margin-bottom: 120rpx;
  }
}

.vote-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid $border-color;
  transition: all $transition-fast;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }

  &.selected {
    background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
    border-left: 6rpx solid #ff6b87;

    .station-name {
      color: #ff6b87;
      font-weight: 600;
    }
  }

  &.voted {
    background: linear-gradient(135deg, rgba(255, 234, 167, 0.3) 0%, rgba(250, 177, 160, 0.3) 100%);

    &::after {
      content: '已投票';
      position: absolute;
      top: 8rpx;
      right: 32rpx;
      font-size: 20rpx;
      color: #ff8e53;
      background: rgba(255, 142, 83, 0.2);
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
    }
  }

  &.rank-top {
    .rank-badge {
      background: $primary-gradient;
    }
  }
}

// 排名标识
.rank-badge {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: $secondary-gradient;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);

  .rank-number {
    font-size: 24rpx;
    font-weight: 700;
    color: white;
  }
}

.rank-number-normal {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: $text-secondary;
}

// 站点信息
.station-info {
  flex: 1;
  margin-right: 24rpx;
}

.station-name {
  font-size: 32rpx;
  font-weight: 500;
  color: $text-primary;
  margin-bottom: 12rpx;
  transition: color $transition-fast;
}

.vote-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.vote-bar {
  flex: 1;
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;
}

.vote-bar-inner {
  height: 100%;
  background: $primary-gradient;
  border-radius: 6rpx;
  transition: width $transition-medium;
  animation: progressSlide 0.8s ease-out;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
  }
}

@keyframes progressSlide {
  from {
    width: 0 !important;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.vote-percentage {
  font-size: 24rpx;
  color: $text-secondary;
  font-weight: 500;
  min-width: 60rpx;
  text-align: right;
}

// 票数显示
.vote-count-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
}

.vote-count {
  font-size: 32rpx;
  font-weight: 700;
  color: #ff6b87;
  transition: all $transition-fast;

  &.count-animate {
    animation: countPulse 0.6s ease-out;
  }
}

@keyframes countPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #ff8e53;
  }
  100% {
    transform: scale(1);
  }
}

.vote-unit {
  font-size: 20rpx;
  color: $text-light;
  margin-top: 4rpx;
}

// 选中指示器
.selection-indicator {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: $success-gradient;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  animation: checkBounce 0.3s ease-out;
}

@keyframes checkBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.check-icon {
  font-size: 20rpx;
  color: white;
  font-weight: 700;
}

// 更多按钮
.more-button {
  padding: 32rpx;
  text-align: center;
  border-top: 1rpx solid $border-color;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color $transition-fast;

  &:active {
    background-color: #f8f9fa;
  }
}

.more-text {
  font-size: 28rpx;
  color: #ff6b87;
  font-weight: 500;
}

.more-icon {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #ff6b87;
  transition: transform $transition-fast;

  &.icon-rotate {
    transform: rotate(180deg);
  }
}

// 加载状态
.loading-card {
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 32rpx;
  }

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #e9ecef;
    border-top: 4rpx solid #ff6b87;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 24rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: $text-secondary;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 底部操作区域
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: $card-background;
  padding: 24rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.vote-tip {
  text-align: center;
  color: $text-secondary;
  font-size: 24rpx;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.vote-action {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.vote-btn {
  @extend .btn-primary;
  width: 100%;
  height: 88rpx;
}

.btn-text {
  position: relative;
  z-index: 2;
}

.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    width: 300rpx;
    height: 300rpx;
    opacity: 0;
  }
}

.remaining-votes {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
}

.votes-label {
  font-size: 24rpx;
  color: $text-secondary;
}

.votes-count {
  font-size: 28rpx;
  font-weight: 700;
  color: #ff6b87;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  min-width: 48rpx;
  text-align: center;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .vote-item {
    padding: 20rpx 24rpx;
  }

  .station-name {
    font-size: 30rpx;
  }

  .vote-count {
    font-size: 28rpx;
  }
}
</style>
