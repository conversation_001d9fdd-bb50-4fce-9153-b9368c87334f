<template>
  <view class="container">
    <!-- TailwindCSS 测试页面 -->
    <view class="header">
      <text class="title">TailwindCSS 测试页面</text>
    </view>
    
    <!-- 基础样式测试 -->
    <view class="section">
      <text class="section-title">基础样式测试</text>
      
      <!-- 颜色和背景 -->
      <view class="test-item bg-red-500 text-white p-4 m-2 rounded-lg">
        红色背景，白色文字，内边距，外边距，圆角
      </view>
      
      <view class="test-item bg-blue-500 text-white p-4 m-2 rounded-lg">
        蓝色背景，白色文字
      </view>
      
      <view class="test-item bg-green-500 text-white p-4 m-2 rounded-lg">
        绿色背景，白色文字
      </view>
    </view>
    
    <!-- 布局测试 -->
    <view class="section">
      <text class="section-title">布局测试</text>
      
      <!-- Flex布局 -->
      <view class="flex justify-between items-center bg-gray-200 p-4 m-2 rounded-lg">
        <text class="text-sm">左侧文字</text>
        <text class="text-lg font-bold">中间粗体</text>
        <text class="text-xs">右侧小字</text>
      </view>
      
      <!-- Grid布局 -->
      <view class="grid grid-cols-2 gap-4 p-4 m-2">
        <view class="bg-purple-500 text-white p-4 rounded text-center">网格1</view>
        <view class="bg-pink-500 text-white p-4 rounded text-center">网格2</view>
        <view class="bg-indigo-500 text-white p-4 rounded text-center">网格3</view>
        <view class="bg-yellow-500 text-black p-4 rounded text-center">网格4</view>
      </view>
    </view>
    
    <!-- 响应式测试 -->
    <view class="section">
      <text class="section-title">响应式测试</text>
      
      <view class="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 bg-teal-500 text-white p-4 m-2 rounded-lg">
        响应式宽度测试
      </view>
    </view>
    
    <!-- 动画测试 -->
    <view class="section">
      <text class="section-title">动画测试</text>
      
      <view class="transform hover:scale-105 transition-transform duration-300 bg-orange-500 text-white p-4 m-2 rounded-lg">
        悬停缩放动画
      </view>
    </view>
    
    <!-- 自定义rpx单位测试 -->
    <view class="section">
      <text class="section-title">rpx单位转换测试</text>
      
      <view class="test-rpx">
        <text class="text-base">这应该是32rpx字体大小（1rem = 32rpx）</text>
      </view>
      
      <view class="w-32 h-32 bg-cyan-500 rounded-lg m-4">
        128rpx x 128rpx 的正方形
      </view>
    </view>
    
    <!-- 按钮测试 -->
    <view class="section">
      <text class="section-title">按钮样式测试</text>
      
      <view class="flex flex-col space-y-4 p-4">
        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          主要按钮
        </button>
        
        <button class="bg-transparent hover:bg-blue-500 text-blue-700 font-semibold hover:text-white py-2 px-4 border border-blue-500 hover:border-transparent rounded">
          次要按钮
        </button>
        
        <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-full">
          圆角按钮
        </button>
      </view>
    </view>
    
    <!-- 卡片测试 -->
    <view class="section">
      <text class="section-title">卡片样式测试</text>
      
      <view class="max-w-sm rounded overflow-hidden shadow-lg bg-white m-4">
        <view class="px-6 py-4">
          <view class="font-bold text-xl mb-2">卡片标题</view>
          <text class="text-gray-700 text-base">
            这是一个使用TailwindCSS样式的卡片组件，包含标题、内容和标签。
          </text>
        </view>
        <view class="px-6 pt-4 pb-2">
          <view class="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
            #标签1
          </view>
          <view class="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2">
            #标签2
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TailwindTest',
  data() {
    return {
      
    }
  },
  onLoad() {
    console.log('TailwindCSS 测试页面加载完成')
  }
}
</script>

<style scoped>
/* 这里可以添加一些自定义样式来补充TailwindCSS */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  @apply text-center py-8;
}

.title {
  @apply text-3xl font-bold text-white;
}

.section {
  @apply bg-white mx-4 my-4 rounded-lg p-4;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4 block;
}

.test-item {
  @apply text-center;
}

.test-rpx {
  @apply p-4 bg-gray-100 rounded;
}

/* 测试自定义CSS与TailwindCSS的结合 */
.custom-gradient {
  background: linear-gradient(45deg, #ff6b87, #ff8e53);
}
</style>
