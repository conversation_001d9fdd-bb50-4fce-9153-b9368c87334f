// postcss.config.js - 使用PostCSS 7兼容版本
module.exports = {
    plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
        // PostCSS 7兼容的自定义插件
        function() {
            return function(root) {
                root.walkRules(function(rule) {
                    rule.selector = rule.selector.replace(/\\/g, '');
                });
                root.walkDecls(function(decl) {
                    decl.value = decl.value.replace(/\\/g, '');
                });
            }
        }
    ]
}
