// postcss.config.js - 使用PostCSS 7兼容版本
module.exports = {
    plugins: [
        require('tailwindcss'),
        require('autoprefixer'),
        // 自定义插件移除反斜杠
        function(opts = {}) {
            return {
                postcssPlugin: 'remove-backslash',
                Once(root) {
                    root.walkRules(rule => {
                        rule.selector = rule.selector.replace(/\\/g, '');
                    });
                    root.walkDecls(decl => {
                        decl.value = decl.value.replace(/\\/g, '');
                    });
                }
            }
        }
    ]
}
