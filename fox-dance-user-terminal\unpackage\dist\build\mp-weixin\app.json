{"pages": ["pages/index/index", "pages/index1/index1", "pages/login/xieYi", "pages/login/login", "pages/buy/buy", "pages/Schedule/Schedule", "pages/mine/mine", "pages/mine/memberCard/myMemberCard", "pages/index/signing", "pages/mine/memberCard/myMemberCardxq", "pages/mine/order/order", "pages/mine/order/logistics", "pages/mine/myCourse/myCourse", "pages/mine/myCourse/myCoursexq", "pages/mine/invitation", "pages/mine/editinformation", "pages/mine/integral", "pages/mine/couponbag", "pages/mine/lessonPackage/lessonPackage", "pages/mine/lessonPackage/lessonPackagexq", "pages/mine/ranking", "pages/mine/messageCenter", "pages/mine/leave/leave", "pages/mine/leave/leaveCard", "pages/mine/leave/leaveLists", "pages/mine/settings/settings", "pages/mine/settings/feedback", "pages/mine/userReport/userReport", "pages/mine/userReport/weeksUserReport", "pages/mine/userReport/monthUserReport", "pages/mine/userReport/yearsUserReport", "pages/buy/pointsMall/search", "pages/buy/pointsMall/searchResults", "pages/buy/pointsMall/success", "pages/Schedule/search", "pages/Schedule/searchResults", "pages/buy/pointsMall/productDetails", "pages/buy/pointsMall/confirmOrder", "pages/buy/coursePackage/myCoursexq", "pages/buy/coursePackage/teacherDetails", "pages/buy/coursePackage/confirmOrder", "pages/buy/coursePackage/success", "pages/Schedule/confirmOrder", "pages/buy/coursePackage/orderPayment", "pages/buy/selectStores", "pages/buy/cardsPayment", "pages/buy/cardsSuccess", "pages/index/switchStores", "pages/index/foxDetail", "pages/index/storesDetail", "pages/index/teacherDetail", "pages/Schedule/Schedulexq", "pages/index/service", "pages/mine/address", "pages/mine/add_address", "pages/mine/tzgl", "pages/mine/tzglxq", "pages/webView/webView", "pages/prizedraw/prizedraw", "pages/prizedraw/dengji", "pages/prizedraw/winningrecord", "pages/prizedraw/winningrecordxq", "pages/prizedraw/confirmOrder", "pages/prizedraw/hb_confirmOrders", "pages/prizedraw/edit_skxx", "pages/prizedraw/success", "pages/prizedraw/selectStores"], "subPackages": [{"root": "pagesSub", "pages": ["demo/demo", "switch/vote", "switch/comment", "switch/comment-detail", "switch/topic-list", "switch/add-topic", "store/store-list"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#fff", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#4F4F51", "selectedColor": "#27282A", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "visible": false}, {"pagePath": "pages/buy/buy", "text": "购买", "visible": false}, {"pagePath": "pages/index1/index1", "text": "约课", "visible": false}, {"pagePath": "pages/Schedule/Schedule", "text": "约课", "visible": false}, {"pagePath": "pages/mine/mine", "text": "我的", "visible": false}]}, "requiredPrivateInfos": ["chooseLocation", "getLocation"], "permission": {"scope.userLocation": {"desc": "获取当前位置"}}, "usingComponents": {"mescroll-body": "/components/mescroll-uni/mescroll-body"}}