(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/open-one-box/open-one-box"],{1488:function(t,e,n){"use strict";n.r(e);var i=n("8821"),o=n("f248");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("f146");var a=n("828b"),s=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"1a123e12",null,!1,i["a"],void 0);e["default"]=s.exports},8821:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement;t._self._c;t.$initSSP();var n=t.__map(t.poolList,(function(e,n){var i=t.__get_orig(e);return"augmented"===t.$scope.data.scopedSlotsCompiler&&t.$setSSP("default",{item:i}),{$orig:i}}));t.$mp.data=Object.assign({},{$root:{l0:n}}),t.$callSSP()},o=[]},c969:function(t,e,n){},f146:function(t,e,n){"use strict";var i=n("c969"),o=n.n(i);o.a},f248:function(t,e,n){"use strict";n.r(e);var i=n("f700"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},f700:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{list:{type:Array,default:function(){return[]}},jackpotLength:{type:Number,default:90},probability:{type:String,default:"probability"},duration:{type:Number,default:8e3},startMusic:{type:String,default:""},endMusic:{type:String,default:""},isPlayAudio:{type:Boolean,default:!0},result:{type:[Number,String],default:null},resultName:{type:String,default:"id"}},data:function(){return{moveCss:"",luckyNums:0,poolList:[],imgbaseUrl:""}},watch:{list:{handler:function(t){t.length>0&&this.init()},immediate:!0}},methods:{init:function(){this.imgbaseUrl=this.$baseUrl,this.luckyNums=this.getRand(this.jackpotLength-15,this.jackpotLength-10);var t=this.generateLottery(this.jackpotLength);this.poolList=t},start:function(){var e=this,n=this.list.filter((function(t){return t[e.resultName]==e.result}))[0];this.poolList.splice(this.luckyNums-1,0,n),this.$nextTick((function(){var n=t.createSelectorQuery().in(e);n.selectAll(".openOneBox-animation,.resultItem").boundingClientRect((function(n){var i=n.reduce((function(t,e){var n=e.left,i=e.width;return{left:Math.abs(t.left-n),width:Math.abs(t.width-i)}}),{left:0,width:0}),o=i.left-i.width/2;e.playVoice1(),e.moveCss="margin-left:".concat(-o,"px;transition:all ").concat(e.duration,"ms cubic-bezier(.1,.59,.1,.9)"),t.setStorageSync("animation",1),setTimeout((function(){t.setStorageSync("animation",0),e.$emit("finsh")}),e.duration+100)})).exec()}))},getRand:function(t,e){return Math.floor(Math.random()*(e-t+1)+t)},generateLottery:function(t){for(var e=[],n=this.generateProbabilityData(),i=0;i<t;i++){var o=Math.floor(Math.random()*n.length),r=n[o];e.push(r)}return e},generateProbabilityData:function(){var t=this,e=[],n=0;this.list.forEach((function(e){e[t.probability]?n+=1*e[t.probability]:n+=1}));for(var i=0;i<this.list.length;i++)for(var o=this.list[i],r=o[this.probability]?o[this.probability]:1,a=Math.round(1*r/n*60),s=0;s<a;s++)e.push(o);return e},playVoice1:function(){var e=this;if(this.isPlayAudio&&""!=this.startMusic){var n=t.createInnerAudioContext();n.autoplay=!0,n.src=this.startMusic,n.onPlay((function(){console.log("开始播放")})),n.onError((function(t){console.log(t.errMsg),console.log(t.errCode),n.destroy()})),n.onEnded((function(){e.playVoice2(),n.destroy()})),n.play()}},playVoice2:function(){var e=t.createInnerAudioContext();e.autoplay=!0,e.src=this.endMusic,e.onPlay((function(){console.log("开始播放")})),e.onError((function(t){console.log(t.errMsg),console.log(t.errCode),e.destroy()})),e.onEnded((function(){e.destroy()})),e.play()}}};e.default=n}).call(this,n("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/open-one-box/open-one-box-create-component',
    {
        'components/open-one-box/open-one-box-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1488"))
        })
    },
    [['components/open-one-box/open-one-box-create-component']]
]);
