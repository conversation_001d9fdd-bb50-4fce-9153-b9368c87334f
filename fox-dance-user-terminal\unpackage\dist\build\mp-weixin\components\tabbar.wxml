<view><view class="{{['tab-bar',(showBox&&!isTabBarReturning)?'tab-bar-exit':'',(isTabBarReturning)?'tab-bar-enter':'',(showBox)?'tab-bar-icon-visible':'']}}"><view class="{{['tab_bgi','',noIndex==2?'tab_bgi2':'']}}"></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tabbarChange',['$0',index],[[['list','',index]]]]]]]}}" class="{{['tab-bar-item',(index===2&&showBox)?'tab-fixed-visible':'']}}" bindtap="__e"><block wx:if="{{index!=2}}"><view><u-icon vue-id="{{'2025dc66-1-'+index}}" name="{{current==index?item.icon+'-fill':item.icon}}" color="{{selColor}}" size="40" bind:__l="__l"></u-icon><view class="tab_text" style="{{'color:'+(current==index?'#131315':'#131315')+';'}}">{{''+item.text+''}}</view></view></block><block wx:if="{{index==2}}"><view><image class="tab_img" style="width:104rpx;height:104rpx;margin-top:-110rpx;" src="{{current==index?item.selectedIconPath:item.iconPath}}" mode="aspectFit"></image></view></block></view></block></view><block wx:if="{{showBox}}"><view data-event-opts="{{[['tap',[['closePreview',['$event']]]]]}}" class="preview-mask" bindtap="__e"><view class="{{['fullscreen-background',(isCardExiting)?'background-exit':'']}}"><view class="header-container"><view class="header-left"><image class="header-image home-icon" src="/static/icon/home.png" mode="aspectFit"></image></view><image class="header-image" src="/static/tabbar/tab_fox1.png" mode="aspectFit"></image><view class="header-right"><image class="header-image" src="/static/icon/个人中心.png" mode="aspectFit"></image></view></view><view class="bottom-hint"><view class="fox-logo-container"><view class="fox-line"></view><text class="fox-text">FOX DANCE STUDIO</text><view class="fox-line"></view></view><text class="fox-subtext">{{cardTipText}}</text></view></view><view data-event-opts="{{[['touchmove',[['onCardTouchMove',['$event']]]],['touchstart',[['onCardTouchStart',['$event']]]],['touchend',[['onCardTouchEnd',['$event']]]]]}}" class="{{['card-container-with-buttons',(showBox&&!isCardExiting)?'show':'',(isCardExiting)?'exit':'']}}" catchtouchmove="__e" catchtouchstart="__e" catchtouchend="__e"><swiper class="{{['card-swiper',(isCardPulling)?'card-pulling':'']}}" current="{{currentPage}}" previous-margin="30rpx" next-margin="30rpx" duration="{{300}}" circular="{{true}}" skip-hidden-item-layout="{{false}}" indicator-dots="{{false}}" data-event-opts="{{[['change',[['onSwiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="card-swiper-item"><view class="{{['card-preview-container','card-item',(currentPage===index)?'active-card':'',(item.g0===1)?'near-active':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['',['$event']]]]]}}" catchtap="__e"><preview-card vue-id="{{'2025dc66-2-'+index}}" title="{{item.$orig.name}}" tag="{{item.$orig.tag}}" image="{{item.$orig.image}}" targetPage="{{item.$orig.targetPage}}" data-event-opts="{{[['^pulling',[['handleCardPulling']]]]}}" bind:pulling="__e" bind:__l="__l"></preview-card></view></swiper-item></block></swiper><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="page-dots" catchtap="__e"><block wx:for="{{products.length}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['',['$event']],['handleDotTap',[i-1]]]]]}}" class="page-dot" style="{{'background-color:'+(currentPage===i?'rgb(232,124,174)':'rgba(0,0,0,0.2)')+';'}}" catchtap="__e"></view></block></view></view></view></block></view>