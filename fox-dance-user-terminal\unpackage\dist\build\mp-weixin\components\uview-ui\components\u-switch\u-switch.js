(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uview-ui/components/u-switch/u-switch"],{"273e":function(t,e,i){"use strict";var n=i("53b1"),a=i.n(n);a.a},"53b1":function(t,e,i){},"54dd3":function(t,e,i){"use strict";i.r(e);var n=i("bad2"),a=i("edef");for(var u in a)["default"].indexOf(u)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(u);i("273e");var o=i("828b"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"ddbe4f62",null,!1,n["a"],void 0);e["default"]=l.exports},bad2:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return u})),i.d(e,"a",(function(){return n}));var n={uLoading:function(){return i.e("components/uview-ui/components/u-loading/u-loading").then(i.bind(null,"f53f"))}},a=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.switchStyle])),i=this.$u.addUnit(this.size),n=this.$u.addUnit(this.size);this.$mp.data=Object.assign({},{$root:{s0:e,g0:i,g1:n}})},u=[]},ed8f:function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"u-switch",props:{loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:50},activeColor:{type:String,default:"#2979ff"},inactiveColor:{type:String,default:"#ffffff"},value:{type:Boolean,default:!1},vibrateShort:{type:Boolean,default:!1},activeValue:{type:[Number,String,Boolean],default:!0},inactiveValue:{type:[Number,String,Boolean],default:!1}},data:function(){return{}},computed:{switchStyle:function(){var t={};return t.fontSize=this.size+"rpx",t.backgroundColor=this.value?this.activeColor:this.inactiveColor,t},loadingColor:function(){return this.value?this.activeColor:null}},methods:{onClick:function(){var e=this;this.disabled||this.loading||(this.vibrateShort&&t.vibrateShort(),this.$emit("input",!this.value),this.$nextTick((function(){e.$emit("change",e.value?e.activeValue:e.inactiveValue)})))}}};e.default=i}).call(this,i("df3c")["default"])},edef:function(t,e,i){"use strict";i.r(e);var n=i("ed8f"),a=i.n(n);for(var u in n)["default"].indexOf(u)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(u);e["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uview-ui/components/u-switch/u-switch-create-component',
    {
        'components/uview-ui/components/u-switch/u-switch-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("54dd3"))
        })
    },
    [['components/uview-ui/components/u-switch/u-switch-create-component']]
]);
