(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/store/store-list"],{"1d7c":function(e,t,n){"use strict";var o=n("f8fb"),r=n.n(o);r.a},"3a2e":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"71a0"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"f53f"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.loading?null:e.filteredStoreList.length),o=!e.loading&&n>0?e.__map(e.filteredStoreList,(function(t,n){var o=e.__get_orig(t),r=e.getStoreImage(t);return{$orig:o,m0:r}})):null;e._isMounted||(e.e0=function(t,n){var o=[],r=arguments.length-2;while(r-- >0)o[r]=arguments[r+2];var a=o[o.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];t=i.store,n=i.index;e.handleStoreClick(String(t.name),String(t.image),n)}),e.$mp.data=Object.assign({},{$root:{g0:n,l0:o}})},a=[]},"473f":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("2300");o(n("3240"));var r=o(n("caf4"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"885e":function(e,t,n){"use strict";n.r(t);var o=n("cfa6"),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},caf4:function(e,t,n){"use strict";n.r(t);var o=n("3a2e"),r=n("885e");for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(a);n("1d7c");var i=n("828b"),s=Object(i["a"])(r["default"],o["b"],o["c"],!1,null,"068acab6",null,!1,o["a"],void 0);t["default"]=s.exports},cfa6:function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n("7eb4")),a=o(n("3b2d")),i=o(n("ee10")),s=n("f34b"),c={name:"StoreList",data:function(){return{storeNames:[],storeList:[],loading:!1,searchKeyword:"",sortBy:"all"}},computed:{filteredStoreList:function(){var e=this.storeList;if(console.log("filteredStoreList",e),this.searchKeyword){var t=this.searchKeyword.toLowerCase();e=e.filter((function(e){return e.name.toLowerCase().includes(t)}))}return this.sortBy,e},filteredStoreNames:function(){return this.filteredStoreList.map((function(e){return e.name}))}},onLoad:function(){console.log("📱 店铺列表页面加载"),this.loadStoreNames()},onShow:function(){console.log("📱 店铺列表页面显示")},onPullDownRefresh:function(){console.log("🔄 触发下拉刷新"),this.refreshStoreList()},methods:{clearSearch:function(){this.searchKeyword=""},onSearchInput:function(e){this.searchKeyword&&(this.searchKeyword=this.searchKeyword.replace(/\s+/g," "))},handleSearch:function(){console.log("🔍 搜索店铺:",this.searchKeyword)},loadStoreNames:function(){var t=this;return(0,i.default)(r.default.mark((function n(){var o;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,t.loading=!0,n.next=4,(0,s.getStoreList)();case 4:o=n.sent,console.log("📊 店铺列表API响应:",o),0===o.code&&o.data?(t.storeList=o.data,t.storeNames=o.data.map((function(e){return e.name}))):(console.error("❌ 店铺列表API返回错误:",o.message),e.showToast({title:o.message||"加载失败",icon:"error",duration:2e3})),n.next=13;break;case 9:n.prev=9,n.t0=n["catch"](0),console.error("❌ 加载店铺列表失败:",n.t0),e.showToast({title:"网络错误，请重试",icon:"error",duration:2e3});case 13:return n.prev=13,t.loading=!1,e.stopPullDownRefresh(),n.finish(13);case 17:case"end":return n.stop()}}),n,null,[[0,9,13,17]])})))()},refreshStoreList:function(){console.log("🔄 刷新店铺列表"),this.loadStoreNames()},getStoreImage:function(e){return e.image&&e.image.trim()?e.image.startsWith("/")?"https://file.foxdance.com.cn".concat(e.image):e.image:"/static/icon/店铺.png"},handleImageError:function(e){console.log("🖼️ 店铺图片加载失败，使用默认图标"),e.target.src="/static/icon/店铺.png"},handleStoreClick:function(t,n,o){if(console.log("🏪 点击店铺:",t,"类型:",(0,a.default)(t),"图片：",n,"索引:",o),void 0===t||null===t){console.warn("⚠️ storeName为undefined，尝试从索引获取");var r=this.filteredStoreList[o];r&&r.name&&(t=r.name,console.log("🔄 已从索引恢复storeName:",t))}var i=this.storeList.find((function(e){return e&&e.name===t}));if(!i)return console.error("❌ 未找到店铺信息:",t),void e.showToast({title:"店铺信息不存在",icon:"error",duration:2e3});console.log("🏪 找到店铺信息:",i),e.navigateTo({url:"/pagesSub/switch/comment?storeId=".concat(i.id,"&storeName=").concat(encodeURIComponent(t),"&storeImage=").concat("https://file.foxdance.com.cn"+n,"&content_type=store")})}}};t.default=c}).call(this,n("df3c")["default"])},f8fb:function(e,t,n){}},[["473f","common/runtime","common/vendor"]]]);