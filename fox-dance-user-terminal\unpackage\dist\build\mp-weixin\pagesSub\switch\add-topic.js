(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/add-topic"],{"063b":function(e,t,n){},"0bc9":function(e,t,n){"use strict";n.r(t);var i=n("ddfb"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=o.a},"0cc1":function(e,t,n){"use strict";n.r(t);var i=n("3451"),o=n("e178");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("d6c0");var a=n("828b"),s=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"120c1850",null,!1,i["a"],void 0);t["default"]=s.exports},3451:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={uniForms:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(n.bind(null,"8d36"))},uniFormsItem:function(){return n.e("uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(n.bind(null,"86fa"))},uLoading:function(){return n.e("components/uview-ui/components/u-loading/u-loading").then(n.bind(null,"f53f"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.uploadedImages,(function(t,n){var i=e.__get_orig(t),o=e.processImageUrl(t);return{$orig:i,m0:o}}))),i=e.uploadedImages.length;e.$mp.data=Object.assign({},{$root:{l0:n,g0:i}})},r=[]},"3a38":function(e,t,n){"use strict";n.r(t);var i=n("610b"),o=n("0bc9");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("fb58");var a=n("828b"),s=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=s.exports},"56ca":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("2300");i(n("3240"));var o=i(n("0cc1"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"610b":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var i=function(){var e=this.$createElement;this._self._c},o=[]},"8c10":function(e,t,n){},d6c0:function(e,t,n){"use strict";var i=n("8c10"),o=n.n(i);o.a},ddfb:function(e,t,n){"use strict";(function(e){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(n("7eb4")),r=i(n("3b2d")),a=i(n("7ca3")),s=i(n("ee10")),l={name:"uniFormsItem",options:{virtualHost:!0},provide:function(){return{uniFormItem:this}},inject:{form:{from:"uniForm",default:null}},props:{rules:{type:Array,default:function(){return null}},name:{type:[String,Array],default:""},required:{type:Boolean,default:!1},label:{type:String,default:""},labelWidth:{type:[String,Number],default:""},labelAlign:{type:String,default:""},errorMessage:{type:[String,Boolean],default:""},leftIcon:String,iconColor:{type:String,default:"#606266"}},data:function(){return{errMsg:"",userRules:null,localLabelAlign:"left",localLabelWidth:"70px",localLabelPos:"left",border:!1,isFirstBorder:!1}},computed:{msg:function(){return this.errorMessage||this.errMsg}},watch:{"form.formRules":function(e){this.init()},"form.labelWidth":function(e){this.localLabelWidth=this._labelWidthUnit(e)},"form.labelPosition":function(e){this.localLabelPos=this._labelPosition()},"form.labelAlign":function(e){}},created:function(){var e=this;this.init(!0),this.name&&this.form&&this.$watch((function(){var t=e.form._getDataValue(e.name,e.form.localData);return t}),(function(t,n){var i=e.form._isEqual(t,n);if(!i){var o=e.itemSetValue(t);e.onFieldChange(o,!1)}}),{immediate:!1})},destroyed:function(){this.__isUnmounted||this.unInit()},methods:{setRules:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.userRules=e,this.init(!1)},setValue:function(){},onFieldChange:function(t){var n=arguments,i=this;return(0,s.default)(o.default.mark((function r(){var s,l,u,c,d,f,h,m,g,p,b,v;return o.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(s=!(n.length>1&&void 0!==n[1])||n[1],l=i.form,u=l.formData,l.localData,c=l.errShowType,d=l.validateCheck,f=l.validateTrigger,h=l._isRequiredField,m=l._realName,g=m(i.name),t||(t=i.form.formData[g]),p=i.itemRules.rules&&i.itemRules.rules.length,i.validator&&p&&0!==p){o.next=7;break}return o.abrupt("return");case 7:if(b=h(i.itemRules.rules||[]),v=null,"bind"!==f&&!s){o.next=18;break}return o.next=12,i.validator.validateUpdate((0,a.default)({},g,t),u);case 12:v=o.sent,b||void 0!==t&&""!==t||(v=null),v&&v.errorMessage?("undertext"===c&&(i.errMsg=v?v.errorMessage:""),"toast"===c&&e.showToast({title:v.errorMessage||"校验错误",icon:"none"}),"modal"===c&&e.showModal({title:"提示",content:v.errorMessage||"校验错误"})):i.errMsg="",d(v||null),o.next=19;break;case 18:i.errMsg="";case 19:return o.abrupt("return",v||null);case 20:case"end":return o.stop()}}),r)})))()},init:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.form||{},n=t.validator,i=t.formRules,o=t.childrens,a=(t.formData,t.localData),s=t._realName,l=t.labelWidth,u=t._getDataValue;t._setDataValue;if(this.localLabelAlign=this._justifyContent(),this.localLabelWidth=this._labelWidthUnit(l),this.localLabelPos=this._labelPosition(),this.form&&e&&o.push(this),n&&i){this.form.isFirstBorder||(this.form.isFirstBorder=!0,this.isFirstBorder=!0),this.group&&(this.group.isFirstBorder||(this.group.isFirstBorder=!0,this.isFirstBorder=!0)),this.border=this.form.border;var c=s(this.name),d=this.userRules||this.rules;"object"===(0,r.default)(i)&&d&&(i[c]={rules:d},n.updateSchema(i));var f=i[c]||{};this.itemRules=f,this.validator=n,this.itemSetValue(u(this.name,a))}},unInit:function(){var e=this;if(this.form){var t=this.form,n=t.childrens,i=t.formData,o=t._realName;n.forEach((function(t,n){t===e&&(e.form.childrens.splice(n,1),delete i[o(t.name)])}))}},itemSetValue:function(e){var t=this.form._realName(this.name),n=this.itemRules.rules||[],i=this.form._getValue(t,e,n);return this.form._setDataValue(t,this.form.formData,i),i},clearValidate:function(){this.errMsg=""},_isRequired:function(){return this.required},_justifyContent:function(){if(this.form){var e=this.form.labelAlign,t=this.labelAlign?this.labelAlign:e;if("left"===t)return"flex-start";if("center"===t)return"center";if("right"===t)return"flex-end"}return"flex-start"},_labelWidthUnit:function(e){return this.num2px(this.labelWidth?this.labelWidth:e||(this.label?70:"auto"))},_labelPosition:function(){return this.form&&this.form.labelPosition||"left"},isTrigger:function(e,t,n){return"submit"!==e&&e?"bind":void 0===e?"bind"!==t?t?"submit":""===n?"bind":"submit":"bind":"submit"},num2px:function(e){return"number"===typeof e?"".concat(e,"px"):e}}};t.default=l}).call(this,n("df3c")["default"])},e178:function(e,t,n){"use strict";n.r(t);var i=n("f5ed"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=o.a},f5ed:function(e,t,n){"use strict";(function(e){var i=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(n("7eb4")),r=i(n("ee10")),a=i(n("3a38")),s=i(n("ea22")),l=(n("d0b6"),{components:{uniForms:function(){Promise.all([n.e("common/vendor"),n.e("node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms")]).then(function(){return resolve(n("0fb6"))}.bind(null,n)).catch(n.oe)},uniFormsItem:a.default},data:function(){return{userId:"",submitting:!1,uploading:!1,uploadedImages:[],formData:{title:"",description:""},rules:{title:{rules:[{required:!0,errorMessage:"请输入话题标题"},{minLength:3,maxLength:100,errorMessage:"标题长度在3-100个字符之间"}]},description:{rules:[{required:!0,errorMessage:"请输入话题描述"},{minLength:5,maxLength:200,errorMessage:"描述长度在5-200个字符之间"}]}}}},onLoad:function(){this.userId=e.getStorageSync("userid")||"18"},methods:{submitForm:function(){var t=this;this.submitting||this.$refs.form.validate().then((function(n){t.submitting=!0,e.showLoading({title:"发布中..."});var i={userId:t.userId,title:t.formData.title,description:t.formData.description,topicImages:t.uploadedImages};s.default.addTopic(i).then((function(t){if(console.log("创建话题API返回数据:",JSON.stringify(t)),0===t.code)e.hideLoading(),e.showToast({title:"发布成功",icon:"success"}),setTimeout((function(){var t=getCurrentPages(),n=t[t.length-2];n&&n.$vm&&n.$vm.fetchTopicList(!0),e.navigateBack()}),1e3);else{var n="发布失败";n=40300===t.code?"只有特定用户可以创建话题":40001===t.code?t.message.includes("标题过长")?"标题过长":"标题或描述不能为空":t.message||"发布失败",e.hideLoading(),e.showToast({title:n,icon:"none"})}})).catch((function(t){console.error("创建话题失败:",t),e.hideLoading(),e.showToast({title:"网络请求错误",icon:"none"})})).finally((function(){t.submitting=!1}))})).catch((function(e){console.log("表单验证错误:",e)}))},chooseImages:function(){var t=this;if(console.log("🔥 开始选择图片"),this.uploading)e.showToast({title:"正在上传中，请稍候",icon:"none"});else{var n=9-this.uploadedImages.length;console.log("🔥 当前已上传图片数量:",this.uploadedImages.length,"剩余可上传:",n),n<=0?e.showToast({title:"最多只能上传9张图片",icon:"none"}):e.chooseImage({count:n,sizeType:["compressed"],sourceType:["album","camera"],success:function(e){console.log("🔥 选择图片成功:",e.tempFilePaths),console.log("🔥 选择的图片数量:",e.tempFilePaths.length),t.uploadImages(e.tempFilePaths)},fail:function(t){console.error("❌ 选择图片失败:",t),e.showToast({title:"选择图片失败: "+(t.errMsg||"未知错误"),icon:"none"})}})}},uploadImages:function(t){var n=this;return(0,r.default)(o.default.mark((function i(){var r,a,l,u,c,d;return o.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(t&&0!==t.length){i.next=2;break}return i.abrupt("return");case 2:n.uploading=!0,e.showLoading({title:"上传图片中..."}),console.log("🔥 开始上传图片，数量:",t.length),i.prev=5,r=0,a=0,l=0;case 9:if(!(l<t.length)){i.next=37;break}return u=t[l],console.log("🔥 上传第".concat(l+1,"张图片:"),u),i.prev=12,i.next=15,s.default.uploadImage(u,"file",{driver:"cos"});case 15:if(c=i.sent,console.log("🔥 第".concat(l+1,"张图片上传结果:"),c),!(1===c.code&&c.data&&c.data.file&&c.data.file.url)){i.next=26;break}return d=c.data.file.url,i.next=21,n.validateImageUrl(d);case 21:n.uploadedImages.push(d),r++,console.log("✅ 图片上传成功:",d),i.next=28;break;case 26:a++,console.error("❌ 图片上传失败，响应格式错误:",c);case 28:i.next=34;break;case 30:i.prev=30,i.t0=i["catch"](12),a++,console.error("❌ 第".concat(l+1,"张图片上传异常:"),i.t0);case 34:l++,i.next=9;break;case 37:e.hideLoading(),r>0?e.showToast({title:"成功上传".concat(r,"张图片").concat(a>0?"，".concat(a,"张失败"):""),icon:r===t.length?"success":"none"}):e.showToast({title:"图片上传失败，请重试",icon:"none"}),i.next=46;break;case 41:i.prev=41,i.t1=i["catch"](5),console.error("❌ 图片上传异常:",i.t1),e.hideLoading(),e.showToast({title:"图片上传失败: "+(i.t1.message||"未知错误"),icon:"none"});case 46:return i.prev=46,n.uploading=!1,i.finish(46);case 49:case"end":return i.stop()}}),i,null,[[5,41,46,49],[12,30]])})))()},deleteImage:function(t){var n=this;e.showModal({title:"确认删除",content:"确定要删除这张图片吗？",success:function(e){e.confirm&&(n.uploadedImages.splice(t,1),console.log("删除图片，当前图片数量:",n.uploadedImages.length))}})},previewImage:function(t){var n=this;if(console.log("🔥 开始预览图片，索引:",t,"图片数组:",this.uploadedImages),this.uploadedImages&&0!==this.uploadedImages.length)if(t<0||t>=this.uploadedImages.length)e.showToast({title:"图片索引错误",icon:"none"});else{var i=this.uploadedImages[t];if(console.log("🔥 当前预览图片URL:",i),i&&"string"===typeof i){var o=this.uploadedImages.map((function(e){return n.processImageUrl(e)})),r=o[t];console.log("🔥 处理后的图片URL数组:",o),console.log("🔥 当前预览URL:",r),e.previewImage({current:r,urls:o,success:function(){console.log("✅ 图片预览成功")},fail:function(t){console.error("❌ 图片预览失败:",t),e.showToast({title:"图片预览失败: "+(t.errMsg||"未知错误"),icon:"none"})}})}else e.showToast({title:"图片URL无效",icon:"none"})}else e.showToast({title:"没有可预览的图片",icon:"none"})},showImageOptions:function(t){var n=this,i=["预览图片","删除图片"];t>0&&i.unshift("设为封面"),e.showActionSheet({itemList:i,success:function(e){"设为封面"===i[e.tapIndex]?n.setAsCover(t):"预览图片"===i[e.tapIndex]?n.previewImage(t):"删除图片"===i[e.tapIndex]&&n.deleteImage(t)}})},setAsCover:function(t){if(0!==t){var n=this.uploadedImages.splice(t,1)[0];this.uploadedImages.unshift(n),e.showToast({title:"已设为封面",icon:"success"})}},processImageUrl:function(e){return e?e.startsWith("/")?"https://file.foxdance.com.cn"+e:e.startsWith("http://")||e.startsWith("https://")?e:"https://"+e:""},handleImageLoadError:function(t){console.error("❌ 图片加载失败，索引:",t,"图片URL:",this.uploadedImages[t]),e.showToast({title:"图片加载失败",icon:"none"})},validateImageUrl:function(e){return(0,r.default)(o.default.mark((function t(){return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,n){if(e&&"string"===typeof e){/^(https?:\/\/|\/)/.test(e)?(console.log("✅ 图片URL验证通过:",e),t(e)):n(new Error("图片URL格式不正确"))}else n(new Error("图片URL格式错误"))})));case 1:case"end":return t.stop()}}),t)})))()}}});t.default=l}).call(this,n("df3c")["default"])},fb58:function(e,t,n){"use strict";var i=n("063b"),o=n.n(i);o.a}},[["56ca","common/runtime","common/vendor"]]]);