<view class="container data-v-120c1850"><uni-forms vue-id="0b8faa09-1" model="{{formData}}" rules="{{rules}}" validate-trigger="submit" err-show-type="toast" data-ref="form" class="data-v-120c1850 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-item data-v-120c1850"><text class="label data-v-120c1850">话题标题</text><uni-forms-item vue-id="{{('0b8faa09-2')+','+('0b8faa09-1')}}" name="title" class="data-v-120c1850" bind:__l="__l" vue-slots="{{['default']}}"><input class="input data-v-120c1850" type="text" placeholder="请输入话题标题" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['formData']]]]]}}" value="{{formData.title}}" bindinput="__e"/></uni-forms-item></view><view class="form-item data-v-120c1850"><text class="label data-v-120c1850">话题描述</text><uni-forms-item vue-id="{{('0b8faa09-3')+','+('0b8faa09-1')}}" name="description" class="data-v-120c1850" bind:__l="__l" vue-slots="{{['default']}}"><textarea class="textarea data-v-120c1850" placeholder="请输入话题描述" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea></uni-forms-item></view><view class="form-item data-v-120c1850"><text class="label data-v-120c1850">话题图片<text class="label-tip data-v-120c1850">（最多9张，第一张将作为封面）</text></text><view class="image-upload-container data-v-120c1850"><view class="image-list data-v-120c1850"><block wx:for="{{$root.l0}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['longpress',[['showImageOptions',[index]]]]]}}" class="image-item data-v-120c1850" bindlongpress="__e"><image class="uploaded-image data-v-120c1850" src="{{image.m0}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['tap',[['previewImage',[index]]]],['error',[['handleImageLoadError',[index]]]]]}}" bindtap="__e" binderror="__e"></image><view data-event-opts="{{[['tap',[['deleteImage',[index]]]]]}}" class="image-delete data-v-120c1850" bindtap="__e"><text class="delete-icon data-v-120c1850">×</text></view><block wx:if="{{index===0}}"><view class="cover-badge data-v-120c1850">封面</view></block></view></block><block wx:if="{{$root.g0<9}}"><view data-event-opts="{{[['tap',[['chooseImages',['$event']]]]]}}" class="add-image-btn data-v-120c1850" bindtap="__e"><text class="add-icon data-v-120c1850">+</text><text class="add-text data-v-120c1850">添加图片</text></view></block></view><block wx:if="{{uploading}}"><view class="upload-progress data-v-120c1850"><u-loading vue-id="{{('0b8faa09-4')+','+('0b8faa09-1')}}" mode="flower" size="30" color="#667eea" class="data-v-120c1850" bind:__l="__l"></u-loading><text class="progress-text data-v-120c1850">正在上传图片...</text></view></block></view></view><view class="submit-btn data-v-120c1850"><button class="btn data-v-120c1850" type="primary" disabled="{{submitting}}" data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" bindtap="__e">{{submitting?'提交中...':'发布话题'}}</button></view></uni-forms></view>