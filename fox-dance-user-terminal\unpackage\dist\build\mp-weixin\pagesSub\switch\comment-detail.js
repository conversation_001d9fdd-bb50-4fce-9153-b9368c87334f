(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/comment-detail"],{"0528":function(e,t,o){"use strict";var n=o("48e0"),i=o.n(n);i.a},"1bce":function(e,t,o){"use strict";(function(e){var n=o("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i,s=n(o("7ca3")),l=n(o("af34")),r=n(o("3b2d")),c=n(o("9a08")),a=(n(o("ea22")),n(o("b95a"))),u=n(o("0843"));function m(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function h(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?m(Object(o),!0).forEach((function(t){(0,s.default)(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):m(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}o("2175");a.default.extend(u.default),a.default.locale("zh-cn");var d={components:{CommentInput:function(){o.e("pagesSub/switch/components/CommentInput").then(function(){return resolve(o("94ee"))}.bind(null,o)).catch(o.oe)},ReplySkeleton:function(){o.e("pagesSub/switch/components/ReplySkeleton").then(function(){return resolve(o("349c"))}.bind(null,o)).catch(o.oe)}},data:function(){return{commentId:null,userId:"",loading:!0,isRefreshing:!1,loadingMore:!1,hasMore:!0,page:1,limit:10,comment:{id:"",content:"",created_at:"",likes:0,is_liked:!1,user:{id:"",nickname:"",avatar:"",level:0}},replies:[],replyCount:0,replyText:"",currentReplyTo:null,inputPlaceholder:"发表您的评论...",sortBy:"hot",scrollViewHeight:"calc(90vh - 110rpx)",showFullContent:!1,showMorePopup:!1,currentMoreComment:null,isReplying:!1,keyboardHeight:0,inputContainerBottom:0,isKeyboardShow:!1,scrollTop:0,scrollIntoView:"",pagination:{page:1,pageSize:10,hasMore:!0,loading:!1},isLoadingMore:!1,loadingText:"加载中..."}},computed:{isCommentOwner:function(){return console.log(this.comment.user,this.userId),this.comment.user&&this.userId&&String(this.comment.user.id)==String(this.userId)}},onLoad:function(t){if(t.id){this.commentId=Number(t.id);var o=t.userId||e.getStorageSync("userid")||"18";this.userId=Number(o),console.log("🔍 评论详情页参数:",{commentId:this.commentId,userId:this.userId,commentIdType:(0,r.default)(this.commentId),userIdType:(0,r.default)(this.userId),commentIdValue:this.commentId,userIdValue:this.userId}),this.fetchCommentDetail(),this.setScrollViewHeight(),this.setupKeyboardListener()}else e.showToast({title:"评论ID不存在",icon:"none"}),setTimeout((function(){e.navigateBack()}),1500)},onShow:function(){e.offKeyboardHeightChange(),this.setupKeyboardListener()},onHide:function(){e.offKeyboardHeightChange(),console.log("页面隐藏，取消键盘高度监听")},onUnload:function(){e.offKeyboardHeightChange(),console.log("页面卸载，取消键盘高度监听")},methods:(i={getTimestamp:function(){return"undefined"!==typeof performance&&performance.now?performance.now():Date.now()},setupKeyboardListener:function(){var t=this;e.onKeyboardHeightChange((function(e){console.log("键盘高度变化:",e.height),t.keyboardHeight=e.height,t.isKeyboardShow=e.height>0,e.height>0?t.inputContainerBottom=e.height:t.inputContainerBottom=0})),console.log("键盘高度监听器已设置")},onInputFocus:function(e){var t=this;console.log("输入框获取焦点"),this.isKeyboardShow=!0,setTimeout((function(){0===t.keyboardHeight&&(t.keyboardHeight=280,t.inputContainerBottom=t.keyboardHeight)}),300)},onInputBlur:function(e){var t=this;console.log("输入框失去焦点"),this.isKeyboardShow=!1,setTimeout((function(){t.isKeyboardShow||(t.keyboardHeight=0,t.inputContainerBottom=0)}),100)},hideMaskAndKeyboard:function(){console.log("点击蒙版层，收起键盘"),this.$refs.commentInput&&this.$refs.commentInput.blur(),e.hideKeyboard(),this.isKeyboardShow=!1,this.keyboardHeight=0,this.inputContainerBottom=0},cancelReply:function(){this.isReplying=!1,this.currentReplyTo=null,this.replyText="",this.inputPlaceholder="发表您的评论...",e.hideKeyboard(),this.$refs.commentInput&&this.$refs.commentInput.$emit("blur")},isReplyOwner:function(e){return e&&e.user&&this.userId&&String(e.user.id)==String(this.userId)},showDeleteCommentConfirm:function(){var t=this;e.showModal({title:"删除评论",content:"确认要删除这条评论吗？删除后无法恢复",confirmText:"删除",confirmColor:"#f56c6c",success:function(e){e.confirm&&t.deleteComment()}})},showMoreOptions:function(e){this.currentMoreComment=e,this.showMorePopup=!0},replyFromMore:function(){var e=this;this.currentMoreComment&&(this.showMorePopup=!1,setTimeout((function(){e.$refs.commentInput&&(e.$refs.commentInput.autoFocus=!1),e.replyToComment(e.currentMoreComment),e.isReplying=!0,setTimeout((function(){e.$refs.commentInput&&e.$refs.commentInput.focus()}),150)}),300))},copyComment:function(){var t=this;this.currentMoreComment&&e.setClipboardData({data:this.currentMoreComment.content,success:function(){e.showToast({title:"复制成功",icon:"success"}),t.showMorePopup=!1}})},deleteReply:function(){var t=this;this.currentMoreComment&&e.showModal({title:"删除回复",content:"确认要删除这条回复吗？删除后无法恢复",confirmText:"删除",confirmColor:"#f56c6c",success:function(o){o.confirm&&c.default.deleteReply(Number(t.currentMoreComment.id),{userId:t.userId}).then((function(o){if(console.log("删除回复API返回数据:",JSON.stringify(o)),0===o.code){var n=t.replies.findIndex((function(e){return e.id==t.currentMoreComment.id}));n>-1&&t.replies.splice(n,1),t.replyCount--,e.showToast({title:"删除成功",icon:"success"}),t.showMorePopup=!1}else e.showToast({title:o.message||"删除失败",icon:"none"})})).catch((function(t){console.error("删除回复失败:",t),e.showToast({title:"网络请求错误",icon:"none"})}))}})},deleteComment:function(){console.log("删除评论ID:",this.commentId,"类型:",(0,r.default)(this.commentId)),c.default.deleteComment(Number(this.commentId),{userId:this.userId}).then((function(t){console.log("删除评论API返回数据:",JSON.stringify(t)),0===t.code?(e.showToast({title:"删除成功",icon:"success"}),setTimeout((function(){e.navigateBack()}),1500)):e.showToast({title:t.message||"删除失败",icon:"none"})})).catch((function(t){console.error("删除评论失败:",t),e.showToast({title:"网络请求错误",icon:"none"})}))},showDeleteReplyConfirm:function(t,o){var n=this;e.showModal({title:"删除回复",content:"确认要删除这条回复吗？删除后无法恢复",confirmText:"删除",confirmColor:"#f56c6c",success:function(e){e.confirm&&n.deleteReply(t,o)}})}},(0,s.default)(i,"deleteReply",(function(t,o){var n=this;console.log("删除回复:",t,o),c.default.deleteReply(Number(t.id),{userId:this.userId}).then((function(t){console.log("删除回复API返回数据:",JSON.stringify(t)),0===t.code?(n.replies.splice(o,1),n.replyCount--,e.showToast({title:"删除成功",icon:"success"})):e.showToast({title:t.message||"删除失败",icon:"none"})})).catch((function(t){console.error("删除回复失败:",t),e.showToast({title:"网络请求错误",icon:"none"})}))})),(0,s.default)(i,"setScrollViewHeight",(function(){var t=e.getSystemInfoSync().statusBarHeight||25,o="calc(90vh - ".concat(t,"px - 20rpx)");this.scrollViewHeight=o,console.log("设置滚动视图高度:",o)})),(0,s.default)(i,"goBack",(function(){e.navigateBack()})),(0,s.default)(i,"fetchCommentDetail",(function(){var t=this;this.loading=!0,c.default.getCommentDetail(this.commentId,{userId:this.userId,sort:this.sortBy,current:1,pageSize:1}).then((function(o){if(console.log("评论详情API返回数据:",JSON.stringify(o)),0===o.code){var n=o.data.comment||{};console.log("原始评论数据:",JSON.stringify(n)),n.created_at=n.createdAt,n.is_liked=n.isLiked,n.reply_count=n.replyCount,n.user.avatar||(n.user.avatar="/static/images/toux.png"),console.log("处理后的评论数据:",JSON.stringify(n)),t.comment=n,t.replyCount=n.replyCount||0,t.fetchRepliesOnly(),t.loading=!1}else e.showToast({title:o.message||"获取评论详情失败",icon:"none"}),setTimeout((function(){e.navigateBack()}),1500)})).catch((function(t){console.error("获取评论详情失败:",t),e.showToast({title:"网络请求错误",icon:"none"}),setTimeout((function(){e.navigateBack()}),1500)}))})),(0,s.default)(i,"fetchReplies",(function(){var t=this;1===this.page?this.loading=!0:this.loadingMore=!0,c.default.getCommentDetail(this.commentId,{userId:this.userId,sort:this.sortBy,current:this.page,pageSize:this.limit}).then((function(o){if(console.log("评论回复API返回数据:",JSON.stringify(o)),0===o.code){var n=o.data,i=n.replies.items||[];if(console.log("原始回复列表:",JSON.stringify(i)),0===i.length)return t.replies=[],void console.log("replies.length=",t.replies.length);i.forEach((function(e){e.created_at=e.createdAt,e.is_liked=e.isLiked,e.showFullContent=!1,e.replyTo?e.reply_to={id:e.replyTo.id,nickname:e.replyTo.nickname}:e.reply_to=void 0,e.user.avatar||(e.user.avatar="/static/images/toux.png")})),console.log("处理后的回复列表:",JSON.stringify(i)),1===t.page?(t.replies=i,t.replyCount=n.replies&&n.replies.total||0):t.replies=[].concat((0,l.default)(t.replies),(0,l.default)(i)),t.hasMore=n.replies&&n.replies.has_more,t.hasMore&&t.page++}else e.showToast({title:o.message||"获取回复列表失败",icon:"none"})})).catch((function(t){console.error("获取回复列表失败:",t),e.showToast({title:"网络请求错误",icon:"none"})})).finally((function(){t.loading=!1,t.loadingMore=!1,t.isRefreshing=!1}))})),(0,s.default)(i,"loadMoreReplies",(function(){var t=this;if(console.log("🔄 触发回复懒加载"),!this.pagination.loading&&this.pagination.hasMore){var o=Date.now(),n=this.lastRequestTime||0;if(o-n<600)console.log("⚠️ 请求过于频繁，跳过回复懒加载");else{this.lastRequestTime=o,this.pagination.loading=!0,this.loadingText="加载更多回复...";var i=this.pagination.page+1,s=this.getTimestamp();console.log("📄 回复当前页码: ".concat(this.pagination.page,", 请求页码: ").concat(i));var l={userId:Number(this.userId),sort:this.sortBy,current:i,pageSize:this.pagination.pageSize};console.log("📋 回复分页请求参数:",JSON.stringify(l)),c.default.getCommentDetail(Number(this.commentId),l).then((function(o){var n=t.getTimestamp(),l=n-s;if(console.log("✅ 回复分页API返回，耗时: ".concat(l.toFixed(2),"ms")),0===o.code){var r=o.data;console.log("📊 回复分页数据概览:",{repliesCount:r.replies&&r.replies.items?r.replies.items.length:0,total:r.replies?r.replies.total:0,hasMore:!!r.replies&&r.replies.hasMore});var c=[];r.replies&&(r.replies.items&&Array.isArray(r.replies.items)?c=r.replies.items:r.replies.records&&Array.isArray(r.replies.records)?c=r.replies.records:Array.isArray(r.replies)&&(c=r.replies));var a=t.processReplyDataOptimized(c);if(a&&a.length>0){var u=new Set(t.replies.map((function(e){return e.id}))),m=a.filter((function(e){return!u.has(e.id)}));console.log("🔄 回复去重: 原始".concat(a.length,"条，去重后").concat(m.length,"条")),m.length>0&&(t.replies=t.replies.concat(m),t.pagination.page=i,console.log("✅ 回复加载成功，页码: ".concat(i,"，新增: ").concat(m.length,"条"))),(r.replies&&!1===r.replies.hasMore||a.length<t.pagination.pageSize)&&(t.pagination.hasMore=!1,console.log("🔚 回复已加载完毕"))}else t.pagination.hasMore=!1,console.log("🔚 回复无更多数据")}else console.error("❌ 回复API返回错误:",o.message),e.showToast({title:o.message||"加载失败",icon:"none"})})).catch((function(t){console.error("❌ 回复懒加载失败:",t),e.showToast({title:"网络请求错误",icon:"none"})})).finally((function(){t.pagination.loading=!1,t.loadingText="加载中...",console.log("🔄 回复加载状态重置")}))}}else console.log("⚠️ 回复正在加载或已无更多数据，跳过请求")})),(0,s.default)(i,"onRefresh",(function(){var e=this;this.isRefreshing=!0,this.pagination={page:1,pageSize:10,hasMore:!0,loading:!1},this.fetchRepliesOnly(),setTimeout((function(){e.isRefreshing=!1}),500)})),(0,s.default)(i,"likeComment",(function(){var t=this,o=this.comment.is_liked?"unlike":"like";c.default.likeComment(Number(this.comment.id),{userId:Number(this.userId),action:o}).then((function(o){console.log("点赞主评论API返回数据:",JSON.stringify(o)),0===o.code?(t.comment.is_liked=o.data.isLiked||o.data.is_liked,t.comment.likes=o.data.likes):e.showToast({title:o.message||"操作失败",icon:"none"})})).catch((function(t){console.error("点赞操作失败:",t),e.showToast({title:"网络请求错误",icon:"none"})}))})),(0,s.default)(i,"likeReply",(function(t,o){var n=this,i=t.is_liked?"unlike":"like";c.default.likeReply(Number(t.id),{userId:Number(this.userId),action:i}).then((function(t){console.log("点赞回复API返回数据:",JSON.stringify(t)),0===t.code?(n.replies[o].is_liked=t.data.isLiked||t.data.is_liked,n.replies[o].likes=t.data.likes):e.showToast({title:t.message||"操作失败",icon:"none"})})).catch((function(t){console.error("点赞回复操作失败:",t),e.showToast({title:"网络请求错误",icon:"none"})}))})),(0,s.default)(i,"replyToMain",(function(){var e=this;this.$refs.commentInput&&(this.$refs.commentInput.autoFocus=!1),this.currentReplyTo=null,this.inputPlaceholder="发表您的评论...",this.isReplying=!0,setTimeout((function(){e.$refs.commentInput&&e.$refs.commentInput.focus()}),150)})),(0,s.default)(i,"replyToComment",(function(e){var t=this;this.$refs.commentInput&&(this.$refs.commentInput.autoFocus=!1),this.currentReplyTo=e,this.isReplying=!0,console.log("replyToComment:",JSON.stringify(e)),console.log("currentReplyTo:",JSON.stringify(this.currentReplyTo)),e&&e.user?(console.log("回复用户信息:",JSON.stringify(e.user)),this.inputPlaceholder="@ ".concat(e.user.nickname,":")):(console.log("回复用户信息不存在"),this.inputPlaceholder="回复评论..."),setTimeout((function(){t.$refs.commentInput&&t.$refs.commentInput.focus()}),150)})),(0,s.default)(i,"sendReply",(function(){var t=this;if(this.replyText.length>1e3)e.showToast({title:"评论字数不能超过1000字",icon:"none"});else if(this.replyText.trim()){var o={userId:Number(this.userId),commentId:Number(this.commentId),content:this.replyText.trim(),replyToId:this.currentReplyTo?Number(this.currentReplyTo.userId):null};console.log("🚀 发送回复数据:",JSON.stringify(o)),console.log("📊 回复数据类型检查:",{userId:(0,r.default)(o.userId),userIdValue:o.userId,commentId:(0,r.default)(o.commentId),commentIdValue:o.commentId,replyToId:(0,r.default)(o.replyToId),replyToIdValue:o.replyToId,content:(0,r.default)(o.content)}),c.default.replyComment(o.commentId,o).then((function(o){console.log("发送回复API返回数据:",JSON.stringify(o)),0===o.code?(e.showToast({title:"回复成功",icon:"success"}),t.$refs.commentInput?t.$refs.commentInput.clear():t.replyText="",t.currentReplyTo=null,t.inputPlaceholder="发表您的评论...",t.isReplying=!1,t.pagination={page:1,pageSize:10,hasMore:!0,loading:!1},t.fetchRepliesOnly()):e.showToast({title:o.message||"回复失败",icon:"none"})})).catch((function(t){console.error("发送回复失败:",t),e.showToast({title:"网络请求错误",icon:"none"})}))}})),(0,s.default)(i,"changeSort",(function(e){this.sortBy!==e&&(this.sortBy=e,this.pagination={page:1,pageSize:10,hasMore:!0,loading:!0},this.loading=!1,this.fetchRepliesOnly())})),(0,s.default)(i,"fetchRepliesOnly",(function(){var t=this;console.log("🔄 开始获取回复列表，当前页码:",this.pagination.page),c.default.getCommentDetail(Number(this.commentId),{userId:Number(this.userId),sort:this.sortBy,current:this.pagination.page,pageSize:this.pagination.pageSize}).then((function(o){if(console.log("✅ 评论回复API返回数据:",JSON.stringify(o)),0===o.code){var n=o.data,i=[];n.replies&&(n.replies.items&&Array.isArray(n.replies.items)?i=n.replies.items:n.replies.records&&Array.isArray(n.replies.records)?i=n.replies.records:Array.isArray(n.replies)&&(i=n.replies)),console.log("📊 原始回复列表数量:",i.length);var s=t.processReplyData(i);if(1===t.pagination.page)t.replies=s,console.log("📝 第一页回复数据已替换，数量:",s.length);else{var r=t.replies.map((function(e){return e.id})),c=s.filter((function(e){return!r.includes(e.id)}));t.replies=[].concat((0,l.default)(t.replies),(0,l.default)(c)),console.log("📝 后续页回复数据已追加，去重后数量:",c.length)}t.replyCount=n.replies&&n.replies.total||0,s.length<t.pagination.pageSize?(t.pagination.hasMore=!1,console.log("🔚 回复数据已加载完毕")):(t.pagination.hasMore=!0,console.log("📄 还有更多回复数据")),console.log("✅ 回复列表加载成功，当前页:".concat(t.pagination.page,"，总数:").concat(t.replies.length,"条"))}else console.error("❌ 获取回复列表API错误:",o.message),e.showToast({title:o.message||"获取回复列表失败",icon:"none"})})).catch((function(t){console.error("❌ 获取回复列表失败:",t),e.showToast({title:"网络请求错误",icon:"none"})})).finally((function(){t.pagination.loading=!1,console.log("🔄 回复列表加载状态重置")}))})),(0,s.default)(i,"formatTime",(function(e){return e?(0,a.default)(e).fromNow():""})),(0,s.default)(i,"getLevelColor",(function(e){return{0:"#cccbc8",1:"#c6ffe6",2:"#61bc84",3:"#4d648d",4:"#1F3A5F",5:"#9c27b0",6:"#6c35de",7:"#ffd299",8:"#FF7F50",9:"#f35d74",10:"#bb2649"}[e]||"#8dc63f"})),(0,s.default)(i,"toggleContent",(function(){var e=this.showFullContent;this.showFullContent=!this.showFullContent,e&&this.scrollToMainComment()})),(0,s.default)(i,"toggleReplyContent",(function(e,t){var o=e.showFullContent;e.showFullContent?this.$set(e,"showFullContent",!1):this.$set(e,"showFullContent",!0),o&&this.scrollToReply(t)})),(0,s.default)(i,"scrollToMainComment",(function(){var e=this;console.log("🎯 开始滚动到主评论"),this.scrollToElementByScrollIntoView("main-comment"),setTimeout((function(){e.scrollToElementByScrollTop("main-comment")}),100)})),(0,s.default)(i,"scrollToReply",(function(e){var t=this,o="reply-".concat(e);console.log("🎯 开始滚动到回复 - ".concat(o)),this.scrollToElementByScrollIntoView(o),setTimeout((function(){t.scrollToElementByScrollTop(o)}),100)})),(0,s.default)(i,"scrollToElementByScrollIntoView",(function(e){var t=this;console.log("📍 使用scrollIntoView滚动到 - ".concat(e)),this.$nextTick((function(){setTimeout((function(){t.scrollIntoView=e,setTimeout((function(){t.scrollIntoView=""}),500),console.log("✅ scrollIntoView设置成功 - ".concat(e))}),150)}))})),(0,s.default)(i,"scrollToElementByScrollTop",(function(t){var o=this;console.log("📍 使用scroll-top滚动到 - ".concat(t)),this.$nextTick((function(){var n=e.createSelectorQuery().in(o);n.select(".comment-container").boundingClientRect(),n.select("#".concat(t)).boundingClientRect(),n.exec((function(e){if(console.log("📊 scroll-top查询结果 - ".concat(t,":"),e),e&&e.length>=2){var n=e[0],i=e[1];if(n&&i){var s=i.top-n.top,l="main-comment"===t?60:80,r=Math.max(0,s-l);console.log("📐 scroll-top计算 - ".concat(t,":"),{scrollViewTop:n.top,elementTop:i.top,relativeTop:s,targetScrollTop:r}),o.scrollTop=0,o.$nextTick((function(){o.scrollTop=r,console.log("✅ scroll-top设置成功 - ".concat(t,", 位置: ").concat(r))}))}}}))}))})),(0,s.default)(i,"debugScrollElements",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"main-comment";console.log("🔍 调试滚动元素 - ".concat(t));var o=e.createSelectorQuery().in(this);o.select("#".concat(t)).boundingClientRect(),o.select(".comment-container").boundingClientRect(),o.exec((function(e){console.log("🔍 调试结果:",{elementId:t,targetElement:e[0],scrollViewElement:e[1],hasTarget:!!e[0],hasScrollView:!!e[1]})}))})),(0,s.default)(i,"processReplyData",(function(e){return e&&Array.isArray(e)?(console.log("处理回复数据，回复数量:",e.length),e.map((function(e){return e?(e.created_at=e.createdAt||e.created_at||(new Date).toISOString(),e.is_liked=e.isLiked||e.is_liked||!1,e.likes=e.likes||0,e.user?(e.user.avatar||(e.user.avatar="/static/images/toux.png"),e.user.nickname=e.user.nickname||"未知用户",e.user.level=e.user.level||0):e.user={id:0,nickname:"未知用户",avatar:"/static/images/toux.png",level:0},e.replyTo&&(e.reply_to=e.replyTo),e):null})).filter((function(e){return null!==e}))):(console.warn("回复数据为空或格式错误"),[])})),(0,s.default)(i,"processReplyDataOptimized",(function(e){var t=this.getTimestamp();if(!e||!Array.isArray(e))return console.warn("⚠️ 回复数据为空或格式错误"),[];console.log("🔄 开始处理回复数据，数量: ".concat(e.length));var o=e.map((function(e){if(!e)return null;var t=h(h({},e),{},{created_at:e.createdAt||e.created_at||(new Date).toISOString(),is_liked:e.isLiked||e.is_liked||!1,likes:e.likes||0,showFullContent:!1});return t.user?(t.user.avatar||(t.user.avatar="/static/images/toux.png"),t.user.nickname=t.user.nickname||"未知用户",t.user.level=t.user.level||0):t.user={id:0,nickname:"未知用户",avatar:"/static/images/toux.png",level:0},e.replyTo&&(t.reply_to=e.replyTo),t})).filter((function(e){return null!==e})),n=this.getTimestamp();return console.log("✅ 回复数据处理完成，耗时: ".concat((n-t).toFixed(2),"ms，处理数量: ").concat(o.length)),o})),i)};t.default=d}).call(this,o("df3c")["default"])},"48e0":function(e,t,o){},"690c":function(e,t,o){"use strict";(function(e,t){var n=o("47a9");o("2300");n(o("3240"));var i=n(o("bc90"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},afbf:function(e,t,o){"use strict";o.d(t,"b",(function(){return i})),o.d(t,"c",(function(){return s})),o.d(t,"a",(function(){return n}));var n={uLoading:function(){return o.e("components/uview-ui/components/u-loading/u-loading").then(o.bind(null,"f53f"))},uIcon:function(){return o.e("components/uview-ui/components/u-icon/u-icon").then(o.bind(null,"71a0"))},uPopup:function(){return o.e("components/uview-ui/components/u-popup/u-popup").then(o.bind(null,"5682"))}},i=function(){var e=this,t=e.$createElement,o=(e._self._c,!e.loading&&e.comment.user.level>=0?e.getLevelColor(e.comment.user.level):null),n=e.loading?null:e.formatTime(e.comment.created_at),i=e.loading||e.showFullContent?null:e.comment.content.length,s=!e.loading&&!e.showFullContent&&i>100?e.comment.content.slice(0,100):null,l=e.loading?null:e.comment.content.length,r=e.loading?null:e.replies.length,c=!e.loading&&r>0?e.__map(e.replies,(function(t,o){var n=e.__get_orig(t),i=e.formatTime(t.created_at),s=t.showFullContent?null:t.content.length,l=!t.showFullContent&&s>100?t.content.slice(0,100):null,r=t.content.length;return{$orig:n,m2:i,g4:s,g5:l,g6:r}})):null,a=e.loading||e.pagination.loading?null:!e.pagination.hasMore&&e.replies.length>0,u=e.currentMoreComment&&e.isReplyOwner(e.currentMoreComment);e._isMounted||(e.e0=function(t){e.isReplying?e.cancelReply():e.hideMaskAndKeyboard()}),e.$mp.data=Object.assign({},{$root:{m0:o,m1:n,g0:i,g1:s,g2:l,g3:r,l0:c,g7:a,m3:u}})},s=[]},bc90:function(e,t,o){"use strict";o.r(t);var n=o("afbf"),i=o("f7ba");for(var s in i)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(s);o("0528");var l=o("828b"),r=Object(l["a"])(i["default"],n["b"],n["c"],!1,null,"5777e3d8",null,!1,n["a"],void 0);t["default"]=r.exports},f7ba:function(e,t,o){"use strict";o.r(t);var n=o("1bce"),i=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(s);t["default"]=i.a}},[["690c","common/runtime","common/vendor"]]]);