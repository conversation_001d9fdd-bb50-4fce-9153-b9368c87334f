(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/comment"],{"03b4":function(t,e,o){"use strict";o.d(e,"b",(function(){return c})),o.d(e,"c",(function(){return s})),o.d(e,"a",(function(){return n}));var n={uLoading:function(){return o.e("components/uview-ui/components/u-loading/u-loading").then(o.bind(null,"f53f"))},uIcon:function(){return o.e("components/uview-ui/components/u-icon/u-icon").then(o.bind(null,"71a0"))},uPopup:function(){return o.e("components/uview-ui/components/u-popup/u-popup").then(o.bind(null,"5682"))}},c=function(){var t=this,e=t.$createElement,o=(t._self._c,t.topicInfo&&t.topicInfo.topicImages&&t.topicInfo.topicImages.length>0),n=o?t.topicInfo.topicImages.length:null,c=o?t.__map(t.topicInfo.topicImages,(function(e,o){var n=t.__get_orig(e),c=t.processImageUrl(e);return{$orig:n,m0:c}})):null,s=t.topicInfo&&t.topicInfo.createTime?t.formatTime(t.topicInfo.createTime):null,i=t.getCurrentFilterTotal(),r="hot"!==t.activeFilter||t.loading&&"hot"===t.activeFilter?null:t.commentListHot.length,a="hot"!==t.activeFilter||t.loading&&"hot"===t.activeFilter||0==r?null:t.__map(t.commentListHot,(function(e,o){var n=t.__get_orig(e),c=t.processImageUrl(e.user.avatar),s=e.user.level>=0?t.getLevelColor(e.user.level):null,i=t.formatTime(e.created_at),r=e.showFullContent?null:e.content.length,a=!e.showFullContent&&r>100?e.content.slice(0,100):null,l=e.content.length,m=e.replies&&e.replies.length>0,u=m?t.__map(e.replies.slice(0,2),(function(e,o){var n=t.__get_orig(e),c=e.content.length,s=c>50?e.content.slice(0,50):null;return{$orig:n,g7:c,g8:s}})):null;return{$orig:n,m3:c,m4:s,m5:i,g3:r,g4:a,g5:l,g6:m,l1:u}})),l="hot"!==t.activeFilter||t.loading&&"hot"===t.activeFilter||0==r||t.pagination.hot.loading?null:!t.pagination.hot.hasMore&&t.commentListHot.length>0,m="new"!==t.activeFilter||t.loading&&"new"===t.activeFilter?null:t.commentListNew.length,u="new"!==t.activeFilter||t.loading&&"new"===t.activeFilter||0==m?null:t.__map(t.commentListNew,(function(e,o){var n=t.__get_orig(e),c=t.processImageUrl(e.user.avatar),s=e.user.level>=0?t.getLevelColor(e.user.level):null,i=t.formatTime(e.created_at),r=e.showFullContent?null:e.content.length,a=!e.showFullContent&&r>100?e.content.slice(0,100):null,l=e.content.length,m=e.replies&&e.replies.length>0,u=m?t.__map(e.replies.slice(0,2),(function(e,o){var n=t.__get_orig(e),c=e.content.length,s=c>50?e.content.slice(0,50):null;return{$orig:n,g15:c,g16:s}})):null;return{$orig:n,m6:c,m7:s,m8:i,g11:r,g12:a,g13:l,g14:m,l3:u}})),h="new"!==t.activeFilter||t.loading&&"new"===t.activeFilter||0==m||t.pagination.new.loading?null:!t.pagination.new.hasMore&&t.commentListNew.length>0,g="my"!==t.activeFilter||t.loading&&"my"===t.activeFilter?null:0==t.commentListMy.length||null===t.commentListMy,p="my"!==t.activeFilter||t.loading&&"my"===t.activeFilter||g?null:t.__map(t.commentListMy,(function(e,o){var n=t.__get_orig(e),c=t.processImageUrl(e.user.avatar),s=e.user.level>=0?t.getLevelColor(e.user.level):null,i=t.formatTime(e.created_at),r=e.showFullContent?null:e.content.length,a=!e.showFullContent&&r>100?e.content.slice(0,100):null,l=e.content.length,m=e.replies&&e.replies.length>0,u=m?t.__map(e.replies.slice(0,2),(function(e,o){var n=t.__get_orig(e),c=e.content.length,s=c>50?e.content.slice(0,50):null;return{$orig:n,g23:c,g24:s}})):null;return{$orig:n,m9:c,m10:s,m11:i,g19:r,g20:a,g21:l,g22:m,l5:u}})),d="my"!==t.activeFilter||t.loading&&"my"===t.activeFilter||g||t.pagination.my.loading?null:!t.pagination.my.hasMore&&t.commentListMy.length>0,f=t.isCommentOwner(t.currentMoreComment);t.$mp.data=Object.assign({},{$root:{g0:o,g1:n,l0:c,m1:s,m2:i,g2:r,l2:a,g9:l,g10:m,l4:u,g17:h,g18:g,l6:p,g25:d,m12:f}})},s=[]},"27de":function(t,e,o){"use strict";var n=o("5efa"),c=o.n(n);c.a},"410b":function(t,e,o){"use strict";(function(t){var n=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var c=n(o("7eb4")),s=n(o("3b2d")),i=n(o("ee10")),r=n(o("9a08")),a=n(o("ea22")),l=(o("f34b"),n(o("b95a"))),m=n(o("0843"));o("2175");l.default.extend(m.default),l.default.locale("zh-cn");var u={components:{CommentInput:function(){o.e("pagesSub/switch/components/CommentInput").then(function(){return resolve(o("94ee"))}.bind(null,o)).catch(o.oe)},CommentSkeleton:function(){o.e("pagesSub/switch/components/CommentSkeleton").then(function(){return resolve(o("7a6c"))}.bind(null,o)).catch(o.oe)}},data:function(){return{activeFilter:"hot",commentList:[],commentText:"",loading:!0,loadingMore:!1,isRefreshing:!1,page:1,limit:10,hasMore:!0,showMorePopup:!1,currentMoreComment:null,isReplyMode:!1,currentReply:null,inputPlaceholder:"说点什么...",pageHeight:"calc(100vh - 120rpx)",contentId:"",contentType:"",topicId:"",storeId:"",storeName:"",storeImage:"",userId:"",topicInfo:null,storeInfo:null,keyboardHeight:0,inputContainerBottom:0,isKeyboardShow:!1,totalComments:0,commentStats:{hotTotal:0,newTotal:0,myTotal:0},commentListHot:[],commentListNew:[],commentListMy:[],pagination:{hot:{page:1,pageSize:10,hasMore:!0,loading:!1},new:{page:1,pageSize:10,hasMore:!0,loading:!1},my:{page:1,pageSize:10,hasMore:!0,loading:!1}},isLoadingMore:!1,loadingText:"加载中...",scrollTop:0,scrollIntoView:""}},onLoad:function(e){var o=this;this.contentId=e.content_id||"",this.contentType=e.content_type||"",this.topicId=e.topicId?Number(e.topicId):null,this.storeId=e.storeId?Number(e.storeId):null,this.storeName=e.storeName?decodeURIComponent(e.storeName):null,this.storeImage=e.storeImage||null,this.contentId||(this.contentId="default_content"),this.contentType||(this.contentType="video");var n=t.getStorageSync("userid")||"222";this.userId=Number(n),console.log("📱 评论页面参数:",{contentId:this.contentId,contentType:this.contentType,topicId:this.topicId,storeId:this.storeId,storeName:this.storeName,userId:this.userId}),this.topicId&&this.fetchTopicInfo(),this.storeId&&this.storeName&&this.setupStoreInfo(),this.fetchCommentStats(),this.fetchComments(),this.setPageHeight(),this.setupKeyboardListener();var c=getCurrentPages(),s=c[c.length-1];s&&(s.debugKeyboard=function(){return o.debugKeyboardState()},console.log("🔧 调试方法已挂载到页面实例: getCurrentPages()[getCurrentPages().length-1].debugKeyboard()"))},onShow:function(){t.offKeyboardHeightChange(),this.setupKeyboardListener()},onHide:function(){t.offKeyboardHeightChange(),console.log("页面隐藏，取消键盘高度监听")},onUnload:function(){t.offKeyboardHeightChange(),console.log("页面卸载，取消键盘高度监听")},methods:{getTimestamp:function(){return"undefined"!==typeof performance&&performance.now?performance.now():Date.now()},setupStoreInfo:function(){console.log("🏪 设置店铺信息 - storeId:",this.storeId,"storeName:",this.storeName,"storeImage:",this.storeImage),this.storeInfo={id:this.storeId,name:this.storeName,title:"".concat(this.storeName,"找搭子"),description:"快来寻找你的搭子吧！",storeImage:this.storeImage,commentUserCount:0,createTime:(new Date).toISOString()},console.log("🏪 店铺信息设置完成:",this.storeInfo),t.setNavigationBarTitle({title:this.storeInfo.title}),this.fetchComments()},fetchStoreComments:function(e,o,n,s,r){var a=this;return(0,i.default)(c.default.mark((function i(){var l,m,u,h,g,p;return c.default.wrap((function(c){while(1)switch(c.prev=c.next){case 0:return c.prev=0,console.log("🏪 调用店铺评论API - storeId:",e,"filter:",n),u=(null===(l=a.$config)||void 0===l||null===(m=l.apis)||void 0===m?void 0:m.vote_baseUrl)||"https://vote.foxdance.com.cn",h="".concat(u,"/api/comments/store/").concat(e),g={userId:o,filter:n,current:s,pageSize:r},console.log("🏪 店铺评论请求URL:",h),console.log("🏪 店铺评论请求参数:",g),c.next=9,new Promise((function(e,o){t.request({url:h,method:"GET",data:g,header:{"Content-Type":"application/json",bausertoken:t.getStorageSync("bausertoken")||t.getStorageSync("token")},success:function(t){var n;(console.log("🏪 店铺评论API响应:",t),200===t.statusCode)?e(t.data):o(new Error("HTTP ".concat(t.statusCode,": ").concat((null===(n=t.data)||void 0===n?void 0:n.message)||"请求失败")))},fail:function(t){console.error("🏪 店铺评论API请求失败:",t),o(t)}})}));case 9:return p=c.sent,c.abrupt("return",p);case 13:throw c.prev=13,c.t0=c["catch"](0),console.error("🏪 获取店铺评论失败:",c.t0),c.t0;case 17:case"end":return c.stop()}}),i,null,[[0,13]])})))()},getCurrentCommentCount:function(){switch(this.activeFilter){case"hot":return this.commentListHot.length;case"new":return this.commentListNew.length;case"my":return this.commentListMy.length;default:return 0}},getCurrentFilterTotal:function(){switch(this.activeFilter){case"hot":return this.commentStats.hotTotal||0;case"new":return this.commentStats.newTotal||0;case"my":return this.commentStats.myTotal||0;default:return 0}},setupKeyboardListener:function(){var e=this;t.onKeyboardHeightChange((function(t){console.log("🎹 键盘高度变化:",t.height),console.log("📱 回复弹窗状态:",e.showReplyPopup),console.log("🎯 回复输入框焦点状态:",e.isReplyInputFocused),e.keyboardHeight=t.height,e.isKeyboardShow=t.height>0,t.height>0?(e.inputContainerBottom=t.height,console.log("🔧 调整主输入框位置:",e.inputContainerBottom),e.isReplyMode&&console.log("� 当前处于回复模式")):(e.inputContainerBottom=0,console.log("📥 键盘收起，重置主输入框位置"))})),console.log("键盘高度监听器已设置")},isCommentOwner:function(t){return!(!t||!t.user)&&String(t.user.id)==String(this.userId)},onInputFocus:function(t){var e=this;console.log("📝 主输入框获取焦点"),this.isReplyMode&&console.log("💬 当前处于回复模式，回复用户:",this.currentReply&&this.currentReply.user?this.currentReply.user.nickname:"未知用户"),this.isKeyboardShow=!0,setTimeout((function(){0===e.keyboardHeight&&(e.keyboardHeight=280,e.inputContainerBottom=e.keyboardHeight)}),300)},onInputBlur:function(t){var e=this;console.log("输入框失去焦点"),this.isKeyboardShow=!1,setTimeout((function(){e.isKeyboardShow||(e.keyboardHeight=0,e.inputContainerBottom=0)}),100)},focusInput:function(){this.$refs.mainCommentInput&&this.$refs.mainCommentInput.focus()},hideMaskAndKeyboard:function(){console.log("点击蒙版层，收起键盘"),this.isReplyMode&&(console.log("🚫 检测到回复模式，取消回复"),this.cancelReplyMode()),this.$refs.mainCommentInput&&this.$refs.mainCommentInput.blur(),t.hideKeyboard(),this.isKeyboardShow=!1,this.keyboardHeight=0,this.inputContainerBottom=0},debugKeyboardState:function(){console.log("🔍 当前键盘适配状态:"),console.log("  键盘高度:",this.keyboardHeight),console.log("  键盘显示状态:",this.isKeyboardShow),console.log("  回复弹窗显示:",this.showReplyPopup),console.log("  回复输入框焦点:",this.isReplyInputFocused),console.log("  回复弹窗底部距离:",this.replyPopupBottom),console.log("  主输入框底部距离:",this.inputContainerBottom)},setPageHeight:function(){t.getSystemInfoSync().statusBarHeight;var e="calc(100vh - ".concat(120,"rpx)");this.pageHeight=e,console.log("设置页面滚动高度:",e)},goBack:function(){t.navigateBack({delta:1})},changeFilter:function(t){this.activeFilter!==t&&(this.activeFilter=t,"hot"===t&&0===this.commentListHot.length?this.fetchCommentsByType("hot"):"new"===t&&0===this.commentListNew.length?this.fetchCommentsByType("new"):"my"===t&&0===this.commentListMy.length&&this.fetchCommentsByType("my"))},fetchComments:function(){this.fetchCommentsByType("hot")},fetchCommentStats:function(){var t=this;return(0,i.default)(c.default.mark((function e(){var o,n;return c.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if((t.topicId||t.storeId)&&t.userId){e.next=3;break}return console.warn("⚠️ 缺少必要参数，无法获取评论统计"),e.abrupt("return");case 3:if(e.prev=3,console.log("🔢 开始获取评论统计信息..."),!t.topicId){e.next=11;break}return e.next=8,a.default.getTopicCommentStats(t.topicId,t.userId);case 8:o=e.sent,e.next=15;break;case 11:if(!t.storeId){e.next=15;break}return e.next=14,t.fetchStoreCommentStats(t.storeId,t.userId);case 14:o=e.sent;case 15:o&&0===o.code?(t.commentStats=o.data,console.log("🔢 评论统计获取成功:",t.commentStats)):(console.error("❌ 获取评论统计失败:",null===(n=o)||void 0===n?void 0:n.message),t.commentStats={hotTotal:0,newTotal:0,myTotal:0}),e.next=22;break;case 18:e.prev=18,e.t0=e["catch"](3),console.error("❌ 获取评论统计异常:",e.t0),t.commentStats={hotTotal:0,newTotal:0,myTotal:0};case 22:case"end":return e.stop()}}),e,null,[[3,18]])})))()},fetchStoreCommentStats:function(e,o){var n=this;return(0,i.default)(c.default.mark((function s(){var i,r,a,l,m,u;return c.default.wrap((function(c){while(1)switch(c.prev=c.next){case 0:return c.prev=0,console.log("🏪 调用店铺评论统计API - storeId:",e),a=(null===(i=n.$config)||void 0===i||null===(r=i.apis)||void 0===r?void 0:r.vote_baseUrl)||"https://vote.foxdance.com.cn",l="".concat(a,"/api/comments/store/").concat(e,"/stats"),m={userId:o},console.log("🏪 店铺评论统计请求URL:",l),console.log("🏪 店铺评论统计请求参数:",m),c.next=9,new Promise((function(e,o){t.request({url:l,method:"GET",data:m,header:{"Content-Type":"application/json",bausertoken:t.getStorageSync("bausertoken")||t.getStorageSync("token")},success:function(t){var n;(console.log("🏪 店铺评论统计API响应:",t),200===t.statusCode)?e(t.data):o(new Error("HTTP ".concat(t.statusCode,": ").concat((null===(n=t.data)||void 0===n?void 0:n.message)||"请求失败")))},fail:function(t){console.error("🏪 店铺评论统计API请求失败:",t),o(t)}})}));case 9:return u=c.sent,c.abrupt("return",u);case 13:throw c.prev=13,c.t0=c["catch"](0),console.error("🏪 获取店铺评论统计失败:",c.t0),c.t0;case 17:case"end":return c.stop()}}),s,null,[[0,13]])})))()},fetchCommentsByType:function(t){var e=this;console.log("请求".concat(t,"评论列表")),this.loading=!0,this.pagination[t]={page:1,pageSize:10,hasMore:!0,loading:!1};var o=this.getTimestamp(),n={userId:Number(this.userId),contentId:this.contentId,contentType:this.contentType,filter:t,current:1,pageSize:this.pagination[t].pageSize};console.log("🚀 开始请求".concat(t,"评论列表，参数:"),n),this.topicId?(console.log("🎯 检测到topicId: ".concat(this.topicId,"，使用话题API获取评论")),console.log("📋 话题API调用参数:",{topicId:Number(this.topicId),userId:Number(this.userId),filter:t,current:n.current,pageSize:n.pageSize}),a.default.getTopicComments(Number(this.topicId),Number(this.userId),t,n.current,n.pageSize).then((function(n){var c=e.getTimestamp(),s=c-o;if(console.log("✅ 话题".concat(t,"评论列表API返回，耗时: ").concat(s.toFixed(2),"ms")),0===n.code){var i=n.data;console.log("📊 话题".concat(t,"评论列表数据概览:"),{total:i.total,commentsCount:i.comments?i.comments.length:0,hasMore:i.hasMore,current:i.current,pages:i.pages});var r=e.processCommentDataOptimized(i);switch(t){case"hot":e.commentListHot=r;break;case"new":e.commentListNew=r;break;case"my":e.commentListMy=r;break}e.totalComments=i&&i.total||0,e.pagination[t].hasMore=!1!==i.hasMore,console.log("🎯 话题".concat(t,"评论加载完成，总数: ").concat(e.totalComments,", 当前显示: ").concat(r.length))}else e.handleApiError(t,n.message||"获取评论失败")})).catch((function(o){console.error("获取".concat(t,"评论列表失败:"),o),e.handleApiError(t,"网络请求错误")})).finally((function(){e.loading=!1,e.isRefreshing=!1}))):this.storeId?(console.log("🏪 检测到storeId: ".concat(this.storeId,"，使用店铺API获取评论")),console.log("📋 店铺API调用参数:",{storeId:Number(this.storeId),userId:Number(this.userId),filter:t,current:n.current,pageSize:n.pageSize}),this.fetchStoreComments(Number(this.storeId),Number(this.userId),t,n.current,n.pageSize).then((function(n){var c=e.getTimestamp(),s=c-o;if(console.log("✅ 店铺".concat(t,"评论列表API返回，耗时: ").concat(s.toFixed(2),"ms")),0===n.code){var i=n.data;console.log("📊 店铺".concat(t,"评论列表数据概览:"),{total:i.total,commentsCount:i.comments?i.comments.length:0,hasMore:i.hasMore,current:i.current,pages:i.pages});var r=e.processCommentDataOptimized(i);switch(t){case"hot":e.commentListHot=r;break;case"new":e.commentListNew=r;break;case"my":e.commentListMy=r;break}e.totalComments=i&&i.total||0,e.pagination[t].hasMore=!1!==i.hasMore,console.log("🎯 店铺".concat(t,"评论加载完成，总数: ").concat(e.totalComments,", 当前显示: ").concat(r.length))}else e.handleApiError(t,n.message||"获取店铺评论失败")})).catch((function(o){console.error("获取店铺".concat(t,"评论列表失败:"),o),e.handleApiError(t,"网络请求错误")})).finally((function(){e.loading=!1,e.isRefreshing=!1}))):(console.log("请求参数:",JSON.stringify(n)),r.default.getCommentList(n).then((function(n){var c=e.getTimestamp(),s=c-o;if(console.log("✅ ".concat(t,"评论列表API返回，耗时: ").concat(s.toFixed(2),"ms")),0===n.code){var i=n.data;console.log("📊 ".concat(t,"评论列表数据概览:"),{total:i.total,commentsCount:i.comments?i.comments.length:0,hasMore:i.hasMore});var r=e.processCommentDataOptimized(i);switch(t){case"hot":e.commentListHot=r;break;case"new":e.commentListNew=r;break;case"my":e.commentListMy=r;break}e.totalComments=i&&i.total||0,e.pagination[t].hasMore=!1!==i.hasMore,console.log("🎯 ".concat(t,"评论加载完成，总数: ").concat(e.totalComments,", 当前显示: ").concat(r.length))}else e.handleApiError(t,n.message||"获取评论失败")})).catch((function(o){console.error("获取".concat(t,"评论列表失败:"),o),e.handleApiError(t,"网络请求错误")})).finally((function(){e.loading=!1,e.isRefreshing=!1})))},handleApiError:function(e,o){var n=this;switch(t.showToast({title:o,icon:"none"}),e){case"hot":this.commentListHot||(this.commentListHot=[]);break;case"new":this.commentListNew||(this.commentListNew=[]);break;case"my":this.commentListMy||(this.commentListMy=[]);break}this.isRefreshing&&setTimeout((function(){t.showModal({title:"提示",content:"获取评论失败，是否重试？",confirmText:"重试",success:function(t){t.confirm&&n.fetchCommentsByType(e)}})}),500)},loadMoreComments:function(){var e=this,o=this.activeFilter;if(console.log("🔄 触发".concat(o,"评论懒加载")),!this.pagination[o].loading&&this.pagination[o].hasMore){var n=Date.now(),c=this.lastRequestTime||0;if(n-c<800)console.log("⚠️ 请求过于频繁，跳过".concat(o,"评论懒加载"));else{this.lastRequestTime=n,this.pagination[o].loading=!0,this.loadingText="加载更多评论...";var s,i=this.pagination[o].page+1,l=this.getTimestamp();if(console.log("📄 ".concat(o,"评论当前页码: ").concat(this.pagination[o].page,", 请求页码: ").concat(i)),this.topicId)console.log("🎯 调用话题评论API: topicId=".concat(this.topicId,", type=").concat(o,", page=").concat(i)),s=a.default.getTopicComments(Number(this.topicId),Number(this.userId),o,i,this.pagination[o].pageSize);else{var m={userId:Number(this.userId),contentId:this.contentId,contentType:this.contentType,filter:o,current:i,pageSize:this.pagination[o].pageSize};console.log("📋 调用普通评论API，参数:",JSON.stringify(m)),s=r.default.getCommentList(m)}s.then((function(n){var c=e.getTimestamp(),s=c-l;if(console.log("✅ ".concat(o,"评论分页API返回，耗时: ").concat(s.toFixed(2),"ms")),0===n.code){var r=n.data;console.log("📊 ".concat(o,"评论分页数据概览:"),{commentsCount:r.comments?r.comments.length:0,total:r.total,hasMore:r.hasMore});var a=[];r.comments&&Array.isArray(r.comments)?a=r.comments:r.items&&Array.isArray(r.items)?a=r.items:Array.isArray(r)&&(a=r);var m=e.processCommentDataOptimized({comments:a});if(m&&m.length>0){var u=new Set(e.getExistingCommentIds(o)),h=m.filter((function(t){return!u.has(t.id)}));if(console.log("� ".concat(o,"评论去重: 原始").concat(m.length,"条，去重后").concat(h.length,"条")),h.length>0){switch(o){case"hot":e.commentListHot=e.commentListHot.concat(h);break;case"new":e.commentListNew=e.commentListNew.concat(h);break;case"my":e.commentListMy=e.commentListMy.concat(h);break}e.pagination[o].page=i,console.log("✅ ".concat(o,"评论加载成功，页码: ").concat(i,"，新增: ").concat(h.length,"条"))}(!1===r.hasMore||m.length<e.pagination[o].pageSize)&&(e.pagination[o].hasMore=!1,console.log("🔚 ".concat(o,"评论已加载完毕")))}else e.pagination[o].hasMore=!1,console.log("🔚 ".concat(o,"评论无更多数据"))}else console.error("❌ ".concat(o,"评论API返回错误:"),n.message),t.showToast({title:n.message||"加载失败",icon:"none"})})).catch((function(e){console.error("❌ ".concat(o,"评论懒加载失败:"),e),t.showToast({title:"网络请求错误",icon:"none"})})).finally((function(){e.pagination[o].loading=!1,e.loadingText="加载中...",console.log("🔄 ".concat(o,"评论加载状态重置"))}))}}else console.log("⚠️ ".concat(o,"评论正在加载或已无更多数据，跳过请求"))},getExistingCommentIds:function(t){var e=[];switch(t){case"hot":e=this.commentListHot;break;case"new":e=this.commentListNew;break;case"my":e=this.commentListMy;break}return e.map((function(t){return t.id}))},processCommentDataOptimized:function(t){var e=this.getTimestamp();if(!t||!t.comments)return console.warn("⚠️ 评论数据为空或格式错误"),[];var o=Array.isArray(t.comments)?t.comments:[];console.log("� 开始处理评论数据，数量: ".concat(o.length));var n=o.map((function(t){if(!t)return null;var e=Object.assign({},t,{created_at:t.createdAt||t.created_at||(new Date).toISOString(),is_liked:t.isLiked||t.is_liked||!1,showFullContent:!1});return e.user?(e.user.avatar||(e.user.avatar="/static/images/toux.png"),e.user.nickname=e.user.nickname||"未知用户",e.user.level=e.user.level||0):e.user={id:0,nickname:"未知用户",avatar:"/static/images/toux.png",level:0},e})).filter((function(t){return null!==t})),c=this.getTimestamp();return console.log("✅ 评论数据处理完成，耗时: ".concat((c-e).toFixed(2),"ms，处理数量: ").concat(n.length)),n},onRefresh:function(){this.isRefreshing=!0,this.pagination[this.activeFilter]={page:1,pageSize:10,hasMore:!0,loading:!1},this.fetchCommentStats(),this.fetchCommentsByType(this.activeFilter)},likeComment:function(e,o,n){var c=this,s=e.is_liked?"unlike":"like";r.default.likeComment(Number(e.id),{userId:Number(this.userId),action:s}).then((function(e){if(console.log("点赞评论API返回数据:",JSON.stringify(e)),0===e.code){var s=e.data.isLiked||e.data.is_liked,i=e.data.likes;switch(n){case"hot":c.commentListHot[o].is_liked=s,c.commentListHot[o].likes=i;break;case"new":c.commentListNew[o].is_liked=s,c.commentListNew[o].likes=i;break;case"my":c.commentListMy[o].is_liked=s,c.commentListMy[o].likes=i;break}}else t.showToast({title:e.message||"操作失败",icon:"none"})})).catch((function(e){console.error("点赞操作失败:",e),t.showToast({title:"网络请求错误",icon:"none"})}))},goToDetail:function(e){t.navigateTo({url:"/pagesSub/switch/comment-detail?id=".concat(e.id,"&userId=").concat(this.userId)})},sendComment:function(){this.commentText.length>1e3?t.showToast({title:"评论字数不能超过1000字",icon:"none"}):this.commentText.trim()&&(this.isReplyMode&&this.currentReply?(console.log("📤 发送回复给用户:",this.currentReply.user.nickname),this.sendReply()):(console.log("📤 发送普通评论"),this.sendNormalComment()))},sendNormalComment:function(){this.topicId?this.sendTopicComment():this.storeId?this.sendStoreComment():this.sendRegularComment()},sendTopicComment:function(){var t=this,e={userId:Number(this.userId),contentId:this.contentId,topicId:Number(this.topicId),content:this.commentText.trim()};console.log("🎯 发送话题评论请求数据:",JSON.stringify(e)),r.default.postComment(e).then((function(e){console.log("发送话题评论API返回数据:",JSON.stringify(e)),0===e.code?t.handleCommentSuccess():t.handleCommentError(e.message||"话题评论失败")})).catch((function(e){console.error("发送话题评论失败:",e),t.handleCommentError("网络错误，请重试")}))},sendStoreComment:function(){var t=this;return(0,i.default)(c.default.mark((function e(){var o,n;return c.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o={userId:Number(t.userId),storeId:Number(t.storeId),content:t.commentText.trim()},console.log("🏪 发送店铺评论请求数据:",JSON.stringify(o)),e.prev=2,e.next=5,t.postStoreComment(o);case 5:n=e.sent,console.log("发送店铺评论API返回数据:",JSON.stringify(n)),0===n.code?t.handleCommentSuccess():t.handleCommentError(n.message||"店铺评论失败"),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](2),console.error("发送店铺评论失败:",e.t0),t.handleCommentError("网络错误，请重试");case 14:case"end":return e.stop()}}),e,null,[[2,10]])})))()},sendRegularComment:function(){var t=this,e={userId:Number(this.userId),contentId:this.contentId,content:this.commentText.trim()};console.log("📝 发送普通评论请求数据:",JSON.stringify(e)),r.default.postComment(e).then((function(e){console.log("发送普通评论API返回数据:",JSON.stringify(e)),0===e.code?t.handleCommentSuccess():t.handleCommentError(e.message||"评论失败")})).catch((function(e){console.error("发送普通评论失败:",e),t.handleCommentError("网络错误，请重试")}))},handleCommentSuccess:function(){this.clearInputAndResetState(),this.fetchCommentsByType("new"),this.activeFilter="new",this.currentTabIndex=1,this.fetchCommentStats(),t.showToast({title:"评论成功",icon:"success"})},handleCommentError:function(e){t.showToast({title:e,icon:"none"})},postStoreComment:function(e){var o=this;return(0,i.default)(c.default.mark((function n(){var s,i,r,a;return c.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=(null===(s=o.$config)||void 0===s||null===(i=s.apis)||void 0===i?void 0:i.vote_baseUrl)||"https://vote.foxdance.com.cn",a="".concat(r,"/api/comments/store"),n.abrupt("return",new Promise((function(o,n){t.request({url:a,method:"POST",data:e,header:{"Content-Type":"application/json",bausertoken:t.getStorageSync("bausertoken")||t.getStorageSync("token")},success:function(t){var e;(console.log("🏪 店铺评论API响应:",t),200===t.statusCode)?o(t.data):n(new Error("HTTP ".concat(t.statusCode,": ").concat((null===(e=t.data)||void 0===e?void 0:e.message)||"请求失败")))},fail:function(t){console.error("🏪 店铺评论API请求失败:",t),n(t)}})})));case 3:case"end":return n.stop()}}),n)})))()},replyComment:function(t){var e=this;console.log("💬 进入回复模式，回复用户:",t.user.nickname),this.isReplyMode=!0,this.currentReply=t,this.inputPlaceholder="@".concat(t.user.nickname),console.log("🔄 已设置回复模式，placeholder:",this.inputPlaceholder),this.$nextTick((function(){e.$refs.mainCommentInput?(e.$refs.mainCommentInput.focus(),console.log("🎯 主输入框已聚焦，准备回复")):console.warn("⚠️ 主输入框引用不存在")}))},showMoreOptions:function(t){this.currentMoreComment=t,this.showMorePopup=!0},replyFromMore:function(){var t=this;this.currentMoreComment&&(this.showMorePopup=!1,setTimeout((function(){t.replyComment(t.currentMoreComment)}),300))},copyComment:function(){var e=this;this.currentMoreComment&&t.setClipboardData({data:this.currentMoreComment.content,success:function(){t.showToast({title:"复制成功",icon:"success"}),e.showMorePopup=!1}})},deleteComment:function(){var e=this;this.currentMoreComment&&t.showModal({title:"删除评论",content:"确认要删除这条评论吗？删除后无法恢复",confirmText:"删除",confirmColor:"#f56c6c",success:function(o){o.confirm&&r.default.deleteComment(Number(e.currentMoreComment.id),{userId:Number(e.userId)}).then((function(o){if(console.log("删除评论API返回数据:",JSON.stringify(o)),0===o.code){t.showToast({title:"删除成功",icon:"success"}),e.showMorePopup=!1;var n=function(t,e){var o=t.findIndex((function(t){return t.id==e}));o>-1&&t.splice(o,1)};n(e.commentListHot,e.currentMoreComment.id),n(e.commentListNew,e.currentMoreComment.id),n(e.commentListMy,e.currentMoreComment.id),e.totalComments--}else t.showToast({title:o.message||"删除失败",icon:"none"})})).catch((function(e){console.error("删除评论失败:",e),t.showToast({title:"网络请求错误",icon:"none"})}))}})},sendReply:function(){var e=this;this.commentText.trim()&&this.currentReply&&r.default.replyComment(Number(this.currentReply.id),{userId:Number(this.userId),content:this.commentText.trim(),replyToId:null}).then((function(o){console.log("回复评论API返回数据:",JSON.stringify(o)),0===o.code?(e.clearInputAndResetState(),e.fetchCommentsByType(e.activeFilter),t.showToast({title:"回复成功",icon:"success"})):t.showToast({title:o.message||"回复失败",icon:"none"})})).catch((function(e){console.error("回复评论失败:",e),t.showToast({title:"网络请求错误",icon:"none"})}))},clearInputAndResetState:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t?(this.$refs.mainCommentInput?this.$refs.mainCommentInput.clear():this.commentText="",console.log("🔄 输入框已清空")):console.log("🔄 保留输入内容"),this.isReplyMode=!1,this.currentReply=null,this.inputPlaceholder="说点什么...",console.log("🔄 回复状态已重置")},cancelReplyMode:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=this.isReplyMode,n=this.currentReply&&this.currentReply.user?this.currentReply.user.nickname:"未知用户",c=this.commentText&&this.commentText.trim();if(this.isReplyMode=!1,this.currentReply=null,this.inputPlaceholder="说点什么...",e&&(this.$refs.mainCommentInput?this.$refs.mainCommentInput.clear():this.commentText=""),o){console.log("❌ 已取消回复模式，原回复对象: ".concat(n));var s="已取消回复";c&&!e&&(s="已取消回复，内容转为评论"),t.showToast({title:s,icon:"none",duration:1500})}},formatTime:function(t){return t?(0,l.default)(t).fromNow():""},fetchTopicInfo:function(){var e=this;return(0,i.default)(c.default.mark((function o(){var n,i;return c.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(e.topicId){o.next=2;break}return o.abrupt("return");case 2:return o.prev=2,console.log("🎯 获取话题信息，topicId:",e.topicId,"userId:",e.userId),o.next=6,a.default.getTopicById(e.topicId,e.userId);case 6:n=o.sent,console.log("🔍 API响应完整数据:",JSON.stringify(n,null,2)),0===n.code&&n.data?(console.log("🔍 API响应data字段:",JSON.stringify(n.data,null,2)),console.log("🔍 topicImages字段类型:",(0,s.default)(n.data.topicImages)),console.log("🔍 topicImages字段值:",n.data.topicImages),console.log("🔍 topicImages是否为数组:",Array.isArray(n.data.topicImages)),e.topicInfo=n.data,console.log("✅ 话题信息获取成功，赋值后的topicInfo:",e.topicInfo),console.log("✅ 赋值后的topicImages:",e.topicInfo.topicImages),e.topicInfo.topicImages&&e.topicInfo.topicImages.length>0?(console.log("🖼️ 话题包含图片，数量:",e.topicInfo.topicImages.length),console.log("🖼️ 原始图片URL列表:",e.topicInfo.topicImages),i=e.topicInfo.topicImages.map((function(t,o){var n=e.processImageUrl(t);return console.log("🖼️ 图片".concat(o+1,": ").concat(t," -> ").concat(n)),n})),console.log("🖼️ 处理后图片URL列表:",i)):(console.warn("📷 话题不包含图片或topicImages为null/empty"),console.warn("📷 topicImages详细信息:",{value:e.topicInfo.topicImages,type:(0,s.default)(e.topicInfo.topicImages),isArray:Array.isArray(e.topicInfo.topicImages),length:e.topicInfo.topicImages?e.topicInfo.topicImages.length:"N/A"}))):(console.warn("⚠️ 获取话题信息失败:",n.message),console.warn("⚠️ 完整响应:",n),t.showToast({title:n.message||"获取话题信息失败",icon:"none"})),o.next=15;break;case 11:o.prev=11,o.t0=o["catch"](2),console.error("❌ 获取话题信息异常:",o.t0),t.showToast({title:"网络请求错误",icon:"none"});case 15:case"end":return o.stop()}}),o,null,[[2,11]])})))()},handleTopicImageError:function(t){if(console.error("❌ 话题图片加载失败，索引:",t),this.topicInfo&&this.topicInfo.topicImages&&this.topicInfo.topicImages[t]){var e=this.topicInfo.topicImages[t],o=this.processImageUrl(e);console.error("❌ 原始URL:",e),console.error("❌ 处理后URL:",o),this.handleImageLoadError(o,"话题图片")}},previewTopicImage:function(e){var o=this;if(console.log("🔥 开始预览话题图片，索引:",e),this.topicInfo&&this.topicInfo.topicImages&&0!==this.topicInfo.topicImages.length)if(e<0||e>=this.topicInfo.topicImages.length)t.showToast({title:"图片索引错误",icon:"none"});else{var n=this.topicInfo.topicImages.map((function(t){return o.processImageUrl(t)})),c=n[e];console.log("🔥 处理后的话题图片URL数组:",n),console.log("🔥 当前预览URL:",c),t.previewImage({current:c,urls:n,success:function(){console.log("✅ 话题图片预览成功")},fail:function(e){console.error("❌ 话题图片预览失败:",e),t.showToast({title:"图片预览失败: "+(e.errMsg||"未知错误"),icon:"none"})}})}else t.showToast({title:"没有可预览的图片",icon:"none"})},getLevelColor:function(t){return{0:"#cccbc8",1:"#c6ffe6",2:"#61bc84",3:"#4d648d",4:"#1F3A5F",5:"#9c27b0",6:"#6c35de",7:"#ffd299",8:"#FF7F50",9:"#f35d74",10:"#bb2649"}[t]||"#8dc63f"},processImageUrl:function(t){if(!t)return"";if(console.log("🔥 处理图片URL:",t),t.startsWith("/")){var e="https://file.foxdance.com.cn"+t;return console.log("🔥 相对路径转换:",t,"->",e),e}if(!t.startsWith("http://")&&!t.startsWith("https://")){var o="https://"+t;return console.log("🔥 添加协议:",t,"->",o),o}return console.log("🔥 URL无需处理:",t),t},handleImageLoadError:function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"图片";console.error("❌ 图片加载失败:",o,e),t.showToast({title:o+"加载失败",icon:"none"})},toggleContent:function(t,e,o){var n=t.showFullContent;switch(o){case"hot":this.$set(this.commentListHot[e],"showFullContent",!t.showFullContent);break;case"new":this.$set(this.commentListNew[e],"showFullContent",!t.showFullContent);break;case"my":this.$set(this.commentListMy[e],"showFullContent",!t.showFullContent);break}n&&this.scrollToComment(e,o)},scrollToComment:function(t,e){var o=this,n="comment-".concat(e,"-").concat(t);console.log("🎯 开始滚动到评论 - ".concat(n,", 索引: ").concat(t,", 类型: ").concat(e)),setTimeout((function(){o.scrollToCommentByScrollTop(n)}),200),setTimeout((function(){o.scrollTop<50&&(console.log("🔄 scroll-top可能失败，尝试scrollIntoView - ".concat(n)),o.scrollToCommentByScrollIntoView(n))}),600)},scrollToCommentByScrollIntoView:function(e){var o=this;console.log("📍 使用scrollIntoView滚动到 - ".concat(e)),this.$nextTick((function(){var n=t.createSelectorQuery().in(o);n.select("#".concat(e)).boundingClientRect((function(t){t?(console.log("📍 找到目标元素 - ".concat(e,":"),t),o.scrollIntoView=e,setTimeout((function(){o.scrollIntoView="",console.log("✅ scrollIntoView设置成功 - ".concat(e))}),800)):(console.warn("⚠️ 未找到目标元素 - ".concat(e)),setTimeout((function(){o.scrollToCommentByScrollTop(e)}),100))})).exec()}))},scrollToCommentByScrollTop:function(e){var o=this;console.log("📍 使用scroll-top滚动到 - ".concat(e)),this.$nextTick((function(){var n=t.createSelectorQuery().in(o);n.select(".page-scroll-view").boundingClientRect(),n.select("#".concat(e)).boundingClientRect(),n.select(".topic-info-section").boundingClientRect(),n.select(".page-scroll-view").scrollOffset(),n.exec((function(t){if(console.log("📊 scroll-top查询结果 - ".concat(e,":"),t),t&&t.length>=3){var n=t[0],c=t[1],s=t[2],i=t[3];if(n&&c){var r=i?i.scrollTop:o.scrollTop||0,a=r+(c.top-n.top),l=Math.max(0,a-120),m=0;s&&(m=s.height||0,console.log("📏 话题信息区域高度: ".concat(m))),console.log("📐 scroll-top详细计算 - ".concat(e,":"),{scrollViewTop:n.top,commentTop:c.top,currentScrollTop:r,commentAbsoluteTop:a,topicInfoHeight:s?s.height:0,topOffset:120,targetScrollTop:l}),l>0?(o.scrollTop=l+1,o.$nextTick((function(){o.scrollTop=l,console.log("✅ scroll-top设置成功 - ".concat(e,", 位置: ").concat(l))}))):(console.warn("⚠️ 计算的滚动位置为0或负数 - ".concat(e,", 位置: ").concat(l)),o.scrollTop=50)}else console.warn("⚠️ 获取元素位置失败 - scrollView: ".concat(!!n,", comment: ").concat(!!c))}else console.warn("⚠️ 查询结果不完整 - ".concat(e,":"),t)}))}))},debugScrollElements:function(e,o){var n="comment-".concat(o,"-").concat(e);console.log("🔍 调试滚动元素 - ".concat(n));var c=t.createSelectorQuery().in(this);c.select("#".concat(n)).boundingClientRect(),c.select(".page-scroll-view").boundingClientRect(),c.exec((function(t){console.log("🔍 调试结果:",{commentId:n,commentElement:t[0],scrollViewElement:t[1],hasComment:!!t[0],hasScrollView:!!t[1]})}))},testScroll:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"hot";console.log("🧪 测试滚动功能 - ".concat(o,"-").concat(e)),this.debugScrollElements(e,o);var n="comment-".concat(o,"-").concat(e);setTimeout((function(){console.log("🧪 设置scrollIntoView - ".concat(n)),t.scrollIntoView=n,setTimeout((function(){t.scrollIntoView="",console.log("🧪 测试完成 - ".concat(n))}),1e3)}),500)},forceScrollToComment:function(e,o){var n=this,c="comment-".concat(o,"-").concat(e);console.log("🚀 强制滚动到评论 - ".concat(c)),this.scrollIntoView=c,this.$nextTick((function(){var e=t.createSelectorQuery().in(n);e.select("#".concat(c)).boundingClientRect((function(t){if(t){console.log("🚀 强制滚动 - 找到元素:",t);var e=Math.max(0,t.top-100);n.scrollTop=e,console.log("🚀 强制滚动 - 设置scrollTop: ".concat(e))}else console.warn("🚀 强制滚动 - 未找到元素: ".concat(c))})).exec()}))},testScrollToPosition:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:300;console.log("🧪 测试滚动到位置: ".concat(t)),this.scrollTop=t},getCurrentScrollStatus:function(){console.log("📊 当前滚动状态:",{scrollTop:this.scrollTop,scrollIntoView:this.scrollIntoView});var e=t.createSelectorQuery().in(this);e.select(".page-scroll-view").scrollOffset((function(t){console.log("📊 实际滚动位置:",t)})).exec()},processCommentData:function(t){if(!t)return console.warn("评论数据为空"),[];var e=t.comments||[];return console.log("处理评论数据，评论数量:",e.length),e.length>0&&e.forEach((function(t){t&&(t.created_at=t.createdAt||(new Date).toISOString(),t.is_liked=t.isLiked||!1,t.reply_count=t.replyCount||0,t.likes=t.likes||0,t.user?(t.user.avatar||(t.user.avatar="/static/images/toux.png"),t.user.nickname=t.user.nickname||"未知用户",t.user.level=t.user.level||0):t.user={id:0,nickname:"未知用户",avatar:"/static/images/toux.png",level:0},t.replies?t.replies.length>0&&t.replies.forEach((function(t){t&&(t.created_at=t.createdAt||(new Date).toISOString(),t.is_liked=t.isLiked||!1,t.replyTo&&(t.reply_to=t.replyTo),t.user?(t.user.avatar||(t.user.avatar="/static/images/toux.png"),t.user.nickname=t.user.nickname||"未知用户"):t.user={id:0,nickname:"未知用户",avatar:"/static/images/toux.png"})})):t.replies=[])})),e}}};e.default=u}).call(this,o("df3c")["default"])},"5efa":function(t,e,o){},"78af":function(t,e,o){"use strict";o.r(e);var n=o("03b4"),c=o("c920");for(var s in c)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return c[t]}))}(s);o("27de");var i=o("828b"),r=Object(i["a"])(c["default"],n["b"],n["c"],!1,null,"3f1b96c0",null,!1,n["a"],void 0);e["default"]=r.exports},c920:function(t,e,o){"use strict";o.r(e);var n=o("410b"),c=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(s);e["default"]=c.a},dd4e:function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("2300");n(o("3240"));var c=n(o("78af"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(c.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])}},[["dd4e","common/runtime","common/vendor"]]]);