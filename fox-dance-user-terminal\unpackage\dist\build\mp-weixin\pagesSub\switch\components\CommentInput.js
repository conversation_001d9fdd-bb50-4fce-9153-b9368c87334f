(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/components/CommentInput"],{"06a9":function(t,e,n){"use strict";n.r(e);var i=n("07b1"),u=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=u.a},"07b1":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"CommentInput",props:{placeholder:{type:String,default:"说点什么..."},buttonText:{type:String,default:"发送"},useImageButton:{type:Boolean,default:!1},maxLength:{type:Number,default:1e3},value:{type:String,default:""},minHeight:{type:Number,default:40},maxHeight:{type:Number,default:120}},data:function(){return{inputText:this.value,textareaHeight:this.minHeight,baseLineHeight:40,autoFocus:!1}},watch:{value:function(t){var e=this;this.inputText=t,this.$nextTick((function(){e.adjustHeight()}))}},mounted:function(){var t=this;this.$nextTick((function(){t.adjustHeight()}))},methods:{adjustHeight:function(){var e=this,n=t.createSelectorQuery().in(this);n.select(".measure-box").boundingClientRect((function(t){if(t){var n=t.height,i=Math.max(e.minHeight,n);i=Math.min(i,e.maxHeight),e.textareaHeight!==i&&(e.textareaHeight=i)}})).exec()},onInput:function(){var e=this;this.inputText.length>this.maxLength&&(this.inputText=this.inputText.slice(0,this.maxLength),t.showToast({title:"评论字数不能超过".concat(this.maxLength,"字"),icon:"none"})),this.$nextTick((function(){e.adjustHeight()})),this.$emit("input",this.inputText)},onSend:function(){this.inputText.trim()&&this.$emit("send",this.inputText)},clear:function(){this.inputText="",this.textareaHeight=this.minHeight,this.$emit("input","")},focus:function(){var e=this;this.autoFocus=!1,this.$nextTick((function(){e.autoFocus=!0,setTimeout((function(){var n=t.createSelectorQuery().in(e);n.select(".input-textarea").fields({properties:["focus"],context:!0},(function(t){t&&t.context&&t.context.focus({success:function(){console.log("微信小程序设置焦点成功")},fail:function(t){console.error("微信小程序设置焦点失败:",t);var n=e.$refs.textareaRef;n&&n.focus()}})})).exec()}),150)}))},blur:function(){this.autoFocus=!1;var e=t.createSelectorQuery().in(this);e.select(".input-textarea").fields({properties:["blur"],context:!0},(function(t){t&&t.context&&t.context.blur({success:function(){console.log("微信小程序失去焦点成功")},fail:function(t){console.error("微信小程序失去焦点失败:",t)}})})).exec()},onFocus:function(t){console.log("输入框获得焦点"),this.$emit("focus",t)},onBlur:function(t){this.$emit("blur",t),this.autoFocus=!1}}};e.default=n}).call(this,n("df3c")["default"])},"6c29":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=(this._self._c,this.useImageButton?null:this.inputText.trim());this.$mp.data=Object.assign({},{$root:{g0:e}})},u=[]},"94ee":function(t,e,n){"use strict";n.r(e);var i=n("6c29"),u=n("06a9");for(var o in u)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(o);n("b778");var c=n("828b"),s=Object(c["a"])(u["default"],i["b"],i["c"],!1,null,"675d458f",null,!1,i["a"],void 0);e["default"]=s.exports},b4b3:function(t,e,n){},b778:function(t,e,n){"use strict";var i=n("b4b3"),u=n.n(i);u.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesSub/switch/components/CommentInput-create-component',
    {
        'pagesSub/switch/components/CommentInput-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("94ee"))
        })
    },
    [['pagesSub/switch/components/CommentInput-create-component']]
]);
