<view class="comment-input-box data-v-675d458f"><view class="input-container data-v-675d458f"><textarea class="input-textarea data-v-675d458f vue-ref" style="{{'height:'+(textareaHeight+'px')+';'}}" placeholder="{{placeholder}}" maxlength="{{maxLength}}" focus="{{autoFocus}}" auto-blur="{{true}}" show-confirm-bar="{{false}}" cursor-spacing="{{10}}" adjust-position="{{false}}" confirm-type="send" data-ref="textareaRef" data-event-opts="{{[['input',[['__set_model',['','inputText','$event',[]]],['onInput',['$event']]]],['confirm',[['onSend',['$event']]]],['focus',[['onFocus',['$event']]]],['blur',[['onBlur',['$event']]]]]}}" value="{{inputText}}" bindinput="__e" bindconfirm="__e" bindfocus="__e" bindblur="__e"></textarea><view data-ref="measureBox" class="measure-box data-v-675d458f vue-ref">{{inputText||placeholder}}</view><block wx:if="{{useImageButton}}"><image class="send-btn-inside data-v-675d458f" src="/static/icon/send.png" mode="aspectFill" data-event-opts="{{[['tap',[['onSend',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><text data-event-opts="{{[['tap',[['onSend',['$event']]]]]}}" class="{{['text-btn-inside','data-v-675d458f',(!$root.g0)?'disabled':'']}}" bindtap="__e">{{buttonText}}</text></block></view></view>