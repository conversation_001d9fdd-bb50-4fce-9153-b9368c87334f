(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/components/CommentSkeleton"],{"0fc0":function(n,e,t){"use strict";t.d(e,"b",(function(){return u})),t.d(e,"c",(function(){return c})),t.d(e,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},c=[]},"3efa":function(n,e,t){"use strict";t.r(e);var u=t("fda9"),c=t.n(u);for(var a in u)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(a);e["default"]=c.a},"4eec":function(n,e,t){"use strict";var u=t("b270"),c=t.n(u);c.a},"7a6c":function(n,e,t){"use strict";t.r(e);var u=t("0fc0"),c=t("3efa");for(var a in c)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return c[n]}))}(a);t("4eec");var f=t("828b"),o=Object(f["a"])(c["default"],u["b"],u["c"],!1,null,"b0d6891c",null,!1,u["a"],void 0);e["default"]=o.exports},b270:function(n,e,t){},fda9:function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"CommentSkeleton"}}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesSub/switch/components/CommentSkeleton-create-component',
    {
        'pagesSub/switch/components/CommentSkeleton-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7a6c"))
        })
    },
    [['pagesSub/switch/components/CommentSkeleton-create-component']]
]);
