(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/components/TopicCard"],{"0410":function(t,e,i){"use strict";i.r(e);var n=i("5b64"),c=i("38f8");for(var o in c)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return c[t]}))}(o);i("ca6c");var a=i("828b"),r=Object(a["a"])(c["default"],n["b"],n["c"],!1,null,"707c8084",null,!1,n["a"],void 0);e["default"]=r.exports},"189c":function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var c=n(i("b95a")),o=n(i("0843"));i("2175"),c.default.extend(o.default),c.default.locale("zh-cn");var a={name:"TopicCard",props:{topic:{type:Object,required:!0}},computed:{hasCoverImage:function(){return this.topic.coverImage&&""!==this.topic.coverImage.trim()&&"null"!==this.topic.coverImage&&"undefined"!==this.topic.coverImage}},methods:{goToDetail:function(){9==this.topic.id?t.navigateTo({url:"/pagesSub/store/store-list"}):t.navigateTo({url:"/pagesSub/switch/comment?topicId="+this.topic.id+"&content_type=topic"})},formatTime:function(t){return t?(0,c.default)(t).fromNow():""},handleImageError:function(){console.warn("话题封面图加载失败:",this.topic.coverImage)}}};e.default=a}).call(this,i("df3c")["default"])},"38f8":function(t,e,i){"use strict";i.r(e);var n=i("189c"),c=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=c.a},"5b64":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return c})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=(this._self._c,this.topic.createTime?this.formatTime(this.topic.createTime):null);this.$mp.data=Object.assign({},{$root:{m0:e}})},c=[]},"6b35":function(t,e,i){},ca6c:function(t,e,i){"use strict";var n=i("6b35"),c=i.n(n);c.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesSub/switch/components/TopicCard-create-component',
    {
        'pagesSub/switch/components/TopicCard-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0410"))
        })
    },
    [['pagesSub/switch/components/TopicCard-create-component']]
]);
