(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesSub/switch/vote"],{"40dd":function(t,e,n){},"78cb":function(t,e,n){"use strict";(function(t){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("7eb4")),i=o(n("3b2d")),s=o(n("af34")),r=o(n("ee10"));function c(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"===typeof t)return u(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(t,e)}(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var o=0,a=function(){};return{s:a,n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,r=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){r=!0,i=t},f:function(){try{s||null==n.return||n.return()}finally{if(r)throw i}}}}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var l={data:function(){return{metroLines:[],selectedLineIndex:0,selectedLineId:-1,currentLineData:null,selected:-1,remainingVotes:0,voteRecords:[],debugShown:!1,locationAuth:!1,voteTitle:"你希望下一家Fox新店开在哪里",voteInfo:"排名靠前的投票会看地铁附近是否有合适的场地才会最终确定开",voteTips:"每人仅限投票一次，fox会员可投两次",allStationsData:null,isAllStationsSelected:!0,displayMode:"initial",voteSuccess:!1,animatingStations:[]}},computed:{currentStations:function(){return this.isAllStationsSelected?this.allStationsData?this.allStationsData.map((function(t){return t.station})):[]:this.currentLineData&&this.currentLineData.stations||[]},currentVoteCounts:function(){if(this.isAllStationsSelected){var t={};return this.allStationsData&&this.allStationsData.forEach((function(e){t[e.station]=e.votes})),t}return this.currentLineData&&this.currentLineData.voteCounts||{}},sortedStations:function(){var t=this;if(this.isAllStationsSelected){if(this.allStationsData&&this.allStationsData.length>0){if("initial"===this.displayMode&&this.allStationsData.length>15)return this.allStationsData.slice(0,15);if("more"===this.displayMode&&this.allStationsData.length>30)return this.allStationsData.slice(0,30)}return this.allStationsData||[]}if(!this.currentStations.length)return[];var e=this.currentStations.map((function(e){return{station:e,votes:t.currentVoteCounts[e]||0}}));return e.sort((function(t,e){return e.votes-t.votes}))}},mounted:function(){this.getVoteInfo(1),this.getAllMetroLines();var e=t.getLaunchOptionsSync().query;e&&e.lineId&&"-1"!==e.lineId&&(this.selectedLineId=parseInt(e.lineId)||-1,this.isAllStationsSelected=!1),-1===this.selectedLineId?this.buildAllStationsRanking():this.getMetroLineById(this.selectedLineId),this.checkLogin()&&(this.getRemainingVotes(),this.getUserVoteRecords()),this.checkLocationPermission()},methods:{toggleShowMoreStations:function(){"initial"===this.displayMode?this.displayMode="more":"more"===this.displayMode&&(this.displayMode="all")},getVoteInfo:function(e){var n=this;return(0,r.default)(a.default.mark((function o(){var i,s,r;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return i=n.getBaseUrl(),o.prev=1,o.next=4,t.request({url:"".concat(i,"/api/vote-info/").concat(e),method:"GET"});case 4:s=o.sent,console.log("获取投票信息:",s),s.data&&0===s.data.code?(r=s.data.data,n.voteTitle=r.title||n.voteTitle,n.voteInfo=r.info||n.voteInfo,n.voteTips=r.tips||n.voteTips):console.error("获取投票信息失败:",s.data),o.next=12;break;case 9:o.prev=9,o.t0=o["catch"](1),console.error("获取投票信息异常:",o.t0);case 12:case"end":return o.stop()}}),o,null,[[1,9]])})))()},getBaseUrl:function(){return"https://vote.foxdance.com.cn"},getUserInfo:function(){var e=t.getStorageSync("token"),n=t.getStorageSync("userid");return{token:e,userId:n}},checkLogin:function(){var e=this.getUserInfo(),n=e.token;return!!n||(t.showToast({title:"请先登录",icon:"none",duration:2e3}),setTimeout((function(){t.navigateTo({url:"/pages/login/login"})}),1500),!1)},getRemainingVotes:function(){var e=this;return(0,r.default)(a.default.mark((function n(){var o,i,s,r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.checkLogin()){n.next=2;break}return n.abrupt("return");case 2:if(o=e.getUserInfo(),i=o.userId,i){n.next=5;break}return n.abrupt("return");case 5:return s=e.getBaseUrl(),n.prev=6,n.next=9,t.request({url:"".concat(s,"/api/ba-user/remaining-votes/").concat(i),method:"GET"});case 9:r=n.sent,r.data&&0===r.data.code?e.remainingVotes=r.data.data:(console.log("userId:",i),console.error("获取剩余投票次数失败:",r.data),e.remainingVotes=0),n.next=17;break;case 13:n.prev=13,n.t0=n["catch"](6),console.error("获取剩余投票次数异常:",n.t0),e.remainingVotes=0;case 17:case"end":return n.stop()}}),n,null,[[6,13]])})))()},getAllMetroLines:function(){var e=this;return(0,r.default)(a.default.mark((function n(){var o,i,r,c;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return o=e.getBaseUrl(),n.prev=1,n.next=4,t.request({url:"".concat(o,"/api/metro-lines"),method:"GET"});case 4:i=n.sent,i.data&&0===i.data.code?(r={id:-1,lineName:"全部线路"},e.metroLines=[r].concat((0,s.default)(i.data.data)),-1!==e.selectedLineId&&(c=e.metroLines.findIndex((function(t){return t.id===e.selectedLineId})),-1!==c&&(e.selectedLineIndex=c,e.isAllStationsSelected=!1)),e.buildAllStationsRanking()):t.showToast({title:"获取地铁线路失败",icon:"none"}),n.next=11;break;case 8:n.prev=8,n.t0=n["catch"](1),t.showToast({title:"网络请求异常",icon:"none"});case 11:case"end":return n.stop()}}),n,null,[[1,8]])})))()},getMetroLineById:function(e){var n=this;return(0,r.default)(a.default.mark((function o(){var i,s;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return i=n.getBaseUrl(),o.prev=1,o.next=4,t.request({url:"".concat(i,"/api/metro-lines/").concat(e),method:"GET"});case 4:s=o.sent,s.data&&0===s.data.code?n.currentLineData=s.data.data:t.showToast({title:"获取线路详情失败",icon:"none"}),o.next=11;break;case 8:o.prev=8,o.t0=o["catch"](1),t.showToast({title:"网络请求异常",icon:"none"});case 11:case"end":return o.stop()}}),o,null,[[1,8]])})))()},buildAllStationsRanking:function(){var e=this;return(0,r.default)(a.default.mark((function n(){var o,i,s,r,u,l,d,f,h,g,v,p,m,S;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.showLoading({title:"处理数据..."}),n.prev=1,o=e.getBaseUrl(),n.next=5,t.request({url:"".concat(o,"/api/metro-lines"),method:"GET"});case 5:if(i=n.sent,!i.data||0!==i.data.code){n.next=31;break}s=i.data.data,r=[],u=c(s),n.prev=10,u.s();case 12:if((l=u.n()).done){n.next=20;break}return d=l.value,n.next=16,t.request({url:"".concat(o,"/api/metro-lines/").concat(d.id),method:"GET"});case 16:if(f=n.sent,f.data&&0===f.data.code){h=f.data.data,g=h.stations||[],v=h.voteCounts||{},p=c(g);try{for(S=function(){var t=m.value,e=v[t]||0,n=r.findIndex((function(e){return e.station===t}));n>=0?(r[n].votes+=e,r[n].lines.includes(d.lineName)||r[n].lines.push(d.lineName)):r.push({station:t,votes:e,lines:[d.lineName]})},p.s();!(m=p.n()).done;)S()}catch(a){p.e(a)}finally{p.f()}}case 18:n.next=12;break;case 20:n.next=25;break;case 22:n.prev=22,n.t0=n["catch"](10),u.e(n.t0);case 25:return n.prev=25,u.f(),n.finish(25);case 28:r.sort((function(t,e){return e.votes-t.votes})),e.allStationsData=r,console.log("手动构建的所有站点数据:",r);case 31:n.next=36;break;case 33:n.prev=33,n.t1=n["catch"](1),console.error("手动构建站点数据失败:",n.t1);case 36:return n.prev=36,t.hideLoading(),n.finish(36);case 39:case"end":return n.stop()}}),n,null,[[1,33,36,39],[10,22,25,28]])})))()},handleLineChange:function(t){var e=this;this.selectedLineIndex=t.detail.value;var n=this.metroLines[this.selectedLineIndex];n&&(this.selectedLineId=n.id,-1===n.id?(this.isAllStationsSelected=!0,this.displayMode="initial",this.allStationsData&&0!==this.allStationsData.length||this.buildAllStationsRanking()):(this.isAllStationsSelected=!1,this.getMetroLineById(n.id)),this.selected=-1,this.$nextTick((function(){e.getUserVoteRecords()})))},select:function(t){this.remainingVotes<=0||(this.selected=t)},vote:function(){var e=this;return(0,r.default)(a.default.mark((function n(){var o,i,s,r,c,u,l,d,f;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!(e.remainingVotes<=0||-1===e.selected)){n.next=2;break}return n.abrupt("return");case 2:if(!e.isAllStationsSelected){n.next=5;break}return t.showToast({title:"请先选择具体线路再投票",icon:"none",duration:2e3}),n.abrupt("return");case 5:if(e.checkLogin()){n.next=7;break}return n.abrupt("return");case 7:if(!(e.remainingVotes<=0)){n.next=10;break}return t.showToast({title:"您的投票次数已用完",icon:"none",duration:2e3}),n.abrupt("return");case 10:if(o=e.getUserInfo(),i=o.token,s=o.userId,s){n.next=14;break}return t.showToast({title:"获取用户信息失败",icon:"none"}),n.abrupt("return");case 14:return r=e.currentStations[e.selected],c=e.getBaseUrl(),n.prev=16,n.next=19,e.getLocationAuth();case 19:if(u=n.sent,u){n.next=23;break}return t.showModal({title:"提示",content:"投票需要获取您的位置信息，是否前往设置页面授权？",success:function(e){e.confirm&&t.openSetting()}}),n.abrupt("return");case 23:return t.showLoading({title:"获取位置中..."}),n.next=26,new Promise((function(e,n){t.getLocation({type:"gcj02",success:function(t){e(t)},fail:function(t){n(t)}})})).catch((function(e){throw console.error("获取位置失败:",e),t.hideLoading(),t.showToast({title:"获取位置失败，请允许位置权限",icon:"none",duration:2e3}),new Error("获取位置失败")}));case 26:return l=n.sent,t.hideLoading(),console.log("开始投票",{lineId:e.selectedLineId,station:r,userId:s,latitude:l.latitude,longitude:l.longitude}),d="".concat(c,"/api/metro-lines/").concat(e.selectedLineId,"/location-vote/").concat(r,"?latitude=").concat(l.latitude,"&longitude=").concat(l.longitude,"&userId=").concat(s),n.next=32,t.request({url:d,method:"POST",header:{bausertoken:i}});case 32:f=n.sent,console.log("投票响应",f),f.data&&0===f.data.code?(e.handleVoteSuccess(r),e.remainingVotes--):e.handleVoteError(f.data),n.next=41;break;case 37:n.prev=37,n.t0=n["catch"](16),console.error("投票异常",n.t0),"获取位置失败"!==n.t0.message&&t.showToast({title:"网络请求异常",icon:"none"});case 41:case"end":return n.stop()}}),n,null,[[16,37]])})))()},getLocationAuth:function(){return(0,r.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){t.getSetting({success:function(t){t.authSetting["scope.userLocation"]?e(!0):e(!1)},fail:function(){e(!1)}})})));case 1:case"end":return e.stop()}}),e)})))()},handleVoteSuccess:function(e){var n=this;this.voteSuccess=!0,this.animatingStations.push(e),void 0!==this.currentVoteCounts[e]?this.currentVoteCounts[e]++:this.currentVoteCounts[e]=1,this.selected=-1,this.getUserVoteRecords(),setTimeout((function(){console.log("刷新线路数据以获取最新票数"),n.getMetroLineById(n.selectedLineId),n.buildAllStationsRanking();var t=n.animatingStations.indexOf(e);t>-1&&n.animatingStations.splice(t,1)}),800),setTimeout((function(){n.voteSuccess=!1}),1e3),t.showToast({title:"投票成功",icon:"success"})},handleVoteError:function(e){var n="投票失败";e&&(console.error("投票失败详情:",e),console.error("错误信息内容:",e.message),n=e.message||"投票失败",5e4===e.code?e.message&&(-1!==e.message.indexOf("仅限广州地区")?n="仅限广州地区用户参与投票":-1!==e.message.indexOf("异常投票行为")&&(n="检测到异常投票行为，请稍后再试")):4e4===e.code&&(n=e.message||"参数错误")),t.showToast({title:n,icon:"none",duration:2e3})},getPercent:function(t){if(!this.currentVoteCounts)return 0;var e=Object.values(this.currentVoteCounts).reduce((function(t,e){return t+Number(e)}),0);return 0===e?0:(t/e*100).toFixed(1)},isVotedStation:function(t){var e=this;return!(!this.voteRecords||!Array.isArray(this.voteRecords)||0===this.voteRecords.length)&&(this.debugShown||(console.log("调试投票记录信息:"),console.log("当前选择的线路ID:",this.selectedLineId,"类型:",(0,i.default)(this.selectedLineId)),console.log("投票记录:",this.voteRecords),this.debugShown=!0),this.isAllStationsSelected?this.voteRecords.some((function(e){return e.stationName===t})):this.voteRecords.some((function(n){var o=String(n.metroLineId),a=String(e.selectedLineId);return o===a&&n.stationName===t})))},getUserVoteRecords:function(){var e=this;return(0,r.default)(a.default.mark((function n(){var o,i,s,r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(o=e.getUserInfo(),i=o.userId,i){n.next=3;break}return n.abrupt("return");case 3:return s=e.getBaseUrl(),n.prev=4,t.showLoading({title:"加载投票记录..."}),n.next=8,t.request({url:"".concat(s,"/api/vote-records/user/").concat(i),method:"GET"});case 8:r=n.sent,t.hideLoading(),r.data&&0===r.data.code?(e.voteRecords=r.data.data||[],console.log("获取到投票记录:",e.voteRecords),e.$forceUpdate()):console.error("获取投票记录失败:",r.data),n.next=17;break;case 13:n.prev=13,n.t0=n["catch"](4),t.hideLoading(),console.error("获取投票记录异常:",n.t0);case 17:case"end":return n.stop()}}),n,null,[[4,13]])})))()},onShareTimeline:function(){return{title:"Fox Dance新店投票"}},onShareAppMessage:function(){return{title:"Fox Dance新店投票，快来投票吧！",path:"/pagesSub/switch/vote"}},checkLocationPermission:function(){var e=this;t.getSetting({success:function(n){e.locationAuth=!!n.authSetting["scope.userLocation"],n.authSetting["scope.userLocation"]||t.showModal({title:"提示",content:"投票需要获取您的位置信息，是否前往设置页面授权？",success:function(n){n.confirm&&t.openSetting({success:function(n){e.locationAuth=!!n.authSetting["scope.userLocation"],n.authSetting["scope.userLocation"]?t.showToast({title:"授权成功",icon:"success"}):t.showToast({title:"授权失败，无法参与投票",icon:"none"})}})}})},fail:function(){e.locationAuth=!1}})},requestLocationPermission:function(){var e=this;t.authorize({scope:"scope.userLocation",success:function(){e.locationAuth=!0,t.showToast({title:"授权成功",icon:"success"})},fail:function(){t.showModal({title:"提示",content:"需要位置权限才能参与投票，是否前往设置页面授权？",success:function(n){n.confirm&&t.openSetting({success:function(n){e.locationAuth=!!n.authSetting["scope.userLocation"],e.locationAuth&&t.showToast({title:"授权成功",icon:"success"})}})}})}})},getBtnText:function(){return this.voteSuccess?"投票成功":this.remainingVotes<=0?"投票次数已用完":-1===this.selected?"请选择站点":"立即投票"},getTotalVotes:function(){return this.sortedStations&&0!==this.sortedStations.length?this.sortedStations.reduce((function(t,e){return t+e.votes}),0):0}}};e.default=l}).call(this,n("df3c")["default"])},"83f6":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.metroLines.length),o=t.sortedStations.length,a=o>0?t.getTotalVotes():null,i=o>0?t.__map(t.sortedStations,(function(e,n){var o=t.__get_orig(e),a=t.isVotedStation(e.station),i=t.getPercent(e.votes),s=t.getPercent(e.votes),r=t.animatingStations.includes(e.station);return{$orig:o,m1:a,m2:i,m3:s,g2:r}})):null,s=o>0?t.isAllStationsSelected&&"all"!==t.displayMode&&t.allStationsData&&t.allStationsData.length>("more"===t.displayMode?30:15):null,r=t.getBtnText();t._isMounted||(t.e0=function(e,n){var o=arguments[arguments.length-1].currentTarget.dataset,a=o.eventParams||o["event-params"];n=a.item;t.select(t.currentStations.indexOf(n.station))}),t.$mp.data=Object.assign({},{$root:{g0:n,g1:o,m0:a,l0:i,g3:s,m4:r}})},a=[]},"856f":function(t,e,n){"use strict";var o=n("40dd"),a=n.n(o);a.a},adf0:function(t,e,n){"use strict";n.r(e);var o=n("78cb"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},ea73:function(t,e,n){"use strict";n.r(e);var o=n("83f6"),a=n("adf0");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("856f");var s=n("828b"),r=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,"19852041",null,!1,o["a"],void 0);e["default"]=r.exports},f1e6:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("2300");o(n("3240"));var a=o(n("ea73"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["f1e6","common/runtime","common/vendor"]]]);