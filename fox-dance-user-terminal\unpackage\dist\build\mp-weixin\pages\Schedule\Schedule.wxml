<view class="schedule" style="{{'--qjbutton-color:'+(qjbutton)+';'+('--qjziti-color:'+(qjziti)+';')}}"><view style="{{('height:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;width:100%')}}"></view><view class="my_head"><view class="my_head_n" style="{{('margin-top:'+(safeAreaTop+10)+'px')}}"><view class="my_head_t" style="{{('height:'+menuButtonInfoHeight+'px')}}"><view class="my_head_t_l"><image src="/static/images/icon18-1.png"></image><text>{{storeInfoName}}</text></view><view class="my_head_t_title">约课</view></view><view class="my_head_b"><view class="lsxq_head_l"><view class="stor_thr_c_n"><view data-event-opts="{{[['tap',[['jbStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',jbToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{jibText==''?'级别':jibText}}<text></text></view><view data-event-opts="{{[['tap',[['wuzStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',wuzToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{wuzText==''?'舞种':wuzText}}<text></text></view><view data-event-opts="{{[['tap',[['laosStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',laosToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{laosText==''?'老师':laosText}}<text></text></view></view></view><view class="lsxq_head_r"><text></text><image src="/static/images/icon36.png" data-event-opts="{{[['tap',[['navTo',['/pages/Schedule/search?id='+array_md_cunc[index_md].id]]]]]}}" bindtap="__e"></image></view></view></view></view><block wx:if="{{jbToggle||wuzToggle||laosToggle}}"><view data-event-opts="{{[['tap',[['gbTcTap',['$event']]]]]}}" class="gg_rgba" bindtap="__e"></view></block><block wx:if="{{jbToggle}}"><view class="teaxzTanc" style="{{('top:'+(safeAreaTop+menuButtonInfoHeight+60)+'px;')}}"><view class="teaxzTanc_t"><block wx:for="{{jibLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jibTap',[index]]]]]}}" class="{{[jibIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['jibReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['jibSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><block wx:if="{{wuzToggle}}"><view class="teaxzTanc" style="{{('top:'+(safeAreaTop+menuButtonInfoHeight+60)+'px;')}}"><view class="teaxzTanc_t"><block wx:for="{{wuzLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['wuzTap',[index]]]]]}}" class="{{[wuzIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['wuzReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['wuzSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><block wx:if="{{laosToggle}}"><view class="teaxzTanc" style="{{('top:'+(safeAreaTop+menuButtonInfoHeight+60)+'px;')}}"><view class="teaxzTanc_t"><block wx:for="{{laosLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['laosTap',[index]]]]]}}" class="{{[laosIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['laosReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['laosSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><view class="rlxz_con" style="margin-top:120rpx;"><view class="rlxz_con_l"><scroll-view scroll-x="true" scroll-left="{{scrollLeft}}" data-event-opts="{{[['scroll',[['scrollJt',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{sjsxLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['sjsxTap',[index,'$0'],[[['sjsxLists','',index]]]]]]]}}" class="{{['rlxz_con_l_li',sjsxIndex==index?'rlxz_con_l_li_ac':'']}}" bindtap="__e"><view>{{item.week}}</view><view>{{item.day}}</view><text></text></view></block></scroll-view></view><view class="rlxz_con_r"><image src="/static/images/icon53.png"></image><picker mode="date" value="{{date_sx}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange_sx',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{date_sx}}</view></picker></view></view><view class="md_xz"><image class="md_xz_bj" src="/static/images/icon52-3.png"></image><view class="md_xz_title">{{array_md[index_md]}}</view><image class="md_xz_xt" src="/static/images/icon52-4.png"></image><picker value="{{index_md}}" range="{{array_md}}" data-event-opts="{{[['change',[['bindPickerChange_md',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{array_md[index_md]}}</view></picker></view><view class="notice flex"><view class="notice_l"><image src="/static/images/index_notice.png" mode="scaleToFill"></image></view><view class="notice_r flex-1"><uni-notice-bar vue-id="4dd08960-1" scrollable="{{true}}" single="{{true}}" text="{{userInfo.notice}}" background-color="transparent" color="#333" bind:__l="__l"></uni-notice-bar></view></view><view class="teaCon"><block wx:for="{{storeCourseLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['storesxqTap',['$0'],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li" bindtap="__e"><view class="teaCon_li_a">{{item.course.name}}</view><view class="teaCon_li_b"><image class="teaCon_li_b_l" src="{{imgbaseUrl+item.teacher.image}}" mode="aspectFill"></image><view class="teaCon_li_b_c"><view class="teaCon_li_b_c_a">{{item.start_time+"-"+item.end_time}}</view><view class="teaCon_li_b_c_b">{{"上课老师："+item.teacher.name}}</view><block wx:if="{{item.frequency*1>0}}"><view class="teaCon_li_b_c_b">{{"次卡消耗："+item.frequency*1+"次"}}</view></block><view class="teaCon_li_b_c_c"><block wx:if="{{item.level_name}}"><text>{{item.level_name}}</text></block><text>{{item.dance_name}}</text></view></view><block wx:if="{{item.status==1}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">待开课</view></block><block wx:else><block wx:if="{{item.status==2}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">授课中</view></block><block wx:else><block wx:if="{{item.status==3}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">已完成</view></block><block wx:else><block wx:if="{{item.status==4}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">等位中</view></block><block wx:else><block wx:if="{{item.status==6}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r yysj" style="background:#BEBEBE;" catchtap="__e"><text>{{item.start_reservation}}</text><text>开始预约</text></view></block><block wx:else><block wx:if="{{item.status==7}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">截止预约</view></block><block wx:else><block wx:if="{{item.equivalent*1==0&&item.appointment_number*1>=item.maximum_reservation*1}}"><view data-event-opts="{{[['tap',[['kqhyts',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">预约</view></block><block wx:else><block wx:if="{{item.member==0}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="teaCon_li_b_r" style="{{(item.member==0?'background:#BEBEBE':'')}}" catchtap="__e">预约</view></block><block wx:else><view data-event-opts="{{[['tap',[['yypdTo',['$0','/pages/Schedule/Schedulexq?id'+item.id],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li_b_r" catchtap="__e">{{item.waiting_number*1>0?'去排队':'预约'}}</view></block></block></block></block></block></block></block></block></view><block wx:if="{{item.appointment_number>0}}"><view class="teaCon_li_c"><view class="teaCon_li_c_l"><block wx:for="{{item.appointment_people}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{imgbaseUrl+item.avatar}}" mode="aspectFit"></image></block></view><view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<block wx:if="{{item.waiting_number*1>0}}"><text>{{item.waiting_number}}</text>人在等位</block></view></view></block></view></block></view><block wx:if="{{$root.g0==0}}"><view class="gg_zwsj" style="margin-bottom:60rpx;"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无课程</text></view></view></block><block wx:if="{{ljtkToggle}}"><view class="yytnCon"><view class="yytnCon_n"><image src="{{imgbaseUrlOss+'/userreport/icon55.png'}}"></image><text data-event-opts="{{[['tap',[['ljktTap',['$event']]]]]}}" bindtap="__e"></text></view><image src="/static/images/icon56.png" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e"></image></view></block><view style="width:100%;height:300rpx;"></view><tabbar class="vue-ref" vue-id="4dd08960-2" current="{{3}}" data-ref="tabbar" bind:__l="__l"></tabbar></view>