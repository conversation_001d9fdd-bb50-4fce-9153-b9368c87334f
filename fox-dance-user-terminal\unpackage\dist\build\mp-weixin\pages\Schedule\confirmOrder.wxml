<block wx:if="{{courseDetail.id}}"><view class="confirmOrder" style="{{'--qjbutton-color:'+(qjbutton)+';'+('--qjziti-color:'+(qjziti)+';')}}"><block wx:if="{{!yuekToggle}}"><view class="xsk_one"><view class="xsk_one_title">{{courseDetail.course.name}}</view><view class="xsk_one_li"><view class="xsk_one_li_l">门店</view><view class="xsk_one_li_r">{{courseDetail.store.name}}</view></view><view class="xsk_one_li"><view class="xsk_one_li_l">课程时长</view><view class="xsk_one_li_r">{{courseDetail.duration+"分钟"}}</view></view><view class="xsk_one_li"><view class="xsk_one_li_l">上课地址</view><view class="xsk_one_li_r">{{courseDetail.store.address}}</view></view><view class="xsk_one_li"><view class="xsk_one_li_l">授课讲师</view><view class="xsk_one_li_r">{{courseDetail.teacher.name}}</view></view></view><view class="xsk_one"><view class="xsk_one_li"><view class="xsk_one_li_l">上课时间</view><view class="xsk_one_li_r">{{courseDetail.start_time}}</view></view><view class="xsk_one_li"><view class="xsk_one_li_l">昵称</view><view class="xsk_one_li_r">{{userInfo.nickname==''?'微信昵称':userInfo.nickname}}</view></view><view class="xsk_one_li"><view class="xsk_one_li_l">手机号</view><view class="xsk_one_li_r">{{userInfo.mobile}}</view></view><view data-event-opts="{{[['tap',[['yhqTap',['$event']]]]]}}" class="xsk_one_li" bindtap="__e"><view class="xsk_one_li_l">会员卡</view><view class="xsk_one_li_r" style="color:#FF6D5C;">{{yhkXzInfo.contract_name==''?'请选择会员卡':yhkXzInfo.contract_name}}<image src="/static/images/right_more.png"></image></view></view></view><view class="memk_six"><view class="memk_six_a"><image src="/static/images/icon29.png"></image>约课注意事项</view><view class="memk_six_b">{{zysxText}}</view></view><view data-event-opts="{{[['tap',[['yukSubTap',['$event']]]]]}}" class="ordzf_foo" style="margin-top:-100rpx;" bindtap="__e">提交约课</view></block><block wx:else><block wx:if="{{ztType==2}}"><view class="yycgCon"><image src="/static/images/icon54.png"></image><view class="yycgCon_a">排队约课中</view><view class="yycgCon_b">可在个人中心“<text data-event-opts="{{[['tap',[['kecGoTap',['$event']]]]]}}" bindtap="__e">我的课程</text>”中查看</view></view></block><block wx:else><view class="yycgCon"><image src="/static/images/icon54.png"></image><view class="yycgCon_a">{{ztType==1?'约课成功':'预约成功'}}</view><view class="yycgCon_b"><block wx:if="{{ztType==4}}">当前正在排队中，</block>可在个人中心“<text data-event-opts="{{[['tap',[['kecGoTap',['$event']]]]]}}" bindtap="__e">我的课程</text>”中查看</view></view></block></block><view class="aqjlViw"></view><block wx:if="{{yhqToggle}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="gg_rgba" bindtap="__e"></view></block><block wx:if="{{yhqToggle}}"><view class="thq_tanc"><view class="thq_tanc_t"><text>会员卡</text><image src="/static/images/popup_close.png" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e"></image></view><view class="thq_tanc_b"><block wx:for="{{cardsLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="mycards_thr_li"><image class="mycards_thr_li_bj" src="{{imgbaseUrl+item.image}}"></image><view class="mycards_thr_li_zt">{{item.status==0?'未激活':item.status==1?'使用中':item.status==2?'请假中':item.status==3?'已耗尽':item.status==4?'已过期':item.status==5?'已转让':item.status==6?'已退款':item.status==7?'已转卡':''}}</view><view class="mycards_thr_li_c"><view class="mycards_thr_li_c_l"><image src="{{imgbaseUrl+userInfo.avatar}}" mode="aspectFill"></image></view><view class="mycards_thr_li_c_r"><view class="mycards_thr_li_c_r_a">{{item.contract_name}}</view><block wx:if="{{item.type*1==0}}"><view class="mycards_thr_li_c_r_b">剩余<text>{{item.surplus_frequency}}</text>次</view></block><block wx:else><view class="mycards_thr_li_c_r_b">{{item.status>0?item.become_time+'到期':'未激活'}}</view></block></view></view><view class="mycards_thr_li_c_r_f"><view class="mycards_thr_li_c_r_f_l">{{"使用期限:"+(item.status==0?'未激活':item.activation_time+' - '+item.become_time)}}</view><view data-event-opts="{{[['tap',[['syhykTap',['$0'],[[['cardsLists','',index]]]]]]]}}" class="mycards_thr_li_c_r_f_r" bindtap="__e">使用</view></view></view></block></view><block wx:if="{{false}}"><view class="thq_tanc_b"><block wx:for="{{cardsLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="mycards_two_li"><view class="mycards_two_li_t" style="align-items:initial;"><image class="mycards_two_li_t_l" src="{{imgbaseUrl+userInfo.avatar}}" mode="aspectFill"></image><view class="mycards_two_li_t_r"><block wx:if="{{item.member_card_number}}"><view>{{"会员ID:"+item.member_card_number}}</view></block><block wx:if="{{item.contract_name}}"><text>{{item.contract_name}}</text></block><text>{{(item.type*1==0?'次卡':'时长卡')+"："}}<block wx:if="{{item.type*1==0}}">{{"剩余"+(item.surplus_frequency+'次　')}}</block>{{item.become_time+'日到期'}}</text></view></view><view data-event-opts="{{[['tap',[['syhykTap',['$0'],[[['cardsLists','',index]]]]]]]}}" class="mycards_two_li_t_b" bindtap="__e">使用</view></view></block></view></block></view></block><block wx:if="{{lxykToggle}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="lxkcCon" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="lxkcCon_n" catchtap="__e"><view class="lxkcCon_t">以下课程与当前课程时间相近，推荐继续预约</view><view class="lxkcCon_c"><block wx:for="{{storeCourseLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['storesxqTap',['$0'],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li" bindtap="__e"><view class="teaCon_li_a">{{item.course.name}}</view><view class="teaCon_li_b"><image class="teaCon_li_b_l" src="{{imgbaseUrl+item.teacher.image}}" mode="aspectFill"></image><view class="teaCon_li_b_c"><view class="teaCon_li_b_c_a">{{item.start_time+"-"+item.end_time}}</view><view class="teaCon_li_b_c_b">{{"上课老师："+item.teacher.name}}</view><block wx:if="{{item.frequency*1>0}}"><view class="teaCon_li_b_c_b">{{"次卡消耗："+item.frequency*1+"次"}}</view></block><view class="teaCon_li_b_c_c"><block wx:if="{{item.level_name}}"><text>{{item.level_name}}</text></block><text>{{item.dance_name}}</text></view></view><block wx:if="{{item.status==1}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">待开课</view></block><block wx:else><block wx:if="{{item.status==2}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">授课中</view></block><block wx:else><block wx:if="{{item.status==3}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">已完成</view></block><block wx:else><block wx:if="{{item.status==4}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">等位中</view></block><block wx:else><block wx:if="{{item.status==6}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r yysj" style="background:#BEBEBE;" catchtap="__e"><text>{{item.start_reservation}}</text><text>开始预约</text></view></block><block wx:else><block wx:if="{{item.status==7}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">截止预约</view></block><block wx:else><block wx:if="{{item.equivalent*1==0&&item.appointment_number*1>=item.maximum_reservation*1}}"><view data-event-opts="{{[['tap',[['kqhyts',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">预约</view></block><block wx:else><block wx:if="{{item.member==0}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="teaCon_li_b_r" style="{{(item.member==0?'background:#BEBEBE':'')}}" catchtap="__e">预约</view></block><block wx:else><view data-event-opts="{{[['tap',[['yypdTo',['$0','/pages/Schedule/Schedulexq?id'+item.id],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li_b_r" catchtap="__e">{{item.waiting_number*1>0?'去排队':'预约'}}</view></block></block></block></block></block></block></block></block></view><block wx:if="{{item.appointment_number>0}}"><view class="teaCon_li_c"><view class="teaCon_li_c_l"><block wx:for="{{item.appointment_people}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{imgbaseUrl+item.avatar}}" mode="aspectFit"></image></block></view><view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<block wx:if="{{item.waiting_number*1>0}}"><text>{{item.waiting_number}}</text>人在等位</block></view></view></block></view></block></view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="lxkcCon_f" bindtap="__e">取消</view></view></view></block><block wx:if="{{ljtkToggle}}"><view class="yytnCon"><view class="yytnCon_n"><image src="{{imgbaseUrlOss+'/userreport/icon55.png'}}"></image><text data-event-opts="{{[['tap',[['ljktTap',['$event']]]]]}}" bindtap="__e"></text></view><image src="/static/images/icon56.png" data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block>