<view class="coursePackage"><view class="les_search"><view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="搜索课包名称" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$event']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchTap',['$event']]]]]}}" class="les_search_r" bindtap="__e">搜索</view></view><view class="{{['cour_one',jbToggle||wuzToggle||laosToggle?'stor_thr_c_fixed':'']}}"><view class="cour_one_n"><view data-event-opts="{{[['tap',[['jbStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',jbToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{jibText==''?'级别':jibText}}<text></text></view><view data-event-opts="{{[['tap',[['wuzStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',wuzToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{wuzText==''?'舞种':wuzText}}<text></text></view><view data-event-opts="{{[['tap',[['laosStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',laosToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{laosText==''?'老师':laosText}}<text></text></view></view><block wx:if="{{jbToggle||wuzToggle||laosToggle}}"><view data-event-opts="{{[['tap',[['gbTcTap',['$event']]]]]}}" class="gg_rgba" bindtap="__e"></view></block><block wx:if="{{jbToggle}}"><view class="teaxzTanc"><view class="teaxzTanc_t"><block wx:for="{{jibLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jibTap',[index]]]]]}}" class="{{[jibIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['jibReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['jibSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><block wx:if="{{wuzToggle}}"><view class="teaxzTanc"><view class="teaxzTanc_t"><block wx:for="{{wuzLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['wuzTap',[index]]]]]}}" class="{{[wuzIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['wuzReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['wuzSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><block wx:if="{{laosToggle}}"><view class="teaxzTanc"><view class="teaxzTanc_t"><block wx:for="{{laosLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['laosTap',[index]]]]]}}" class="{{[laosIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['laosReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['laosSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block></view><view class="cour_two"><block wx:for="{{coursePackageLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/coursePackage/myCoursexq?id='+item.id]]]]]}}" class="cour_two_li" bindtap="__e"><view class="cour_two_li_l"><image src="{{imgbaseUrl+item.image}}" mode="aspectFill"></image></view><view class="cour_two_li_r"><view class="cour_two_li_r_a">{{item.name}}</view><block wx:if="{{item.levelTable}}"><view class="cour_two_li_r_b">{{item.levelTable.name+"/"+item.danceTable.name}}</view></block><view class="cour_two_li_r_c">{{"课程时长："+item.duration*1+"分钟"}}</view><view class="cour_two_li_r_d">{{"讲师:"+(item.teacher?item.teacher.name:'-')}}</view><view class="cour_two_li_r_e"><view class="cour_two_li_r_e_l">{{"已售"+item.sales_volume*1}}<text>{{"¥"+item.price*1}}</text></view><view class="cour_two_li_r_e_r">详情</view></view></view></view></block><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block></view><view class="aqjlViw"></view></view>