<block wx:if="{{loding}}"><view class="teacherDetails" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><u-navbar vue-id="7af7d000-1" title="讲师详情" back-icon-name="arrow-left" back-icon-color="#ffffff" background="{{$root.a0}}" border-bottom="{{false}}" title-color="{{navBg==1?'#fff':'#fff'}}" title-size="32" bind:__l="__l"></u-navbar><view class="lsxq_ban" style="{{('margin-top:-'+(safeAreaTop+20+menuButtonInfoHeight)+'px;')}}"><image src="{{imgbaseUrl+teacherImage}}" mode="aspectFill"></image></view><view class="lsxq_title"><text>{{teacherName}}</text></view><view class="lsxq_two"><view class="cour_two"><block wx:for="{{coursePackageLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/coursePackage/myCoursexq?id='+item.id]]]]]}}" class="cour_two_li" bindtap="__e"><view class="cour_two_li_l"><image src="{{imgbaseUrl+item.image}}" mode="aspectFill"></image></view><view class="cour_two_li_r"><view class="cour_two_li_r_a">{{item.name}}</view><view class="cour_two_li_r_b">{{item.levelTable.name+"/"+item.danceTable.name}}</view><view class="cour_two_li_r_c">{{"课程时长："+item.duration*1+"分钟"}}</view><view class="cour_two_li_r_d">{{"讲师:"+teacherName}}</view><view class="cour_two_li_r_e"><view class="cour_two_li_r_e_l">{{"已售"+item.sales_volume*1}}<text>{{"¥"+item.price*1}}</text></view><view class="cour_two_li_r_e_r">详情</view></view></view></view></block><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block></view></view><view class="aqjlViw"></view></view></block>