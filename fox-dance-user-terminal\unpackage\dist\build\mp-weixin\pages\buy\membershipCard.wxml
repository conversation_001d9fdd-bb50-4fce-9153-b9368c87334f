<view class="membershipCard"><view class="memk_one"><view data-event-opts="{{[['tap',[['cardTab',[0]]]]]}}" class="{{[cardType==0?'memk_one_ac':'']}}" bindtap="__e">次卡<text></text></view><view data-event-opts="{{[['tap',[['cardTab',[1]]]]]}}" class="{{[cardType==1?'memk_one_ac':'']}}" bindtap="__e">时长卡<text></text></view></view><block wx:if="{{loding}}"><view class="memk_ban"><image class="memk_ban_bj" src="{{cardType==0?imgbaseUrl+cardsInfo.secondary_card_image:imgbaseUrl+cardsInfo.duration_chart}}" mode="aspectFill"></image><view class="memk_ban_n"><view data-event-opts="{{[['tap',[['xzmdTap',['$event']]]]]}}" class="memk_ban_n_v" bindtap="__e"><view>{{mdText}}</view><text></text></view></view></view><view class="memk_two"><view class="memk_two_t"><view class="kcxq_two_t_n"><text>尊享特权</text><text style="display:none;"></text></view></view><view class="memk_two_b"><block wx:for="{{cardsInfo.privilege}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="memk_two_b_li"><view class="memk_two_b_li_l"><block wx:if="{{index<=8}}">0</block>{{index+1}}</view><view class="memk_two_b_li_r"><view>{{item}}</view></view></view></block></view></view><block wx:if="{{$root.g0>0}}"><view class="memk_thr"><block wx:for="{{cardsInfo.card}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['yhTap',[index]]]]]}}" class="{{['memk_thr_li',hyType==index?'memk_thr_li_ac':'']}}" bindtap="__e"><view class="memk_thr_li_a"><text>{{item.number}}</text>{{item.type*1==0?'次':item.type*1==1?'年':item.type*1==2?'个月':''}}</view><view class="memk_thr_li_b">{{item.average_price}}</view><view class="memk_thr_li_c"><text>{{"￥"+item.price*1}}</text></view><image class="memk_thr_li_xz" src="/static/images/icon28-1.png"></image></view></block></view></block><view data-event-opts="{{[['tap',[['ljktTap',['$event']]]]]}}" class="memk_fou" bindtap="__e">立即开通</view><view class="memk_fiv">开通会员前请阅读<text data-event-opts="{{[['tap',[['navTo',['/pages/login/xieYi?type=99']]]]]}}" catchtap="__e">《会员服务协议》</text></view><view class="memk_six"><view class="memk_six_a"><image src="/static/images/icon29.png"></image>约课说明</view><view class="memk_six_b">{{cardsInfo.appointment_instructions}}</view></view></block><block wx:if="{{xyToggle}}"><view class="xytc"><view class="xytcCon"><view class="xytcCon_a">会员服务协议提示</view><view class="xytcCon_b">欢迎使用Fox舞蹈小程序!为了更好的保您的个人权益，在使用本产品前，请先阅读并同意以下内容:</view><view class="xytcCon_c"><scroll-view class="scroll-view vue-ref" style="{{'height:'+(scrollViewHeight+'px')+';'}}" scroll-y="{{true}}" id="myScrollView" data-ref="scrollViewRef" data-event-opts="{{[['scroll',[['onScroll',['$event']]]]]}}" bindscroll="__e"><view><rich-text nodes="{{xyCont}}"></rich-text></view></scroll-view></view><view class="xytcCon_b">如您同意以上内容，请点击同意并继续，开始使用我们的产品和服务!</view><view class="xytcCon_f"><view data-event-opts="{{[['tap',[['tyTap',['$event']]]]]}}" class="ty" style="{{(isScrollToBottom?'':'opacity:.7')}}" bindtap="__e">{{isScrollToBottom?'同意并继续':'请仔细阅读下滑查看完毕'}}</view><view data-event-opts="{{[['tap',[['xyGbTap',['$event']]]]]}}" class="noty" bindtap="__e">不同意</view></view></view></view></block></view>