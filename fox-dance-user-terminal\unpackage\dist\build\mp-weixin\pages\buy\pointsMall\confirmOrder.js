(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/pointsMall/confirmOrder"],{"0767":function(t,n,e){"use strict";var a=e("879c"),o=e.n(a);o.a},2761:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("2300");a(e("3240"));var o=a(e("c6a1"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"38de":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=e("d0b6"),o={data:function(){return{isLogined:!0,productxq:{id:0},imgbaseUrl:"",remark:"",shouAddr:{area:""},qjbutton:"#131315",jinzLd:!0}},onShow:function(){t.getStorageSync("diancan")?this.shouAddr=t.getStorageSync("diancan"):this.shouAddr={area:""}},onLoad:function(n){this.qjbutton=t.getStorageSync("storeInfo").button,this.imgbaseUrl=this.$baseUrl,this.productxq=JSON.parse(n.productxq),this.addressData(),console.log(this.productxq)},methods:{dhSubTap:function(){if(""==this.shouAddr.area)return t.showToast({icon:"none",title:"请选择收货地址~",duration:2e3}),!1;var n=this;if(!n.jinzLd)return t.showToast({icon:"none",title:"您点击的太快了~",duration:2e3}),!1;n.jinzLd=!1,t.showLoading({title:"支付中..."}),(0,a.exchangeSubApi)({goods_id:n.productxq.id,addr_id:n.shouAddr.id,sku_id:n.productxq.skuid,num:n.productxq.num,remark:n.remark}).then((function(e){console.log("提交兑换",e),1==e.code?t.requestPayment({timeStamp:e.data.timeStamp,nonceStr:e.data.nonceStr,package:e.data.package,signType:e.data.signType,paySign:e.data.paySign,success:function(e){t.hideLoading(),n.jinzLd=!0,t.redirectTo({url:"/pages/buy/pointsMall/success"})},fail:function(e){console.log("fail:"+JSON.stringify(e),"不回话接口"),"requestPayment:fail cancel"==e.errMsg&&(n.jinzLd=!0,t.hideLoading(),t.showToast({icon:"none",title:"支付取消",duration:2e3}))}}):(n.jinzLd=!0,t.hideLoading(),t.showToast({icon:"none",title:e.msg,duration:2e3}))}))},goToAddr:function(n){t.navigateTo({url:"/pages/mine/address?type=".concat(n)})},addressData:function(){var n=this;(0,a.addrList)({page:1,size:999}).then((function(e){if(1==e.code){console.log(e,"地址列表");for(var a=[],o=0;o<e.data.length;o++)1==e.data[o].is_default&&a.push(e.data[o]);0==a.length?n.shouAddr={area:""}:(n.shouAddr=a[0],t.setStorageSync("diancan",a[0]))}else n.mescroll.endErr()}))},navTo:function(n){t.navigateTo({url:n})}}};n.default=o}).call(this,e("df3c")["default"])},"4eeb":function(t,n,e){"use strict";e.r(n);var a=e("38de"),o=e.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);n["default"]=o.a},"879c":function(t,n,e){},"8abb":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},o=[]},c6a1:function(t,n,e){"use strict";e.r(n);var a=e("8abb"),o=e("4eeb");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);e("0767");var r=e("828b"),d=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=d.exports}},[["2761","common/runtime","common/vendor"]]]);