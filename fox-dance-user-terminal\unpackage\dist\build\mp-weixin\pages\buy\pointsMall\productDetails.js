(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/pointsMall/productDetails"],{"0d24":function(t,e,n){"use strict";var i=n("3b71"),o=n.n(i);o.a},"380c":function(t,e,n){"use strict";n.r(e);var i=n("73df"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"3b71":function(t,e,n){},4744:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("2300");i(n("3240"));var o=i(n("94f8"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"73df":function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n("d0b6"),a=i(n("ef4a")),s={components:{shoppingselect:function(){n.e("pages/buy/specification").then(function(){return resolve(n("f02e"))}.bind(null,n)).catch(n.oe)}},data:function(){return{loding:!1,isLogined:!0,score:0,imgbaseUrl:"",swiperIndex:0,bannerLists:[],kcjsDetail:"",goodsDetail:{id:0},qjbutton:"#131315"}},onShow:function(){this.isLogined=!!t.getStorageSync("token"),this.isLogined&&this.userData(),this.imgbaseUrl=this.$baseUrl},onLoad:function(e){this.qjbutton=t.getStorageSync("storeInfo").button,this.goodsData(e.id)},methods:{goodsData:function(e){t.showLoading({title:"加载中"});var n=this;(0,o.mallListsxqApi)({goods_id:e}).then((function(e){console.log("商品详情",e),1==e.code&&(n.kcjsDetail=a.default.formatRichText(e.data.details),n.goodsDetail=e.data,t.hideLoading())}))},userData:function(){var t=this;(0,o.userInfoApi)({}).then((function(e){console.log("个人中心",e),1==e.code&&(t.score=e.data.score)}))},dhTap:function(){if(this.isLogined)return this.goodsDetail.image=0==this.goodsDetail.images.length?"":this.goodsDetail.images[0],this.$refs.shopCar.startTanc(this.goodsDetail),!1;t.showToast({icon:"none",title:"请先登录",duration:2e3}),setTimeout((function(){t.navigateTo({url:"/pages/login/login"})}),1e3)},swiperChange:function(t){this.swiperIndex=t.detail.current},navTo:function(e){t.navigateTo({url:e})}}};e.default=s}).call(this,n("df3c")["default"])},"94f8":function(t,e,n){"use strict";n.r(e);var i=n("e282"),o=n("380c");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("0d24");var s=n("828b"),u=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=u.exports},e282:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=(this._self._c,this.goodsDetail.id?this.goodsDetail.images.length:null),n=this.goodsDetail.id?this.goodsDetail.parameter.length:null;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n}})},o=[]}},[["4744","common/runtime","common/vendor"]]]);