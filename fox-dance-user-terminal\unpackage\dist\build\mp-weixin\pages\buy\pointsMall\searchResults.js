(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/pointsMall/searchResults"],{"50dc":function(t,a,e){"use strict";e.r(a);var n=e("eabd"),o=e("ed61");for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(i);e("76b6");var s=e("828b"),l=Object(s["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);a["default"]=l.exports},"60cf5":function(t,a,e){"use strict";(function(t,a){var n=e("47a9");e("2300");n(e("3240"));var o=n(e("50dc"));t.__webpack_require_UNI_MP_PLUGIN__=e,a(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"76b6":function(t,a,e){"use strict";var n=e("caac"),o=e.n(n);o.a},b573:function(t,a,e){"use strict";(function(t){Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=e("d0b6"),o={data:function(){return{isLogined:!0,shopCateIndex:0,keywords:"",shopCate:[],mallLists:[],page:1,total_pages:1,zanwsj:!1,status:"loading",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"实在没有了",scrollTop:0,score:0,imgbaseUrl:"",qjbutton:"#131315"}},onShow:function(){this.isLogined=!!t.getStorageSync("token"),this.isLogined?this.userData():this.loding=!0,this.imgbaseUrl=this.$baseUrl,this.mallLists=[],this.page=1,this.mallData()},onLoad:function(a){this.qjbutton=t.getStorageSync("storeInfo").button,this.keywords=a.keywords,t.setNavigationBarTitle({title:this.keywords})},methods:{searchTap:function(){this.mallLists=[],this.page=1,this.mallData()},userData:function(){var t=this;(0,n.userInfoApi)({}).then((function(a){console.log("个人中心",a),1==a.code&&(t.score=a.data.score)}))},mallData:function(){t.showLoading({title:"加载中"});var a=this;(0,n.mallListsApi)({name:a.keywords,page:a.page,size:10}).then((function(e){if(console.log("积分商城",e),1==e.code){var n=e.data.data;a.score=e.data.score,a.mallLists=a.mallLists.concat(n),a.zanwsj=!!a.mallLists.length,a.page++,a.total_pages=e.data.last_page,1!=a.page&&(a.total_pages>=a.page?a.status="loading":a.status="nomore"),0==a.mallLists.length?a.zanwsj=!0:a.zanwsj=!1,1*e.data.total<=10&&(a.status="nomore"),a.loding=!0,t.hideLoading(),t.stopPullDownRefresh()}}))},onReachBottom:function(){console.log("到底了"),1!=this.page&&this.total_pages>=this.page&&this.mallData()},onPullDownRefresh:function(){this.page=1,this.mallLists=[],this.mallData()},dhTap:function(a){if(this.isLogined){if(this.score<1*a.redeem_points)return t.showToast({icon:"none",title:"积分不足",duration:2e3}),!1;var e=JSON.stringify(a);t.navigateTo({url:"/pages/buy/pointsMall/confirmOrder?productxq="+e})}else t.showToast({icon:"none",title:"请先登录",duration:2e3}),setTimeout((function(){t.navigateTo({url:"/pages/login/login"})}),1e3)},navTo:function(a,e){if(e)return t.navigateTo({url:a}),!1;""!=t.getStorageSync("token")&&void 0!=t.getStorageSync("token")&&t.getStorageSync("token")?t.navigateTo({url:a}):(t.showToast({icon:"none",title:"请先登录"}),setTimeout((function(){t.navigateTo({url:"/pages/login/login"})}),1e3))}}};a.default=o}).call(this,e("df3c")["default"])},caac:function(t,a,e){},eabd:function(t,a,e){"use strict";e.d(a,"b",(function(){return n})),e.d(a,"c",(function(){return o})),e.d(a,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},o=[]},ed61:function(t,a,e){"use strict";e.r(a);var n=e("b573"),o=e.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(i);a["default"]=o.a}},[["60cf5","common/runtime","common/vendor"]]]);