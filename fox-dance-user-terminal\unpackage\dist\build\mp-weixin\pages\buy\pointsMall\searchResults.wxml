<view class="searchResults" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="les_search"><view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="请搜索你想要的商品" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$0'],['keywords']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchTap',['$0'],['keywords']]]]]}}" class="les_search_r" bindtap="__e">搜索</view></view><view class="seajg_con"><block wx:for="{{mallLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/pointsMall/productDetails?id='+item.id,'1']]]]]}}" class="seajg_con_li" bindtap="__e"><image src="{{imgbaseUrl+item.image}}" mode="aspectFill"></image><view class="seajg_con_li_a">{{item.name}}</view><view class="seajg_con_li_b"><view>{{"￥"+item.redeem_points}}</view><text data-event-opts="{{[['tap',[['dhTap',['$0'],[[['mallLists','',index]]]]]]]}}" catchtap="__e">购买</text></view></view></block></view><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block><view class="aqjlViw"></view></view>