(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/buy/specification"],{"081c":function(e,t,i){"use strict";var n=i("5296"),s=i.n(n);s.a},"3b9f":function(e,t,i){"use strict";i.r(t);var n=i("ee0f"),s=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=s.a},5296:function(e,t,i){},"80d0":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=(e._self._c,e.gwcToggle?e.__map(e.guige,(function(t,i){var n=e.__get_orig(t),s=e.guige.length;return{$orig:n,g0:s}})):null);e._isMounted||(e.e0=function(t){e.gwcToggle=!1},e.e1=function(t){e.gwcToggle=!1}),e.$mp.data=Object.assign({},{$root:{l0:i}})},s=[]},ee0f:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isLogined:!0,gwcToggle:!1,selnum:1,xuanzText:"",guige:[],goodsDetial:{},carData:{},price:0,imgbaseUrl:"",dqggItem:{id:0}}},onShow:function(){},methods:{ljdhTap:function(){var t=this;if(0==this.dqggItem.id)return e.showToast({icon:"none",title:"请选择完整的规格",duration:2e3}),!1;if(0==this.dqggItem.stock)return e.showToast({icon:"none",title:"当前规格暂无库存",duration:2e3}),!1;if(this.selnum>this.dqggItem.stock)return e.showToast({icon:"none",title:"暂无更多库存",duration:2e3}),!1;var i=JSON.stringify({id:this.goodsDetial.id,name:this.goodsDetial.name,image:this.dqggItem.image,redeem_points:this.price,num:this.selnum,xuanztext:this.xuanzText,skuid:this.dqggItem.id});e.navigateTo({url:"/pages/buy/pointsMall/confirmOrder?productxq="+i,success:function(e){t.gwcToggle=!1}})},startTanc:function(e){this.selnum=1,this.dqggItem={id:0};for(var t=0;t<e.spec_list.length;t++)e.spec_list[t].indexSel=-1;this.imgbaseUrl=this.$baseUrl,this.gwcToggle=!0,this.goodsDetial=e,this.guige=e.spec_list,this.price=e.redeem_points,this.xuanzText="请选择",console.log(e)},selguigClick:function(e,t){console.log(e,t),this.guige[e].indexSel==t?this.guige[e].indexSel=-1:this.guige[e].indexSel=t;for(var i=[],n=[],s=0;s<this.guige.length;s++)-1!=this.guige[s].indexSel&&(i.push(this.guige[s].value[this.guige[s].indexSel]),n.push(this.guige[s].name+":"+this.guige[s].value[this.guige[s].indexSel]));this.xuanzText=i.join(","),0==n.length||n.length!=this.guige.length?(this.price=this.goodsDetial.redeem_points,this.dqggItem={id:0}):this.goodspriceApi(n)},goodspriceApi:function(e){console.log(e,"newsArr");for(var t=e.join(";"),i=this.goodsDetial.spec_data.skuList,n=0;n<i.length;n++)i[n].spec==t&&(this.dqggItem=i[n]);console.log(this.dqggItem,"this.dqggItem")},jian:function(e){if(1==this.selnum)return!1;this.selnum--},add:function(e){this.selnum++},navTo:function(t){e.navigateTo({url:t})}}};t.default=i}).call(this,i("df3c")["default"])},f02e:function(e,t,i){"use strict";i.r(t);var n=i("80d0"),s=i("3b9f");for(var o in s)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(o);i("081c");var g=i("828b"),u=Object(g["a"])(s["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=u.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/buy/specification-create-component',
    {
        'pages/buy/specification-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("f02e"))
        })
    },
    [['pages/buy/specification-create-component']]
]);
