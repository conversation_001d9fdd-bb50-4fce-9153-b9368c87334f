(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{"002e":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uIcon:function(){return n.e("components/uview-ui/components/u-icon/u-icon").then(n.bind(null,"71a0"))},uniNoticeBar:function(){return n.e("uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar").then(n.bind(null,"ad69"))},uPopup:function(){return n.e("components/uview-ui/components/u-popup/u-popup").then(n.bind(null,"5682"))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.kczxBan.length);t._isMounted||(t.e0=function(e){t.showConcat=!1}),t.$mp.data=Object.assign({},{$root:{g0:n}})},i=[]},"65ab":function(t,e,n){"use strict";n.r(e);var o=n("002e"),a=n("9cec");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("a4b6");var c=n("828b"),s=Object(c["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports},"9cec":function(t,e,n){"use strict";n.r(e);var o=n("d6b8"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},a4b6:function(t,e,n){"use strict";var o=n("c1f8"),a=n.n(o);a.a},a52b:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("2300");o(n("3240"));var a=o(n("65ab"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},c1f8:function(t,e,n){},d6b8:function(t,e,n){"use strict";(function(t){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("7eb4")),i=o(n("ee10")),c=n("d0b6"),s=n("e14b"),r={components:{tabbar:function(){n.e("components/tabbar").then(function(){return resolve(n("a714"))}.bind(null,n)).catch(n.oe)}},data:function(){return{isLogined:!0,navBg:"",notice_text:[],showConcat:!1,imgbaseUrl:"",userInfo:{avatar:"",frequency:0,nickname:"",notice:"",score:0,poster:[],store:{address:"",name:"",id:0},luck_draw_frequency:0,level_name:"LV0",experience_value:0,upgrade:0},storesLists:[],kczxBan:[],kczxIndex:0,topBan:[],topBanBj:[],topBanIndex:0,navHeight:0,pageBj:"#fff",cssVariables:{},image2:"",loding:!0}},onShow:function(){this.$refs.tabbar.setColor(),this.imgbaseUrl=this.$baseUrl,this.isLogined=!!t.getStorageSync("token"),this.isLogined?this.getContractData():this.loding=!1,t.getStorageSync("postion")&&this.homeData(),t.hideTabBar(),this.isLogined&&t.getStorageSync("pid")&&this.changePidData()},onPageScroll:function(e){var n=t.upx2px(100),o=e.scrollTop,a=o/n>1?1:o/n;this.navBg=a},onLoad:function(e){t.setStorageSync("qhwc",1),this.navHeight=t.getSystemInfoSync().statusBarHeight+44,t.getStorageSync("postion"),this.getPosition(),this.storeData(),t.hideTabBar(),e.pid&&(t.setStorageSync("pid",e.pid),this.isLogined&&this.changePidData(),console.log("options进去了？userid",t.getStorageSync("pid")))},methods:{getContractData:function(){var e=this;(0,c.getContractApi)({}).then((function(n){console.log("获取未签署的合同",n),1==n.code&&(e.loding=!!n.data,n.data&&t.reLaunch({url:"/pages/index/signing?id="+n.data}))}))},qmTap:function(){this.sctxTap(this.image2)},toPop1:function(){this.$refs.signature1.toPop()},sctxTap:function(e){console.log(e,"tempFilePaths");t.showLoading({title:"加载中"}),(0,c.upImg)(e,"file",{driver:"cos"}).then((function(e){console.log("上传图片",e),1==e.code&&t.hideLoading()}))},goBannerTap:function(e){""!=e&&(e.includes("http")?t.navigateTo({url:"/pages/webView/webView?url="+e}):t.navigateTo({url:e}))},changePidData:function(){(0,c.changePidApi)({pid:t.getStorageSync("pid")}).then((function(t){console.log("更改邀请人",t),t.code}))},topChange:function(t){this.topBanIndex=t.detail.current},kczxChange:function(t){this.kczxIndex=t.detail.current},teacherData:function(){(0,c.teacherApi)({}).then((function(e){console.log("老师",e),1==e.code&&t.hideLoading()}))},storeData:function(){var e=this;(0,c.storeListsApi)({type:1,limit:9999}).then((function(n){console.log("门店列表",n),1==n.code&&(t.hideLoading(),e.storesLists=n.data.data)}))},openImg:function(e,n){for(var o=[],a=0;a<n.length;a++)o.push(this.imgbaseUrl+n[a]);console.log(e,n),t.previewImage({current:e,urls:o})},homeData:function(){var e=this;t.showLoading({title:"加载中"});var n=this;(0,c.homeDataApi)({longitude:t.getStorageSync("postion").longitude,latitude:t.getStorageSync("postion").latitude,store_id:t.getStorageSync("storeInfo")?t.getStorageSync("storeInfo").id:0}).then((function(o){1==o.code&&(console.log("首页",o),e.pageBj=o.data.store.background,e.kczxBan=o.data.course_carousel,e.topBan=o.data.carousel,e.topBanBj=o.data.carousel,t.setStorageSync("storeInfo",o.data.store),n.userInfo=o.data,n.$refs.tabbar.setColor(o.data.ecology),t.hideLoading())}))},getPosition:function(){var e=this;return(0,i.default)(a.default.mark((function n(){var o,i;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return o=e,n.next=3,(0,s.authIsPass)("scope.userLocation");case 3:if(i=n.sent,0!=e.IsOpenMap){n.next=6;break}return n.abrupt("return");case 6:console.log(i),i?t.getLocation({type:"wgs84",success:function(e){console.log("定位3",e);var n={latitude:e.latitude,longitude:e.longitude};t.setStorageSync("postion",n),o.homeData()},fail:function(t){}}):(e.IsOpenMap=!1,t.authorize({scope:"scope.userLocation",fail:function(e){t.showModal({title:"使用该功能必须允许位置服务，是否重新授权？",showCancel:!1,success:function(e){var n=e.confirm;n&&t.openSetting({success:function(){t.getLocation({type:"wgs84",success:function(e){console.log("定位1",e);var n={latitude:e.latitude,longitude:e.longitude};t.setStorageSync("postion",n),o.homeData()},fail:function(t){}}),console.log("开启权限成功")},fail:function(){console.log("开启权限失败")}})}})},success:function(){t.getLocation({type:"wgs84",success:function(e){console.log("定位2",e);var n={latitude:e.latitude,longitude:e.longitude};t.setStorageSync("postion",n),o.homeData()},fail:function(t){}})}}));case 8:case"end":return n.stop()}}),n)})))()},navTo:function(e,n){if(n)return t.navigateTo({url:e}),!1;""!=t.getStorageSync("token")&&void 0!=t.getStorageSync("token")&&t.getStorageSync("token")?t.navigateTo({url:e}):(t.showToast({icon:"none",title:"请先登录"}),t.navigateTo({url:"/pages/login/login"}))}},onShareAppMessage:function(){return{title:"FOX舞蹈，快来和我一起体验吧，超多福利等你来拿!",path:t.getStorageSync("userid")}}};e.default=r}).call(this,n("df3c")["default"])}},[["a52b","common/runtime","common/vendor"]]]);