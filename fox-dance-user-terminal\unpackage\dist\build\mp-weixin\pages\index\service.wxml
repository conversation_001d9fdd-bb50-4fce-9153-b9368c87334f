<block wx:if="{{loding}}"><view class="service"><view class="serkf_one"><block wx:if="{{kefuInfo.to_user_id}}"><view class="serkf_one_n"><view class="serkf_one_t"><image class="serkf_one_t_l" src="{{imgbaseUrl+kefuInfo.to_user_avatar}}" mode="aspectFill"></image><view class="serkf_one_t_c"><view class="serkf_one_t_c_a">{{kefuInfo.to_user_nickname}}</view><block wx:if="{{kefuInfo.wechat_number!=''}}"><view class="serkf_one_t_c_b">{{"微信号："+kefuInfo.wechat_number}}<image src="/static/images/icon57.png" data-event-opts="{{[['tap',[['copyText',['zagsjdksag4']]]]]}}" bindtap="__e"></image></view></block></view><image class="serkf_one_t_r" src="{{imgbaseUrl+kefuInfo.wechat_or_code}}" data-event-opts="{{[['tap',[['openImg',[imgbaseUrl+kefuInfo.wechat_or_code]]]]]}}" bindtap="__e"></image></view><view class="serkf_one_b"><image src="/static/images/icon58.png"></image>客服正在赶来，请耐心等待。可以添加客服微信联系哟~</view></view></block></view><view class="serv_con"><block wx:for="{{servLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['serv_con_li',item.is_tourist==1?'serv_con_li_br':'']}}"><block wx:if="{{index>0&&(index+1)%5===0}}"><view class="serv_con_li_date">{{item._add_time}}</view></block><image class="serv_con_li_l" src="{{item.is_tourist==1?userInfo.avatar==''?'/static/images/toux.png':imgbaseUrl+userInfo.avatar:imgbaseUrl+kefuInfo.to_user_avatar}}"></image><view class="serv_con_li_r"><view><block wx:if="{{item.msn_type==1}}">{{item.msn}}</block><block wx:if="{{item.msn_type==3}}"><image src="{{item.msn}}" mode="aspectFill" data-event-opts="{{[['tap',[['openImg',['$0'],[[['servLists','',index,'msn']]]]]]]}}" bindtap="__e"></image></block></view></view></view></block><block wx:if="{{iscxhh}}"><view class="serv_con_hhend">当前会话已结束，点此<text data-event-opts="{{[['tap',[['cxfqTap',['$event']]]]]}}" bindtap="__e">重新发起会话</text></view></block></view><view class="serv_foo"><input type="text" placeholder="请输入您想咨询的内容" confirm-type="send" data-event-opts="{{[['confirm',[['sendTap',['$event']]]],['input',[['__set_model',['','msn','$event',[]]]]]]}}" value="{{msn}}" bindconfirm="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendTap',['$event']]]]]}}" bindtap="__e">发送</view></view><view class="aqjlViw"></view></view></block>