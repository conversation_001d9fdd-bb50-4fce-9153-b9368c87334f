(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/signing"],{"4ee2":function(n,t,e){"use strict";e.r(t);var o=e("7699"),i=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(a);t["default"]=i.a},"4f6a":function(n,t,e){"use strict";e.r(t);var o=e("5de1"),i=e("4ee2");for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);e("6465");var c=e("828b"),u=Object(c["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=u.exports},"5de1":function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){return o}));var o={jpSignaturePopup:function(){return e.e("uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup").then(e.bind(null,"821b"))}},i=function(){var n=this.$createElement;this._self._c},a=[]},6465:function(n,t,e){"use strict";var o=e("8d90"),i=e.n(o);i.a},7699:function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=e("d0b6"),i=e("f2bd"),a={data:function(){return{isLogined:!0,imgbaseUrl:"",image2:"",url:"",htImgsrc:"",htId:0}},onShow:function(){},onLoad:function(n){console.log("options12",n,i.apis),this.imgbaseUrl=this.$baseUrl_ht,this.htId=n.id?n.id:0,this.htconData()},methods:{cesTap:function(){n.showModal({title:"提示",content:"查询到您有未签署的合同，请点击下一步继续签署",showCancel:!1,confirmText:"下一步",success:function(t){t.confirm?(console.log("用户点击确定"),n.reLaunch({url:"/pages/index/signing?id=23"})):t.cancel&&console.log("用户点击取消")}})},htconData:function(){n.showLoading({title:"生成合同中"});var t=this;(0,o.hqhtnrApi)({id:t.htId}).then((function(e){1==e.code&&(n.hideLoading(),t.htImgsrc=e.data)}))},qmTap:function(){if(""==this.image2)return n.showToast({icon:"none",title:"请先进行签字",duration:2e3}),!1;this.sctxTap(this.image2)},toPop1:function(){this.$refs.signature1.toPop()},sctxTap:function(t){console.log(t,"tempFilePaths");var e=this;n.showLoading({title:"加载中"}),(0,o.upImg)(t,"file").then((function(t){console.log("上传图片",t),1==t.code&&(n.hideLoading(),e.qshtSub(t.data.file.url))}))},qshtSub:function(t){n.showLoading({title:"加载中"});var e=this;(0,o.signContractApi)({id:e.htId,image:t}).then((function(t){1==t.code&&(n.hideLoading(),e.getContractData(t.data),n.showToast({icon:"success",title:"签署成功",duration:2e3}))}))},getContractData:function(t){(0,o.getContractApi)({}).then((function(e){console.log("获取未签署的合同",e),1==e.code&&(e.data?n.showModal({title:"提示",content:"查询到您还有"+t+"份未签署的合同，请点击下一步继续签署",showCancel:!1,confirmText:"下一步",success:function(t){t.confirm?(console.log("用户点击确定"),n.reLaunch({url:"/pages/index/signing?id="+e.data})):t.cancel&&console.log("用户点击取消")}}):setTimeout((function(){n.switchTab({url:"/pages/index/index"})}),1500))}))},navTo:function(t){n.navigateTo({url:t})}}};t.default=a}).call(this,e("df3c")["default"])},"8d90":function(n,t,e){},a962:function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("2300");o(e("3240"));var i=o(e("4f6a"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["a962","common/runtime","common/vendor"]]]);