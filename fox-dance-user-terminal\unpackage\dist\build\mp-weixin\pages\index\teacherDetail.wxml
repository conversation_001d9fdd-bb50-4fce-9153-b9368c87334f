<block wx:if="{{loding}}"><view class="teacherDetail" style="{{'--qjbutton-color:'+(qjbutton)+';'+('--qjziti-color:'+(qjziti)+';')}}"><view class="lsxq_head"><view class="lsxq_head_l"><view class="stor_thr_c_n"><view data-event-opts="{{[['tap',[['jbStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',jbToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{jibText==''?'级别':jibText}}<text></text></view><view data-event-opts="{{[['tap',[['wuzStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',wuzToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{wuzText==''?'舞种':wuzText}}<text></text></view><view data-event-opts="{{[['tap',[['laosStartTap',['$event']]]]]}}" class="{{['stor_thr_c_li',laosToggle?'stor_thr_c_li_ac':'']}}" bindtap="__e">{{laosText==''?'老师':laosText}}<text></text></view></view></view><view class="lsxq_head_r"><text></text><image src="/static/images/icon36.png" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e"></image></view><block wx:if="{{searchToggle}}"><view class="les_search"><view class="les_search_l"><image src="/static/images/search.png" data-event-opts="{{[['tap',[['searchTap',['$0'],['keywords']]]]]}}" bindtap="__e"></image><input type="text" placeholder-style="color:#999999" placeholder="请输入课程名称" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$0'],['keywords']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="les_search_r" bindtap="__e">清除</view></view></block></view><block wx:if="{{jbToggle||wuzToggle||laosToggle}}"><view data-event-opts="{{[['tap',[['gbTcTap',['$event']]]]]}}" class="gg_rgba" bindtap="__e"></view></block><block wx:if="{{jbToggle}}"><view class="teaxzTanc"><view class="teaxzTanc_t"><block wx:for="{{jibLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jibTap',[index]]]]]}}" class="{{[jibIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['jibReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['jibSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><block wx:if="{{wuzToggle}}"><view class="teaxzTanc"><view class="teaxzTanc_t"><block wx:for="{{wuzLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['wuzTap',[index]]]]]}}" class="{{[wuzIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['wuzReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['wuzSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><block wx:if="{{laosToggle}}"><view class="teaxzTanc"><view class="teaxzTanc_t"><block wx:for="{{laosLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['laosTap',[index]]]]]}}" class="{{[laosIndex==index?'teaxzTanc_t_ac':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="teaxzTanc_b"><view data-event-opts="{{[['tap',[['laosReact',['$event']]]]]}}" bindtap="__e">重置</view><text data-event-opts="{{[['tap',[['laosSubTap',['$event']]]]]}}" bindtap="__e">提交</text></view></view></block><view class="tea_ban"><view class="tea_ban_a">{{teacherList[uswiperIndex].name}}</view><view class="tea_ban_uswiper"><u-swiper vue-id="775eb78e-1" list="{{teacherList}}" mode="none" autoplay="{{false}}" current="{{uswiperIndex}}" effect3d="{{true}}" effect3d-previous-margin="{{200}}" data-event-opts="{{[['^change',[['changeSwiper']]]]}}" bind:change="__e" bind:__l="__l"></u-swiper></view><image class="tea_ban_yd" src="/static/images/icon47.png"></image></view><view class="ord_nav tea_one"><view data-event-opts="{{[['tap',[['navTap',[0]]]]]}}" class="{{['ord_nav_li',type==0?'ord_nav_li_ac':'']}}" bindtap="__e"><view><text>任课详情</text><text></text></view></view><view data-event-opts="{{[['tap',[['navTap',[1]]]]]}}" class="{{['ord_nav_li',type==1?'ord_nav_li_ac':'']}}" bindtap="__e"><view><text>老师评价</text><text></text></view></view></view><block wx:if="{{type==0&&teacherList[uswiperIndex].masterpiece!=''}}"><view class="{{['kcxq_video',speedState?'qpvideo':'']}}"><video src="{{teacherList[uswiperIndex].isoss?teacherList[uswiperIndex].masterpiece:imgbaseUrl+teacherList[uswiperIndex].masterpiece}}" controls="{{true}}" id="videoId" data-event-opts="{{[['fullscreenchange',[['handleFullScreen',['$event']]]],['controlstoggle',[['handleControlstoggle',['$event']]]]]}}" bindfullscreenchange="__e" bindcontrolstoggle="__e"><cover-view hidden="{{!(controlsToggle)}}" class="speed"><cover-view data-event-opts="{{[['tap',[['speedTap',['$event']]]]]}}" class="doubleSpeed" bindtap="__e">倍速</cover-view></cover-view><block wx:if="{{speedNum}}"><cover-view class="speedNumBox"><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[0.5]]]]]}}" class="{{['number',0.5==speedRate?'activeClass':'']}}" catchtap="__e">0.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[0.8]]]]]}}" class="{{['number',0.8==speedRate?'activeClass':'']}}" catchtap="__e">0.8倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1]]]]]}}" class="{{['number',1==speedRate?'activeClass':'']}}" catchtap="__e">1倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1.25]]]]]}}" class="{{['number',1.25==speedRate?'activeClass':'']}}" catchtap="__e">1.25倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1.5]]]]]}}" class="{{['number',1.5==speedRate?'activeClass':'']}}" catchtap="__e">1.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[2]]]]]}}" class="{{['number',2==speedRate?'activeClass':'']}}" catchtap="__e">2倍速</cover-view><cover-view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="number" catchtap="__e">取消</cover-view></cover-view></block></video></view></block><view class="kcxq_one"><view class="kcxq_one_b" style="margin-top:0;"><image class="kcxq_one_b_l" src="/static/images/toux.png"></image><view class="kcxq_one_b_r"><view class="kcxq_one_b_r_l"><view>{{teacherList[uswiperIndex].name}}</view><block wx:if="{{teacherList[uswiperIndex].work_year*1>0}}"><text>{{teacherList[uswiperIndex].work_year+"年经验"}}</text></block></view></view></view><view class="kcxq_one_c"><view>{{"擅长舞种："+teacherList[uswiperIndex].skilled_dance}}</view><view>{{"课程级别："+teacherList[uswiperIndex].level_name}}</view><block wx:if="{{teacherList[uswiperIndex].section_number*1>0}}"><view>{{"最近任课："+teacherList[uswiperIndex].day+"天"+teacherList[uswiperIndex].section_number+"节"}}</view></block><block wx:else><view>最近暂无任课</view></block></view></view><block wx:if="{{type==1}}"><view class="lspjCon"><view class="lspjCon_t"><view>老师评分<text>{{teacherList[uswiperIndex].score}}</text></view></view><view class="lspjCon_b"><block wx:for="{{teacherList[uswiperIndex].evaluate}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text>{{item}}</text></block></view></view></block><block wx:if="{{$root.g0}}"><view class="rlxz_con"><view class="rlxz_con_l"><scroll-view scroll-x="true" scroll-left="{{scrollLeft}}" data-event-opts="{{[['scroll',[['scrollJt',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{sjsxLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['sjsxTap',[index,'$0'],[[['sjsxLists','',index]]]]]]]}}" class="{{['rlxz_con_l_li',sjsxIndex==index?'rlxz_con_l_li_ac':'']}}" bindtap="__e"><view>{{item.week}}</view><view>{{item.day}}</view><text></text></view></block></scroll-view></view><view class="rlxz_con_r"><image src="/static/images/icon53.png"></image><picker mode="date" value="{{date_sx}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange_sx',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{date_sx}}</view></picker></view></view></block><block wx:if="{{$root.g1}}"><view class="md_xz md_xzCon"><image class="md_xz_bj" src="/static/images/icon52-3.png"></image><view class="md_xz_title">{{array_md[index_md]}}</view><image class="md_xz_xt" src="/static/images/icon52-4.png"></image><picker value="{{index_md}}" range="{{array_md}}" data-event-opts="{{[['change',[['bindPickerChange_md',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{array_md[index_md]}}</view></picker></view></block><block wx:if="{{type==0}}"><view class="teaCon"><block wx:for="{{storeCourseLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['storesxqTap',['$0'],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li" bindtap="__e"><view class="teaCon_li_a">{{item.course.name}}</view><view class="teaCon_li_b"><image class="teaCon_li_b_l" src="{{imgbaseUrl+item.teacher.image}}" mode="aspectFill"></image><view class="teaCon_li_b_c"><view class="teaCon_li_b_c_a">{{item.start_time+"-"+item.end_time}}</view><view class="teaCon_li_b_c_b">{{"上课老师："+item.teacher.name}}</view><block wx:if="{{item.frequency*1>0}}"><view class="teaCon_li_b_c_b">{{"次卡消耗："+item.frequency*1+"次"}}</view></block><view class="teaCon_li_b_c_c"><block wx:if="{{item.level_name}}"><text>{{item.level_name}}</text></block><text>{{item.dance_name}}</text></view></view><block wx:if="{{item.status==1}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">待开课</view></block><block wx:else><block wx:if="{{item.status==2}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">授课中</view></block><block wx:else><block wx:if="{{item.status==3}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">已完成</view></block><block wx:else><block wx:if="{{item.status==4}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">等位中</view></block><block wx:else><block wx:if="{{item.status==6}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r yysj" style="background:#BEBEBE;" catchtap="__e"><text>{{item.start_reservation}}</text><text>开始预约</text></view></block><block wx:else><block wx:if="{{item.status==7}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">截止预约</view></block><block wx:else><block wx:if="{{item.equivalent*1==0&&item.appointment_number*1>=item.maximum_reservation*1}}"><view data-event-opts="{{[['tap',[['kqhyts',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">预约</view></block><block wx:else><block wx:if="{{item.member==0}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="teaCon_li_b_r" style="{{(item.member==0?'background:#BEBEBE':'')}}" catchtap="__e">预约</view></block><block wx:else><view data-event-opts="{{[['tap',[['yypdTo',['$0','/pages/Schedule/Schedulexq?id'+item.id],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li_b_r" catchtap="__e">{{item.waiting_number*1>0?'去排队':'预约'}}</view></block></block></block></block></block></block></block></block></view><view class="teaCon_li_c"><view class="teaCon_li_c_l"><block wx:for="{{item.appointment_people}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{imgbaseUrl+item.avatar}}" mode="aspectFit"></image></block></view><view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<block wx:if="{{item.waiting_number*1>0}}"><text>{{item.waiting_number}}</text>人在等位</block></view></view></view></block></view></block><block wx:if="{{$root.g2}}"><view class="gg_zwsj" style="margin-bottom:60rpx;"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无课程</text></view></view></block><block wx:if="{{ljtkToggle}}"><view class="yytnCon"><view class="yytnCon_n"><image src="{{imgbaseUrlOss+'/userreport/icon55.png'}}"></image><text data-event-opts="{{[['tap',[['ljktTap',['$event']]]]]}}" bindtap="__e"></text></view><image src="/static/images/icon56.png" data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" bindtap="__e"></image></view></block><view class="aqjlViw"></view></view></block>