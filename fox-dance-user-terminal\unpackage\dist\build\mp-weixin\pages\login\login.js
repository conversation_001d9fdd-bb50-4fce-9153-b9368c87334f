(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/login/login"],{"086a":function(t,e,n){"use strict";n.r(e);var i=n("20b3"),o=n("bd1d");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("8d5b");var r=n("828b"),c=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"0b72":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("2300");i(n("3240"));var o=i(n("086a"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"20b3":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uNavbar:function(){return n.e("components/uview-ui/components/u-navbar/u-navbar").then(n.bind(null,"856d"))}},o=function(){var t=this.$createElement;this._self._c},a=[]},"29a7":function(t,e,n){"use strict";(function(t,i){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("7ca3")),r=n("d0b6");function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(Object(n),!0).forEach((function(e){(0,a.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var u={data:function(){return{bgs:"",safeAreaTop:t.getWindowInfo().safeArea.top,menuButtonInfoHeight:i.getMenuButtonBoundingClientRect().height,isAgreement:!1,avatar:"",nickname:"",imgbaseUrl:"",editzlToggle:!1}},onLoad:function(t){this.imgbaseUrl=this.$baseUrl;var e=i.getSystemInfoSync();this.bgs="100% "+(Number(e.statusBarHeight)+456)+"px",this.getCode()},methods:{qxTap:function(){this.editzlToggle=!1,i.getStorageSync("tokenwx")?(i.removeStorageSync("tokenwx"),i.switchTab({url:"/pages/index/index"})):setTimeout((function(){i.navigateBack()}),1500)},subTap:function(){return"/static/images/avatar.png"==this.avatar?(i.showToast({icon:"none",title:"请上传头像",duration:2e3}),!1):0==this.nickname.split(" ").join("").length?(i.showToast({icon:"none",title:"请输入昵称",duration:2e3}),!1):(i.showLoading({title:"加载中"}),void(0,r.toeditUserApi)({avatar:this.avatar,nickname:this.nickname}).then((function(t){1==t.code&&(i.hideLoading(),i.showToast({title:"设置成功",duration:2e3}),i.getStorageSync("tokenwx")?(i.removeStorageSync("tokenwx"),setTimeout((function(){i.switchTab({url:"/pages/index/index"})}),1500)):setTimeout((function(){i.navigateBack()}),1500))})))},chooseAvatarsc:function(t){var e=this,n=t.detail.avatarUrl;i.showLoading({title:"加载中"}),(0,r.upImg)(n,"file",{driver:"cos"}).then((function(t){console.log("上传图片",t),1==t.code&&(i.hideLoading(),e.avatar=t.data.file.url)}))},userData:function(){i.showLoading({title:"加载中"});var t=this;(0,r.userInfoApi)({}).then((function(e){1==e.code&&(console.log("个人信息",e),t.avatar=e.data.avatar,t.nickname=""==e.data.nickname?"微信昵称":e.data.nickname,i.hideLoading())}))},tisTap:function(){i.showToast({title:"请先阅读并同意授权《用户注册购卡协议》",icon:"none",duration:1e3})},IsAgree:function(){this.isAgreement=!this.isAgreement},getCode:function(){var t=this;i.login({provider:"weixin",success:function(e){console.log(e),t.minicode=e.code}})},getPhoneNumber:function(t){console.log(t,"45646546546");var e=this;if(1==this.isAgreement){if(!t.detail.errMsg.includes("getPhoneNumber:ok"))return;i.showLoading({title:"登录中...",icon:"loading"});var n=i.getStorageSync("parentId"),o=i.getStorageSync("silverGrantId"),a={code:this.minicode,phone_code:t.detail.code,pid:i.getStorageSync("pid")?i.getStorageSync("pid"):""};n&&(a=s(s({},a),{},{parentId:n})),o&&(a=s(s({},a),{},{silverGrantId:o})),(0,r.login)(s({},a)).then((function(t){console.log(t,"登录"),1==t.code?(i.hideLoading(),i.showToast({title:"登录成功",icon:"success",duration:2e3}),i.setStorageSync("token",t.data.userinfo.token),i.setStorageSync("userid",t.data.userinfo.id),e.getContractData(t.data.userinfo.avatar)):(e.getCode(),i.hideLoading(),i.showToast({title:"登录失败",icon:"error",duration:1e3}))}))}else i.showToast({title:"请先同意协议",icon:"none",duration:1e3})},getContractData:function(t){var e=this;(0,r.getContractApi)({}).then((function(n){console.log("获取未签署的合同",n),1==n.code&&(n.data?setTimeout((function(){i.reLaunch({url:"/pages/index/signing?id="+n.data})}),1200):"/static/images/avatar.png"==t?e.editzlToggle=!0:i.getStorageSync("tokenwx")?(i.removeStorageSync("tokenwx"),i.switchTab({url:"/pages/index/index"})):setTimeout((function(){i.navigateBack()}),1500))}))},showPass:function(){this.isType=!this.isType},codeChange:function(t){this.codeTips=t},navTo:function(t){i.navigateTo({url:t})},checkId:function(t){confirmId({identity:t}).then((function(e){1==e.code&&(1==t?i.setStorageSync("identity","user"):2==t&&i.setStorageSync("identity","master"),i.switchTab({url:"/pages/index/index"}))}))}}};e.default=u}).call(this,n("3223")["default"],n("df3c")["default"])},"8d5b":function(t,e,n){"use strict";var i=n("9a89"),o=n.n(i);o.a},"9a89":function(t,e,n){},bd1d:function(t,e,n){"use strict";n.r(e);var i=n("29a7"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a}},[["0b72","common/runtime","common/vendor"]]]);