(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/add_address"],{"13e5":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={uSwitch:function(){return i.e("components/uview-ui/components/u-switch/u-switch").then(i.bind(null,"54dd3"))},uPopup:function(){return i.e("components/uview-ui/components/u-popup/u-popup").then(i.bind(null,"5682"))}},o=function(){var t=this,e=t.$createElement;t._self._c;t._isMounted||(t.e0=function(e){t.sex=1},t.e1=function(e){t.sex=2},t.e2=function(e){t.showPopup=!1})},s=[]},"30d7":function(t,e,i){"use strict";i.r(e);var n=i("8533"),o=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=o.a},"573d":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("2300");n(i("3240"));var o=n(i("fbf5"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"6c60":function(t,e,i){"use strict";var n=i("7ebb"),o=i.n(n);o.a},"7ebb":function(t,e,i){},8533:function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("d0b6"),o={data:function(){return{switchVal:!1,showPopup:!1,name:"",sex:1,address:"",address_detail:"",lat:"",lng:"",mobile:"",Edit_id:"",editType:0,loding:!1,qjbutton:"#131315"}},onLoad:function(e){this.qjbutton=t.getStorageSync("storeInfo").button,t.setNavigationBarTitle({title:0==e.type?"添加地址":"编辑地址"});var i=this;if(console.log(e,"opt"),this.editType=e.type,e.data){if("undefined"==e.data)return this.loding=!0,!1;var n=JSON.parse(e.data);this.Edit_id=n.id,this.name=n.name,this.sex=n.gender,this.address=n.area,this.address_detail=n.detail,this.lat=n.lat,this.lng=n.lng,this.mobile=n.phone,this.switchVal=1==n.is_default,t.showLoading({title:"加载中"}),setTimeout((function(){t.hideLoading(),i.loding=!0}),500)}},methods:{choose:function(){var e=this;t.chooseLocation({success:function(t){e.address_detail=t.address,e.address=t.name,e.lat=t.latitude,e.lng=t.longitude,console.log("位置名称："+t.name),console.log("详细地址："+t.address),console.log("纬度："+t.latitude),console.log("经度："+t.longitude)}})},change:function(t){console.log("change"),this.switchVal=t},submit:function(){var e=this;if(""!=this.name)if(""!=this.address)if(""!=this.address_detail){if(""!=this.mobile){if(!/^1[3456789]\d{9}$/.test(this.mobile))return t.showToast({icon:"none",title:"请输入正确的手机号码",duration:2e3}),!1;t.showLoading({title:"加载中"});var i={name:this.name,phone:this.mobile,gender:this.sex,area:this.address,detail:this.address_detail,is_default:String(0==this.switchVal?"0":"1"),addr_id:0==this.editType?0:this.Edit_id};0==this.editType&&delete i.addr_id,(0,n.addrAdd)(i).then((function(i){1==i.code&&(t.hideLoading(),e.$toast({title:0==e.editType?"添加成功":"编辑成功"}),setTimeout((function(){t.navigateBack()}),1500))}))}else this.$toast({title:"请输入手机号"})}else this.$toast({title:"请输入详细地址"});else this.$toast({title:"请选择地址"});else this.$toast({title:"请输入收货人姓名"})},delAddress:function(e){var i=this;t.showModal({title:"提示",content:"确定要删除该地址吗？",success:function(e){var o=this;e.confirm?(t.showLoading({title:"加载中"}),(0,n.addrDel)({id:i.Edit_id}).then((function(e){1==e.code&&(t.hideLoading(),t.getStorageSync("diancan")&&t.getStorageSync("diancan").addressId==o.nowId&&t.removeStorageSync("diancan"),i.$toast({title:"删除成功"}),setTimeout((function(){t.navigateBack()}),1e3))}))):e.cancel&&console.log("用户点击取消")}})},confirmDel:function(){var e=this;(0,n.addrDel)({addressId:this.Edit_id}).then((function(i){1==i.code&&(e.$toast({title:"删除成功"}),setTimeout((function(){t.navigateBack()}),500))}))}}};e.default=o}).call(this,i("df3c")["default"])},fbf5:function(t,e,i){"use strict";i.r(e);var n=i("13e5"),o=i("30d7");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("6c60");var a=i("828b"),d=Object(a["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=d.exports}},[["573d","common/runtime","common/vendor"]]]);