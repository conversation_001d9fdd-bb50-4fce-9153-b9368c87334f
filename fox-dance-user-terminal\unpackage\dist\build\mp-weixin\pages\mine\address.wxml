<block wx:if="{{loding}}"><view class="address" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><block wx:if="{{$root.g0>0}}"><view class="list" style="margin-bottom:128rpx;"><block wx:for="{{lists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['checkAddr',['$0'],[[['lists','',index]]]]]]]}}" class="li" bindtap="__e"><view class="li_t"><view class="li_t_info">{{''+item.name+"("+(item.gender==1?'先生':'女士')+")   "+item.phone+''}}</view><view class="li_t_detail">{{item.area+item.detail}}</view></view><view class="li_d flex row-between"><view data-event-opts="{{[['tap',[['isDefault',['$0'],[[['lists','',index]]]]]]]}}" class="li_d_l flex" catchtap="__e"><block wx:if="{{item.is_default==1}}"><image class="ac" src="/static/images/dzxz-11.png" mode="scaleToFill"></image></block><block wx:else><image src="/static/images/dzxz.png" mode="scaleToFill"></image></block>默认地址</view><view class="li_d_use flex"><view data-event-opts="{{[['tap',[['goTo',[1,'$0'],[[['lists','',index]]]]]]]}}" class="li_d_li flex" catchtap="__e"><image src="/static/images/addr_edit.png" mode="scaleToFill"></image>编辑</view><view data-event-opts="{{[['tap',[['delAddress',['$0'],[[['lists','',index,'id']]]]]]]}}" class="li_d_li flex" catchtap="__e"><image src="/static/images/addr_del.png" mode="scaleToFill"></image>删除</view></view></view></view></block></view></block><block wx:if="{{$root.g1==0}}"><view class="gg_zwsj_w"><image src="/static/images/addr_kong.png"></image><text style="display:block;">收货地址空空如也~</text></view></block><view class="aqjlViw"></view><view class="add_foo"><view data-event-opts="{{[['tap',[['goTo',[0]]]]]}}" bindtap="__e">添加地址</view></view><u-popup bind:input="__e" vue-id="55cfb3f5-1" mode="center" border-radius="20" value="{{showPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPopup','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="prompt"><view class="prompt_t"><view class="prompt_t_img"><image src="/static/images/popup-icon.png" mode="scaleToFill"></image></view><view class="prompt_t_text">提示</view></view><view class="prompt_c">确定退出当前删除？</view><view class="prompt_d"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="prompt_d_l" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmDel',['$event']]]]]}}" class="prompt_d_r" bindtap="__e">确定</view></view></view></u-popup></view></block>