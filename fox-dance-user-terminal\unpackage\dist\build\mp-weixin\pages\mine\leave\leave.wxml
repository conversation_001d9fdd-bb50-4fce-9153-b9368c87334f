<view class="leave" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="lea_one"><text data-event-opts="{{[['tap',[['navTo',['/pages/mine/leave/leaveLists']]]]]}}" bindtap="__e">请假记录</text></view><view class="lea_two"><view class="lea_two_a"><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/memberCard/myMemberCard?xzhyk=1']]]]]}}" class="lea_two_a_li" bindtap="__e"><view class="lea_two_a_li_l"><text>*</text>请假卡片</view><view class="lea_two_a_li_r"><view class="uni-input"><text>{{selectCards.out_trade_no==''?'请选择':'会员ID：'+selectCards.out_trade_no}}</text><image src="/static/images/right_more.png"></image></view></view></view></view><view class="lea_two_a"><view class="lea_two_a_li"><view class="lea_two_a_li_l"><text>*</text>开始时间</view><view class="lea_two_a_li_r"><view class="uni-input"><text>{{date_start==''?'请选择':date_start}}</text><image src="/static/images/right_more.png"></image></view><view class="lea_nav_r_sj"><uni-datetime-picker bind:input="__e" vue-id="15c0c7ea-1" type="date" value="{{date_start}}" data-event-opts="{{[['^input',[['__set_model',['','date_start','$event',[]]]]]]}}" bind:__l="__l"></uni-datetime-picker></view></view></view><view class="lea_two_a_li"><view class="lea_two_a_li_l"><text>*</text>结束时间</view><view class="lea_two_a_li_r"><view class="uni-input"><text>{{date_end==''?'请选择':date_end}}</text><image src="/static/images/right_more.png"></image></view><view class="lea_nav_r_sj"><uni-datetime-picker bind:input="__e" vue-id="15c0c7ea-2" type="date" value="{{date_end}}" data-event-opts="{{[['^input',[['__set_model',['','date_end','$event',[]]]]]]}}" bind:__l="__l"></uni-datetime-picker></view></view></view></view><view class="lea_two_b"><view class="lea_two_b_a"><view class="lea_two_a_li_l"><text>*</text>备注</view></view><textarea placeholder="请输入请假理由" placeholder-style="color: #999999;" data-event-opts="{{[['input',[['__set_model',['','notes','$event',[]]]]]]}}" value="{{notes}}" bindinput="__e"></textarea></view></view><view data-event-opts="{{[['tap',[['qjsubTap',['$event']]]]]}}" class="lea_two_sub" bindtap="__e">确认请假</view><view class="aqjlViw"></view></view>