<view class="leaveLists" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="lea_nav"><view class="lea_nav_l"><view data-event-opts="{{[['tap',[['navTap',[0]]]]]}}" class="{{['lea_nav_l_li',type==0?'lea_nav_l_li_ac':'']}}" bindtap="__e"><view><text>全部</text><text></text></view></view><view data-event-opts="{{[['tap',[['navTap',[1]]]]]}}" class="{{['lea_nav_l_li',type==1?'lea_nav_l_li_ac':'']}}" bindtap="__e"><view><text>进行中</text><text></text></view></view><view data-event-opts="{{[['tap',[['navTap',[2]]]]]}}" class="{{['lea_nav_l_li',type==2?'lea_nav_l_li_ac':'']}}" bindtap="__e"><view><text>已过期</text><text></text></view></view></view><view class="lea_nav_r"><text>筛选时间</text><image src="/static/images/icon27.png"></image><view class="lea_nav_r_sj"><uni-datetime-picker vue-id="b732d3c6-1" type="daterange" value="{{range}}" data-event-opts="{{[['^change',[['maskClick']]],['^input',[['__set_model',['','range','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></uni-datetime-picker></view></view></view><view class="lea_con"><block wx:for="{{leaveLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="lea_con_li"><view class="lea_con_li_a"><view class="lea_con_li_a_l">请假<text></text>{{item.card.type*1==0?'次卡':'时长卡'}}</view><view class="lea_con_li_a_r" style="{{(item.status==1?'color:#F48477':'')}}">{{item.status==0?'进行中':item.status==1?'已过期':item.status==2?'已销假':''}}</view></view><view class="lea_con_li_b">{{item.start_time+" - "+item.end_time}}</view><block wx:if="{{item.status==0}}"><view class="lea_con_li_c"><view data-event-opts="{{[['tap',[['xjTap',['$0'],[[['leaveLists','',index,'id']]]]]]]}}" bindtap="__e">提前销假</view></view></block></view></block></view><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block><view class="aqjlViw"></view><block wx:if="{{xjToggle}}"><view class="xjTanc"><view class="xjTanc_n"><view class="xjTanc_a">温馨提示</view><view class="xjTanc_b">是否提前销假？</view><view class="xjTanc_c"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['xjSubTap',['$event']]]]]}}" class="bak" bindtap="__e">确认</view></view></view></view></block></view>