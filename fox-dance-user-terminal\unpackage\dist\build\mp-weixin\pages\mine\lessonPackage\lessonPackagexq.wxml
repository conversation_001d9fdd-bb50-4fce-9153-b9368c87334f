<block wx:if="{{coursePackageInfo.id}}"><view class="lessonPackagexq"><block wx:if="{{coursePackageInfo.introduce_video}}"><view class="{{['kcxq_video',speedState?'qpvideo':'']}}"><video src="{{coursePackageInfo.isoss?coursePackageInfo.introduce_video:imgbaseUrl+coursePackageInfo.introduce_video}}" controls="{{true}}" id="videoId" data-event-opts="{{[['fullscreenchange',[['handleFullScreen',['$event']]]],['controlstoggle',[['handleControlstoggle',['$event']]]]]}}" bindfullscreenchange="__e" bindcontrolstoggle="__e"><cover-view hidden="{{!(controlsToggle)}}" class="speed"><cover-view data-event-opts="{{[['tap',[['speedTap',['$event']]]]]}}" class="doubleSpeed" bindtap="__e">倍速</cover-view></cover-view><block wx:if="{{speedNum}}"><cover-view class="speedNumBox"><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[0.5]]]]]}}" class="{{['number',0.5==speedRate?'activeClass':'']}}" catchtap="__e">0.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[0.8]]]]]}}" class="{{['number',0.8==speedRate?'activeClass':'']}}" catchtap="__e">0.8倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1]]]]]}}" class="{{['number',1==speedRate?'activeClass':'']}}" catchtap="__e">1倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1.25]]]]]}}" class="{{['number',1.25==speedRate?'activeClass':'']}}" catchtap="__e">1.25倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1.5]]]]]}}" class="{{['number',1.5==speedRate?'activeClass':'']}}" catchtap="__e">1.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[2]]]]]}}" class="{{['number',2==speedRate?'activeClass':'']}}" catchtap="__e">2倍速</cover-view><cover-view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="number" catchtap="__e">取消</cover-view></cover-view></block></video></view></block><view class="ord_nav kcxq_tab"><view data-event-opts="{{[['tap',[['tabTap',[0]]]]]}}" class="{{['ord_nav_li',type==0?'ord_nav_li_ac':'']}}" bindtap="__e"><view><text>介绍</text><text></text></view></view><view data-event-opts="{{[['tap',[['tabTap',[1]]]]]}}" class="{{['ord_nav_li',type==1?'ord_nav_li_ac':'']}}" bindtap="__e"><view><text>目录</text><text></text></view></view></view><block wx:if="{{type==0}}"><view class="kbxq_one" style="margin-top:0;"><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>课程介绍</text><text></text></view></view><view class="kbxq_one_b">{{''+coursePackageInfo.introduce+''}}</view><view class="kbxq_one_c"><block wx:for="{{coursePackageInfo.images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{imgbaseUrl+item}}" mode="widthFix"></image></block></view></view></block><block wx:if="{{type==0}}"><view class="kbxq_one"><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>适用人群</text><text></text></view></view><view class="kbxq_one_b">{{''+coursePackageInfo.apply_crowd+''}}</view></view></block><block wx:if="{{type==0}}"><view class="kbxq_one"><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>讲师详情</text><text></text></view></view><view class="kcxq_one_b"><image class="kcxq_one_b_l" src="{{imgbaseUrl+coursePackageInfo.teacher.image}}"></image><view class="kcxq_one_b_r"><view class="kcxq_one_b_r_l"><view>{{coursePackageInfo.teacher.name}}</view><text>{{"擅长舞种："+coursePackageInfo.danceTable.name}}</text></view><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/coursePackage/teacherDetails?id='+coursePackageInfo.teacher.id,'1']]]]]}}" class="kcxq_one_b_r_r" bindtap="__e">讲师详情<image src="/static/images/introduce_more.png"></image></view></view></view></view></block><block wx:if="{{type==1}}"><view class="kbxq_ml"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="kbxq_ml_li"><view data-event-opts="{{[['tap',[['mlTap',[index]]]]]}}" class="kbxq_ml_li_t" bindtap="__e"><view>{{index+1+"."+item.$orig.name}}</view><text style="{{(item.$orig.toggle?'border-bottom: 14rpx solid #999;border-top:none':'')}}"></text></view><block wx:if="{{item.$orig.toggle}}"><view class="kbxq_ml_li_b"><block wx:for="{{item.l0}}" wx:for-item="itemerj" wx:for-index="indexerj" wx:key="indexerj"><block wx:if="{{itemerj.g0>0}}"><view data-event-opts="{{[['tap',[['videoTap',['$0',index,indexerj],[[['kmLists','',index],['catalog','',indexerj]]]]]]]}}" class="kbxq_ml_li_b_li" bindtap="__e"><view>{{itemerj.$orig.name}}</view><text style="{{(itemerj.$orig.view?'color:#999999':'')}}">{{itemerj.$orig.viewing_status==1?'已看':'未看'}}</text></view></block></block><block wx:if="{{item.g1==0}}"><view style="width:100%;text-align:center;font-size:26rpx;margin-top:30rpx;">暂无目录</view></block></view></block></view></block></view></block><block wx:if="{{videoToggle}}"><view class="{{['video_tanc',speedState_tc?'qpvideo':'']}}"><video src="{{videoItem.isoss?videoItem.video:imgbaseUrl+videoItem.video}}" controls="{{true}}" id="videoId_tc" data-event-opts="{{[['play',[['playVideo',['$event']]]],['timeupdate',[['timeupdateVideo',['$event']]]],['ended',[['playVideo',['$event']]]],['pause',[['playVideo',['$event']]]],['fullscreenchange',[['handleFullScreen_tc',['$event']]]],['controlstoggle',[['handleControlstoggle_tc',['$event']]]]]}}" bindplay="__e" bindtimeupdate="__e" bindended="__e" bindpause="__e" bindfullscreenchange="__e" bindcontrolstoggle="__e"><cover-view hidden="{{!(controlsToggle_tc)}}" class="speed"><cover-view data-event-opts="{{[['tap',[['speedTap_tc',['$event']]]]]}}" class="doubleSpeed" bindtap="__e">倍速</cover-view></cover-view><block wx:if="{{speedNum_tc}}"><cover-view class="speedNumBox"><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[0.5]]]]]}}" class="{{['number',0.5==speedRate_tc?'activeClass':'']}}" catchtap="__e">0.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[0.8]]]]]}}" class="{{['number',0.8==speedRate_tc?'activeClass':'']}}" catchtap="__e">0.8倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[1]]]]]}}" class="{{['number',1==speedRate_tc?'activeClass':'']}}" catchtap="__e">1倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[1.25]]]]]}}" class="{{['number',1.25==speedRate_tc?'activeClass':'']}}" catchtap="__e">1.25倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[1.5]]]]]}}" class="{{['number',1.5==speedRate_tc?'activeClass':'']}}" catchtap="__e">1.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[2]]]]]}}" class="{{['number',2==speedRate_tc?'activeClass':'']}}" catchtap="__e">2倍速</cover-view><cover-view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="number" catchtap="__e">取消</cover-view></cover-view></block></video><image src="/static/images/icon56.png" data-event-opts="{{[['tap',[['gbVideoTap',['$event']]]]]}}" bindtap="__e"></image></view></block><view class="aqjlViw"></view></view></block>