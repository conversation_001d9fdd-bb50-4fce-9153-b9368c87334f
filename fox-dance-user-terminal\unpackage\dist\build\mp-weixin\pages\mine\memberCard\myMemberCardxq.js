(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/memberCard/myMemberCardxq"],{2956:function(t,o,n){"use strict";(function(t){var e=n("47a9");Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i=n("d0b6"),a=(e(n("ef4a")),{data:function(){return{imgbaseUrl:"",isLogined:!0,myCardInfo:{id:0},jhhyToggle:!1,pageId:0,xyCont:"",xyToggle:!1,scrollViewHeight:300,isScrollToBottom:!1,qjbutton:"#131315",ismr:!1}},onShow:function(){this.qjbutton=t.getStorageSync("storeInfo").button},onLoad:function(t){this.imgbaseUrl=this.$baseUrl_ht,this.pageId=t.id,this.myCardxqData()},methods:{ckhtTap:function(o){var n="https"==this.myCardInfo.contract.substring(0,5)?this.myCardInfo.contract:this.imgbaseUrl+this.myCardInfo.contract;t.showLoading({title:"下载中..."}),t.downloadFile({url:n,success:function(o){if(200===o.statusCode){var n=o.tempFilePath;t.openDocument({filePath:n,showMenu:!0,success:function(){},fail:function(){t.showToast({title:"无法打开文件",icon:"none"})}})}},fail:function(o){t.showToast({title:"下载失败",icon:"none"})},complete:function(){t.hideLoading()}})},switch1Change:function(t){console.log(t,"默认会员"),this.ismr=t.detail.value,this.setcardData()},setcardData:function(o){t.showLoading({title:"加载中"});(0,i.setcardApi)({id:this.pageId}).then((function(o){console.log("设置默认会员卡",o),1==o.code&&(t.hideLoading(),t.showToast({title:"设置成功",duration:2e3}))}))},onScroll:function(o){var n=this,e=this;this.$nextTick((function(){var i=t.createSelectorQuery().in(n);i.select(".scroll-view").boundingClientRect(),i.select(".scroll-view").scrollOffset(),i.exec((function(t){if(console.log(t,"res"),t&&t[0]&&t[1]){var n=t[0].height,i=t[1].scrollTop,a=o.detail.scrollHeight;i+n>=a-20&&(console.log("已经滚动到底部"),e.isScrollToBottom=!0)}}))}))},xyGbTap:function(){this.xyToggle=!1,this.isScrollToBottom=!1},tyTap:function(){if(!this.isScrollToBottom)return!1;this.jhhySubTap()},myCardxqData:function(o){t.showLoading({title:"加载中"});var n=this;(0,i.myCardxqApi)({id:n.pageId}).then((function(o){console.log("门店详情",o),1==o.code&&(n.ismr=1==o.data.default,n.myCardInfo=o.data,n.xyCont=o.data.agreement,t.hideLoading())}))},jhhySubTap:function(){t.showLoading({title:"加载中"});var o=this;(0,i.jhcardApi)({id:o.pageId}).then((function(n){console.log("激活会员",n),1==n.code&&(o.jhhyToggle=!1,o.xyToggle=!1,o.isScrollToBottom=!1,o.myCardxqData(),t.hideLoading(),t.showToast({title:"激活成功",duration:2e3}))}))}}});o.default=a}).call(this,n("df3c")["default"])},"49d9":function(t,o,n){"use strict";(function(t,o){var e=n("47a9");n("2300");e(n("3240"));var i=e(n("94b1"));t.__webpack_require_UNI_MP_PLUGIN__=n,o(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"94b1":function(t,o,n){"use strict";n.r(o);var e=n("ea8c"),i=n("c50f");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(o,t,(function(){return i[t]}))}(a);var c=n("828b"),s=Object(c["a"])(i["default"],e["b"],e["c"],!1,null,"6644be7f",null,!1,e["a"],void 0);o["default"]=s.exports},c50f:function(t,o,n){"use strict";n.r(o);var e=n("2956"),i=n.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){n.d(o,t,(function(){return e[t]}))}(a);o["default"]=i.a},ea8c:function(t,o,n){"use strict";n.d(o,"b",(function(){return e})),n.d(o,"c",(function(){return i})),n.d(o,"a",(function(){}));var e=function(){var t=this,o=t.$createElement;t._self._c;t._isMounted||(t.e0=function(o){t.xyToggle=!0},t.e1=function(o){t.jhhyToggle=!1})},i=[]}},[["49d9","common/runtime","common/vendor"]]]);