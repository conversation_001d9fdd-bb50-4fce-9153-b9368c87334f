<view class="messageCenter" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="mes_one"><view data-event-opts="{{[['tap',[['tabTap',[0]]]]]}}" class="{{[type==0?'mes_one_ac':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['tabTap',[1]]]]]}}" class="{{[type==1?'mes_one_ac':'']}}" bindtap="__e">消息</view><view data-event-opts="{{[['tap',[['tabTap',[2]]]]]}}" class="{{[type==2?'mes_one_ac':'']}}" bindtap="__e">通知</view></view><view class="mes_two"><block wx:for="{{messageLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['yhbgTap',['$0'],[[['messageLists','',index]]]]]]]}}" class="mes_two_li" bindtap="__e"><block wx:if="{{item.type==1||item.type==7}}"><image class="mes_two_li_tz" src="/static/images/icon26.png"></image></block><block wx:else><image class="mes_two_li_xx" src="{{item.profile==''?'/static/images/icon26-1.png':imgbaseUrl+item.profile}}"></image></block><view class="mes_two_li_c"><view class="mes_two_li_c_t _div"><view>{{item.type==1?'系统消息':'通知'}}</view><text>{{item.create_time}}</text></view><view class="mes_two_li_c_b _div"><view>{{item.content}}</view><text style="display:none;">2</text></view></view></view></block></view><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/tzgl']]]]]}}" class="tzxf" bindtap="__e"><text>通知</text><text>管理</text></view><view class="aqjlViw"></view></view>