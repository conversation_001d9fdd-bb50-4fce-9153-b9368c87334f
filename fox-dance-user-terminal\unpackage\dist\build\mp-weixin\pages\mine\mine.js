(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/mine"],{be31:function(n,e,t){"use strict";(function(n){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=t("d0b6"),a={components:{tabbar:function(){t.e("components/tabbar").then(function(){return resolve(t("a714"))}.bind(null,t)).catch(t.oe)}},data:function(){return{loding:!1,isLogined:!0,navBg:"",zsewmToggle:!1,userInfo:{avatar:"",train_count:0,train_day:0,train_time:0,score:0},imgbaseUrl:"",qjbutton:"#131315"}},onPageScroll:function(e){var t=n.upx2px(100),o=e.scrollTop,a=o/t>1?1:o/t;this.navBg=a},onLoad:function(){this.qjbutton=n.getStorageSync("storeInfo").button,n.hideTabBar()},onShow:function(){n.hideTabBar(),this.imgbaseUrl=this.$baseUrl,this.isLogined=!!n.getStorageSync("token"),this.isLogined?(this.userData(),console.log(this.$baseUrl,"this.$baseUrl")):this.loding=!0,n.getStorageSync("selectCards")&&n.removeStorageSync("selectCards"),this.$refs.tabbar&&this.$refs.tabbar.setColor()},methods:{userData:function(){n.showLoading({title:"加载中"});var e=this;(0,o.userInfoApi)({}).then((function(t){console.log("个人中心",t),1==t.code&&(e.loding=!0,e.userInfo=t.data,n.hideLoading())}))},imgUploadTap:function(){n.showLoading({title:"加载中"}),n.downloadFile({url:this.userInfo.share,success:function(e){n.saveImageToPhotosAlbum({filePath:e.tempFilePath,success:function(){n.hideLoading(),n.showToast({title:"保存成功"})},fail:function(e){n.hideLoading(),console.log(e,"保存失败"),n.showToast({icon:"none",mask:!0,title:"保存失败",duration:3e3})}})}})},userReportTap:function(){if(""==n.getStorageSync("token")||void 0==n.getStorageSync("token")||!n.getStorageSync("token"))return n.showToast({icon:"none",title:"请先登录"}),n.navigateTo({url:"/pages/login/login"}),!1;n.navigateTo({url:"/pages/mine/userReport/userReport"})},navTo:function(e,t){if(t)return n.navigateTo({url:e}),!1;""!=n.getStorageSync("token")&&void 0!=n.getStorageSync("token")&&n.getStorageSync("token")?n.navigateTo({url:e}):(n.showToast({icon:"none",title:"请先登录"}),n.navigateTo({url:"/pages/login/login"}))}}};e.default=a}).call(this,t("df3c")["default"])},c4b0:function(n,e,t){"use strict";t.r(e);var o=t("f8c6"),a=t("e738");for(var i in a)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(i);t("fa82");var r=t("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"77706f4e",null,!1,o["a"],void 0);e["default"]=s.exports},ca71:function(n,e,t){},da7d:function(n,e,t){"use strict";(function(n,e){var o=t("47a9");t("2300");o(t("3240"));var a=o(t("c4b0"));n.__webpack_require_UNI_MP_PLUGIN__=t,e(a.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},e738:function(n,e,t){"use strict";t.r(e);var o=t("be31"),a=t.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(i);e["default"]=a.a},f8c6:function(n,e,t){"use strict";t.d(e,"b",(function(){return a})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){return o}));var o={uNavbar:function(){return t.e("components/uview-ui/components/u-navbar/u-navbar").then(t.bind(null,"856d"))}},a=function(){var n=this,e=n.$createElement,t=(n._self._c,n.loding?{background:"rgba(255,255, 255,"+n.navBg+")"}:null);n._isMounted||(n.e0=function(e){n.zsewmToggle=!n.zsewmToggle},n.e1=function(e){n.zsewmToggle=!n.zsewmToggle}),n.$mp.data=Object.assign({},{$root:{a0:t}})},i=[]},fa82:function(n,e,t){"use strict";var o=t("ca71"),a=t.n(o);a.a}},[["da7d","common/runtime","common/vendor"]]]);