<block wx:if="{{courseDetail.id}}"><view class="myCoursexq" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><block wx:if="{{$root.g0>0}}"><view class="{{['kcxq_video',speedState?'qpvideo':'']}}"><block wx:for="{{courseDetail.videoFileList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><video src="{{item.url}}" controls="{{true}}" id="{{'videoId_'+index}}" data-event-opts="{{[['fullscreenchange',[['handleFullScreen',['$event']]]],['controlstoggle',[['handleControlstoggle',['$event']]]]]}}" bindfullscreenchange="__e" bindcontrolstoggle="__e"><cover-view hidden="{{!(controlsToggle)}}" class="speed"><cover-view data-event-opts="{{[['tap',[['downloadFile',['$0'],[[['courseDetail.videoFileList','',index,'url']]]]]]]}}" class="doubleSpeed" bindtap="__e">下载视频</cover-view><cover-view data-event-opts="{{[['tap',[['speedTap',[index]]]]]}}" class="doubleSpeed" bindtap="__e">倍速</cover-view></cover-view><block wx:if="{{item.toggle}}"><cover-view class="speedNumBox"><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[0.5,index]]]]]}}" class="{{['number',0.5==speedRate?'activeClass':'']}}" catchtap="__e">0.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[0.8,index]]]]]}}" class="{{['number',0.8==speedRate?'activeClass':'']}}" catchtap="__e">0.8倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1,index]]]]]}}" class="{{['number',1==speedRate?'activeClass':'']}}" catchtap="__e">1倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1.25,index]]]]]}}" class="{{['number',1.25==speedRate?'activeClass':'']}}" catchtap="__e">1.25倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[1.5,index]]]]]}}" class="{{['number',1.5==speedRate?'activeClass':'']}}" catchtap="__e">1.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate',[2,index]]]]]}}" class="{{['number',2==speedRate?'activeClass':'']}}" catchtap="__e">2倍速</cover-view><cover-view data-event-opts="{{[['tap',[['speedTap',[index]]]]]}}" class="number" catchtap="__e">取消</cover-view></cover-view></block></video></block></view></block><view class="kcxq_one"><view class="kcxq_one_a">{{courseDetail.course.name}}<block wx:if="{{courseDetail.level_name!=''}}"><text>{{courseDetail.level_name}}</text></block></view><block wx:if="{{courseDetail.notes!=''}}"><view class="kcxq_one_bz"><image src="/static/images/icon59.png"></image>{{courseDetail.notes}}</view></block><block wx:if="{{courseDetail.teacher}}"><view class="kcxq_one_b"><image class="kcxq_one_b_l" src="{{imgbaseUrl+courseDetail.teacher.image}}" mode="aspectFit"></image><view class="kcxq_one_b_r"><view class="kcxq_one_b_r_l"><view>{{courseDetail.teacher.name}}</view><block wx:if="{{courseDetail.teacher.work_year*1>0}}"><text>{{courseDetail.teacher.work_year+"年经验"}}</text></block></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/teacherDetail?id='+courseDetail.teacher.id]]]]]}}" class="kcxq_one_b_r_r" bindtap="__e">老师详情<image src="/static/images/introduce_more.png"></image></view></view></view></block><view class="kcxq_one_c" style="{{(courseDetail.teacher?'':'margin-left:0')}}"><view>{{"上课时间："+courseDetail.start_time}}</view><view>{{"课程时长："+courseDetail.duration+"分钟"}}</view><view data-event-opts="{{[['tap',[['dhTap',['$event']]]]]}}" bindtap="__e">{{"上课地址："+courseDetail.store.address}}<image src="/static/images/icon18.png"></image></view></view></view><block wx:if="{{courseDetail.appointment_number*1>0}}"><view class="kcxq_two"><view class="kcxq_two_xf">{{"已预约"+courseDetail.appointment_number*1+"人"}}<block wx:if="{{courseDetail.waiting_number*1>0}}">{{"、前方"+courseDetail.waiting_number*1+"人在等位"}}</block></view><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>学生</text><text></text></view></view><view class="kcxq_two_b"><block wx:for="{{courseDetail.appointment_people}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><image src="{{imgbaseUrl+item.avatar}}"></image><text>{{item.nickname}}</text></view></block></view></view></block><view class="{{['kcxq_two','kcxq_thr',speedState_tc?'qpvideo':'']}}"><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>本节回顾</text><text></text></view></view><view class="kcxq_thr_b"><block wx:if="{{courseDetail.music_link!=''}}"><view class="kcxq_thr_b_t"><view><image src="/static/images/icon37.png"></image><text>{{courseDetail.music_link}}</text></view></view></block><block wx:for="{{courseDetail.reviewVideoFileList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="videoLi"><block wx:if="{{!item.toggleVideo}}"><image class="videoLi_bf" src="/static/images/bf.png"></image></block><block wx:if="{{!item.toggleVideo}}"><image class="videoLi_bj" src="{{item.image_url}}" mode="aspectFill" data-event-opts="{{[['tap',[['bfTap',[index,'$0'],[[['courseDetail.reviewVideoFileList','',index,'url']]]]]]]}}" bindtap="__e"></image></block><block wx:if="{{courseDetail.review_video!=''&&item.toggleVideo}}"><video style="display:block;width:100%;position:relative;" src="{{item.url}}" controls="{{true}}" id="{{'videoId_tc_'+index}}" data-event-opts="{{[['fullscreenchange',[['handleFullScreen_tc',['$event']]]],['controlstoggle',[['handleControlstoggle_tc',['$event']]]]]}}" bindfullscreenchange="__e" bindcontrolstoggle="__e"><cover-view hidden="{{!(controlsToggle_tc)}}" class="speed"><cover-view data-event-opts="{{[['tap',[['downloadFile',['$0'],[[['courseDetail.reviewVideoFileList','',index,'url']]]]]]]}}" class="doubleSpeed" bindtap="__e">下载视频</cover-view><cover-view data-event-opts="{{[['tap',[['speedTap_tc',[index]]]]]}}" class="doubleSpeed" bindtap="__e">倍速</cover-view></cover-view><block wx:if="{{item.toggle}}"><cover-view class="speedNumBox"><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[0.5,index]]]]]}}" class="{{['number',0.5==speedRate_tc?'activeClass':'']}}" catchtap="__e">0.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[0.8,index]]]]]}}" class="{{['number',0.8==speedRate_tc?'activeClass':'']}}" catchtap="__e">0.8倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[1,index]]]]]}}" class="{{['number',1==speedRate_tc?'activeClass':'']}}" catchtap="__e">1倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[1.25,index]]]]]}}" class="{{['number',1.25==speedRate_tc?'activeClass':'']}}" catchtap="__e">1.25倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[1.5,index]]]]]}}" class="{{['number',1.5==speedRate_tc?'activeClass':'']}}" catchtap="__e">1.5倍速</cover-view><cover-view data-event-opts="{{[['tap',[['handleSetSpeedRate_tc',[2,index]]]]]}}" class="{{['number',2==speedRate_tc?'activeClass':'']}}" catchtap="__e">2倍速</cover-view><cover-view data-event-opts="{{[['tap',[['speedTap_tc',[index]]]]]}}" class="number" catchtap="__e">取消</cover-view></cover-view></block></video></block></view></block></view><block wx:if="{{$root.g1}}"><view style="width:100%;text-align:center;font-size:26rpx;">暂无回顾内容</view></block></view><view class="aqjlViw"></view><view class="kcxq_foo"><view class="kcxq_foo_l"><view data-event-opts="{{[['tap',[['homeTap',['$event']]]]]}}" bindtap="__e"><image src="/static/tabbar/tab_home.png"></image>首页</view></view><view class="kcxq_foo_r"><block wx:if="{{courseDetail.status==1}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="back_999" bindtap="__e">取消预约</view></block><block wx:else><block wx:if="{{courseDetail.status==2}}"><view class="back_999">授课中</view></block><block wx:else><block wx:if="{{courseDetail.status==3}}"><view class="back_999">已完成</view></block><block wx:else><block wx:if="{{courseDetail.status==4}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="back_999" bindtap="__e">取消等位</view></block><block wx:else><block wx:if="{{courseDetail.status==5}}"><view class="back_999">已取消</view></block><block wx:else><block wx:if="{{courseDetail.status==6}}"><view class="back_999">未开始预约</view></block><block wx:else><block wx:if="{{courseDetail.status==7}}"><view class="back_999">截止预约</view></block><block wx:else><block wx:if="{{courseDetail.equivalent*1==0&&courseDetail.appointment_number*1>=courseDetail.maximum_reservation*1}}"><view data-event-opts="{{[['tap',[['kqhyts',['$event']]]]]}}" class="back" style="background:#BEBEBE;" catchtap="__e">预约</view></block><block wx:else><block wx:if="{{courseDetail.member==0}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="back" style="{{(courseDetail.member==0?'background:#BEBEBE':'')}}" catchtap="__e">预约</view></block><block wx:else><view data-event-opts="{{[['tap',[['yypdTo',['$0','/pages/Schedule/Schedulexq?id'+courseDetail.id],['courseDetail']]]]]}}" class="back" catchtap="__e">{{''+(courseDetail.waiting_number*1>0?'去排队':'预约')}}</view></block></block></block></block></block></block></block></block></block></view></view><block wx:if="{{yyToggle}}"><view class="xjTanc"><view class="xjTanc_n"><view class="xjTanc_a">温馨提示</view><view class="xjTanc_b">是否取消预约？</view><view class="xjTanc_c"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['qxyySubTap',['$event']]]]]}}" class="bak" bindtap="__e">确认</view></view></view></view></block><block wx:if="{{ljtkToggle}}"><view class="yytnCon"><view class="yytnCon_n"><image src="{{imgbaseUrlOss+'/userreport/icon55.png'}}"></image><text data-event-opts="{{[['tap',[['ljktTap',['$event']]]]]}}" bindtap="__e"></text></view><image src="/static/images/icon56.png" data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block>