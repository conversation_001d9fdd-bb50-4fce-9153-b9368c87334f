(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/settings/settings"],{"3d35":function(n,t,e){"use strict";var o=e("f541"),c=e.n(o);c.a},"480b":function(n,t,e){"use strict";e.r(t);var o=e("ab43"),c=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(a);t["default"]=c.a},"69e6":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return c})),e.d(t,"a",(function(){}));var o=function(){var n=this.$createElement;this._self._c},c=[]},"8ca2":function(n,t,e){"use strict";e.r(t);var o=e("69e6"),c=e("480b");for(var a in c)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(a);e("3d35");var u=e("828b"),i=Object(u["a"])(c["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=i.exports},ab43:function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={data:function(){return{isLogined:!0}},onShow:function(){},methods:{logoutTap:function(){n.showModal({title:"温馨提示",content:"确定要退出登录吗？",success:function(t){t.confirm?(n.removeStorageSync("token"),n.removeStorageSync("userid"),n.showToast({icon:"none",title:"退出成功",duration:2e3}),setTimeout((function(){n.navigateBack()}),1e3)):t.cancel&&console.log("用户点击取消")}})},navTo:function(t){n.navigateTo({url:t})}}};t.default=e}).call(this,e("df3c")["default"])},cddb:function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("2300");o(e("3240"));var c=o(e("8ca2"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(c.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},f541:function(n,t,e){}},[["cddb","common/runtime","common/vendor"]]]);