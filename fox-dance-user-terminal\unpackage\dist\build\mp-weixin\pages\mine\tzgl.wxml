<view class="tzgl"><view class="tzglOne"><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/tzglxq']]]]]}}" class="tzglOne_t" bindtap="__e"><view class="tzglOne_t_l">小程序消息管理</view><view class="tzglOne_t_r">详细设置<image src="/static/images/right_more.png"></image></view></view></view><view class="tzglTwo"><view class="tzglOne_t"><view class="tzglOne_t_l">公众号消息设置</view><block wx:if="{{glInfo.bind_status==0}}"><view class="tzglOne_t_r"><image src="/static/images/icon88.png"></image>未绑定</view></block><block wx:if="{{glInfo.bind_status==1}}"><view class="tzglOne_t_r" style="color:#131315;"><image src="/static/images/icon88-1.png"></image>已绑定</view></block></view><view class="tzglTwo_b"><view data-event-opts="{{[['tap',[['wemTap',['$event']]]]]}}" bindtap="__e"><image src="/static/images/icon89.png"></image>{{glInfo.bind_status==0?'绑定微信公众号':glInfo.bind_status==1?'解绑微信公众号':''}}</view><view data-event-opts="{{[['tap',[['xxtsTap',['$event']]]]]}}" bindtap="__e"><image src="/static/images/icon90.png"></image>消息推送测试</view></view></view><block wx:if="{{ewmToggle}}"><view class="bdewmCon"><view class="bdewmCon_t"><view class="bdewmCon_t_a">绑定二维码</view><image class="bdewmCon_t_b" src="{{glInfo.qrcode}}" mode="aspectFill" show-menu-by-longpress="{{true}}"></image><view class="bdewmCon_t_c">使用说明</view><view class="bdewmCon_t_d"><text></text>长按二维码围片识别<text></text></view></view><image class="bdewmCon_b" src="/static/images/popup_close1.png" data-event-opts="{{[['tap',[['ewmEndTap',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{succrssTsToggle}}"><view class="tstsTanc"><view class="tstsTanc_n"><image class="tstsTanc_bj" src="/static/images/icon91.png"></image><view class="tstsTanc_n_n"><view class="tstsTanc_a">提示</view><view class="tstsTanc_b">通知推送完毕，请查收，如您未收到消息，请联系商家。</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="tstsTanc_c" bindtap="__e">我知道了</view></view></view></view></block><block wx:if="{{errTsToggle}}"><view class="tstsTanc"><view class="tstsTanc_n"><image class="tstsTanc_bj" src="/static/images/icon91-1.png"></image><view class="tstsTanc_n_n"><view class="tstsTanc_a">提示</view><view class="tstsTanc_b">通知消息推送不成功，请检查以下可能</view><view class="tstsTanc_b tstsTanc_b_cha"><text></text>您的账号暂未绑定公众号，请先关注</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="tstsTanc_c" bindtap="__e">我知道了</view></view></view></view></block><view class="aqjlViw"></view></view>