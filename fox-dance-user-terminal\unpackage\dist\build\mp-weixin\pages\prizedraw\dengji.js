(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/prizedraw/dengji"],{"14f5":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a("d0b6"),n={data:function(){return{isLogined:!0,navLists:["全部","待兑换","已兑换","已过期"],type:0,courseLists:[],page:1,total_pages:1,zanwsj:!1,status:"loading",loadingText:"努力加载中",loadmoreText:"轻轻上拉",nomoreText:"实在没有了",imgbaseUrl:"",qjbutton:"#131315"}},onLoad:function(){this.qjbutton=t.getStorageSync("storeInfo").button},onShow:function(){this.imgbaseUrl=this.$baseUrl,this.page=1,this.courseLists=[],this.courseData(),t.setStorageSync("dy_type","dj")},methods:{goodsSpTo:function(e){1*e.type==1&&t.navigateTo({url:"/pages/prizedraw/winningrecordxq?id="+e.goods_id+"&jpid="+e.id+"&status="+e.status})},dhTap:function(e){1==e.type&&(e.goods.jpid=e.id,t.setStorageSync("dhspGoods",e.goods),t.navigateTo({url:"/pages/prizedraw/confirmOrder"})),2==e.type&&t.navigateTo({url:"/pages/prizedraw/hb_confirmOrders?jpid="+e.id+"&price="+e.price}),3==e.type&&t.navigateTo({url:"/pages/prizedraw/selectStores?jpid="+e.id}),4==e.type&&this.wmkDhData(e.id)},wmkDhData:function(e){t.showLoading({title:"加载中"}),(0,o.wmkyhqdhApi)({id:e}).then((function(e){console.log("无门槛代金券提交",e),1==e.code&&(t.hideLoading(),t.redirectTo({url:"/pages/prizedraw/success"}))}))},ckwlTap:function(e){t.navigateTo({url:"/pages/mine/order/logistics?id="+e.id+"&name="+e.goods.name+"&images="+e.goods.image+"&type=2"})},navTap:function(t){this.type=t,this.page=1,this.courseLists=[],this.courseData()},courseData:function(){t.showLoading({title:"加载中"});var e=this;(0,o.LevelRewardsApi)({page:e.page,size:10,type:e.type}).then((function(a){if(console.log("等级奖励",a),1==a.code){var o=a.data.data;e.courseLists=e.courseLists.concat(o),e.zanwsj=!!e.courseLists.length,e.page++,e.total_pages=a.data.last_page,1!=e.page&&(e.total_pages>=e.page?e.status="loading":e.status="nomore"),0==e.courseLists.length?e.zanwsj=!0:e.zanwsj=!1,1*a.data.total<=10&&(e.status="nomore"),e.loding=!0,t.hideLoading(),t.stopPullDownRefresh()}}))},onReachBottom:function(){1!=this.page&&this.total_pages>=this.page&&this.courseData()},onPullDownRefresh:function(){console.log("我被下拉了"),this.page=1,this.courseLists=[],this.courseData()},navTo:function(e){t.navigateTo({url:e})}}};e.default=n}).call(this,a("df3c")["default"])},"3c2b":function(t,e,a){"use strict";a.r(e);var o=a("c57a"),n=a("e330");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("7b3c");var s=a("828b"),r=Object(s["a"])(n["default"],o["b"],o["c"],!1,null,"80908974",null,!1,o["a"],void 0);e["default"]=r.exports},"7b3c":function(t,e,a){"use strict";var o=a("ab60"),n=a.n(o);n.a},ab60:function(t,e,a){},c57a:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},n=[]},e330:function(t,e,a){"use strict";a.r(e);var o=a("14f5"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},f666:function(t,e,a){"use strict";(function(t,e){var o=a("47a9");a("2300");o(a("3240"));var n=o(a("3c2b"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(n.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["f666","common/runtime","common/vendor"]]]);