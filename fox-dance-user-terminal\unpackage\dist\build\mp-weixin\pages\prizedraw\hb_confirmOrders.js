(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/prizedraw/hb_confirmOrders"],{"0361":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var o=function(){var e=this.$createElement;this._self._c},a=[]},"0616":function(e,t,n){},"2f61":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("2300");o(n("3240"));var a=o(n("db52"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"4c3f":function(e,t,n){"use strict";var o=n("0616"),a=n.n(o);a.a},7647:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n("d0b6"),a={data:function(){return{isLogined:!0,productxq:{id:1},imgbaseUrl:"",remark:"",shouAddr:{area:""},switchVal:!1,jpid:0,price:0,payment_code:"",qjbutton:"#131315"}},onShow:function(){e.getStorageSync("diancan")?this.shouAddr=e.getStorageSync("diancan"):this.shouAddr={area:""},this.userData()},onLoad:function(t){this.qjbutton=e.getStorageSync("storeInfo").button,this.imgbaseUrl=this.$baseUrl,this.addressData(),console.log(this.productxq),this.jpid=t.jpid,this.price=t.price,e.getStorageSync("skxxMr")||e.setStorageSync("skxxMr",1)},onUnload:function(){console.log("onHide","onHide"),2==e.getStorageSync("skxxMr")&&""!=this.payment_code&&null!=this.payment_code&&(console.log(this.payment_code,"this.payment_code"),this.tjxxTap("notz"))},methods:{dhSubTap:function(){if(null==this.payment_code||""==this.payment_code)return e.showToast({title:"请传收款信息",icon:"none",duration:2e3}),!1;e.showLoading({title:"加载中"});var t=this;"cj"==e.getStorageSync("dy_type")&&(0,o.exchangeRedPackApi)({id:t.jpid}).then((function(n){console.log("红包兑换提交",n),1==n.code&&(2==e.getStorageSync("skxxMr")?t.tjxxTap():(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"})))})),"dj"==e.getStorageSync("dy_type")&&(0,o.exchangeRedPackLevelApi)({id:t.jpid}).then((function(n){console.log("红包兑换提交",n),1==n.code&&(2==e.getStorageSync("skxxMr")?t.tjxxTap():(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"})))}))},tjxxTap:function(t){(0,o.paymentcodeApi)({payment_code:""}).then((function(n){1==n.code&&(t||(e.hideLoading(),e.redirectTo({url:"/pages/prizedraw/success"})))}))},userData:function(){e.showLoading({title:"加载中"});var t=this;(0,o.userInfoApi)({}).then((function(n){1==n.code&&(console.log("个人信息",n),t.payment_code=n.data.payment_code,e.hideLoading())}))},change:function(e){console.log("change"),this.switchVal=e},goToAddr:function(t){e.navigateTo({url:"/pages/mine/address?type=".concat(t)})},addressData:function(){var t=this;(0,o.addrList)({page:1,size:999}).then((function(n){if(1==n.code){console.log(n,"地址列表");for(var o=[],a=0;a<n.data.length;a++)1==n.data[a].is_default&&o.push(n.data[a]);0==o.length?t.shouAddr={area:""}:(t.shouAddr=o[0],e.setStorageSync("diancan",o[0]))}else t.mescroll.endErr()}))},navTo:function(t){e.navigateTo({url:t})}}};t.default=a}).call(this,n("df3c")["default"])},db52:function(e,t,n){"use strict";n.r(t);var o=n("0361"),a=n("ee62");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("4c3f");var c=n("828b"),r=Object(c["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=r.exports},ee62:function(e,t,n){"use strict";n.r(t);var o=n("7647"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=a.a}},[["2f61","common/runtime","common/vendor"]]]);