<block wx:if="{{productxq.id}}"><view class="confirmOrder jpdh" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><block wx:if="{{!payment_code}}"><view data-event-opts="{{[['tap',[['navTo',['/pages/prizedraw/edit_skxx']]]]]}}" class="qrdd_a" bindtap="__e"><image src="/static/images/icon33-1.png"></image>添加微信收款码</view></block><block wx:else><view class="hbdhCon_a"><image class="hbdhCon_a_bj" src="/static/images/icon62.png"></image><view data-event-opts="{{[['tap',[['navTo',['/pages/prizedraw/edit_skxx']]]]]}}" class="hbdhCon_a_n" bindtap="__e"><image class="hbdhCon_a_l" src="{{payment_code==null||payment_code==''?'/static/images/icon70.png':imgbaseUrl+payment_code}}" mode="aspectFill"></image><view class="hbdhCon_a_r">收款信息</view></view></view></block><view class="qrdd_c"><view class="qrdd_c_li"><view class="jlcon_li_hb"><view class="pri_two_b_li_hb"><image class="pri_two_b_li_hb_bj" src="/static/images/icon83.png"></image><view class="pri_two_b_li_hb_n">{{price}}<image src="/static/images/icon83-1.png"></image></view></view></view><view class="qrdd_c_li_r"><view class="qrdd_c_li_r_a">{{price+"元现金红包"}}</view><view class="qrdd_c_li_r_c"><view></view><text>x1</text></view></view></view></view><view class="aqjlViw"></view><view class="peodex_foo"><view class="peodex_foo_l">共1件<text></text></view><view data-event-opts="{{[['tap',[['dhSubTap',['$event']]]]]}}" class="peodex_foo_r" bindtap="__e">兑换</view></view></view></block>