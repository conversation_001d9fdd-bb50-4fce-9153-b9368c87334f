(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/prizedraw/selectStores"],{"0a63":function(t,e,n){"use strict";n.r(e);var i=n("b89b"),s=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=s.a},"0f81":function(t,e,n){},1393:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("2300");i(n("3240"));var s=i(n("ac9f"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"3aa6":function(t,e,n){"use strict";var i=n("0f81"),s=n.n(i);s.a},"719f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=(this._self._c,this.loding?this.storesLists.length:null),n=this.loding?this.storesLists.length:null;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n}})},s=[]},ac9f:function(t,e,n){"use strict";n.r(e);var i=n("719f"),s=n("0a63");for(var a in s)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(a);n("3aa6");var o=n("828b"),d=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=d.exports},b89b:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("d0b6"),s={data:function(){return{isLogined:!0,loding:!1,storesLists:[],name:"",keywords:"",tabIndex:0,rzIndex:0,jlIndex:0,imgbaseUrl:"",storeInfo:{address:""},mdlist:!1,typeIndex:0,jpid:0,selectIndex:-1}},onLoad:function(t){this.jpid=t.jpid,this.typeIndex=t.type},onShow:function(){this.imgbaseUrl=this.$baseUrl,this.storeData(),this.storeInfo=t.getStorageSync("storeInfo")},methods:{selectmdTap:function(){if(-1==this.selectIndex)return t.showToast({title:"请选择门店",icon:"none",duration:1e3}),!1;t.showLoading({title:"加载中"}),"cj"==t.getStorageSync("dy_type")&&(0,i.exchangeMemberCardApi)({id:this.jpid,store_id:this.storesLists[this.selectIndex].id}).then((function(e){console.log("抽奖-门店提交",e),1==e.code&&(t.hideLoading(),t.redirectTo({url:"/pages/prizedraw/success"}))})),"dj"==t.getStorageSync("dy_type")&&(0,i.exchangeMemberCardLevelApi)({id:this.jpid,store_id:this.storesLists[this.selectIndex].id}).then((function(e){console.log("等级-门店提交",e),1==e.code&&(t.hideLoading(),t.redirectTo({url:"/pages/prizedraw/success"}))}))},selStoresTap:function(t){this.selectIndex=t},qhmdTap:function(e){if(console.log(e),e.id==t.getStorageSync("storeInfo").id)return t.showToast({title:"当前门店，无需切换",icon:"none",duration:1e3}),!1;t.setStorageSync("storeInfo",{address:e.address,id:e.id,name:e.name}),t.showToast({title:"切换成功",icon:"success",duration:1e3}),setTimeout((function(){t.navigateBack()}),1e3)},searchTap:function(){this.storeData()},tabTap:function(t){if(this.tabIndex=t,1==t){var e=this.rzIndex;e++,this.rzIndex=e++,3==this.rzIndex&&(this.rzIndex=1),this.jlIndex=0}if(2==t){var n=this.jlIndex;n++,this.jlIndex=n++,3==this.jlIndex&&(this.jlIndex=1),this.rzIndex=0}this.storeData()},storeData:function(){t.showLoading({title:"加载中"});var e=this;if(0==this.tabIndex)var n=1;else if(1==this.tabIndex)n=0==this.rzIndex?1:1==this.rzIndex?2:2==this.rzIndex?3:1;else if(2==this.tabIndex)n=0==this.jlIndex?1:1==this.jlIndex?4:2==this.jlIndex?5:1;(0,i.storeListsApi)({name:e.keywords,type:n,longitude:t.getStorageSync("postion").longitude,latitude:t.getStorageSync("postion").latitude,limit:9999}).then((function(n){if(console.log("门店列表",n),1==n.code){t.hideLoading(),e.loding=!0;for(var i=0;i<n.data.data.length;i++)n.data.data[i].select=!1;if(t.getStorageSync("ck_selectStores")&&0==e.typeIndex)for(var s=t.getStorageSync("ck_selectStores"),a=0;a<n.data.data.length;a++)for(var o=0;o<s.length;o++)n.data.data[a].id==s[o].id&&(n.data.data[a].select=!0);if(t.getStorageSync("sck_selectStores")&&1==e.typeIndex){var d=t.getStorageSync("sck_selectStores");for(a=0;a<n.data.data.length;a++)for(o=0;o<d.length;o++)n.data.data[a].id==d[o].id&&(n.data.data[a].select=!0)}e.storesLists=n.data.data}}))},dhTap:function(e){t.openLocation({name:e.address,latitude:1*e.latitude,longitude:1*e.longitude,success:function(){console.log("success")}})},navTo:function(e){t.navigateTo({url:e})}}};e.default=s}).call(this,n("df3c")["default"])}},[["1393","common/runtime","common/vendor"]]]);