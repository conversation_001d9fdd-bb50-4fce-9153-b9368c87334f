<block wx:if="{{loding}}"><view class="switchStores black"><view class="mdqh_head"><view class="mdqh_head_t"><view class="mdqh_head_t_l"><image src="/static/images/icon18-1.png"></image>{{storeInfo.name}}</view><view class="mdqh_head_t_r"><image src="/static/images/search.png"></image><input type="text" placeholder="搜索门店名称" placeholder-style="color: #999999;" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$0'],['keywords']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="mdqh_head_b"><view data-event-opts="{{[['tap',[['tabTap',[0]]]]]}}" class="{{['mdqh_head_b_li',tabIndex==0?'mdqh_head_b_li_ac':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['tabTap',[1]]]]]}}" class="{{['mdqh_head_b_li',tabIndex==1?'mdqh_head_b_li_ac':'']}}" bindtap="__e">入驻<block wx:if="{{rzIndex==0}}"><image src="/static/images/icon46.png"></image></block><block wx:if="{{rzIndex==1}}"><image src="/static/images/icon46-1.png"></image></block><block wx:if="{{rzIndex==2}}"><image src="/static/images/icon46-2.png"></image></block></view><view data-event-opts="{{[['tap',[['tabTap',[2]]]]]}}" class="{{['mdqh_head_b_li',tabIndex==2?'mdqh_head_b_li_ac':'']}}" bindtap="__e">最近距离<block wx:if="{{jlIndex==0}}"><image src="/static/images/icon46.png"></image></block><block wx:if="{{jlIndex==1}}"><image src="/static/images/icon46-1.png"></image></block><block wx:if="{{jlIndex==2}}"><image src="/static/images/icon46-2.png"></image></block></view></view></view><view class="mdqh_con" style="margin-bottom:138rpx;"><block wx:for="{{storesLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/index/storesDetail?id='+item.id]]]]]}}" class="mdqh_con_li" style="{{('background:'+item.background)}}" bindtap="__e"><image class="mdqh_con_li_l" src="{{imgbaseUrl+item.image}}"></image><view class="mdqh_con_li_r"><view class="mdqh_con_li_r_a" style="{{('color:'+item.written_words)}}">{{item.name}}</view><view class="mdqh_con_li_r_b">{{item.introduce}}</view><view data-event-opts="{{[['tap',[['dhTap',['$0'],[[['storesLists','',index]]]]]]]}}" class="mdqh_con_li_r_c" bindtap="__e"><image src="/static/images/icon35.png"></image><view>{{item.address}}</view><image src="/static/images/icon61.png"></image></view><view class="mdqh_con_li_r_d"><text>{{"距离你"+item.distance+"km"}}</text><image style="{{(selectIndex==index?'background-color:'+item.button:'')}}" src="{{item.select?'/static/images/dzxz-3.png':'/static/images/dzxz.png'}}" data-event-opts="{{[['tap',[['selStoresTap',[index]]]]]}}" catchtap="__e"></image></view></view></view></block></view><block wx:if="{{$root.g0==0}}"><view class="gg_zwsj" style="margin-top:92rpx;"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无门店</text></view></view></block><block wx:if="{{$root.g1>0}}"><view class="peode_foo" style="background:#F8F8FA;"><view data-event-opts="{{[['tap',[['selectmdTap']]]]}}" style="{{('background:'+storesLists[0].button)}}" bindtap="__e">我选好了</view></view></block><view class="aqjlViw"></view></view></block>