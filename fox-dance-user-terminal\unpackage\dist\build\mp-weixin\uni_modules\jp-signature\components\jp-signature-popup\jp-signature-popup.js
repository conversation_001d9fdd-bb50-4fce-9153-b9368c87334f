(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup"],{4328:function(t,e,n){"use strict";n.r(e);var i=n("4bca"),o=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=o.a},"4bca":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{popup:{type:[Boolean,String],default:!1},label:{type:String,default:"手写签名"},value:{type:String,default:""},required:{type:[Boolean,String],default:!1},placeholder:{type:String,default:"点击签名"},readonly:{type:[Boolean,String],default:!1},openSmooth:{type:[Boolean,String],default:!0},boundingBox:{type:[Boolean,String],default:!0}},data:function(){return{showPopup:!1,isHeight:!1,height1:t.getSystemInfoSync().windowWidth/2,width:t.getSystemInfoSync().windowWidth,height:t.getSystemInfoSync().windowHeight,showPicker:!1}},methods:{moveHandle:function(){},toImg:function(){this.$emit("toImg",this.value)},undo:function(){this.$refs.signatureRef.undo()},toPop:function(){this.showPopup=!0},toDeleteImg:function(){this.$emit("input","")},toclear:function(){this.isHeight=!1,this.showPopup=!1},close:function(){this.isHeight=!1,this.showPopup=!1,this.$refs.signatureRef.clear()},deleteImg:function(){this.$refs.signatureRef.clear()},toDataURL:function(t){this.$emit("input",t),this.showPicker=!1},Tomagnify:function(){this.isHeight=!this.isHeight,this.$refs.signatureRef.clear()},isEmpty:function(){var e=this;console.log("搜索1"),this.$refs.signatureRef.canvasToTempFilePath({quality:.8,success:function(n){console.log(e.required,"res",n),e.required&&n.isEmpty?(console.log("请先签名hah"),t.showToast({title:"请先签名",icon:"none"})):(e.$emit("input",n.tempFilePath),e.$emit("change",n.tempFilePath),e.isHeight=!1,e.showPopup=!1)},fail:function(e){console.log(e,"fail"),t.showToast({title:"请先签名",icon:"none"})},complete:function(t){console.log(t,"err")}})}},beforeCreate:function(){},created:function(){}};e.default=n}).call(this,n("df3c")["default"])},"821b":function(t,e,n){"use strict";n.r(e);var i=n("cee2"),o=n("4328");for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(u);n("c9d2");var s=n("828b"),a=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"15356f30",null,!1,i["a"],void 0);e["default"]=a.exports},8793:function(t,e,n){},c9d2:function(t,e,n){"use strict";var i=n("8793"),o=n.n(i);o.a},cee2:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return i}));var i={jpSignature:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/jp-signature/components/jp-signature/jp-signature")]).then(n.bind(null,"f597"))}},o=function(){var t=this.$createElement;this._self._c},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup-create-component',
    {
        'uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("821b"))
        })
    },
    [['uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup-create-component']]
]);
