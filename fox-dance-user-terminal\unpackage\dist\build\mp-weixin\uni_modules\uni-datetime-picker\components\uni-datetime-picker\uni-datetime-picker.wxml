<view class="uni-date"><view data-event-opts="{{[['tap',[['show',['$event']]]]]}}" class="uni-date-editor" bindtap="__e"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><view class="{{['uni-date-editor--x',(disabled)?'uni-date-editor--x__disabled':'',(border)?'uni-date-x--border':'']}}"><block wx:if="{{!isRange}}"><view class="uni-date-x uni-date-single"><uni-icons class="icon-calendar" vue-id="55cceed7-1" type="calendar" color="#c0c4cc" size="22" bind:__l="__l"></uni-icons><view class="uni-date__x-input">{{displayValue||singlePlaceholderText}}</view></view></block><block wx:else><view class="uni-date-x uni-date-range"><uni-icons class="icon-calendar" vue-id="55cceed7-2" type="calendar" color="#c0c4cc" size="22" bind:__l="__l"></uni-icons><view class="uni-date__x-input text-center">{{displayRangeValue.startDate||startPlaceholderText}}</view><view class="range-separator">{{rangeSeparator}}</view><view class="uni-date__x-input text-center">{{displayRangeValue.endDate||endPlaceholderText}}</view></view></block><block wx:if="{{showClearIcon}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="uni-date__icon-clear" catchtap="__e"><uni-icons vue-id="55cceed7-3" type="clear" color="#c0c4cc" size="22" bind:__l="__l"></uni-icons></view></block></view></block></view><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" hidden="{{!(pickerVisible)}}" class="uni-date-mask--pc" bindtap="__e"></view><block wx:if="{{!isPhone}}"><view data-ref="datePicker" hidden="{{!(pickerVisible)}}" class="uni-date-picker__container vue-ref"><block wx:if="{{!isRange}}"><view class="uni-date-single--x" style="{{(pickerPositionStyle)}}"><view class="uni-popper__arrow"></view><block wx:if="{{hasTime}}"><view class="uni-date-changed popup-x-header"><input class="uni-date__input text-center" type="text" placeholder="{{selectDateText}}" data-event-opts="{{[['input',[['__set_model',['','inputDate','$event',[]]]]]]}}" value="{{inputDate}}" bindinput="__e"/><time-picker bind:input="__e" style="width:100%;" vue-id="55cceed7-4" type="time" border="{{false}}" disabled="{{!inputDate}}" start="{{timepickerStartTime}}" end="{{timepickerEndTime}}" hideSecond="{{hideSecond}}" value="{{pickerTime}}" data-event-opts="{{[['^input',[['__set_model',['','pickerTime','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><input class="uni-date__input text-center" type="text" placeholder="{{selectTimeText}}" disabled="{{!inputDate}}" data-event-opts="{{[['input',[['__set_model',['','pickerTime','$event',[]]]]]]}}" value="{{pickerTime}}" bindinput="__e"/></time-picker></view></block><calendar class="vue-ref" style="padding:0 8px;" vue-id="55cceed7-5" showMonth="{{false}}" start-date="{{calendarRange.startDate}}" end-date="{{calendarRange.endDate}}" date="{{calendarDate}}" default-value="{{defaultValue}}" data-ref="pcSingle" data-event-opts="{{[['^change',[['singleChange']]]]}}" bind:change="__e" bind:__l="__l"></calendar><block wx:if="{{hasTime}}"><view class="popup-x-footer"><text data-event-opts="{{[['tap',[['confirmSingleChange',['$event']]]]]}}" class="confirm-text" bindtap="__e">{{okText}}</text></view></block></view></block><block wx:else><view class="uni-date-range--x" style="{{(pickerPositionStyle)}}"><view class="uni-popper__arrow"></view><block wx:if="{{hasTime}}"><view class="popup-x-header uni-date-changed"><view class="popup-x-header--datetime"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{startDateText}}" data-event-opts="{{[['input',[['__set_model',['$0','startDate','$event',[]],['tempRange']]]]]}}" value="{{tempRange.startDate}}" bindinput="__e"/><time-picker bind:input="__e" vue-id="55cceed7-6" type="time" start="{{timepickerStartTime}}" border="{{false}}" disabled="{{!tempRange.startDate}}" hideSecond="{{hideSecond}}" value="{{tempRange.startTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','startTime','$event',[]],['tempRange']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{startTimeText}}" disabled="{{!tempRange.startDate}}" data-event-opts="{{[['input',[['__set_model',['$0','startTime','$event',[]],['tempRange']]]]]}}" value="{{tempRange.startTime}}" bindinput="__e"/></time-picker></view><uni-icons style="line-height:40px;" vue-id="55cceed7-7" type="arrowthinright" color="#999" bind:__l="__l"></uni-icons><view class="popup-x-header--datetime"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{endDateText}}" data-event-opts="{{[['input',[['__set_model',['$0','endDate','$event',[]],['tempRange']]]]]}}" value="{{tempRange.endDate}}" bindinput="__e"/><time-picker bind:input="__e" vue-id="55cceed7-8" type="time" end="{{timepickerEndTime}}" border="{{false}}" disabled="{{!tempRange.endDate}}" hideSecond="{{hideSecond}}" value="{{tempRange.endTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','endTime','$event',[]],['tempRange']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{endTimeText}}" disabled="{{!tempRange.endDate}}" data-event-opts="{{[['input',[['__set_model',['$0','endTime','$event',[]],['tempRange']]]]]}}" value="{{tempRange.endTime}}" bindinput="__e"/></time-picker></view></view></block><view class="popup-x-body"><calendar class="vue-ref" style="padding:0 8px;" vue-id="55cceed7-9" showMonth="{{false}}" start-date="{{calendarRange.startDate}}" end-date="{{calendarRange.endDate}}" range="{{true}}" pleStatus="{{endMultipleStatus}}" data-ref="left" data-event-opts="{{[['^change',[['leftChange']]],['^firstEnterCale',[['updateRightCale']]]]}}" bind:change="__e" bind:firstEnterCale="__e" bind:__l="__l"></calendar><calendar class="vue-ref" style="padding:0 8px;border-left:1px solid #F1F1F1;" vue-id="55cceed7-10" showMonth="{{false}}" start-date="{{calendarRange.startDate}}" end-date="{{calendarRange.endDate}}" range="{{true}}" pleStatus="{{startMultipleStatus}}" data-ref="right" data-event-opts="{{[['^change',[['rightChange']]],['^firstEnterCale',[['updateLeftCale']]]]}}" bind:change="__e" bind:firstEnterCale="__e" bind:__l="__l"></calendar></view><block wx:if="{{hasTime}}"><view class="popup-x-footer"><text data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" bindtap="__e">{{clearText}}</text><text data-event-opts="{{[['tap',[['confirmRangeChange',['$event']]]]]}}" class="confirm-text" bindtap="__e">{{okText}}</text></view></block></view></block></view></block><block wx:if="{{isPhone}}"><calendar class="vue-ref" vue-id="55cceed7-11" clearDate="{{false}}" date="{{calendarDate}}" defTime="{{mobileCalendarTime}}" start-date="{{calendarRange.startDate}}" end-date="{{calendarRange.endDate}}" selectableTimes="{{mobSelectableTime}}" startPlaceholder="{{startPlaceholder}}" endPlaceholder="{{endPlaceholder}}" default-value="{{defaultValue}}" pleStatus="{{endMultipleStatus}}" showMonth="{{false}}" range="{{isRange}}" hasTime="{{hasTime}}" insert="{{false}}" hideSecond="{{hideSecond}}" data-ref="mobile" data-event-opts="{{[['^confirm',[['mobileChange']]],['^maskClose',[['close']]],['^change',[['calendarClick']]]]}}" bind:confirm="__e" bind:maskClose="__e" bind:change="__e" bind:__l="__l"></calendar></block></view>