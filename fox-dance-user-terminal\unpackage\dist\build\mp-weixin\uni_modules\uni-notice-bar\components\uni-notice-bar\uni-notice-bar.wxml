<block wx:if="{{show}}"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="uni-noticebar data-v-52287df2" style="{{'background-color:'+(backgroundColor)+';'}}" bindtap="__e"><block wx:if="{{showIcon===true||showIcon==='true'}}"><uni-icons class="uni-noticebar-icon data-v-52287df2" vue-id="3b16ced1-1" type="sound" color="{{color}}" size="{{fontSize*1.5}}" bind:__l="__l"></uni-icons></block><view data-ref="textBox" class="{{['uni-noticebar__content-wrapper','data-v-52287df2','vue-ref',(scrollable)?'uni-noticebar__content-wrapper--scrollable':'',(!scrollable&&(single||moreText))?'uni-noticebar__content-wrapper--single':'']}}" style="{{'height:'+(scrollable?fontSize*1.5+'px':'auto')+';'}}"><view class="{{['uni-noticebar__content','data-v-52287df2',(scrollable)?'uni-noticebar__content--scrollable':'',(!scrollable&&(single||moreText))?'uni-noticebar__content--single':'']}}" id="{{elIdBox}}"><text class="{{['uni-noticebar__content-text','data-v-52287df2','vue-ref',(scrollable)?'uni-noticebar__content-text--scrollable':'',(!scrollable&&(single||showGetMore))?'uni-noticebar__content-text--single':'']}}" style="{{'color:'+(color)+';'+('font-size:'+(fontSize+'px')+';')+('line-height:'+(fontSize*1.5+'px')+';')+('width:'+(wrapWidth+'px')+';')+('animation-duration:'+(animationDuration)+';')+('-webkit-animation-duration:'+(animationDuration)+';')+('animation-play-state:'+(webviewHide?'paused':animationPlayState)+';')+('-webkit-animation-play-state:'+(webviewHide?'paused':animationPlayState)+';')+('animation-delay:'+(animationDelay)+';')+('-webkit-animation-delay:'+(animationDelay)+';')}}" id="{{elId}}" data-ref="animationEle">{{text}}</text></view></view><block wx:if="{{isShowGetMore}}"><view data-event-opts="{{[['tap',[['clickMore',['$event']]]]]}}" class="uni-noticebar__more uni-cursor-point data-v-52287df2" bindtap="__e"><block wx:if="{{$root.g0>0}}"><text style="{{'color:'+(moreColor)+';'+('font-size:'+(fontSize+'px')+';')}}" class="data-v-52287df2">{{moreText}}</text></block><block wx:else><uni-icons vue-id="3b16ced1-2" type="right" color="{{moreColor}}" size="{{fontSize*1.1}}" class="data-v-52287df2" bind:__l="__l"></uni-icons></block></view></block><block wx:if="{{isShowClose}}"><view class="uni-noticebar-close uni-cursor-point data-v-52287df2"><uni-icons vue-id="3b16ced1-3" type="closeempty" color="{{color}}" size="{{fontSize*1.1}}" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" class="data-v-52287df2" bind:__l="__l"></uni-icons></view></block></view></block>