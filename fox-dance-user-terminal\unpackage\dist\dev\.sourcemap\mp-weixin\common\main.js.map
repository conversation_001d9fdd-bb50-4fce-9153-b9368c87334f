{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/App.vue?8d9c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/App.vue?7152", "uni-app:///App.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/App.vue?5ff3", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/App.vue?10b0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "component", "MescrollBody", "use", "uView", "prototype", "$toast", "toast", "$baseUrl", "$baseUrl_admin", "$baseUrlOss", "$baseUrl_ht", "mixin", "share", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "uni", "console", "onShow", "onHide", "methods"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAS3D;AACA;AACA;AAKA;AAEA;AAEA;AA8CA;AAAoC;AAAA;AAnEpC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAc3DC,YAAG,CAACC,SAAS,CAAC,eAAe,EAAEC,YAAY,CAAC;AAC5C;;AAEAF,YAAG,CAACG,GAAG,CAACC,gBAAK,CAAC;AAMdJ,YAAG,CAACK,SAAS,CAACC,MAAM,GAAGC,YAAK;AAC5B;AACAP,YAAG,CAACK,SAAS,CAACG,QAAQ,GAAG,8BAA8B;AACvDR,YAAG,CAACK,SAAS,CAACI,cAAc,GAAG,+BAA+B;AAC9D;AACAT,YAAG,CAACK,SAAS,CAACK,WAAW,GAAG,8BAA8B;AAC1DV,YAAG,CAACK,SAAS,CAACM,WAAW,GAAG,kCAAkC;;AAI9D;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAEAX,YAAG,CAACY,KAAK,CAACC,cAAK,CAAC;AAEhBb,YAAG,CAACc,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIlB,YAAG,mBACfgB,YAAG,EACL;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AC5EZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACoL;AACpL,gBAAgB,8LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAqrB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACEzsB;;eAGA;EACAC;IACAC;IACAC;IACAD;IACAA;EAEA;EACAE;IACAD;EACA;EACAE;IACAF;EACA;EACAG,UAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAoyC,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACAxzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n// 引入浏览器兼容性修复\r\n\r\n\r\n\r\n\r\n\r\n\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nimport httpApi from '@/config/http.api.js'\r\n//引入mescrollBody 组件\r\nimport MescrollBody from \"@/components/mescroll-uni/mescroll-body.vue\"\r\nVue.component('mescroll-body', MescrollBody)\r\n//引入 uview-ui\r\nimport uView from \"@/components/uview-ui\";\r\nVue.use(uView);\r\nimport utils from '@/utils/utils.js'\r\n// 轻提示\r\nimport {\r\n\ttoast\r\n} from './utils/tools'\r\nVue.prototype.$toast = toast\r\n// Vue.prototype.$baseUrl = 'https://danceadmin.xinzhiyukeji.cn'\r\nVue.prototype.$baseUrl = 'https://file.foxdance.com.cn'\r\nVue.prototype.$baseUrl_admin = 'https://admin.foxdance.com.cn'\r\n// Vue.prototype.$baseUrlOss = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com'\r\nVue.prototype.$baseUrlOss = 'https://file.foxdance.com.cn'\r\nVue.prototype.$baseUrl_ht = 'https://contract.foxdance.com.cn'\r\n\r\n\r\n\r\n// import ZAudio from '@/components/uniapp-zaudio' //HbuilderX插件导入方式, import可能需要修改目录名哦\r\n// // import ZAudio from 'uniapp-zaudio' // npm引用方式\r\n\r\n// let zaudio = new ZAudio({\r\n//   // continuePlay: true, //续播\r\n//   autoPlay: false, //自动播放 部分浏览器不支持\r\n// });\r\n// Vue.prototype.$zaudio = zaudio; //挂载vue原型链上\r\n\r\n// //模拟音频初始数据,切勿业务中使用\r\n// var data = [\r\n//   {\r\n//     src:\r\n//       \"https://96.f.1ting.com/local_to_cube_202004121813/96kmp3/zzzzzmp3/2016aJan/18X/18d_DeH/01.mp3\",\r\n//     title: \"恭喜发财\",\r\n//     singer: \"刘德华\",\r\n//     coverImgUrl:\r\n//       \"https://img.1ting.com/images/special/75/s150_f84ef5082b0420f74cd2546b986ab0fc.jpg\",\r\n//   },\r\n//   {\r\n//     src:\r\n//       \"https://96.f.1ting.com/local_to_cube_202004121813/96kmp3/zzzzzmp3/2015kNov/25X/25m_XiaoQ/03.mp3\",\r\n//     title: \"好运来\",\r\n//     singer: \"作者1111\",\r\n//     coverImgUrl:\r\n//       \"https://img.1ting.com/images/special/204/s150_77254cd4a4da1a33b8faf89c4cbf6e40.jpg\",\r\n//   },\r\n// ];\r\n// zaudio.setAudio(data); //添加音频\r\n// zaudio.setRender(0)//渲染第一首音频\r\n\r\n\r\n// 分享 \r\nimport share from '@/utils/share.js'\t\r\nVue.mixin(share)\r\n\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "\r\n<script>\r\n\timport{\r\n\t\tconfig\r\n\t} from '@/config/http.achieve.js'\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tuni.hideTabBar()\r\n\t\t\tconsole.log('App Launch')\r\n\t\t\tuni.removeStorageSync('pageIndex')\r\n\t\t\tuni.removeStorageSync('pageZiliao')\r\n\t\t\t\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"@/components/uview-ui/index.scss\";\r\n\t@import \"@/styles/base.scss\";\r\n\t@import \"@/styles/common.scss\";\r\n\t@import \"@/styles/style.css\";\r\n\t@import \"@/styles/style_fz.css\";\r\n\t@import '@/styles/animate.min.css';\r\n\tpage{\r\n\t\tbackground-color: #f6f6f6;\r\n\t}\r\n\timage{\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t\r\n\t/*每个页面公共css */\r\n\t// 使用时仅需设置 宽高 圆角 和字号\r\n\t.btn {\r\n\t\tbackground-color: #131315;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 11;\r\n\t}\r\n\t\r\n\t\r\n\t.inputRow {\r\n\t\theight: 110rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 0 40rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-bottom: 2rpx solid #f8f8f8;\r\n\t\r\n\t\t.laber {\r\n\t\t\tline-height: 110rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333333;\r\n\t\r\n\t\t\timage {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tmargin-right: 14rpx;\r\n\t\t\t\tmargin-left: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tinput {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 100%;\r\n\t\t\ttext-align: right;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tline-height: 110rpx;\r\n\t\t}\r\n\t\r\n\t\timage {\r\n\t\t\twidth: 16rpx;\r\n\t\t\theight: 28rpx;\r\n\t\t\tmargin-left: 24rpx;\r\n\t\t\tmargin-top: 4rpx;\r\n\t\t}\r\n\t\r\n\t\ttext {\r\n\t\t\tmargin-left: 24rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333333;\r\n\t\t}\r\n\t\r\n\t\t.switch {\r\n\t\t\twidth: 113rpx;\r\n\t\t\theight: 58rpx;\r\n\t\t}\r\n\t\r\n\t\ttextarea {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 130rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t\r\n\t//晨阳设计图 二次确认弹窗常用样式  参考 /pages/setting/setting 退出登录弹窗\r\n\t.prompt {\r\n\t\twidth: 600rpx;\r\n\t\theight: 340rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0 0 10rpx 0 rgba(228, 239, 244, 0.6);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\t\t.prompt_t {\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tpadding: 22rpx 0 0 25rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\r\n\t\t\timage {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 33rpx;\r\n\t\t\t\theight: 33rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t}\r\n\t\r\n\t\t\t.prompt_t_text {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #E93B3D;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.prompt_c {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tpadding: 0 40rpx;\r\n\t\t\ttext-align: center\r\n\t\t}\r\n\t\r\n\t\t.prompt_d {\r\n\t\t\theight: 113rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tborder-top: 1rpx solid #d5d5d5;\r\n\t\r\n\t\t\t.prompt_d_l {\r\n\t\t\t\twidth: 50%;\r\n\t\t\t\tborder-right: 1rpx solid #d5d5d5;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #8D8D8D;\r\n\t\t\t\tline-height: 113rpx;\r\n\t\t\t}\r\n\t\r\n\t\t\t.prompt_d_r {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\twidth: 50%;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tline-height: 113rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n</style>", "import mod from \"-!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752117068114\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}