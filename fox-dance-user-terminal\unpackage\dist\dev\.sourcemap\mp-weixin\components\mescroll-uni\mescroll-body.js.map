{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/mescroll-body.vue?8135", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/mescroll-body.vue?d309", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/mescroll-body.vue?455a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/mescroll-body.vue?2e89", "uni-app:///components/mescroll-uni/mescroll-body.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/mescroll-body.vue?32ab", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/mescroll-body.vue?547d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/wxs/wxs.wxs?5f41", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/mescroll-uni/wxs/wxs.wxs?3b88"], "names": ["mixins", "components", "MescrollEmpty", "MescrollTop", "data", "mescroll", "optDown", "optUp", "downHight", "downRate", "downLoadType", "upLoadType", "isShowEmpty", "isShowToTop", "windowHeight", "windowBottom", "statusBarHeight", "props", "down", "up", "top", "topbar", "bottom", "safearea", "height", "bottombar", "type", "default", "sticky", "watch", "computed", "minHeight", "numTop", "padTop", "numBottom", "padBottom", "isDownReset", "transition", "translateY", "isDownLoading", "downRotate", "downText", "methods", "toPx", "num", "emptyClick", "toTopClick", "created", "inOffset", "vm", "outOffset", "onMoving", "showLoading", "beforeEndDownScroll", "endDownScroll", "clearTimeout", "callback", "showNoMore", "hideUpScroll", "empty", "onShow", "toTop", "MeScroll", "setTimeout", "selector", "uni", "scrollTop", "duration", "console"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0c;AAC1c;AACiE;AACL;AACa;;;AAGzE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,waAAM;AACR,EAAE,ibAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4aAAU;AACZ;AACA;;AAEA;AAC0O;AAC1O,WAAW,4PAAM,iBAAiB,oQAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiFjvB;AAEA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;AAEA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eASA;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;QAAAC;QAAAC;MAAA;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;MAAA;MACAC;MACAC;IACA;IACAC;EACA;;EACAC;IACAnB;MACA;IACA;EACA;EACAoB;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;QACA;UACA;YACA;YACAC;UACA;YACA;YACAA;UACA;YACA;YACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACA;EACAC;IACA;IAEA;MACA;MACA7B;QACA8B;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACA;UACAF;UACAA;QACA;QACAG;UACAH;UACAA;QACA;QACAI;UACAJ;UACA;QACA;QACAK;UACAL;UACAA;UACA;YAAAM;YAAAN;UAAA;UACAA;YAAA;YACA;UACA;QACA;QACA;QACAO;UACAP;QACA;MACA;MACA;MACA9B;QACA;QACAiC;UACAH;QACA;QACA;QACAQ;UACAR;YACAA;UACA;QACA;QACA;QACAS;UACAT;QACA;QACA;QACAU;UACAC;YACA;YACAX;UACA;QACA;QACA;QACAY;UACAD;YACA;YACAX;UACA;QACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IAEAa;IACA;MAAA5C;MAAAC;IAAA;IACA2C;;IAEA;IACAb;IACA;IACAA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACAA;;IAEA;IACAA;MACA;QACA;QACAc;UAAA;UACA;UACA;YACAC;UACA;YACAA;UAMA;UACAC;YACA;cACA;cACA7C;cACA6C;gBACAC;gBACAC;cACA;YACA;cACAC;YACA;UACA;QACA;MACA;QACA;QACAH;UACAC;UACAC;QACA;MACA;IACA;;IAEA;IACA;MACAlB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7VA;AAAA;AAAA;AAAA;AAA8hC,CAAgB,m+BAAG,EAAC,C;;;;;;;;;;;ACAljC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAA+X,CAAgB,0bAAG,EAAC,C;;;;;;;;;;;;ACAnZ;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "components/mescroll-uni/mescroll-body.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-body.vue?vue&type=template&id=5eb4c084&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzIzOSwiYXR0cnMiOnsic3JjIjoiLi93eHMvd3hzLnd4cyIsIm1vZHVsZSI6Ind4c0JpeiIsImxhbmciOiJ3eHMifSwiZW5kIjozMjM5fSwicmVuZGVyQml6Ijp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzM3MiwiYXR0cnMiOnsibW9kdWxlIjoicmVuZGVyQml6IiwibGFuZyI6ImpzIn0sImVuZCI6MzQ2NX19&\"\nvar renderjs\nimport script from \"./mescroll-body.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-body.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-body.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./wxs/wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5CProject%5Cfox%5C%E7%94%A8%E6%88%B7%E7%AB%AF%5Cfox-dance-user-terminal%5Ccomponents%5Cmescroll-uni%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/mescroll-uni/mescroll-body.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=template&id=5eb4c084&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzIzOSwiYXR0cnMiOnsic3JjIjoiLi93eHMvd3hzLnd4cyIsIm1vZHVsZSI6Ind4c0JpeiIsImxhbmciOiJ3eHMifSwiZW5kIjozMjM5fSwicmVuZGVyQml6Ijp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzM3MiwiYXR0cnMiOnsibW9kdWxlIjoicmVuZGVyQml6IiwibGFuZyI6ImpzIn0sImVuZCI6MzQ2NX19&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view \r\n\tclass=\"mescroll-body mescroll-render-touch\" \r\n\t:class=\"{'mescorll-sticky': sticky}\"\r\n\t:style=\"{'minHeight':minHeight, 'padding-top': padTop, 'padding-bottom': padBottom}\" \r\n\t@touchstart=\"wxsBiz.touchstartEvent\" \r\n\t@touchmove=\"wxsBiz.touchmoveEvent\" \r\n\t@touchend=\"wxsBiz.touchendEvent\" \r\n\t@touchcancel=\"wxsBiz.touchendEvent\"\r\n\t:change:prop=\"wxsBiz.propObserver\"\r\n\t:prop=\"wxsProp\"\r\n\t>\r\n\t\t<!-- 状态栏 -->\r\n\t\t<view v-if=\"topbar&&statusBarHeight\" class=\"mescroll-topbar\" :style=\"{height: statusBarHeight+'px', background: topbar}\"></view>\r\n\t\t\r\n\t\t<view class=\"mescroll-body-content mescroll-wxs-content\" :style=\"{ transform: translateY, transition: transition }\" :change:prop=\"wxsBiz.callObserver\" :prop=\"callProp\">\r\n\t\t\t<!-- 下拉加载区域 (支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-down组件实现)-->\r\n\t\t\t<!-- <mescroll-down :option=\"mescroll.optDown\" :type=\"downLoadType\" :rate=\"downRate\"></mescroll-down> -->\r\n\t\t\t<view v-if=\"mescroll.optDown.use\" class=\"mescroll-downwarp\" :style=\"{'background':mescroll.optDown.bgColor,'color':mescroll.optDown.textColor}\">\r\n\t\t\t\t<view class=\"downwarp-content\">\r\n\t\t\t\t\t<view class=\"downwarp-progress mescroll-wxs-progress\" :class=\"{'mescroll-rotate': isDownLoading}\" :style=\"{'border-color':mescroll.optDown.textColor, 'transform': downRotate}\"></view>\r\n\t\t\t\t\t<view class=\"downwarp-tip\">{{downText}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\r\n\t\t\t<!-- 列表内容 -->\r\n\t\t\t<slot></slot>\r\n\r\n\t\t\t<!-- 空布局 -->\r\n\t\t\t<!-- <mescroll-empty v-if=\"isShowEmpty\" :option=\"mescroll.optUp.empty\" @emptyclick=\"emptyClick\"></mescroll-empty> -->\r\n\t\t\t\r\n\t\r\n\r\n\t\t\t<!-- 上拉加载区域 (下拉刷新时不显示, 支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-up组件实现)-->\r\n\t\t\t<!-- <mescroll-up v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" :option=\"mescroll.optUp\" :type=\"upLoadType\"></mescroll-up> -->\r\n\t\t\t<view v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==2\" class=\"mescroll-upwarp\" :style=\"{'background':mescroll.optUp.bgColor,'color':mescroll.optUp.textColor}\">\r\n\t\t\t\t<!-- 加载中 (此处不能用v-if,否则android小程序快速上拉可能会不断触发上拉回调) -->\r\n\t\t\t\t<view v-show=\"upLoadType===1\">\r\n\t\t\t\t\t<view class=\"upwarp-progress mescroll-rotate\" :style=\"{'border-color':mescroll.optUp.textColor}\"></view>\r\n\t\t\t\t\t<view class=\"upwarp-tip\">{{ mescroll.optUp.textLoading }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 无数据 -->\r\n\t\t\t\t<!-- <view v-if=\"upLoadType===2\" class=\"upwarp-nodata\">{{ mescroll.optUp.textNoMore }}</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效)\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<view v-if=\"bottombar && windowBottom>0\" class=\"mescroll-bottombar\" :style=\"{height: windowBottom+'px'}\"></view>\r\n\t\t<!-- #endif -->\r\n\t\t\r\n\t\t<!-- 适配iPhoneX -->\r\n\t\t<view v-if=\"safearea\" class=\"mescroll-safearea\"></view>\r\n\t\t\r\n\t\t<!-- 回到顶部按钮 (fixed元素需写在transform外面,防止降级为absolute)-->\r\n\t\t<mescroll-top v-model=\"isShowToTop\" :option=\"mescroll.optUp.toTop\" @click=\"toTopClick\"></mescroll-top>\r\n\t\t\r\n\t\t<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\r\n\t\t<!-- renderjs的数据载体,不可写在mescroll-downwarp内部,避免use为false时,载体丢失,无法更新数据 -->\r\n\t\t<view :change:prop=\"renderBiz.propObserver\" :prop=\"wxsProp\"></view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<!-- 微信小程序, QQ小程序, app, h5使用wxs -->\r\n<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\r\n<script src=\"./wxs/wxs.wxs\" module=\"wxsBiz\" lang=\"wxs\"></script>\r\n<!-- #endif -->\r\n\r\n<!-- app, h5使用renderjs -->\r\n<!-- #ifdef APP-PLUS || H5 -->\r\n<script module=\"renderBiz\" lang=\"renderjs\">\r\n\timport renderBiz from './wxs/renderjs.js';\r\n\texport default {\r\n\t\tmixins: [renderBiz]\r\n\t}\r\n</script>\r\n<!-- #endif -->\r\n\r\n<script>\r\n\t// 引入mescroll-uni.js,处理核心逻辑\r\n\timport MeScroll from './mescroll-uni.js';\r\n\t// 引入全局配置\r\n\timport GlobalOption from './mescroll-uni-option.js';\r\n\t// 引入空布局组件\r\n\timport MescrollEmpty from './components/mescroll-empty.vue';\r\n\t// 引入回到顶部组件\r\n\timport MescrollTop from './components/mescroll-top.vue';\r\n\t// 引入兼容wxs(含renderjs)写法的mixins\r\n\timport WxsMixin from './wxs/mixins.js';\r\n\t\r\n\texport default {\r\n\t\tmixins: [WxsMixin],\r\n\t\tcomponents: {\r\n\t\t\tMescrollEmpty,\r\n\t\t\tMescrollTop\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmescroll: {optDown:{},optUp:{}}, // mescroll实例\r\n\t\t\t\tdownHight: 0, //下拉刷新: 容器高度\r\n\t\t\t\tdownRate: 0, // 下拉比率(inOffset: rate<1; outOffset: rate>=1)\r\n\t\t\t\tdownLoadType: 0, // 下拉刷新状态: 0(loading前), 1(inOffset), 2(outOffset), 3(showLoading), 4(endDownScroll)\r\n\t\t\t\tupLoadType: 0, // 上拉加载状态：0（loading前），1（loading中），2（没有更多了,显示END文本提示），3（没有更多了,不显示END文本提示）\r\n\t\t\t\tisShowEmpty: false, // 是否显示空布局\r\n\t\t\t\tisShowToTop: false, // 是否显示回到顶部按钮\r\n\t\t\t\twindowHeight: 0, // 可使用窗口的高度\r\n\t\t\t\twindowBottom: 0, // 可使用窗口的底部位置\r\n\t\t\t\tstatusBarHeight: 0 // 状态栏高度\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tdown: Object, // 下拉刷新的参数配置\r\n\t\t\tup: Object, // 上拉加载的参数配置\r\n\t\t\ttop: [String, Number], // 下拉布局往下的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\r\n\t\t\ttopbar: [Boolean, String], // top的偏移量是否加上状态栏高度, 默认false (使用场景:取消原生导航栏时,配置此项可留出状态栏的占位, 支持传入字符串背景,如色值,背景图,渐变)\r\n\t\t\tbottom: [String, Number], // 上拉布局往上的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\r\n\t\t\tsafearea: Boolean, // bottom的偏移量是否加上底部安全区的距离, 默认false (需要适配iPhoneX时使用)\r\n\t\t\theight: [String, Number], // 指定mescroll最小高度,默认windowHeight,使列表不满屏仍可下拉\r\n\t\t\tbottombar:{ // 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效)\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tsticky: Boolean // 是否支持sticky,默认false; 当值配置true时,需避免在mescroll-body标签前面加非定位的元素,否则下拉区域无法会隐藏\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\tdownLoadType(val) {\r\n\t\t\t\tthis.$emit('changedownloding', val)\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// mescroll最小高度,默认windowHeight,使列表不满屏仍可下拉\r\n\t\t\tminHeight(){\r\n\t\t\t\treturn typeof this.height == 'string' ? this.height : this.toPx(this.height || '100%') + 'px'\r\n\t\t\t},\r\n\t\t\t// 下拉布局往下偏移的距离 (px)\r\n\t\t\tnumTop() {\r\n\t\t\t\treturn this.toPx(this.top)\r\n\t\t\t},\r\n\t\t\tpadTop() {\r\n\t\t\t\treturn this.numTop + 'px';\r\n\t\t\t},\r\n\t\t\t// 上拉布局往上偏移 (px)\r\n\t\t\tnumBottom() {\r\n\t\t\t\treturn this.toPx(this.bottom);\r\n\t\t\t},\r\n\t\t\tpadBottom() {\r\n\t\t\t\treturn this.numBottom + 'px';\r\n\t\t\t},\r\n\t\t\t// 是否为重置下拉的状态\r\n\t\t\tisDownReset() {\r\n\t\t\t\treturn this.downLoadType === 3 || this.downLoadType === 4;\r\n\t\t\t},\r\n\t\t\t// 过渡\r\n\t\t\ttransition() {\r\n\t\t\t\treturn this.isDownReset ? 'transform 300ms' : '';\r\n\t\t\t},\r\n\t\t\ttranslateY() {\r\n\t\t\t\treturn this.downHight > 0 ? 'translateY(' + this.downHight + 'px)' : ''; // transform会使fixed失效,需注意把fixed元素写在mescroll之外\r\n\t\t\t},\r\n\t\t\t// 是否在加载中\r\n\t\t\tisDownLoading(){\r\n\t\t\t\treturn this.downLoadType === 3\r\n\t\t\t},\r\n\t\t\t// 旋转的角度\r\n\t\t\tdownRotate(){\r\n\t\t\t\treturn 'rotate(' + 360 * this.downRate + 'deg)'\r\n\t\t\t},\r\n\t\t\t// 文本提示\r\n\t\t\tdownText(){\r\n\t\t\t\tif(!this.mescroll) return \"\"; // 避免头条小程序初始化时报错\r\n\t\t\t\tswitch (this.downLoadType){\r\n\t\t\t\t\tcase 1: return this.mescroll.optDown.textInOffset;\r\n\t\t\t\t\tcase 2: return this.mescroll.optDown.textOutOffset;\r\n\t\t\t\t\tcase 3: return this.mescroll.optDown.textLoading;\r\n\t\t\t\t\tcase 4: return this.mescroll.isDownEndSuccess ? this.mescroll.optDown.textSuccess : this.mescroll.isDownEndSuccess==false ? this.mescroll.optDown.textErr : this.mescroll.optDown.textInOffset;\r\n\t\t\t\t\tdefault: return this.mescroll.optDown.textInOffset;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//number,rpx,upx,px,% --> px的数值\r\n\t\t\ttoPx(num) {\r\n\t\t\t\tif (typeof num === 'string') {\r\n\t\t\t\t\tif (num.indexOf('px') !== -1) {\r\n\t\t\t\t\t\tif (num.indexOf('rpx') !== -1) {\r\n\t\t\t\t\t\t\t// \"10rpx\"\r\n\t\t\t\t\t\t\tnum = num.replace('rpx', '');\r\n\t\t\t\t\t\t} else if (num.indexOf('upx') !== -1) {\r\n\t\t\t\t\t\t\t// \"10upx\"\r\n\t\t\t\t\t\t\tnum = num.replace('upx', '');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// \"10px\"\r\n\t\t\t\t\t\t\treturn Number(num.replace('px', ''));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (num.indexOf('%') !== -1) {\r\n\t\t\t\t\t\t// 传百分比,则相对于windowHeight,传\"10%\"则等于windowHeight的10%\r\n\t\t\t\t\t\tlet rate = Number(num.replace('%', '')) / 100;\r\n\t\t\t\t\t\treturn this.windowHeight * rate;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn num ? uni.upx2px(Number(num)) : 0;\r\n\t\t\t},\r\n\t\t\t// 点击空布局的按钮回调\r\n\t\t\temptyClick() {\r\n\t\t\t\tthis.$emit('emptyclick', this.mescroll);\r\n\t\t\t},\r\n\t\t\t// 点击回到顶部的按钮回调\r\n\t\t\ttoTopClick() {\r\n\t\t\t\tthis.mescroll.scrollTo(0, this.mescroll.optUp.toTop.duration); // 执行回到顶部\r\n\t\t\t\tthis.$emit('topclick', this.mescroll); // 派发点击回到顶部按钮的回调\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 使用created初始化mescroll对象; 如果用mounted部分css样式编译到H5会失效\r\n\t\tcreated() {\r\n\t\t\tlet vm = this;\r\n\r\n\t\t\tlet diyOption = {\r\n\t\t\t\t// 下拉刷新的配置\r\n\t\t\t\tdown: {\r\n\t\t\t\t\tinOffset() {\r\n\t\t\t\t\t\tvm.downLoadType = 1; // 下拉的距离进入offset范围内那一刻的回调 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t},\r\n\t\t\t\t\toutOffset() {\r\n\t\t\t\t\t\tvm.downLoadType = 2; // 下拉的距离大于offset那一刻的回调 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tonMoving(mescroll, rate, downHight) {\r\n\t\t\t\t\t\t// 下拉过程中的回调,滑动过程一直在执行;\r\n\t\t\t\t\t\tvm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downRate = rate; //下拉比率 (inOffset: rate<1; outOffset: rate>=1)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tshowLoading(mescroll, downHight) {\r\n\t\t\t\t\t\tvm.downLoadType = 3; // 显示下拉刷新进度的回调 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbeforeEndDownScroll(mescroll){\r\n\t\t\t\t\t\tvm.downLoadType = 4; \r\n\t\t\t\t\t\treturn mescroll.optDown.beforeEndDelay // 延时结束的时长\r\n\t\t\t\t\t},\r\n\t\t\t\t\tendDownScroll() {\r\n\t\t\t\t\t\tvm.downLoadType = 4; // 结束下拉 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downHight = 0; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tif(vm.downResetTimer) {clearTimeout(vm.downResetTimer); vm.downResetTimer = null} // 移除重置倒计时\r\n\t\t\t\t\t\tvm.downResetTimer = setTimeout(()=>{ // 过渡动画执行完毕后,需重置为0的状态,避免下次inOffset不及时显示textInOffset\r\n\t\t\t\t\t\t\tif(vm.downLoadType === 4) vm.downLoadType = 0\r\n\t\t\t\t\t\t},300)\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 派发下拉刷新的回调\r\n\t\t\t\t\tcallback: function(mescroll) {\r\n\t\t\t\t\t\tvm.$emit('down', mescroll);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t// 上拉加载的配置\r\n\t\t\t\tup: {\r\n\t\t\t\t\t// 显示加载中的回调\r\n\t\t\t\t\tshowLoading() {\r\n\t\t\t\t\t\tvm.upLoadType = 1;\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 显示无更多数据的回调\r\n\t\t\t\t\tshowNoMore() {\r\n\t\t\t\t\t\tvm.$nextTick(() => {\r\n\t\t\t\t\t\t\tvm.upLoadType = 2;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 隐藏上拉加载的回调\r\n\t\t\t\t\thideUpScroll(mescroll) {\r\n\t\t\t\t\t\tvm.upLoadType = mescroll.optUp.hasNext ? 0 : 3;\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 空布局\r\n\t\t\t\t\tempty: {\r\n\t\t\t\t\t\tonShow(isShow) {\r\n\t\t\t\t\t\t\t// 显示隐藏的回调\r\n\t\t\t\t\t\t\tvm.isShowEmpty = isShow;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 回到顶部\r\n\t\t\t\t\ttoTop: {\r\n\t\t\t\t\t\tonShow(isShow) {\r\n\t\t\t\t\t\t\t// 显示隐藏的回调\r\n\t\t\t\t\t\t\tvm.isShowToTop = isShow;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 派发上拉加载的回调\r\n\t\t\t\t\tcallback: function(mescroll) {\r\n\t\t\t\t\t\tvm.$emit('up', mescroll);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tMeScroll.extend(diyOption, GlobalOption); // 混入全局的配置\r\n\t\t\tlet myOption = JSON.parse(JSON.stringify({down: vm.down,up: vm.up})); // 深拷贝,避免对props的影响\r\n\t\t\tMeScroll.extend(myOption, diyOption); // 混入具体界面的配置\r\n\r\n\t\t\t// 初始化MeScroll对象\r\n\t\t\tvm.mescroll = new MeScroll(myOption, true); // 传入true,标记body为滚动区域\r\n\t\t\t// init回调mescroll对象\r\n\t\t\tvm.$emit('init', vm.mescroll);\r\n\t\t\t\r\n\t\t\t// 设置高度\r\n\t\t\tconst sys = uni.getSystemInfoSync();\r\n\t\t\tif (sys.windowHeight) vm.windowHeight = sys.windowHeight;\r\n\t\t\tif (sys.windowBottom) vm.windowBottom = sys.windowBottom;\r\n\t\t\tif (sys.statusBarHeight) vm.statusBarHeight = sys.statusBarHeight;\r\n\t\t\t// 使down的bottomOffset生效\r\n\t\t\tvm.mescroll.setBodyHeight(sys.windowHeight);\r\n\r\n\t\t\t// 因为使用的是page的scroll,这里需自定义scrollTo\r\n\t\t\tvm.mescroll.resetScrollTo((y, t) => {\r\n\t\t\t\tif(typeof y === 'string'){\r\n\t\t\t\t\t// 滚动到指定view (y为css选择器)\r\n\t\t\t\t\tsetTimeout(()=>{ // 延时确保view已渲染; 不使用$nextTick\r\n\t\t\t\t\t\tlet selector;\r\n\t\t\t\t\t\tif(y.indexOf('#')==-1 && y.indexOf('.')==-1){\r\n\t\t\t\t\t\t\tselector = '#'+y // 不带#和. 则默认为id选择器\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tselector = y\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS || H5 || MP-ALIPAY || MP-DINGTALK\r\n\t\t\t\t\t\t\tif(y.indexOf('>>>')!=-1){ // 不支持跨自定义组件的后代选择器 (转为普通的选择器即可跨组件查询)\r\n\t\t\t\t\t\t\t\tselector = y.split('>>>')[1].trim()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.createSelectorQuery().select(selector).boundingClientRect(function(rect){\r\n\t\t\t\t\t\t\tif (rect) {\r\n\t\t\t\t\t\t\t\tlet top = rect.top\r\n\t\t\t\t\t\t\t\ttop += vm.mescroll.getScrollTop()\r\n\t\t\t\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\t\t\t\tscrollTop: top,\r\n\t\t\t\t\t\t\t\t\tduration: t\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else{\r\n\t\t\t\t\t\t\t\tconsole.error(selector + ' does not exist');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).exec()\r\n\t\t\t\t\t},30)\r\n\t\t\t\t} else{\r\n\t\t\t\t\t// 滚动到指定位置 (y必须为数字)\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tscrollTop: y,\r\n\t\t\t\t\t\tduration: t\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// 具体的界面如果不配置up.toTop.safearea,则取本vue的safearea值\r\n\t\t\tif (vm.up && vm.up.toTop && vm.up.toTop.safearea != null) {} else {\r\n\t\t\t\tvm.mescroll.optUp.toTop.safearea = vm.safearea;\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t@import \"./mescroll-body.css\";\r\n\t@import \"./components/mescroll-down.css\";\r\n\t@import './components/mescroll-up.css';\r\n</style>\r\n", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114215758\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5CProject%5Cfox%5C%E7%94%A8%E6%88%B7%E7%AB%AF%5Cfox-dance-user-terminal%5Ccomponents%5Cmescroll-uni%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5CProject%5Cfox%5C%E7%94%A8%E6%88%B7%E7%AB%AF%5Cfox-dance-user-terminal%5Ccomponents%5Cmescroll-uni%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('wxsCall')\n     }"], "sourceRoot": ""}