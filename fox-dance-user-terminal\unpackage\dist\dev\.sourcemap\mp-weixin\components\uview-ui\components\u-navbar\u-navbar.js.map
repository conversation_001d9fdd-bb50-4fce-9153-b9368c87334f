{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-navbar/u-navbar.vue?9de7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-navbar/u-navbar.vue?1622", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-navbar/u-navbar.vue?7573", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-navbar/u-navbar.vue?ee5b", "uni-app:///components/uview-ui/components/u-navbar/u-navbar.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-navbar/u-navbar.vue?2c6f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/components/uview-ui/components/u-navbar/u-navbar.vue?6a43"], "names": ["menuButtonInfo", "name", "props", "height", "type", "default", "backIconColor", "backBg", "backIconName", "backIconSize", "backText", "backTextStyle", "color", "title", "titleWidth", "titleColor", "titleBold", "titleSize", "isBack", "background", "isFixed", "immersive", "borderBottom", "zIndex", "customBack", "data", "statusBarHeight", "isHome", "computed", "navbarInnerStyle", "style", "navbarStyle", "Object", "titleStyle", "rightButton<PERSON><PERSON><PERSON>", "navbarHeight", "created", "methods", "goBack", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAsvB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsC1wB;AACA;AACA;AACA;;AAEAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,gBAuBA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;UACAO;QACA;MACA;IACA;IACA;IACAC;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;QACA;UACAc;QACA;MACA;IACA;IACA;IACAC;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;EACA;EACAoB;IACA;MACAzB;MACA0B;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACAC;MACA;;MAEA;MACAA;MAEA;IACA;IACA;IACAC;MACA;MACAD;MACA;MACAE;MACA;IACA;IACA;IACAC;MACA;;MAMA;MACA;MACAH;MACAA,8FACAI,mBACA;MAEAJ;MACA;IACA;IACA;IACAK;MAKA;MACA;MACA;MACA;MACA;IAEA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACA;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAA66C,CAAgB,kxCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-navbar/u-navbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-navbar.vue?vue&type=template&id=41a564b3&scoped=true&\"\nvar renderjs\nimport script from \"./u-navbar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-navbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-navbar.vue?vue&type=style&index=0&id=41a564b3&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"41a564b3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-navbar/u-navbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=template&id=41a564b3&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.navbarStyle])\n  var s1 = _vm.__get_style([_vm.navbarInnerStyle])\n  var s2 =\n    _vm.isBack && _vm.backText ? _vm.__get_style([_vm.backTextStyle]) : null\n  var s3 = _vm.title ? _vm.__get_style([_vm.titleStyle]) : null\n  var m0 = _vm.isFixed && !_vm.immersive ? Number(_vm.navbarHeight) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"u-navbar\" :style=\"[navbarStyle]\"\r\n\t\t\t:class=\"{ 'u-navbar-fixed': isFixed, 'u-border-bottom': borderBottom }\">\r\n\t\t\t<view class=\"u-status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n\t\t\t<view class=\"u-navbar-inner\" :style=\"[navbarInnerStyle]\">\r\n\t\t\t\t<view class=\"u-back-wrap\" v-if=\"isBack\" @tap=\"goBack\">\r\n\t\t\t\t\t<view class=\"u-icon-wrap\" :style=\"{backgroundColor: backBg, borderRadius: '50%', padding: '8rpx' }\">\r\n\t\t\t\t\t\t<u-icon :name=\"isHome ? 'home' : backIconName\" :color=\"backIconColor\" :size=\"backIconSize\">\r\n\t\t\t\t\t\t</u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"u-icon-wrap u-back-text u-line-1\" v-if=\"backText\" :style=\"[backTextStyle]\">\r\n\t\t\t\t\t\t{{ backText }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-navbar-content-title\" v-if=\"title\" :style=\"[titleStyle]\">\r\n\t\t\t\t\t<view class=\"u-title u-line-1\" :style=\"{\r\n\t\t\t\t\t\t\tcolor: titleColor,\r\n\t\t\t\t\t\t\tfontSize: titleSize + 'rpx',\r\n\t\t\t\t\t\t\tfontWeight: titleBold ? 'bold' : 'normal'\r\n\t\t\t\t\t\t}\">\r\n\t\t\t\t\t\t{{ title }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-slot-content\">\r\n\t\t\t\t\t<slot></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-slot-right\">\r\n\t\t\t\t\t<slot name=\"right\"></slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 解决fixed定位后导航栏塌陷的问题 -->\r\n\t\t<view class=\"u-navbar-placeholder\" v-if=\"isFixed && !immersive\"\r\n\t\t\t:style=\"{ width: '100%', height: Number(navbarHeight) + statusBarHeight + 'px' }\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 获取系统状态栏的高度\r\n\tlet systemInfo = uni.getSystemInfoSync();\r\n\tlet menuButtonInfo = {};\r\n\t// 如果是小程序，获取右上角胶囊的尺寸信息，避免导航栏右侧内容与胶囊重叠(支付宝小程序非本API，尚未兼容)\r\n\t// #ifdef MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ\r\n\tmenuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n\t// #endif\r\n\t/**\r\n\t * navbar 自定义导航栏\r\n\t * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uniapp自带的导航栏。\r\n\t * @tutorial https://www.uviewui.com/components/navbar.html\r\n\t * @property {String Number} height 导航栏高度(不包括状态栏高度在内，内部自动加上)，注意这里的单位是px（默认44）\r\n\t * @property {String} back-icon-color 左边返回图标的颜色（默认#606266）\r\n\t * @property {String} back-icon-name 左边返回图标的名称，只能为uView自带的图标（默认arrow-left）\r\n\t * @property {String Number} back-icon-size 左边返回图标的大小，单位rpx（默认30）\r\n\t * @property {String} back-text 返回图标右边的辅助提示文字\r\n\t * @property {Object} back-text-style 返回图标右边的辅助提示文字的样式，对象形式（默认{ color: '#606266' }）\r\n\t * @property {String} title 导航栏标题，如设置为空字符，将会隐藏标题占位区域\r\n\t * @property {String Number} title-width 导航栏标题的最大宽度，内容超出会以省略号隐藏，单位rpx（默认250）\r\n\t * @property {String} title-color 标题的颜色（默认#606266）\r\n\t * @property {String Number} title-size 导航栏标题字体大小，单位rpx（默认32）\r\n\t * @property {Function} custom-back 自定义返回逻辑方法\r\n\t * @property {String Number} z-index 固定在顶部时的z-index值（默认980）\r\n\t * @property {Boolean} is-back 是否显示导航栏左边返回图标和辅助文字（默认true）\r\n\t * @property {Object} background 导航栏背景设置，见官网说明（默认{ background: '#ffffff' }）\r\n\t * @property {Boolean} is-fixed 导航栏是否固定在顶部（默认true）\r\n\t * @property {Boolean} immersive 沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效（默认false）\r\n\t * @property {Boolean} border-bottom 导航栏底部是否显示下边框，如定义了较深的背景颜色，可取消此值（默认true）\r\n\t * @example <u-navbar back-text=\"返回\" title=\"剑未配妥，出门已是江湖\"></u-navbar>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-navbar\",\r\n\t\tprops: {\r\n\t\t\t// 导航栏高度，单位px，非rpx\r\n\t\t\theight: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 返回箭头的颜色\r\n\t\t\tbackIconColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t\tbackBg: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'transparent'\r\n\t\t\t},\r\n\t\t\t// 左边返回的图标\r\n\t\t\tbackIconName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'nav-back'\r\n\t\t\t},\r\n\t\t\t// 左边返回图标的大小，rpx\r\n\t\t\tbackIconSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: '42'\r\n\t\t\t},\r\n\t\t\t// 返回的文字提示\r\n\t\t\tbackText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 返回的文字的 样式\r\n\t\t\tbackTextStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tcolor: '#606266'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 导航栏标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 标题的宽度，如果需要自定义右侧内容，且右侧内容很多时，可能需要减少这个宽度，单位rpx\r\n\t\t\ttitleWidth: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: '250'\r\n\t\t\t},\r\n\t\t\t// 标题的颜色\r\n\t\t\ttitleColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t\t// 标题字体是否加粗\r\n\t\t\ttitleBold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 标题的字体大小\r\n\t\t\ttitleSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 32\r\n\t\t\t},\r\n\t\t\tisBack: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 对象形式，因为用户可能定义一个纯色，或者线性渐变的颜色\r\n\t\t\tbackground: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tbackground: '#ffffff'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 导航栏是否固定在顶部\r\n\t\t\tisFixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效\r\n\t\t\timmersive: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否显示导航栏的下边框\r\n\t\t\tborderBottom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 自定义返回逻辑\r\n\t\t\tcustomBack: {\r\n\t\t\t\ttype: Function,\r\n\t\t\t\tdefault: null\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuButtonInfo: menuButtonInfo,\r\n\t\t\t\tstatusBarHeight: systemInfo.statusBarHeight,\r\n\t\t\t\tisHome: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 导航栏内部盒子的样式\r\n\t\t\tnavbarInnerStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\t// 导航栏宽度，如果在小程序下，导航栏宽度为胶囊的左边到屏幕左边的距离\r\n\t\t\t\tstyle.height = this.navbarHeight + 'px';\r\n\t\t\t\t// // 如果是各家小程序，导航栏内部的宽度需要减少右边胶囊的宽度\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tlet rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left;\r\n\t\t\t\tstyle.paddingRight = rightButtonWidth + 'px';\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 整个导航栏的样式\r\n\t\t\tnavbarStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tstyle.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.navbar;\r\n\t\t\t\t// 合并用户传递的背景色对象\r\n\t\t\t\tObject.assign(style, this.background);\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 导航中间的标题的样式\r\n\t\t\ttitleStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\t// #ifndef MP\r\n\t\t\t\tstyle.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';\r\n\t\t\t\tstyle.right = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\t// 此处是为了让标题显示区域即使在小程序有右侧胶囊的情况下也能处于屏幕的中间，是通过绝对定位实现的\r\n\t\t\t\tlet rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left;\r\n\t\t\t\tstyle.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px';\r\n\t\t\t\tstyle.right = rightButtonWidth - (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 +\r\n\t\t\t\t\trightButtonWidth +\r\n\t\t\t\t\t'px';\r\n\t\t\t\t// #endif\r\n\t\t\t\tstyle.width = uni.upx2px(this.titleWidth) + 'px';\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 转换字符数值为真正的数值\r\n\t\t\tnavbarHeight() {\r\n\t\t\t\t// #ifdef  H5\r\n\t\t\t\treturn this.height ? this.height : 44;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS || MP\r\n\t\t\t\t// 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)\r\n\t\t\t\t// 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式\r\n\t\t\t\t// return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度\r\n\t\t\t\tlet height = systemInfo.platform == 'ios' || systemInfo.platform == 'devtools' ? 44 : 48;\r\n\t\t\t\treturn this.height ? this.height : height;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tconst page = getCurrentPages().length\r\n\t\t\tif (page == 1) {\r\n\t\t\t\tthis.isHome = true\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack() {\r\n\t\t\t\t// 如果自定义了点击返回按钮的函数，则执行，否则执行返回逻辑\r\n\t\t\t\tif (typeof this.customBack === 'function') {\r\n\t\t\t\t\t// 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this\r\n\t\t\t\t\t// 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文\r\n\t\t\t\t\tthis.customBack.bind(this.$u.$parent.call(this))();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isHome ? uni.switchTab({\r\n\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t}) : uni.navigateBack();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\t.u-navbar {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.u-navbar-fixed {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\ttop: 0;\r\n\t\tz-index: 991;\r\n\t}\r\n\r\n\t.u-status-bar {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.u-navbar-inner {\r\n\t\twidth: 100%;\r\n\t\t@include vue-flex;\r\n\t\tjustify-content: space-between;\r\n\t\tposition: relative;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.u-back-wrap {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t\tflex-grow: 0;\r\n\t\tpadding: 14rpx 14rpx 14rpx 24rpx;\r\n\t}\r\n\r\n\t.u-back-text {\r\n\t\tpadding-left: 4rpx;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.u-navbar-content-title {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex: 1;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.u-navbar-centent-slot {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.u-title {\r\n\t\tline-height: 60rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.u-navbar-right {\r\n\t\tflex: 1;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\r\n\t.u-slot-content {\r\n\t\tflex: 1;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=style&index=0&id=41a564b3&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-navbar.vue?vue&type=style&index=0&id=41a564b3&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114333065\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}