{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue?9b10", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue?1267", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue?7e53", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue?82c1", "uni-app:///uni_modules/uni-forms/components/uni-forms/uni-forms.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue?f2de", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue?496c"], "names": ["<PERSON><PERSON>", "formVm", "name", "emits", "options", "virtualHost", "props", "value", "type", "default", "modelValue", "model", "rules", "errShowType", "validate<PERSON><PERSON>ger", "labelPosition", "labelWidth", "labelAlign", "border", "provide", "uniForm", "data", "formData", "formRules", "computed", "localData", "watch", "handler", "deep", "immediate", "created", "methods", "setRules", "setValue", "validate", "validateField", "invalidFields", "clearValidate", "item", "submit", "i", "console", "checkAll", "childrens", "callback", "promise", "results", "tempFormData", "child", "result", "keepitem", "detail", "errors", "resetFormData", "validate<PERSON><PERSON><PERSON>", "_getValue", "_isRequiredField", "_setDataValue", "_getDataValue", "_realName", "_isRealName", "_isEqual"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACmM;AACnM,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAswB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACS1xB;AACA;AAcA;;;;;;;;;;AADA;;AAEAA;EACA;IACA;EACA;IACA;IACA;MACA;MACA;QACAC;QACA;MACA;IACA;IACA;IACAA;EACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,gBAwBA;EACAC;EACAC;EACAC;IAKAC;EAEA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACA;MACAC;IACA;EACA;EACAC;IACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACAd;MACAe;QACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IAyBA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QAAA;MAAA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MAAA;MAAA;MACA7B;MACA;MACA;QACA;QACA;UACA8B,mFACAlC,4BACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAmC;MAAA;MACA/B;MACA;QACA;UACAgC;QACA;UACA;UACA;YACAA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MAAA,2BACAC;QACA;UAAA;QAAA;QACA;UACA;YACA;UACA;QACA;MAAA;MANA;QAAA;MAOA;MAEA;QACAC;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAC,gBACA;gBAAA,yBACAH;kBACA;oBAAA;kBAAA;kBACA;oBACAG;kBACA;gBAAA;gBAJA;kBAAA;gBAKA;;gBAEA;gBACA;kBACAC;gBACA;gBAGA;gBACA;kBACAC;oBACAD;sBACA;oBACA;kBACA;gBACA;gBAEAE,cACA;gBACAC,0DACA;gBAAA,wCACAJ;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAH;gBACAQ;gBACA9C;gBAAA;gBAAA,OACA8C;cAAA;gBAAAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACAH;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAKA;kBACA;gBACA;gBACA;kBACAI;oBACA;oBACA;oBACA;sBACAH;oBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA;oBACAI;sBACA5C;sBACA6C;oBACA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACAC;gBACAA;gBACAT;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBAAA,iCACAA;cAAA;gBAAA,iCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;AACA;AACA;AACA;IACAS;MACA;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC9YA;AAAA;AAAA;AAAA;AAAi7C,CAAgB,2vCAAG,EAAC,C;;;;;;;;;;;ACAr8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-forms.vue?vue&type=template&id=5a49926c&\"\nvar renderjs\nimport script from \"./uni-forms.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-forms.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-forms.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms.vue?vue&type=template&id=5a49926c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-forms\">\r\n\t\t<form>\r\n\t\t\t<slot></slot>\r\n\t\t</form>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport Validator from './validate.js';\r\n\timport {\r\n\t\tdeepCopy,\r\n\t\tgetValue,\r\n\t\tisRequiredField,\r\n\t\tsetDataValue,\r\n\t\tgetDataValue,\r\n\t\trealName,\r\n\t\tisRealName,\r\n\t\trawData,\r\n\t\tisEqual\r\n\t} from './utils.js'\r\n\r\n\t// #ifndef VUE3\r\n\t// 后续会慢慢废弃这个方法\r\n\timport Vue from 'vue';\r\n\tVue.prototype.binddata = function(name, value, formName) {\r\n\t\tif (formName) {\r\n\t\t\tthis.$refs[formName].setValue(name, value);\r\n\t\t} else {\r\n\t\t\tlet formVm;\r\n\t\t\tfor (let i in this.$refs) {\r\n\t\t\t\tconst vm = this.$refs[i];\r\n\t\t\t\tif (vm && vm.$options && vm.$options.name === 'uniForms') {\r\n\t\t\t\t\tformVm = vm;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (!formVm) return console.error('当前 uni-froms 组件缺少 ref 属性');\r\n\t\t\tformVm.setValue(name, value);\r\n\t\t}\r\n\t};\r\n\t// #endif\r\n\t/**\r\n\t * Forms 表单\r\n\t * @description 由输入框、选择器、单选框、多选框等控件组成，用以收集、校验、提交数据\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=2773\r\n\t * @property {Object} rules\t表单校验规则\r\n\t * @property {String} validateTrigger = [bind|submit|blur]\t校验触发器方式 默认 submit\r\n\t * @value bind\t\t发生变化时触发\r\n\t * @value submit\t提交时触发\r\n\t * @value blur\t  失去焦点时触发\r\n\t * @property {String} labelPosition = [top|left]\tlabel 位置 默认 left\r\n\t * @value top\t\t顶部显示 label\r\n\t * @value left\t左侧显示 label\r\n\t * @property {String} labelWidth\tlabel 宽度，默认 70px\r\n\t * @property {String} labelAlign = [left|center|right]\tlabel 居中方式  默认 left\r\n\t * @value left\t\tlabel 左侧显示\r\n\t * @value center\tlabel 居中\r\n\t * @value right\t\tlabel 右侧对齐\r\n\t * @property {String} errShowType = [undertext|toast|modal]\t校验错误信息提示方式\r\n\t * @value undertext\t错误信息在底部显示\r\n\t * @value toast\t\t\t错误信息toast显示\r\n\t * @value modal\t\t\t错误信息modal显示\r\n\t * @event {Function} submit\t提交时触发\r\n\t * @event {Function} validate\t校验结果发生变化触发\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniForms',\r\n\t\temits: ['validate', 'submit'],\r\n\t\toptions: {\n\t\t\t// #ifdef MP-TOUTIAO\n\t\t\tvirtualHost: false,\n\t\t\t// #endif\n\t\t\t// #ifndef MP-TOUTIAO\n\t\t\tvirtualHost: true\n\t\t\t// #endif\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 即将弃用\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// vue3 替换 value 属性\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 1.4.0 开始将不支持 v-model ，且废弃 value 和 modelValue\r\n\t\t\tmodel: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 表单校验规则\r\n\t\t\trules: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//校验错误信息提示方式 默认 undertext 取值 [undertext|toast|modal]\r\n\t\t\terrShowType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'undertext'\r\n\t\t\t},\r\n\t\t\t// 校验触发器方式 默认 bind 取值 [bind|submit]\r\n\t\t\tvalidateTrigger: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'submit'\r\n\t\t\t},\r\n\t\t\t// label 位置，默认 left 取值  top/left\r\n\t\t\tlabelPosition: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'left'\r\n\t\t\t},\r\n\t\t\t// label 宽度\r\n\t\t\tlabelWidth: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// label 居中方式，默认 left 取值 left/center/right\r\n\t\t\tlabelAlign: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'left'\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tuniForm: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 表单本地值的记录，不应该与传如的值进行关联\r\n\t\t\t\tformData: {},\r\n\t\t\t\tformRules: {}\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 计算数据源变化的\r\n\t\t\tlocalData() {\r\n\t\t\t\tconst localVal = this.model || this.modelValue || this.value\r\n\t\t\t\tif (localVal) {\r\n\t\t\t\t\treturn deepCopy(localVal)\r\n\t\t\t\t}\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听数据变化 ,暂时不使用，需要单独赋值\r\n\t\t\t// localData: {},\r\n\t\t\t// 监听规则变化\r\n\t\t\trules: {\r\n\t\t\t\thandler: function(val, oldVal) {\r\n\t\t\t\t\tthis.setRules(val)\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true,\r\n\t\t\t\timmediate: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tlet getbinddata = getApp().$vm.$.appContext.config.globalProperties.binddata\r\n\t\t\tif (!getbinddata) {\r\n\t\t\t\tgetApp().$vm.$.appContext.config.globalProperties.binddata = function(name, value, formName) {\r\n\t\t\t\t\tif (formName) {\r\n\t\t\t\t\t\tthis.$refs[formName].setValue(name, value);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet formVm;\r\n\t\t\t\t\t\tfor (let i in this.$refs) {\r\n\t\t\t\t\t\t\tconst vm = this.$refs[i];\r\n\t\t\t\t\t\t\tif (vm && vm.$options && vm.$options.name === 'uniForms') {\r\n\t\t\t\t\t\t\t\tformVm = vm;\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (!formVm) return console.error('当前 uni-froms 组件缺少 ref 属性');\r\n\t\t\t\t\t\tif(formVm.model)formVm.model[name] = value\r\n\t\t\t\t\t\tif(formVm.modelValue)formVm.modelValue[name] = value\r\n\t\t\t\t\t\tif(formVm.value)formVm.value[name] = value\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\r\n\t\t\t// 子组件实例数组\r\n\t\t\tthis.childrens = []\r\n\t\t\t// TODO 兼容旧版 uni-data-picker ,新版本中无效，只是避免报错\r\n\t\t\tthis.inputChildrens = []\r\n\t\t\tthis.setRules(this.rules)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 设置规则 ，主要用于小程序自定义检验规则\r\n\t\t\t * @param {Array} rules 规则源数据\r\n\t\t\t */\r\n\t\t\tsetRules(rules) {\r\n\t\t\t\t// TODO 有可能子组件合并规则的时机比这个要早，所以需要合并对象 ，而不是直接赋值，可能会被覆盖\r\n\t\t\t\tthis.formRules = Object.assign({}, this.formRules, rules)\r\n\t\t\t\t// 初始化校验函数\r\n\t\t\t\tthis.validator = new Validator(rules);\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 设置数据，用于设置表单数据，公开给用户使用 ， 不支持在动态表单中使用\r\n\t\t\t * @param {Object} key\r\n\t\t\t * @param {Object} value\r\n\t\t\t */\r\n\t\t\tsetValue(key, value) {\r\n\t\t\t\tlet example = this.childrens.find(child => child.name === key);\r\n\t\t\t\tif (!example) return null;\r\n\t\t\t\tthis.formData[key] = getValue(key, value, (this.formRules[key] && this.formRules[key].rules) || [])\r\n\t\t\t\treturn example.onFieldChange(this.formData[key]);\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 手动提交校验表单\r\n\t\t\t * 对整个表单进行校验的方法，参数为一个回调函数。\r\n\t\t\t * @param {Array} keepitem 保留不参与校验的字段\r\n\t\t\t * @param {type} callback 方法回调\r\n\t\t\t */\r\n\t\t\tvalidate(keepitem, callback) {\r\n\t\t\t\treturn this.checkAll(this.formData, keepitem, callback);\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 部分表单校验\r\n\t\t\t * @param {Array|String} props 需要校验的字段\r\n\t\t\t * @param {Function} 回调函数\r\n\t\t\t */\r\n\t\t\tvalidateField(props = [], callback) {\r\n\t\t\t\tprops = [].concat(props);\r\n\t\t\t\tlet invalidFields = {};\r\n\t\t\t\tthis.childrens.forEach(item => {\r\n\t\t\t\t\tconst name = realName(item.name)\r\n\t\t\t\t\tif (props.indexOf(name) !== -1) {\r\n\t\t\t\t\t\tinvalidFields = Object.assign({}, invalidFields, {\r\n\t\t\t\t\t\t\t[name]: this.formData[name]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn this.checkAll(invalidFields, [], callback);\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 移除表单项的校验结果。传入待移除的表单项的 prop 属性或者 prop 组成的数组，如不传则移除整个表单的校验结果\r\n\t\t\t * @param {Array|String} props 需要移除校验的字段 ，不填为所有\r\n\t\t\t */\r\n\t\t\tclearValidate(props = []) {\r\n\t\t\t\tprops = [].concat(props);\r\n\t\t\t\tthis.childrens.forEach(item => {\r\n\t\t\t\t\tif (props.length === 0) {\r\n\t\t\t\t\t\titem.errMsg = '';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconst name = realName(item.name)\r\n\t\t\t\t\t\tif (props.indexOf(name) !== -1) {\r\n\t\t\t\t\t\t\titem.errMsg = '';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法 ，即将废弃\r\n\t\t\t * 手动提交校验表单\r\n\t\t\t * 对整个表单进行校验的方法，参数为一个回调函数。\r\n\t\t\t * @param {Array} keepitem 保留不参与校验的字段\r\n\t\t\t * @param {type} callback 方法回调\r\n\t\t\t */\r\n\t\t\tsubmit(keepitem, callback, type) {\r\n\t\t\t\tfor (let i in this.dataValue) {\r\n\t\t\t\t\tconst itemData = this.childrens.find(v => v.name === i);\r\n\t\t\t\t\tif (itemData) {\r\n\t\t\t\t\t\tif (this.formData[i] === undefined) {\r\n\t\t\t\t\t\t\tthis.formData[i] = this._getValue(i, this.dataValue[i]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!type) {\r\n\t\t\t\t\tconsole.warn('submit 方法即将废弃，请使用validate方法代替！');\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn this.checkAll(this.formData, keepitem, callback, 'submit');\r\n\t\t\t},\r\n\r\n\t\t\t// 校验所有\r\n\t\t\tasync checkAll(invalidFields, keepitem, callback, type) {\r\n\t\t\t\t// 不存在校验规则 ，则停止校验流程\r\n\t\t\t\tif (!this.validator) return\r\n\t\t\t\tlet childrens = []\r\n\t\t\t\t// 处理参与校验的item实例\r\n\t\t\t\tfor (let i in invalidFields) {\r\n\t\t\t\t\tconst item = this.childrens.find(v => realName(v.name) === i)\r\n\t\t\t\t\tif (item) {\r\n\t\t\t\t\t\tchildrens.push(item)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 如果validate第一个参数是funciont ,那就走回调\r\n\t\t\t\tif (!callback && typeof keepitem === 'function') {\r\n\t\t\t\t\tcallback = keepitem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet promise;\r\n\t\t\t\t// 如果不存在回调，那么使用 Promise 方式返回\r\n\t\t\t\tif (!callback && typeof callback !== 'function' && Promise) {\r\n\t\t\t\t\tpromise = new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tcallback = function(valid, invalidFields) {\r\n\t\t\t\t\t\t\t!valid ? resolve(invalidFields) : reject(valid);\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet results = [];\r\n\t\t\t\t// 避免引用错乱 ，建议拷贝对象处理\r\n\t\t\t\tlet tempFormData = JSON.parse(JSON.stringify(invalidFields))\r\n\t\t\t\t// 所有子组件参与校验,使用 for 可以使用  awiat\r\n\t\t\t\tfor (let i in childrens) {\r\n\t\t\t\t\tconst child = childrens[i]\r\n\t\t\t\t\tlet name = realName(child.name);\r\n\t\t\t\t\tconst result = await child.onFieldChange(tempFormData[name]);\r\n\t\t\t\t\tif (result) {\r\n\t\t\t\t\t\tresults.push(result);\r\n\t\t\t\t\t\t// toast ,modal 只需要执行第一次就可以\r\n\t\t\t\t\t\tif (this.errShowType === 'toast' || this.errShowType === 'modal') break;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\tif (Array.isArray(results)) {\r\n\t\t\t\t\tif (results.length === 0) results = null;\r\n\t\t\t\t}\r\n\t\t\t\tif (Array.isArray(keepitem)) {\r\n\t\t\t\t\tkeepitem.forEach(v => {\r\n\t\t\t\t\t\tlet vName = realName(v);\r\n\t\t\t\t\t\tlet value = getDataValue(v, this.localData)\r\n\t\t\t\t\t\tif (value !== undefined) {\r\n\t\t\t\t\t\t\ttempFormData[vName] = value\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// TODO submit 即将废弃\r\n\t\t\t\tif (type === 'submit') {\r\n\t\t\t\t\tthis.$emit('submit', {\r\n\t\t\t\t\t\tdetail: {\r\n\t\t\t\t\t\t\tvalue: tempFormData,\r\n\t\t\t\t\t\t\terrors: results\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('validate', results);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// const resetFormData = rawData(tempFormData, this.localData, this.name)\r\n\t\t\t\tlet resetFormData = {}\r\n\t\t\t\tresetFormData = rawData(tempFormData, this.name)\r\n\t\t\t\tcallback && typeof callback === 'function' && callback(results, resetFormData);\r\n\r\n\t\t\t\tif (promise && callback) {\r\n\t\t\t\t\treturn promise;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 返回validate事件\r\n\t\t\t * @param {Object} result\r\n\t\t\t */\r\n\t\t\tvalidateCheck(result) {\r\n\t\t\t\tthis.$emit('validate', result);\r\n\t\t\t},\r\n\t\t\t_getValue: getValue,\r\n\t\t\t_isRequiredField: isRequiredField,\r\n\t\t\t_setDataValue: setDataValue,\r\n\t\t\t_getDataValue: getDataValue,\r\n\t\t\t_realName: realName,\r\n\t\t\t_isRealName: isRealName,\r\n\t\t\t_isEqual: isEqual\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-forms {}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752030104709\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}