{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/store/store-list.vue?fe78", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/store/store-list.vue?c822", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/store/store-list.vue?d524", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/store/store-list.vue?4655", "uni-app:///pagesSub/store/store-list.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/store/store-list.vue?5f72", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/store/store-list.vue?9d74"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "storeNames", "loading", "loadingText", "contentdown", "contentrefresh", "contentnomore", "onLoad", "console", "onShow", "onPullDownRefresh", "methods", "loadStoreNames", "response", "uni", "title", "icon", "duration", "refreshStoreList", "handleStoreClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqD9uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACAC;IACA;EACA;EAEAC;IACAD;EACA;EAEA;EACAE;IACAF;IACA;EACA;EAEAG;IACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAJ;gBAAA;gBAAA,OAEA;cAAA;gBAAAK;gBACAL;gBAEA;kBACA;kBACAA;;kBAEA;kBACA;oBACAM;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;kBACAT;kBACAM;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;gBACAM;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACAH;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAI;MACAV;MACA;IACA;IAEA;AACA;AACA;IACAW;MACAX;MAEAM;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAAy3C,CAAgB,oxCAAG,EAAC,C;;;;;;;;;;;ACA74C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/store/store-list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/store/store-list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./store-list.vue?vue&type=template&id=daf52aa6&scoped=true&\"\nvar renderjs\nimport script from \"./store-list.vue?vue&type=script&lang=js&\"\nexport * from \"./store-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./store-list.vue?vue&type=style&index=0&id=daf52aa6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"daf52aa6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/store/store-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store-list.vue?vue&type=template&id=daf52aa6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading ? _vm.storeNames.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store-list.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"store-list-container\">\n    <!-- 页面标题 -->\n    <view class=\"page-header\">\n      <view class=\"title\">店铺列表</view>\n      <view class=\"subtitle\">选择您喜欢的舞蹈工作室</view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\n    </view>\n\n    <!-- 店铺网格布局 -->\n    <view v-else class=\"store-grid\">\n      <view \n        v-for=\"(storeName, index) in storeNames\" \n        :key=\"index\"\n        class=\"store-card\"\n        @click=\"handleStoreClick(storeName, index)\"\n      >\n        <view class=\"store-card-content\">\n          <view class=\"store-icon\">\n            <text class=\"icon\">🏪</text>\n          </view>\n          <view class=\"store-name\">{{ storeName }}</view>\n          <view class=\"store-status\">营业中</view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"storeNames.length === 0\" class=\"empty-state\">\n        <view class=\"empty-icon\">🏪</view>\n        <view class=\"empty-text\">暂无店铺信息</view>\n        <view class=\"empty-desc\">请稍后再试</view>\n      </view>\n    </view>\n\n    <!-- 刷新按钮 -->\n    <view class=\"refresh-container\">\n      <button \n        class=\"refresh-btn\" \n        @click=\"refreshStoreList\"\n        :disabled=\"loading\"\n      >\n        <text class=\"refresh-icon\">🔄</text>\n        <text class=\"refresh-text\">{{ loading ? '加载中...' : '刷新列表' }}</text>\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getStoreNames } from '@/api/store'\n\nexport default {\n  name: 'StoreList',\n  data() {\n    return {\n      storeNames: [], // 店铺名称列表\n      loading: false, // 加载状态\n      loadingText: {\n        contentdown: '下拉刷新',\n        contentrefresh: '正在刷新...',\n        contentnomore: '没有更多数据'\n      }\n    }\n  },\n  \n  onLoad() {\n    console.log('📱 店铺列表页面加载')\n    this.loadStoreNames()\n  },\n  \n  onShow() {\n    console.log('📱 店铺列表页面显示')\n  },\n  \n  // 下拉刷新\n  onPullDownRefresh() {\n    console.log('🔄 触发下拉刷新')\n    this.refreshStoreList()\n  },\n  \n  methods: {\n    /**\n     * 加载店铺名称列表\n     */\n    async loadStoreNames() {\n      try {\n        this.loading = true\n        console.log('🏪 开始加载店铺名称列表')\n        \n        const response = await getStoreNames()\n        console.log('📊 店铺名称API响应:', response)\n        \n        if (response.code === 0 && response.data) {\n          this.storeNames = response.data\n          console.log('✅ 成功加载店铺名称 - 数量:', this.storeNames.length)\n          \n          // 显示成功提示\n          if (this.storeNames.length > 0) {\n            uni.showToast({\n              title: `加载成功，共${this.storeNames.length}家店铺`,\n              icon: 'success',\n              duration: 2000\n            })\n          }\n        } else {\n          console.error('❌ 店铺名称API返回错误:', response.message)\n          uni.showToast({\n            title: response.message || '加载失败',\n            icon: 'error',\n            duration: 2000\n          })\n        }\n      } catch (error) {\n        console.error('❌ 加载店铺名称失败:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'error',\n          duration: 2000\n        })\n      } finally {\n        this.loading = false\n        // 停止下拉刷新\n        uni.stopPullDownRefresh()\n      }\n    },\n    \n    /**\n     * 刷新店铺列表\n     */\n    refreshStoreList() {\n      console.log('🔄 刷新店铺列表')\n      this.loadStoreNames()\n    },\n    \n    /**\n     * 处理店铺点击事件\n     */\n    handleStoreClick(storeName, index) {\n      console.log('🏪 点击店铺:', storeName, '索引:', index)\n      \n      uni.showToast({\n        title: `您选择了：${storeName}`,\n        icon: 'success',\n        duration: 2000\n      })\n      \n      // 这里可以添加跳转到店铺详情页的逻辑\n      // uni.navigateTo({\n      //   url: `/pagesSub/store/store-detail?name=${encodeURIComponent(storeName)}&index=${index}`\n      // })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.store-list-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b87 0%, #a8727e 100%);\n  padding: 40rpx 30rpx;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 60rpx;\n  \n  .title {\n    font-size: 48rpx;\n    font-weight: bold;\n    color: #ffffff;\n    margin-bottom: 20rpx;\n    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\n  }\n  \n  .subtitle {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.9);\n    opacity: 0.8;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n}\n\n.store-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30rpx;\n  margin-bottom: 60rpx;\n}\n\n.store-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 32rpx;\n  padding: 40rpx 20rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10rpx);\n  \n  &:active {\n    transform: scale(0.95);\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);\n  }\n}\n\n.store-card-content {\n  text-align: center;\n  \n  .store-icon {\n    margin-bottom: 20rpx;\n    \n    .icon {\n      font-size: 60rpx;\n      display: block;\n    }\n  }\n  \n  .store-name {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333333;\n    margin-bottom: 12rpx;\n    line-height: 1.4;\n    word-break: break-all;\n  }\n  \n  .store-status {\n    font-size: 24rpx;\n    color: #52c41a;\n    background: rgba(82, 196, 26, 0.1);\n    padding: 8rpx 16rpx;\n    border-radius: 20rpx;\n    display: inline-block;\n  }\n}\n\n.empty-state {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 100rpx 40rpx;\n  \n  .empty-icon {\n    font-size: 120rpx;\n    margin-bottom: 30rpx;\n    opacity: 0.6;\n  }\n  \n  .empty-text {\n    font-size: 36rpx;\n    color: rgba(255, 255, 255, 0.9);\n    margin-bottom: 16rpx;\n    font-weight: bold;\n  }\n  \n  .empty-desc {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.7);\n  }\n}\n\n.refresh-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 40rpx;\n}\n\n.refresh-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  border-radius: 50rpx;\n  padding: 24rpx 48rpx;\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  transition: all 0.3s ease;\n  \n  &:active {\n    background: rgba(255, 255, 255, 0.3);\n    transform: scale(0.95);\n  }\n  \n  &[disabled] {\n    opacity: 0.6;\n  }\n  \n  .refresh-icon {\n    font-size: 32rpx;\n    color: #ffffff;\n  }\n  \n  .refresh-text {\n    font-size: 28rpx;\n    color: #ffffff;\n    font-weight: 500;\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 750rpx) {\n  .store-grid {\n    gap: 20rpx;\n  }\n  \n  .store-card {\n    padding: 30rpx 15rpx;\n  }\n  \n  .store-card-content .store-name {\n    font-size: 28rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store-list.vue?vue&type=style&index=0&id=daf52aa6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store-list.vue?vue&type=style&index=0&id=daf52aa6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751943767610\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}