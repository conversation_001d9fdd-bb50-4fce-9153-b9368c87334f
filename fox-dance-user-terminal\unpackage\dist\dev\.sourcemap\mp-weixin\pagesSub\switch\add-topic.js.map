{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/add-topic.vue?d385", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/add-topic.vue?b3bb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/add-topic.vue?4c8e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/add-topic.vue?e63c", "uni-app:///pagesSub/switch/add-topic.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue?dd46", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue?15c0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue?299f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue?dc63", "uni-app:///uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue?eef0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue?1f59", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/add-topic.vue?3662", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/add-topic.vue?8f72"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniForms", "uniFormsItem", "data", "userId", "submitting", "uploading", "uploadedImages", "formData", "title", "description", "rules", "required", "errorMessage", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "onLoad", "methods", "submitForm", "uni", "topicImages", "topicApi", "console", "icon", "setTimeout", "prevPage", "chooseImages", "count", "sizeType", "sourceType", "success", "fail", "uploadImages", "successCount", "failCount", "i", "filePath", "driver", "result", "imageUrl", "deleteImage", "content", "previewImage", "current", "urls", "showImageOptions", "itemList", "setAsCover", "processImageUrl", "handleImageLoadError", "validateImageUrl", "reject", "resolve", "name", "options", "virtualHost", "provide", "uniFormItem", "inject", "form", "from", "default", "props", "type", "label", "labelWidth", "labelAlign", "leftIcon", "iconColor", "errMsg", "userRules", "localLabelAlign", "localLabelWidth", "localLabelPos", "border", "isFirstBorder", "computed", "msg", "watch", "created", "immediate", "destroyed", "setRules", "setValue", "onFieldChange", "formtrigger", "localData", "errShowType", "validate<PERSON><PERSON><PERSON>", "validate<PERSON><PERSON>ger", "_isRequiredField", "_realName", "value", "ruleLen", "isRequiredField", "init", "validator", "formRules", "childrens", "_getDataValue", "_setDataValue", "unInit", "itemSetValue", "clearValidate", "_isRequired", "_justifyContent", "_labelWidthUnit", "_labelPosition", "isTrigger", "num2px"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,8RAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAytB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsE7uB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;UACAE,QACA;YACAC;YACAC;UACA,GACA;YACAC;YACAC;YACAF;UACA;QAEA;QACAH;UACAC,QACA;YACAC;YACAC;UACA,GACA;YACAC;YACAC;YACAF;UACA;QAEA;MACA;IACA;EACA;EACAG;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MAEA;QACA;QAEAC;UACAV;QACA;;QAEA;QACA;UACAL;UACAK;UACAC;UACAU;QACA;;QAEA;QACAC;UACAC;UACA;YACAH;YACAA;cACAV;cACAc;YACA;;YAEA;YACAC;cACA;cACA;cACA;;cAEA;cACA;gBACA;gBACAC;cACA;cAEAN;YACA;UACA;YACA;YACA;YACA;cACAN;YACA;cACA;gBACAA;cACA;gBACAA;cACA;YACA;cACAA;YACA;YAEAM;YACAA;cACAV;cACAc;YACA;UACA;QACA;UACAD;UACAH;UACAA;YACAV;YACAc;UACA;QACA;UACA;QACA;MACA;QACAD;MACA;IACA;IAEA;IACAI;MAAA;MACAJ;MAEA;QACAH;UACAV;UACAc;QACA;QACA;MACA;MAEA;MACAD;MAEA;QACAH;UACAV;UACAc;QACA;QACA;MACA;MAEAJ;QACAQ;QACAC;QAAA;QACAC;QACAC;UACAR;UACAA;UACA;QACA;QACAS;UACAT;UACAH;YACAV;YACAc;UACA;QACA;MACA;IACA;IAEA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAb;kBACAV;gBACA;gBAEAa;gBAAA;gBAGA;gBACAW;gBACAC;gBAEAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAd;gBAAA;gBAAA;gBAAA,OAIAD;kBAAAgB;gBAAA;cAAA;gBAAAC;gBACAhB;gBAAA,MAEAgB;kBAAA;kBAAA;gBAAA;gBACAC,iCAEA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAN;gBACAX;gBAAA;gBAAA;cAAA;gBAEAY;gBACAZ;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAY;gBACAZ;cAAA;gBAxBAa;gBAAA;gBAAA;cAAA;gBA4BAhB;gBAEA;kBACAA;oBACAV;oBACAc;kBACA;gBACA;kBACAJ;oBACAV;oBACAc;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAD;gBACAH;gBACAA;kBACAV;kBACAc;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MAAA;MACArB;QACAV;QACAgC;QACAX;UACA;YACA;YACAR;UACA;QACA;MACA;IACA;IAEA;IACAoB;MAAA;MACApB;MAEA;QACAH;UACAV;UACAc;QACA;QACA;MACA;MAEA;QACAJ;UACAV;UACAc;QACA;QACA;MACA;;MAEA;MACA;MACAD;MAEA;QACAH;UACAV;UACAc;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACAD;MACAA;MAEAH;QACAwB;QAAA;QACAC;QAAA;QACAd;UACAR;QACA;QACAS;UACAT;UACAH;YACAV;YACAc;UACA;QACA;MACA;IACA;IAEA;IACAsB;MAAA;MACA;MACA;QACAC;MACA;MAEA3B;QACA2B;QACAhB;UACA;YACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiB;MACA;MAEA;MACA;MAEA5B;QACAV;QACAc;MACA;IACA;IAEA;IACAyB;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA3B;MACAH;QACAV;QACAc;MACA;IACA;IAEA;IACA2B;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACA;kBACA;kBACA;oBACAC;oBACA;kBACA;;kBAEA;kBACA;kBACA;oBACAA;oBACA;kBACA;kBAEA7B;kBACA8B;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AACmM;AACnM,gBAAgB,8LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2wB,CAAgB,6sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgC/xB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,gBAwBA;EACAC;EACAC;IAKAC;EAEA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAnD;MACAoD;MACAF;QACA;MACA;IACA;IACA;IACAR;MACAU;MACAF;IACA;IACAjD;MACAmD;MACAF;IACA;IACAG;MACAD;MACAF;IACA;IACA;IACAI;MACAF;MACAF;IACA;IACA;IACAK;MACAH;MACAF;IACA;IACA;IACAhD;MACAkD;MACAF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAM;IACAC;MACAL;MACAF;IACA;EACA;EACA1D;IACA;MACAkE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;;MAEA;IAEA;IACA;MACA;MACA;IAEA;IACA;MACA;MACA;IACA;IACA,iDAEA;EACA;EACAC;IAAA;IACA;IACA;MACA;;MAOA;MACA,YACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;MACA;QACAC;MACA,EACA;IACA;EAEA;EAEAC;IACA;IACA;EACA;EAQAhE;IACA;AACA;AACA;AACA;AACA;IACAiE;MAAA;MACA;MACA;IACA;IACA;IACAC;MACA;IAAA,CACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,cASA,aAPA7E,iCACA8E,mCACAC,uCACAC,2CACAC,+CACAC,iDACAC;gBAEAtC;gBACA;kBACAuC;gBACA;gBACA;gBACA;;gBAEA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACAC;gBACAxD,eACA;gBAAA,MACAmD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA,kEACApC,cAEA7C,SACA;cAAA;gBAJA8B;gBAMA;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACA;oBACA;oBACA;kBACA;kBACA;oBACAnB;sBACAV;sBACAc;oBACA;kBACA;kBACA;oBACAJ;sBACAV;sBACAgC;oBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA+C;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA,iCAEAlD;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;IACAyD;MAAA;MACA,WAUA;QATAC;QACAC;QACAC;QACA1F;QACA8E;QACAK;QACA1B;QACAkC;QACAC;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACAH;UACAtF;QACA;QACAqF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAK;MAAA;MACA;QACA,iBAIA;UAHAH;UACA1F;UACAmF;QAEAO;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA,IACAvC,aACA,UADAA;QAEA;QACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAwC;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;YACA;cACA;YACA;YACA;UACA;UACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC5cA;AAAA;AAAA;AAAA;AAAs7C,CAAgB,gwCAAG,EAAC,C;;;;;;;;;;;ACA18C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAw3C,CAAgB,mxCAAG,EAAC,C;;;;;;;;;;;ACA54C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/switch/add-topic.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/switch/add-topic.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add-topic.vue?vue&type=template&id=5b362a9d&scoped=true&\"\nvar renderjs\nimport script from \"./add-topic.vue?vue&type=script&lang=js&\"\nexport * from \"./add-topic.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add-topic.vue?vue&type=style&index=0&id=5b362a9d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b362a9d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/add-topic.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-topic.vue?vue&type=template&id=5b362a9d&scoped=true&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.uploadedImages, function (image, index) {\n    var $orig = _vm.__get_orig(image)\n    var m0 = _vm.processImageUrl(image)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = _vm.uploadedImages.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-topic.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-topic.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<uni-forms ref=\"form\" :model=\"formData\" :rules=\"rules\" validate-trigger=\"submit\" err-show-type=\"toast\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">话题标题</text>\n\t\t\t\t<uni-forms-item name=\"title\">\n\t\t\t\t\t<input class=\"input\" type=\"text\" v-model=\"formData.title\" placeholder=\"请输入话题标题\" />\n\t\t\t\t</uni-forms-item>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">话题描述</text>\n\t\t\t\t<uni-forms-item name=\"description\">\n\t\t\t\t\t<textarea class=\"textarea\" v-model=\"formData.description\" placeholder=\"请输入话题描述\" />\n\t\t\t\t</uni-forms-item>\n\t\t\t</view>\n\n\t\t\t<!-- 图片上传区域 -->\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">话题图片 <text class=\"label-tip\">（最多9张，第一张将作为封面）</text></text>\n\t\t\t\t<view class=\"image-upload-container\">\n\t\t\t\t\t<!-- 已上传的图片列表 -->\n\t\t\t\t\t<view class=\"image-list\">\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"image-item\"\n\t\t\t\t\t\t\tv-for=\"(image, index) in uploadedImages\"\n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t@longpress=\"showImageOptions(index)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\tclass=\"uploaded-image\"\n\t\t\t\t\t\t\t\t:src=\"processImageUrl(image)\"\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t@tap=\"previewImage(index)\"\n\t\t\t\t\t\t\t\t@error=\"handleImageLoadError(index)\"\n\t\t\t\t\t\t\t\t:lazy-load=\"true\"\n\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t<view class=\"image-delete\" @tap=\"deleteImage(index)\">\n\t\t\t\t\t\t\t\t<text class=\"delete-icon\">×</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view v-if=\"index === 0\" class=\"cover-badge\">封面</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 添加图片按钮 -->\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"add-image-btn\"\n\t\t\t\t\t\t\tv-if=\"uploadedImages.length < 9\"\n\t\t\t\t\t\t\t@tap=\"chooseImages\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"add-icon\">+</text>\n\t\t\t\t\t\t\t<text class=\"add-text\">添加图片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 上传进度提示 -->\n\t\t\t\t\t<view v-if=\"uploading\" class=\"upload-progress\">\n\t\t\t\t\t\t<u-loading mode=\"flower\" size=\"30\" color=\"#667eea\"></u-loading>\n\t\t\t\t\t\t<text class=\"progress-text\">正在上传图片...</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"submit-btn\">\n\t\t\t\t<button class=\"btn\" type=\"primary\" @click=\"submitForm\" :disabled=\"submitting\">{{ submitting ? '提交中...' : '发布话题' }}</button>\n\t\t\t</view>\n\t\t</uni-forms>\n\t</view>\n</template>\n\n<script>\n\timport topicApi from '@/config/topic.api.js';\n\timport { upImg } from '@/config/http.achieve.js';\n\t// 引入uni-forms组件\n\timport { uniForms, uniFormsItem } from '@dcloudio/uni-ui';\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tuniForms,\n\t\t\tuniFormsItem\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserId: '',\n\t\t\t\tsubmitting: false,\n\t\t\t\tuploading: false, // 图片上传状态\n\t\t\t\tuploadedImages: [], // 已上传的图片URL数组\n\t\t\t\tformData: {\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tdescription: ''\n\t\t\t\t},\n\t\t\t\trules: {\n\t\t\t\t\ttitle: {\n\t\t\t\t\t\trules: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\terrorMessage: '请输入话题标题'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tminLength: 3,\n\t\t\t\t\t\t\t\tmaxLength: 100,\n\t\t\t\t\t\t\t\terrorMessage: '标题长度在3-100个字符之间'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\tdescription: {\n\t\t\t\t\t\trules: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\trequired: true,\n\t\t\t\t\t\t\t\terrorMessage: '请输入话题描述'\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tminLength: 5,\n\t\t\t\t\t\t\t\tmaxLength: 200,\n\t\t\t\t\t\t\t\terrorMessage: '描述长度在5-200个字符之间'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 获取用户ID\n\t\t\tthis.userId = uni.getStorageSync('userid') || '18';\n\t\t},\n\t\tmethods: {\n\t\t\tsubmitForm() {\n\t\t\t\tif (this.submitting) return;\n\t\t\t\t\n\t\t\t\tthis.$refs.form.validate().then(res => {\n\t\t\t\t\tthis.submitting = true;\n\t\t\t\t\t\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '发布中...'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 构建请求数据\n\t\t\t\t\tconst requestData = {\n\t\t\t\t\t\tuserId: this.userId,\n\t\t\t\t\t\ttitle: this.formData.title,\n\t\t\t\t\t\tdescription: this.formData.description,\n\t\t\t\t\t\ttopicImages: this.uploadedImages // 添加图片数组\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 调用API创建话题\n\t\t\t\t\ttopicApi.addTopic(requestData).then(res => {\n\t\t\t\t\t\tconsole.log('创建话题API返回数据:', JSON.stringify(res));\n\t\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '发布成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 返回上一页并刷新\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t// 返回话题列表页面并刷新\n\t\t\t\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\t\t\t\tconst prevPage = pages[pages.length - 2]; // 上一页\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 通知上一页刷新\n\t\t\t\t\t\t\t\tif (prevPage && prevPage.$vm) {\n\t\t\t\t\t\t\t\t\t// 调用上一页的刷新方法\n\t\t\t\t\t\t\t\t\tprevPage.$vm.fetchTopicList(true);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 处理不同的错误情况\n\t\t\t\t\t\t\tlet errorMessage = '发布失败';\n\t\t\t\t\t\t\tif (res.code === 40300) {\n\t\t\t\t\t\t\t\terrorMessage = '只有特定用户可以创建话题';\n\t\t\t\t\t\t\t} else if (res.code === 40001) {\n\t\t\t\t\t\t\t\tif (res.message.includes('标题过长')) {\n\t\t\t\t\t\t\t\t\terrorMessage = '标题过长';\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\terrorMessage = '标题或描述不能为空';\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\terrorMessage = res.message || '发布失败';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: errorMessage,\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tconsole.error('创建话题失败:', err);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '网络请求错误',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}).finally(() => {\n\t\t\t\t\t\tthis.submitting = false;\n\t\t\t\t\t});\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.log('表单验证错误:', err);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 选择图片\n\t\t\tchooseImages() {\n\t\t\t\tconsole.log('🔥 开始选择图片');\n\n\t\t\t\tif (this.uploading) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '正在上传中，请稍候',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst remainingCount = 9 - this.uploadedImages.length;\n\t\t\t\tconsole.log('🔥 当前已上传图片数量:', this.uploadedImages.length, '剩余可上传:', remainingCount);\n\n\t\t\t\tif (remainingCount <= 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '最多只能上传9张图片',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: remainingCount,\n\t\t\t\t\tsizeType: ['compressed'], // 使用压缩图片\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('🔥 选择图片成功:', res.tempFilePaths);\n\t\t\t\t\t\tconsole.log('🔥 选择的图片数量:', res.tempFilePaths.length);\n\t\t\t\t\t\tthis.uploadImages(res.tempFilePaths);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tconsole.error('❌ 选择图片失败:', error);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '选择图片失败: ' + (error.errMsg || '未知错误'),\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 上传图片到COS\n\t\t\tasync uploadImages(tempFilePaths) {\n\t\t\t\tif (!tempFilePaths || tempFilePaths.length === 0) return;\n\n\t\t\t\tthis.uploading = true;\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '上传图片中...'\n\t\t\t\t});\n\n\t\t\t\tconsole.log('🔥 开始上传图片，数量:', tempFilePaths.length);\n\n\t\t\t\ttry {\n\t\t\t\t\t// 逐个上传图片，避免并发问题\n\t\t\t\t\tlet successCount = 0;\n\t\t\t\t\tlet failCount = 0;\n\n\t\t\t\t\tfor (let i = 0; i < tempFilePaths.length; i++) {\n\t\t\t\t\t\tconst filePath = tempFilePaths[i];\n\t\t\t\t\t\tconsole.log(`🔥 上传第${i + 1}张图片:`, filePath);\n\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 使用新的图片上传API\n\t\t\t\t\t\t\tconst result = await topicApi.uploadImage(filePath, 'file', { driver: 'cos' });\n\t\t\t\t\t\t\tconsole.log(`🔥 第${i + 1}张图片上传结果:`, result);\n\n\t\t\t\t\t\t\tif (result.code === 1 && result.data && result.data.file && result.data.file.url) {\n\t\t\t\t\t\t\t\tconst imageUrl = result.data.file.url;\n\n\t\t\t\t\t\t\t\t// 验证图片URL的可访问性\n\t\t\t\t\t\t\t\tawait this.validateImageUrl(imageUrl);\n\n\t\t\t\t\t\t\t\tthis.uploadedImages.push(imageUrl);\n\t\t\t\t\t\t\t\tsuccessCount++;\n\t\t\t\t\t\t\t\tconsole.log('✅ 图片上传成功:', imageUrl);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tfailCount++;\n\t\t\t\t\t\t\t\tconsole.error('❌ 图片上传失败，响应格式错误:', result);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (uploadError) {\n\t\t\t\t\t\t\tfailCount++;\n\t\t\t\t\t\t\tconsole.error(`❌ 第${i + 1}张图片上传异常:`, uploadError);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\tif (successCount > 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: `成功上传${successCount}张图片${failCount > 0 ? `，${failCount}张失败` : ''}`,\n\t\t\t\t\t\t\ticon: successCount === tempFilePaths.length ? 'success' : 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片上传失败，请重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 图片上传异常:', error);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片上传失败: ' + (error.message || '未知错误'),\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.uploading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 删除图片\n\t\t\tdeleteImage(index) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '确定要删除这张图片吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.uploadedImages.splice(index, 1);\n\t\t\t\t\t\t\tconsole.log('删除图片，当前图片数量:', this.uploadedImages.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 预览图片\n\t\t\tpreviewImage(index) {\n\t\t\t\tconsole.log('🔥 开始预览图片，索引:', index, '图片数组:', this.uploadedImages);\n\n\t\t\t\tif (!this.uploadedImages || this.uploadedImages.length === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '没有可预览的图片',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (index < 0 || index >= this.uploadedImages.length) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片索引错误',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 验证图片URL的有效性\n\t\t\t\tconst currentImageUrl = this.uploadedImages[index];\n\t\t\t\tconsole.log('🔥 当前预览图片URL:', currentImageUrl);\n\n\t\t\t\tif (!currentImageUrl || typeof currentImageUrl !== 'string') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片URL无效',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 处理图片URL，确保可访问性\n\t\t\t\tconst processedUrls = this.uploadedImages.map(url => {\n\t\t\t\t\treturn this.processImageUrl(url);\n\t\t\t\t});\n\n\t\t\t\tconst processedCurrentUrl = processedUrls[index];\n\t\t\t\tconsole.log('🔥 处理后的图片URL数组:', processedUrls);\n\t\t\t\tconsole.log('🔥 当前预览URL:', processedCurrentUrl);\n\n\t\t\t\tuni.previewImage({\n\t\t\t\t\tcurrent: processedCurrentUrl,  // 修复：使用图片URL而不是索引\n\t\t\t\t\turls: processedUrls,           // 修复：使用处理后的URL数组\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('✅ 图片预览成功');\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tconsole.error('❌ 图片预览失败:', error);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片预览失败: ' + (error.errMsg || '未知错误'),\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 显示图片操作选项\n\t\t\tshowImageOptions(index) {\n\t\t\t\tconst itemList = ['预览图片', '删除图片'];\n\t\t\t\tif (index > 0) {\n\t\t\t\t\titemList.unshift('设为封面');\n\t\t\t\t}\n\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: itemList,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (itemList[res.tapIndex] === '设为封面') {\n\t\t\t\t\t\t\tthis.setAsCover(index);\n\t\t\t\t\t\t} else if (itemList[res.tapIndex] === '预览图片') {\n\t\t\t\t\t\t\tthis.previewImage(index);\n\t\t\t\t\t\t} else if (itemList[res.tapIndex] === '删除图片') {\n\t\t\t\t\t\t\tthis.deleteImage(index);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 设为封面（移动到第一位）\n\t\t\tsetAsCover(index) {\n\t\t\t\tif (index === 0) return;\n\n\t\t\t\tconst image = this.uploadedImages.splice(index, 1)[0];\n\t\t\t\tthis.uploadedImages.unshift(image);\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '已设为封面',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 处理图片URL，确保可访问性\n\t\t\tprocessImageUrl(url) {\n\t\t\t\tif (!url) return '';\n\n\t\t\t\t// 如果URL是相对路径，转换为绝对路径\n\t\t\t\tif (url.startsWith('/')) {\n\t\t\t\t\treturn 'https://file.foxdance.com.cn' + url;\n\t\t\t\t}\n\n\t\t\t\t// 如果URL不包含协议，添加https\n\t\t\t\tif (!url.startsWith('http://') && !url.startsWith('https://')) {\n\t\t\t\t\treturn 'https://' + url;\n\t\t\t\t}\n\n\t\t\t\treturn url;\n\t\t\t},\n\n\t\t\t// 处理图片加载错误\n\t\t\thandleImageLoadError(index) {\n\t\t\t\tconsole.error('❌ 图片加载失败，索引:', index, '图片URL:', this.uploadedImages[index]);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '图片加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 验证图片URL的可访问性\n\t\t\tasync validateImageUrl(imageUrl) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// 在微信小程序中，我们可以通过创建一个临时的image组件来验证URL\n\t\t\t\t\t// 但这里我们简化处理，主要检查URL格式\n\t\t\t\t\tif (!imageUrl || typeof imageUrl !== 'string') {\n\t\t\t\t\t\treject(new Error('图片URL格式错误'));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 检查URL是否为有效格式\n\t\t\t\t\tconst urlPattern = /^(https?:\\/\\/|\\/)/;\n\t\t\t\t\tif (!urlPattern.test(imageUrl)) {\n\t\t\t\t\t\treject(new Error('图片URL格式不正确'));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('✅ 图片URL验证通过:', imageUrl);\n\t\t\t\t\tresolve(imageUrl);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.container {\n\t\tpadding: 32rpx 24rpx;\n\t\tbackground: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);\n\t\tmin-height: 100vh;\n\t}\n\n\t.form-item {\n\t\tmargin-bottom: 48rpx;\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(20rpx);\n\t\tborder-radius: 32rpx;\n\t\tpadding: 32rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.8);\n\n\t\t.label {\n\t\t\tdisplay: block;\n\t\t\tfont-size: 32rpx;\n\t\t\tbackground: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\t\t\t-webkit-background-clip: text;\n\t\t\t-webkit-text-fill-color: transparent;\n\t\t\tbackground-clip: text;\n\t\t\tfont-weight: 600;\n\t\t\tmargin-bottom: 28rpx;\n\t\t\tletter-spacing: 0.5rpx;\n\n\t\t\t.label-tip {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #8a8a8a;\n\t\t\t\tfont-weight: 400;\n\t\t\t\topacity: 0.8;\n\t\t\t}\n\t\t}\n\n\t\t.input {\n\t\t\twidth: 92%;\n\t\t\theight: 104rpx;\n\t\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\t\tbackdrop-filter: blur(10rpx);\n\t\t\tborder-radius: 24rpx;\n\t\t\tpadding: 0 28rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #4a4a4a;\n\t\t\tborder: 1rpx solid rgba(255, 107, 135, 0.2);\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);\n\t\t\ttransition: all 0.3s ease;\n\t\t\tletter-spacing: 0.3rpx;\n\n\t\t\t&:focus {\n\t\t\t\tborder-color: rgba(255, 107, 135, 0.5);\n\t\t\t\tbox-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);\n\t\t\t\ttransform: translateY(-2rpx);\n\t\t\t}\n\t\t}\n\n\t\t.textarea {\n\t\t\twidth: 92%;\n\t\t\theight: 360rpx;\n\t\t\tbackground: rgba(255, 255, 255, 0.9);\n\t\t\tbackdrop-filter: blur(10rpx);\n\t\t\tborder-radius: 24rpx;\n\t\t\tpadding: 28rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #4a4a4a;\n\t\t\tborder: 1rpx solid rgba(255, 107, 135, 0.2);\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);\n\t\t\ttransition: all 0.3s ease;\n\t\t\tline-height: 1.7;\n\t\t\tletter-spacing: 0.3rpx;\n\n\t\t\t&:focus {\n\t\t\t\tborder-color: rgba(255, 107, 135, 0.5);\n\t\t\t\tbox-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);\n\t\t\t\ttransform: translateY(-2rpx);\n\t\t\t}\n\t\t}\n\t}\n\n\t// 图片上传样式 - 小红书风格\n\t.image-upload-container {\n\t\t.image-list {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tgap: 24rpx;\n\t\t}\n\n\t\t.image-item {\n\t\t\tposition: relative;\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tborder-radius: 24rpx;\n\t\t\toverflow: hidden;\n\t\t\tbackground: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n\t\t\tborder: 2rpx solid rgba(255, 107, 135, 0.2);\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.1);\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.95);\n\t\t\t}\n\n\t\t\t.uploaded-image {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\tobject-fit: cover;\n\t\t\t\ttransition: transform 0.3s ease;\n\t\t\t}\n\n\t\t\t.image-delete {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 12rpx;\n\t\t\t\tright: 12rpx;\n\t\t\t\twidth: 44rpx;\n\t\t\t\theight: 44rpx;\n\t\t\t\tbackground: rgba(255, 107, 135, 0.9);\n\t\t\t\tbackdrop-filter: blur(10rpx);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.3);\n\n\t\t\t\t.delete-icon {\n\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.cover-badge {\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbackground: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\t\t\t\tcolor: #ffffff;\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tpadding: 10rpx 0;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tletter-spacing: 0.5rpx;\n\t\t\t}\n\t\t}\n\n\t\t.add-image-btn {\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tborder: 2rpx dashed rgba(255, 107, 135, 0.4);\n\t\t\tborder-radius: 24rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t&:active {\n\t\t\t\tbackground: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n\t\t\t\tborder-color: rgba(255, 107, 135, 0.6);\n\t\t\t\ttransform: scale(0.95);\n\t\t\t}\n\n\t\t\t.add-icon {\n\t\t\t\tfont-size: 52rpx;\n\t\t\t\tcolor: #ff6b87;\n\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\n\t\t\t.add-text {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #ff6b87;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tletter-spacing: 0.3rpx;\n\t\t\t}\n\t\t}\n\n\t\t.upload-progress {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-top: 24rpx;\n\t\t\tpadding: 24rpx;\n\t\t\tbackground: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);\n\t\t\tborder-radius: 20rpx;\n\t\t\tborder: 1rpx solid rgba(255, 107, 135, 0.1);\n\n\t\t\t.progress-text {\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #ff6b87;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tletter-spacing: 0.3rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.submit-btn {\n\t\tmargin-top: 80rpx;\n\n\t\t.btn {\n\t\t\tbackground: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\t\t\tcolor: #ffffff;\n\t\t\tfont-weight: 600;\n\t\t\tborder: none;\n\t\t\tborder-radius: 32rpx;\n\t\t\theight: 104rpx;\n\t\t\tfont-size: 34rpx;\n\t\t\tbox-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.3);\n\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\t\tletter-spacing: 1rpx;\n\t\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\n\t\t\t&:active {\n\t\t\t\ttransform: translateY(2rpx) scale(0.98);\n\t\t\t\tbox-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.3);\n\t\t\t}\n\n\t\t\t&[disabled] {\n\t\t\t\tbackground: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);\n\t\t\t\tcolor: #94a3b8;\n\t\t\t\tbox-shadow: none;\n\t\t\t\ttransform: none;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* 小红书风格动画效果 */\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(30rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.form-item {\n\t\tanimation: fadeInUp 0.6s ease-out;\n\t}\n\n\t/* 毛玻璃效果 */\n\t.glass-effect {\n\t\tbackdrop-filter: blur(20rpx);\n\t\t-webkit-backdrop-filter: blur(20rpx);\n\t}\n</style>", "import { render, staticRenderFns, recyclableRender, components } from \"./uni-forms-item.vue?vue&type=template&id=1359f286&\"\nvar renderjs\nimport script from \"./uni-forms-item.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-forms-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-forms-item.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=template&id=1359f286&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-forms-item\"\r\n\t\t:class=\"['is-direction-' + localLabelPos ,border?'uni-forms-item--border':'' ,border && isFirstBorder?'is-first-border':'']\">\r\n\t\t<slot name=\"label\">\r\n\t\t\t<view class=\"uni-forms-item__label\" :class=\"{'no-label':!label && !required}\"\r\n\t\t\t\t:style=\"{width:localLabelWidth,justifyContent: localLabelAlign}\">\r\n\t\t\t\t<text v-if=\"required\" class=\"is-required\">*</text>\r\n\t\t\t\t<text>{{label}}</text>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t<view class=\"uni-forms-item__content\">\r\n\t\t\t<slot></slot>\r\n\t\t\t<view class=\"uni-forms-item__error\" :class=\"{'msg--active':msg}\">\r\n\t\t\t\t<text>{{msg}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t<view class=\"uni-forms-item__nuve-content\">\r\n\t\t\t<view class=\"uni-forms-item__content\">\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-forms-item__error\" :class=\"{'msg--active':msg}\">\r\n\t\t\t\t<text class=\"error-text\">{{msg}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * uni-fomrs-item 表单子组件\r\n\t * @description uni-fomrs-item 表单子组件，提供了基础布局已经校验能力\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=2773\r\n\t * @property {Boolean} required 是否必填，左边显示红色\"*\"号\r\n\t * @property {String } \tlabel \t\t\t\t输入框左边的文字提示\r\n\t * @property {Number } \tlabelWidth \t\t\tlabel的宽度，单位px（默认70）\r\n\t * @property {String } \tlabelAlign = [left|center|right] label的文字对齐方式（默认left）\r\n\t * \t@value left\t\tlabel 左侧显示\r\n\t * \t@value center\tlabel 居中\r\n\t * \t@value right\tlabel 右侧对齐\r\n\t * @property {String } \terrorMessage \t\t显示的错误提示内容，如果为空字符串或者false，则不显示错误信息\r\n\t * @property {String } \tname \t\t\t\t表单域的属性名，在使用校验规则时必填\r\n\t * @property {String } \tleftIcon \t\t\t【1.4.0废弃】label左边的图标，限 uni-ui 的图标名称\r\n\t * @property {String } \ticonColor \t\t【1.4.0废弃】左边通过icon配置的图标的颜色（默认#606266）\r\n\t * @property {String} validateTrigger = [bind|submit|blur]\t【1.4.0废弃】校验触发器方式 默认 submit\r\n\t * \t@value bind \t发生变化时触发\r\n\t * \t@value submit 提交时触发\r\n\t * \t@value blur \t失去焦点触发\r\n\t * @property {String } \tlabelPosition = [top|left] 【1.4.0废弃】label的文字的位置（默认left）\r\n\t * \t@value top\t顶部显示 label\r\n\t * \t@value left\t左侧显示 label\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'uniFormsItem',\r\n\t\toptions: {\n\t\t\t// #ifdef MP-TOUTIAO\n\t\t\tvirtualHost: false,\n\t\t\t// #endif\n\t\t\t// #ifndef MP-TOUTIAO\n\t\t\tvirtualHost: true\n\t\t\t// #endif\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tuniFormItem: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tinject: {\r\n\t\t\tform: {\r\n\t\t\t\tfrom: 'uniForm',\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 表单校验规则\r\n\t\t\trules: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 表单域的属性名，在使用校验规则时必填\r\n\t\t\tname: {\r\n\t\t\t\ttype: [String, Array],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\trequired: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tlabel: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// label的宽度\r\n\t\t\tlabelWidth: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// label 居中方式，默认 left 取值 left/center/right\r\n\t\t\tlabelAlign: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 强制显示错误信息\r\n\t\t\terrorMessage: {\r\n\t\t\t\ttype: [String, Boolean],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 1.4.0 弃用，统一使用 form 的校验时机\r\n\t\t\t// validateTrigger: {\r\n\t\t\t// \ttype: String,\r\n\t\t\t// \tdefault: ''\r\n\t\t\t// },\r\n\t\t\t// 1.4.0 弃用，统一使用 form 的label 位置\r\n\t\t\t// labelPosition: {\r\n\t\t\t// \ttype: String,\r\n\t\t\t// \tdefault: ''\r\n\t\t\t// },\r\n\t\t\t// 1.4.0 以下属性已经废弃，请使用  #label 插槽代替\r\n\t\t\tleftIcon: String,\r\n\t\t\ticonColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\terrMsg: '',\r\n\t\t\t\tuserRules: null,\r\n\t\t\t\tlocalLabelAlign: 'left',\r\n\t\t\t\tlocalLabelWidth: '70px',\r\n\t\t\t\tlocalLabelPos: 'left',\r\n\t\t\t\tborder: false,\r\n\t\t\t\tisFirstBorder: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 处理错误信息\r\n\t\t\tmsg() {\r\n\t\t\t\treturn this.errorMessage || this.errMsg;\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 规则发生变化通知子组件更新\r\n\t\t\t'form.formRules'(val) {\r\n\t\t\t\t// TODO 处理头条vue3 watch不生效的问题\r\n\t\t\t\t// #ifndef MP-TOUTIAO\r\n\t\t\t\tthis.init()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t'form.labelWidth'(val) {\r\n\t\t\t\t// 宽度\r\n\t\t\t\tthis.localLabelWidth = this._labelWidthUnit(val)\r\n\r\n\t\t\t},\r\n\t\t\t'form.labelPosition'(val) {\r\n\t\t\t\t// 标签位置\r\n\t\t\t\tthis.localLabelPos = this._labelPosition()\r\n\t\t\t},\r\n\t\t\t'form.labelAlign'(val) {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.init(true)\r\n\t\t\tif (this.name && this.form) {\r\n\t\t\t\t// TODO 处理头条vue3 watch不生效的问题\r\n\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\tthis.$watch('form.formRules', () => {\r\n\t\t\t\t\tthis.init()\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// 监听变化\r\n\t\t\t\tthis.$watch(\r\n\t\t\t\t\t() => {\r\n\t\t\t\t\t\tconst val = this.form._getDataValue(this.name, this.form.localData)\r\n\t\t\t\t\t\treturn val\r\n\t\t\t\t\t},\r\n\t\t\t\t\t(value, oldVal) => {\r\n\t\t\t\t\t\tconst isEqual = this.form._isEqual(value, oldVal)\r\n\t\t\t\t\t\t// 简单判断前后值的变化，只有发生变化才会发生校验\r\n\t\t\t\t\t\t// TODO  如果 oldVal = undefined ，那么大概率是源数据里没有值导致 ，这个情况不哦校验 ,可能不严谨 ，需要在做观察\r\n\t\t\t\t\t\t// fix by mehaotian 暂时取消 && oldVal !== undefined ，如果formData 中不存在，可能会不校验\r\n\t\t\t\t\t\tif (!isEqual) {\r\n\t\t\t\t\t\t\tconst val = this.itemSetValue(value)\r\n\t\t\t\t\t\t\tthis.onFieldChange(val, false)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\timmediate: false\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\tdestroyed() {\r\n\t\t\tif (this.__isUnmounted) return\r\n\t\t\tthis.unInit()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\tunmounted() {\r\n\t\t\tthis.__isUnmounted = true\r\n\t\t\tthis.unInit()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 设置规则 ，主要用于小程序自定义检验规则\r\n\t\t\t * @param {Array} rules 规则源数据\r\n\t\t\t */\r\n\t\t\tsetRules(rules = null) {\r\n\t\t\t\tthis.userRules = rules\r\n\t\t\t\tthis.init(false)\r\n\t\t\t},\r\n\t\t\t// 兼容老版本表单组件\r\n\t\t\tsetValue() {\r\n\t\t\t\t// console.log('setValue 方法已经弃用，请使用最新版本的 uni-forms 表单组件以及其他关联组件。');\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 外部调用方法\r\n\t\t\t * 校验数据\r\n\t\t\t * @param {any} value 需要校验的数据\r\n\t\t\t * @param {boolean} 是否立即校验\r\n\t\t\t * @return {Array|null} 校验内容\r\n\t\t\t */\r\n\t\t\tasync onFieldChange(value, formtrigger = true) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tformData,\r\n\t\t\t\t\tlocalData,\r\n\t\t\t\t\terrShowType,\r\n\t\t\t\t\tvalidateCheck,\r\n\t\t\t\t\tvalidateTrigger,\r\n\t\t\t\t\t_isRequiredField,\r\n\t\t\t\t\t_realName\r\n\t\t\t\t} = this.form\r\n\t\t\t\tconst name = _realName(this.name)\r\n\t\t\t\tif (!value) {\r\n\t\t\t\t\tvalue = this.form.formData[name]\r\n\t\t\t\t}\r\n\t\t\t\t// fixd by mehaotian 不在校验前清空信息，解决闪屏的问题\r\n\t\t\t\t// this.errMsg = '';\r\n\r\n\t\t\t\t// fix by mehaotian 解决没有检验规则的情况下，抛出错误的问题\r\n\t\t\t\tconst ruleLen = this.itemRules.rules && this.itemRules.rules.length\r\n\t\t\t\tif (!this.validator || !ruleLen || ruleLen === 0) return;\r\n\r\n\t\t\t\t// 检验时机\r\n\t\t\t\t// let trigger = this.isTrigger(this.itemRules.validateTrigger, this.validateTrigger, validateTrigger);\r\n\t\t\t\tconst isRequiredField = _isRequiredField(this.itemRules.rules || []);\r\n\t\t\t\tlet result = null;\r\n\t\t\t\t// 只有等于 bind 时 ，才能开启时实校验\r\n\t\t\t\tif (validateTrigger === 'bind' || formtrigger) {\r\n\t\t\t\t\t// 校验当前表单项\r\n\t\t\t\t\tresult = await this.validator.validateUpdate({\r\n\t\t\t\t\t\t\t[name]: value\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformData\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\t// 判断是否必填,非必填，不填不校验，填写才校验 ,暂时只处理 undefined  和空的情况\r\n\t\t\t\t\tif (!isRequiredField && (value === undefined || value === '')) {\r\n\t\t\t\t\t\tresult = null;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 判断错误信息显示类型\r\n\t\t\t\t\tif (result && result.errorMessage) {\r\n\t\t\t\t\t\tif (errShowType === 'undertext') {\r\n\t\t\t\t\t\t\t// 获取错误信息\r\n\t\t\t\t\t\t\tthis.errMsg = !result ? '' : result.errorMessage;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (errShowType === 'toast') {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: result.errorMessage || '校验错误',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (errShowType === 'modal') {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: result.errorMessage || '校验错误'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.errMsg = ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 通知 form 组件更新事件\r\n\t\t\t\t\tvalidateCheck(result ? result : null)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.errMsg = ''\r\n\t\t\t\t}\r\n\t\t\t\treturn result ? result : null;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 初始组件数据\r\n\t\t\t */\r\n\t\t\tinit(type = false) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tvalidator,\r\n\t\t\t\t\tformRules,\r\n\t\t\t\t\tchildrens,\r\n\t\t\t\t\tformData,\r\n\t\t\t\t\tlocalData,\r\n\t\t\t\t\t_realName,\r\n\t\t\t\t\tlabelWidth,\r\n\t\t\t\t\t_getDataValue,\r\n\t\t\t\t\t_setDataValue\r\n\t\t\t\t} = this.form || {}\r\n\t\t\t\t// 对齐方式\r\n\t\t\t\tthis.localLabelAlign = this._justifyContent()\r\n\t\t\t\t// 宽度\r\n\t\t\t\tthis.localLabelWidth = this._labelWidthUnit(labelWidth)\r\n\t\t\t\t// 标签位置\r\n\t\t\t\tthis.localLabelPos = this._labelPosition()\r\n\t\t\t\t// 将需要校验的子组件加入form 队列\r\n\t\t\t\tthis.form && type && childrens.push(this)\r\n\r\n\t\t\t\tif (!validator || !formRules) return\r\n\t\t\t\t// 判断第一个 item\r\n\t\t\t\tif (!this.form.isFirstBorder) {\r\n\t\t\t\t\tthis.form.isFirstBorder = true;\r\n\t\t\t\t\tthis.isFirstBorder = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 判断 group 里的第一个 item\r\n\t\t\t\tif (this.group) {\r\n\t\t\t\t\tif (!this.group.isFirstBorder) {\r\n\t\t\t\t\t\tthis.group.isFirstBorder = true;\r\n\t\t\t\t\t\tthis.isFirstBorder = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.border = this.form.border;\r\n\t\t\t\t// 获取子域的真实名称\r\n\t\t\t\tconst name = _realName(this.name)\r\n\t\t\t\tconst itemRule = this.userRules || this.rules\r\n\t\t\t\tif (typeof formRules === 'object' && itemRule) {\r\n\t\t\t\t\t// 子规则替换父规则\r\n\t\t\t\t\tformRules[name] = {\r\n\t\t\t\t\t\trules: itemRule\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvalidator.updateSchema(formRules);\r\n\t\t\t\t}\r\n\t\t\t\t// 注册校验规则\r\n\t\t\t\tconst itemRules = formRules[name] || {}\r\n\t\t\t\tthis.itemRules = itemRules\r\n\t\t\t\t// 注册校验函数\r\n\t\t\t\tthis.validator = validator\r\n\t\t\t\t// 默认值赋予\r\n\t\t\t\tthis.itemSetValue(_getDataValue(this.name, localData))\r\n\t\t\t},\r\n\t\t\tunInit() {\r\n\t\t\t\tif (this.form) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tchildrens,\r\n\t\t\t\t\t\tformData,\r\n\t\t\t\t\t\t_realName\r\n\t\t\t\t\t} = this.form\r\n\t\t\t\t\tchildrens.forEach((item, index) => {\r\n\t\t\t\t\t\tif (item === this) {\r\n\t\t\t\t\t\t\tthis.form.childrens.splice(index, 1)\r\n\t\t\t\t\t\t\tdelete formData[_realName(item.name)]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 设置item 的值\r\n\t\t\titemSetValue(value) {\r\n\t\t\t\tconst name = this.form._realName(this.name)\r\n\t\t\t\tconst rules = this.itemRules.rules || []\r\n\t\t\t\tconst val = this.form._getValue(name, value, rules)\r\n\t\t\t\tthis.form._setDataValue(name, this.form.formData, val)\r\n\t\t\t\treturn val\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 移除该表单项的校验结果\r\n\t\t\t */\r\n\t\t\tclearValidate() {\r\n\t\t\t\tthis.errMsg = '';\r\n\t\t\t},\r\n\r\n\t\t\t// 是否显示星号\r\n\t\t\t_isRequired() {\r\n\t\t\t\t// TODO 不根据规则显示 星号，考虑后续兼容\r\n\t\t\t\t// if (this.form) {\r\n\t\t\t\t// \tif (this.form._isRequiredField(this.itemRules.rules || []) && this.required) {\r\n\t\t\t\t// \t\treturn true\r\n\t\t\t\t// \t}\r\n\t\t\t\t// \treturn false\r\n\t\t\t\t// }\r\n\t\t\t\treturn this.required\r\n\t\t\t},\r\n\r\n\t\t\t// 处理对齐方式\r\n\t\t\t_justifyContent() {\r\n\t\t\t\tif (this.form) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tlabelAlign\r\n\t\t\t\t\t} = this.form\r\n\t\t\t\t\tlet labelAli = this.labelAlign ? this.labelAlign : labelAlign;\r\n\t\t\t\t\tif (labelAli === 'left') return 'flex-start';\r\n\t\t\t\t\tif (labelAli === 'center') return 'center';\r\n\t\t\t\t\tif (labelAli === 'right') return 'flex-end';\r\n\t\t\t\t}\r\n\t\t\t\treturn 'flex-start';\r\n\t\t\t},\r\n\t\t\t// 处理 label宽度单位 ,继承父元素的值\r\n\t\t\t_labelWidthUnit(labelWidth) {\r\n\r\n\t\t\t\t// if (this.form) {\r\n\t\t\t\t// \tconst {\r\n\t\t\t\t// \t\tlabelWidth\r\n\t\t\t\t// \t} = this.form\r\n\t\t\t\treturn this.num2px(this.labelWidth ? this.labelWidth : (labelWidth || (this.label ? 70 : 'auto')))\r\n\t\t\t\t// }\r\n\t\t\t\t// return '70px'\r\n\t\t\t},\r\n\t\t\t// 处理 label 位置\r\n\t\t\t_labelPosition() {\r\n\t\t\t\tif (this.form) return this.form.labelPosition || 'left'\r\n\t\t\t\treturn 'left'\r\n\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 触发时机\r\n\t\t\t * @param {Object} rule 当前规则内时机\r\n\t\t\t * @param {Object} itemRlue 当前组件时机\r\n\t\t\t * @param {Object} parentRule 父组件时机\r\n\t\t\t */\r\n\t\t\tisTrigger(rule, itemRlue, parentRule) {\r\n\t\t\t\t//  bind  submit\r\n\t\t\t\tif (rule === 'submit' || !rule) {\r\n\t\t\t\t\tif (rule === undefined) {\r\n\t\t\t\t\t\tif (itemRlue !== 'bind') {\r\n\t\t\t\t\t\t\tif (!itemRlue) {\r\n\t\t\t\t\t\t\t\treturn parentRule === '' ? 'bind' : 'submit';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn 'submit';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn 'bind';\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn 'submit';\r\n\t\t\t\t}\r\n\t\t\t\treturn 'bind';\r\n\t\t\t},\r\n\t\t\tnum2px(num) {\r\n\t\t\t\tif (typeof num === 'number') {\r\n\t\t\t\t\treturn `${num}px`\r\n\t\t\t\t}\r\n\t\t\t\treturn num\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-forms-item {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\t// 在 nvue 中，使用 margin-bottom error 信息会被隐藏\r\n\t\tpadding-bottom: 22px;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tmargin-bottom: 22px;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\r\n\t\t&__label {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t\ttext-align: left;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #606266;\r\n\t\t\theight: 36px;\r\n\t\t\tpadding: 0 12px 0 0;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tvertical-align: middle;\r\n\t\t\tflex-shrink: 0;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t/* #endif */\r\n\t\t\t&.no-label {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\t/* #ifndef MP-TOUTIAO */\r\n\t\t\t// display: flex;\r\n\t\t\t// align-items: center;\r\n\t\t\t/* #endif */\r\n\t\t\tposition: relative;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tflex: 1;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t/* #endif */\r\n\t\t\tflex-direction: row;\r\n\r\n\t\t\t/* #ifndef APP || H5 || MP-WEIXIN || APP-NVUE */\r\n\t\t\t// TODO 因为小程序平台会多一层标签节点 ，所以需要在多余节点继承当前样式\r\n\t\t\t&>uni-easyinput,\r\n\t\t\t&>uni-data-picker {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t/* #endif */\r\n\r\n\t\t}\r\n\r\n\t\t& .uni-forms-item__nuve-content {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\r\n\t\t&__error {\r\n\t\t\tcolor: #f56c6c;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tline-height: 1;\r\n\t\t\tpadding-top: 4px;\r\n\t\t\tposition: absolute;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\ttop: 100%;\r\n\t\t\tleft: 0;\r\n\t\t\ttransition: transform 0.3s;\r\n\t\t\ttransform: translateY(-100%);\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\tbottom: 5px;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\topacity: 0;\r\n\r\n\t\t\t.error-text {\r\n\t\t\t\t// 只有 nvue 下这个样式才生效\r\n\t\t\t\tcolor: #f56c6c;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t}\r\n\r\n\t\t\t&.msg--active {\r\n\t\t\t\topacity: 1;\r\n\t\t\t\ttransform: translateY(0%);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 位置修饰样式\r\n\t\t&.is-direction-left {\r\n\t\t\tflex-direction: row;\r\n\t\t}\r\n\r\n\t\t&.is-direction-top {\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.uni-forms-item__label {\r\n\t\t\t\tpadding: 0 0 8px;\r\n\t\t\t\tline-height: 1.5715;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\twhite-space: initial;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.is-required {\r\n\t\t\t// color: $uni-color-error;\r\n\t\t\tcolor: #dd524d;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.uni-forms-item--border {\r\n\t\tmargin-bottom: 0;\r\n\t\tpadding: 10px 0;\r\n\t\t// padding-bottom: 0;\r\n\t\tborder-top: 1px #eee solid;\r\n\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\t.uni-forms-item__content {\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\talign-items: flex-start;\r\n\r\n\t\t\t.uni-forms-item__error {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttop: 5px;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tpadding-top: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\r\n\t\t.uni-forms-item__error {\r\n\t\t\tposition: relative;\r\n\t\t\ttop: 0px;\r\n\t\t\tleft: 0;\r\n\t\t\tpadding-top: 0;\r\n\t\t\tmargin-top: 5px;\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\r\n\t}\r\n\r\n\t.is-first-border {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tborder: none;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tborder-width: 0;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-forms-item.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752116123811\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-topic.vue?vue&type=style&index=0&id=5b362a9d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add-topic.vue?vue&type=style&index=0&id=5b362a9d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752116122136\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}