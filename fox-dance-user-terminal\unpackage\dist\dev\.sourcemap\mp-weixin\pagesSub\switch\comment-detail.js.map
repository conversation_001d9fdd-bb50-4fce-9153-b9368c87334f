{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?8002", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?7f26", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?6319", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?157f", "uni-app:///pagesSub/switch/comment-detail.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?df4c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?38ab"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "dayjs", "components", "CommentInput", "ReplySkeleton", "data", "commentId", "userId", "loading", "isRefreshing", "loadingMore", "hasMore", "page", "limit", "comment", "id", "content", "created_at", "likes", "is_liked", "user", "nickname", "avatar", "level", "replies", "replyCount", "replyText", "currentReplyTo", "inputPlaceholder", "sortBy", "scrollViewHeight", "show<PERSON>ull<PERSON><PERSON>nt", "showMorePopup", "currentMoreComment", "isReplying", "keyboardHeight", "inputContainerBottom", "isKeyboardShow", "scrollTop", "scrollIntoView", "pagination", "pageSize", "isLoadingMore", "loadingText", "computed", "isCommentOwner", "console", "onLoad", "commentIdType", "userIdType", "commentIdValue", "userIdValue", "uni", "title", "icon", "setTimeout", "onShow", "onHide", "onUnload", "methods", "getTimestamp", "setupKeyboardListener", "onInputFocus", "onInputBlur", "hideMaskAndKeyboard", "cancelReply", "isReply<PERSON><PERSON><PERSON>", "showDeleteCommentConfirm", "confirmText", "confirmColor", "success", "showMoreOptions", "replyFromMore", "copyComment", "deleteReply", "commentApi", "deleteComment", "showDeleteReplyConfirm", "reply", "sort", "current", "commentData", "replyItems", "repliesCount", "total", "rawReplies", "action", "replyToId", "replyToIdValue", "type", "timeString", "index", "elementId", "query", "scrollViewTop", "elementTop", "relativeTop", "targetScrollTop", "targetElement", "scrollViewElement", "<PERSON><PERSON><PERSON><PERSON>", "hasScrollView", "processedReply"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAA8tB,CAAgB,6sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuLlvB;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAIAC;AACAA;AAAA,eAEA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAL;UACAM;UACAC;UACAC;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACA5B;QACA6B;QACA9B;QACAH;MACA;MACAkC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;;MAEA;MACA;MACA;MAEAD;QACAxC;QACAC;QACAyC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;IACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EAEA;EACAI;IACA;IACAJ;IACA;EACA;EAEAK;IACA;IACAL;IACAN;EACA;EAEAY;IACA;IACAN;IACAN;EACA;EACAa;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAEAT;QACAN;QACA;QACA;QAEA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MAQAA;IACA;IAEA;IACAgB;MAAA;MACAhB;MACA;;MAEA;;MAEA;MACAS;QACA;UACA;UACA;UACA;QACA;MACA;IAEA;IAEA;IACAQ;MAAA;MACAjB;MACA;;MAEA;MACAS;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAS;MACAlB;;MAEA;MACA;QACA;MACA;;MAEA;MACAM;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;MACAb;MACA;MACA;QACA;MACA;IACA;IACA;IACAc;MACA;IACA;IAEA;IACAC;MAAA;MACAf;QACAC;QACArC;QACAoD;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;QACAjB;UACA;UACA;YACA;UACA;;UAEA;UACA;UACA;;UAEA;UACAA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAkB;MAAA;MACA;MAEArB;QACA/C;QACAiE;UACAlB;YACAC;YACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAoB;MAAA;MACA;MAEAtB;QACAC;QACArC;QACAoD;QACAC;QACAC;UACA;YACA;YACAK;cACApE;YACA;cACAuC;cACA;gBACA;gBACA;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBACA;gBACA;gBAEAM;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;cACA;gBACAF;kBACAC;kBACAC;gBACA;cACA;YACA;cACAR;cACAM;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAsB;MACA9B;MACA6B;QACApE;MACA;QACAuC;QACA;UACAM;YACAC;YACAC;UACA;;UAEA;UACAC;YACAH;UACA;QACA;UACAA;YACAC;YACAC;UACA;QACA;MACA;QACAR;QACAM;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAuB;MAAA;MACAzB;QACAC;QACArC;QACAoD;QACAC;QACAC;UACA;YACA;UACA;QACA;MACA;IACA;EAAA,+EAGAQ;IAAA;IACAhC;IACA6B;MACApE;IACA;MACAuC;MACA;QACA;QACA;QACA;QACA;QAEAM;UACAC;UACAC;QACA;MACA;QACAF;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;EACA,kGAEA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACAR;EACA,wEACA;IACAM;EACA,gGACA;IAAA;IACA;;IAEA;IACAuB;MACApE;MACAwE;MACAC;MACAvC;IACA;MACAK;MACA;QACA;QACA;QACAA;;QAEA;QACAmC;QACAA;QACAA;;QAEA;QACA;UACAA;QACA;QAEAnC;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;MACA;QACAM;UACAC;UACAC;QACA;;QAEA;QACAC;UACAH;QACA;MACA;IACA;MACAN;MACAM;QACAC;QACAC;MACA;;MAEA;MACAC;QACAH;MACA;IACA;EACA,oFACA;IAAA;IACA;MACA;MACA;IACA;MACA;IACA;;IAEA;IACAuB;MACApE;MACAwE;MACAC;MACAvC;IACA;MACAK;MACA;QACA;;QAEA;QACA;QAEAA;QACA;UACA;UACAA;UACA;QACA;;QAEA;QACAoC;UACAJ;UACAA;UACA;UACAA;;UAEA;UACA;YACAA;cACA/D;cACAM;YACA;UACA;YACAyD;UACA;;UAEA;UACA;YACAA;UACA;QACA;QAEAhC;QAEA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;QACA;UACA;QACA;MACA;QACAM;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;MACA;MACA;MACA;IACA;EACA,0FAEA;IAAA;IACAR;;IAEA;IACA;MACAA;MACA;IACA;;IAEA;IACA;IACA;IACA;MAAA;MACAA;MACA;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;IAEAA;;IAEA;IACA;IACA;MACAvC;MAAA;MACAwE;MACAC;MAAA;MACAvC;IACA;IAEAK;;IAEA;IACA6B;MACA;MACA;MAEA7B;MAEA;QACA;;QAEA;QACAA;UACAqC;UACAC;UACAzE;QACA;;QAEA;QACA;QACA;UACA;YACA0E;UACA;YACAA;UACA;YACAA;UACA;QACA;QAEA;QAEA;UACA;UACA;YAAA;UAAA;UACA;YAAA;UAAA;UAEAvC;UAEA;YACA;YACA;;YAEA;YACA;YAEAA;UACA;;UAEA;UACA;YACA;YACAA;UACA;QACA;UACA;UACA;UACAA;QACA;MACA;QACA;QACAA;QACAM;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;MACA;MACA;MACA;MACAR;IACA;EACA,8EACA;IAAA;IACA;;IAEA;IACA;MACAlC;MACA6B;MACA9B;MACAH;IACA;;IAEA;IACA;IACA+C;MACA;IACA;EACA,kFACA;IAAA;IACA;IACA;IAEAoB;MACApE;MAAA;MACA+E;IACA;MACAxC;MACA;QACA;QACA;QACA;MACA;QACAM;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;EACA,4EACAwB;IAAA;IACA;IACA;IAEAH;MACApE;MAAA;MACA+E;IACA;MACAxC;MACA;QACA;QACA;QACA;MACA;QACAM;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;EACA,kFACA;IAAA;IACA;IACA;IACA;MACA;IACA;IAEA;IACA;IACA;;IAEA;IACAC;MACA;QACA;MACA;IACA;EACA,sFACAuB;IAAA;IACA;IACA;IACA;MACA;IACA;IAEA;IACA;IACAhC;IACAA;;IAEA;IACA;MACAA;MACA;IACA;MACAA;MACA;IACA;;IAEA;IACAS;MACA;QACA;MACA;IACA;EACA,8EACA;IAAA;IACA;MACAH;QACAC;QACAC;MACA;MACA;IACA;IAEA;IAEA;MACA/C;MAAA;MACAD;MAAA;MACAU;MACAuE;IACA;;IAEAzC;IACAA;MACAvC;MACA4C;MACA7C;MACA4C;MACAqC;MACAC;MACAxE;IACA;IAEA2D;MACA7B;MACA;QACAM;UACAC;UACAC;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;UACA1C;UACA6B;UACA9B;UACAH;QACA;QACA;MACA;QACA4C;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;EACA,8EACAmC;IACA;;IAEA;IACA;;IAEA;IACA;MACA7E;MACA6B;MACA9B;MACAH;IACA;;IAEA;IACA;IACA;EACA,4FAGA;IAAA;IACAsC;;IAEA;IACA6B;MACApE;MAAA;MACAwE;MACAC;MAAA;MACAvC;IACA;MACAK;MACA;QACA;;QAEA;QACA;QACA;UACA;YACAoC;UACA;YACAA;UACA;YACAA;UACA;QACA;QAEApC;;QAEA;QACA;QAEA;UACA;UACA;UACAA;QACA;UACA;UACA;YAAA;UAAA;UACA;YAAA;UAAA;UACA;UACAA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;QAEAA;MACA;QACAA;QACAM;UACAC;UACAC;QACA;MACA;IACA;MACAR;MACAM;QACAC;QACAC;MACA;IACA;MACA;MACAR;IACA;EACA,8EACA4C;IACA;IACA;EACA,oFACAnE;IACA;MACA;MAAA;MACA;MACA;MAAA;MACA;MACA;MACA;MACA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;IACA;;IACA;EACA,sFAEA;IACA;IACA;;IAEA;IACA;MACA;IACA;EACA,8FAGAuD;IACA;;IAEA;IACA;MACA;IACA;MACA;IACA;;IAEA;IACA;MACA;IACA;EACA,kGAGA;IAAA;IACAhC;;IAEA;IACA;;IAEA;IACAS;MACA;IACA;EACA,oFAGAoC;IAAA;IACA;IACA7C;;IAEA;IACA;;IAEA;IACAS;MACA;IACA;EACA,wHAGAqC;IAAA;IACA9C;IAEA;MACAS;QACA;QACA;;QAEA;QACAA;UACA;QACA;QAEAT;MACA;IACA;EACA,8GAGA8C;IAAA;IACA9C;IAEA;MACA;MAEA+C;MACAA;MAEAA;QACA/C;QAEA;UACA;UACA;UAEA;YACA;YACA;YACA;YAEAA;cACAgD;cACAC;cACAC;cACAC;YACA;;YAEA;YACA;YACA;cACA;cACAnD;YACA;UACA;QACA;MACA;IACA;EACA,kGAGA;IAAA;IACAA;IAEA;IACA+C;IACAA;IAEAA;MACA/C;QACA8C;QACAM;QACAC;QACAC;QACAC;MACA;IACA;EACA,0FAGA7E;IACA;MACAsB;MACA;IACA;IAEAA;IAEA;MACA;;MAEA;MACAgC;MACAA;MACAA;;MAEA;MACA;QACAA;UACA/D;UACAM;UACAC;UACAC;QACA;MACA;QACA;QACA;UACAuD;QACA;;QAEA;QACAA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEA;IACA;MAAA;IAAA;EACA,4GAGAtD;IACA;IAEA;MACAsB;MACA;IACA;IAEAA;IAEA;MACA;;MAEA;MACA,qDACAgC;QACA7D;QACAE;QACAD;QACAa;MAAA,EACA;;MAEA;MACA;QACAuE;UACAvF;UACAM;UACAC;UACAC;QACA;MACA;QACA;QACA;UACA+E;QACA;QACAA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEA;IACA;MAAA;IAAA;IAEA;IACAxD;IAEA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACr4CA;AAAA;AAAA;AAAA;AAA63C,CAAgB,wxCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/switch/comment-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/switch/comment-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./comment-detail.vue?vue&type=template&id=4c2dc355&scoped=true&\"\nvar renderjs\nimport script from \"./comment-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./comment-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4c2dc355\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/comment-detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=template&id=4c2dc355&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    !_vm.loading && _vm.comment.user.level >= 0\n      ? _vm.getLevelColor(_vm.comment.user.level)\n      : null\n  var m1 = !_vm.loading ? _vm.formatTime(_vm.comment.created_at) : null\n  var g0 =\n    !_vm.loading && !_vm.showFullContent ? _vm.comment.content.length : null\n  var g1 =\n    !_vm.loading && !_vm.showFullContent && g0 > 100\n      ? _vm.comment.content.slice(0, 100)\n      : null\n  var g2 = !_vm.loading ? _vm.comment.content.length : null\n  var g3 = !_vm.loading ? _vm.replies.length : null\n  var l0 =\n    !_vm.loading && !!(g3 > 0)\n      ? _vm.__map(_vm.replies, function (reply, index) {\n          var $orig = _vm.__get_orig(reply)\n          var m2 = _vm.formatTime(reply.created_at)\n          var g4 = !reply.showFullContent ? reply.content.length : null\n          var g5 =\n            !reply.showFullContent && g4 > 100\n              ? reply.content.slice(0, 100)\n              : null\n          var g6 = reply.content.length\n          return {\n            $orig: $orig,\n            m2: m2,\n            g4: g4,\n            g5: g5,\n            g6: g6,\n          }\n        })\n      : null\n  var g7 =\n    !_vm.loading && !_vm.pagination.loading\n      ? !_vm.pagination.hasMore && _vm.replies.length > 0\n      : null\n  var m3 = _vm.currentMoreComment && _vm.isReplyOwner(_vm.currentMoreComment)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.isReplying ? _vm.cancelReply() : _vm.hideMaskAndKeyboard()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g7: g7,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"comment-detail\">\n\n    <!-- 评论详情 -->\n    <scroll-view\n      ref=\"replyScrollView\"\n      scroll-y\n      class=\"comment-container\"\n      @scrolltolower=\"loadMoreReplies\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"isRefreshing\"\n      :style=\"{ height: scrollViewHeight }\"\n      :scroll-top=\"scrollTop\"\n      :scroll-into-view=\"scrollIntoView\"\n      :scroll-with-animation=\"true\">\n      <view class=\"safe-area-inset\"></view>\n      <view v-if=\"loading\" class=\"loading\">\n        <u-loading mode=\"flower\" size=\"50\"></u-loading>\n      </view>\n      <block v-else>\n        <!-- 主评论 -->\n        <view class=\"main-comment\" id=\"main-comment\">\n          <view class=\"user-info\">\n            <view class=\"avatar-wrap\">\n              <image :src=\"comment.user.avatar\" mode=\"aspectFill\" :lazy-load=\"true\" class=\"avatar\"></image>\n            </view>\n            <view class=\"user-meta\">\n              <view class=\"nickname\">\n                {{ comment.user.nickname }}\n                <view v-if=\"comment.user.level >= 0\" class=\"user-level\"\n                  :style=\"{ backgroundColor: getLevelColor(comment.user.level) }\">Lv{{ comment.user.level }}</view>\n              </view>\n              <view class=\"time\">{{ formatTime(comment.created_at) }}</view>\n            </view>\n            <view class=\"like-btn\" @tap=\"likeComment\">\n              <u-icon :name=\"comment.is_liked ? 'heart-fill' : 'heart'\" :color=\"comment.is_liked ? '#f56c6c' : '#999'\"\n                size=\"28\"></u-icon>\n              <text>{{ comment.likes }}</text>\n            </view>\n          </view>\n          <view class=\"content\">\n            <text>{{ showFullContent ? comment.content : (comment.content.length > 100 ? comment.content.slice(0, 100) +\n              '...' : comment.content) }}</text>\n            <view v-if=\"comment.content.length > 100\" class=\"expand-btn\" @tap=\"toggleContent\">\n              {{ showFullContent ? '收起' : '展开' }}\n            </view>\n          </view>\n          <view class=\"actions\">\n            <!-- <view class=\"reply-btn\" @tap=\"replyToMain\">\n              <uni-icons type=\"chatbubble\" color=\"#999\" size=\"18\"></uni-icons>\n              <text>回复</text>\n            </view> -->\n            <!-- 添加删除按钮，仅当当前用户是评论者时显示 -->\n            <view v-if=\"isCommentOwner\" class=\"delete-btn\" @tap=\"showDeleteCommentConfirm\">\n              <u-icon name=\"trash\" color=\"#999\" size=\"28\"></u-icon>\n              <!-- <text>删除</text> -->\n            </view>\n          </view>\n        </view>\n\n        <!-- 回复列表 -->\n        <view class=\"replies-container\">\n          <view class=\"replies-header\">\n            <text>回复 ({{ replyCount }})</text>\n            <view class=\"sort-options\">\n              <view class=\"van-tabs\">\n                <view class=\"van-tabs__wrap\">\n                  <view class=\"van-tabs__nav\">\n                    <view class=\"van-tab no-highlight\" :class=\"{ 'van-tab--active': sortBy === 'hot' }\"\n                      @tap=\"changeSort('hot')\">\n                      <view class=\"van-tab__text\">最热</view>\n                    </view>\n                    <view class=\"van-tab no-highlight\" :class=\"{ 'van-tab--active': sortBy === 'new' }\"\n                      @tap=\"changeSort('new')\">\n                      <view class=\"van-tab__text\">最新</view>\n                    </view>\n                    <view class=\"van-tab no-highlight\" :class=\"{ 'van-tab--active': sortBy === 'my' }\"\n                      @tap=\"changeSort('my')\">\n                      <view class=\"van-tab__text\">我的</view>\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <view class=\"reply-list\">\n            <view v-if=\"!(replies.length > 0)\" class=\"empty-replies\">\n              <image src=\"/static/icon/no-messages.png\" mode=\"\" class=\"empty-image\"></image>\n              <!-- <view slot=\"message\">还没有人回复，快来抢沙发吧~</view> -->\n            </view>\n            <view v-else class=\"reply-item\" v-for=\"(reply, index) in replies\" :key=\"index\" :id=\"`reply-${index}`\">\n              <view class=\"reply-user-info\">\n                <image :src=\"reply.user.avatar\" mode=\"aspectFill\" :lazy-load=\"true\" class=\"reply-avatar\"></image>\n                <view class=\"reply-user-meta\">\n                  <view class=\"reply-nickname\">\n                    {{ reply.user.nickname }}\n                    <text v-if=\"reply.reply_to\" class=\"reply-text\">回复</text>\n                    <text v-if=\"reply.reply_to\" class=\"reply-to\">{{ reply.reply_to.nickname }}</text>\n                  </view>\n                  <view class=\"reply-time\">{{ formatTime(reply.created_at) }}</view>\n                </view>\n                <view class=\"reply-like\" @tap=\"likeReply(reply, index)\">\n                  <u-icon :name=\"reply.is_liked ? 'heart-fill' : 'heart'\" :color=\"reply.is_liked ? '#f56c6c' : '#999'\"\n                    size=\"24\"></u-icon>\n                  <text>{{ reply.likes }}</text>\n                </view>\n              </view>\n              <view class=\"reply-content\" @tap=\"replyToComment(reply)\">\n                <text>{{ reply.showFullContent ? reply.content : (reply.content.length > 100 ? reply.content.slice(0,\n                  100) + '...' : reply.content) }}</text>\n                <view v-if=\"reply.content.length > 100\" class=\"expand-btn\" @tap.stop=\"toggleReplyContent(reply, index)\">\n                  {{ reply.showFullContent ? '收起' : '展开' }}\n                </view>\n              </view>\n              <view class=\"reply-actions\">\n                <view class=\"reply-reply\" @tap=\"replyToComment(reply)\">\n                  <image src=\"/static/icon/chat.png\" mode=\"aspectFill\"></image>\n                  <!-- <text>回复</text> -->\n                </view>\n                <!-- 添加回复删除按钮，仅当当前用户是回复者时显示 -->\n                <!-- <view v-if=\"isReplyOwner(reply)\" class=\"reply-delete\" @tap=\"showDeleteReplyConfirm(reply, index)\">\n                  <u-icon name=\"trash\" color=\"#999\" size=\"24\"></u-icon>\n                  <text>删除</text>\n                </view> -->\n                <view class=\"more-btn\" @tap.stop=\"showMoreOptions(reply)\">\n                  <image src=\"/static/icon/more.png\" mode=\"aspectFill\"></image>\n                </view>\n              </view>\n            </view>\n\n            <!-- 加载更多状态 - 使用骨架屏优化 -->\n            <view v-if=\"pagination.loading\" class=\"loading-more-skeleton\">\n              <!-- 显示回复骨架屏而不是loading图标 -->\n              <reply-skeleton v-for=\"n in 4\" :key=\"n\"></reply-skeleton>\n            </view>\n            <view v-else-if=\"!pagination.hasMore && replies.length > 0\" class=\"no-more\">\n              <text>没有更多回复了</text>\n            </view>\n          </view>\n        </view>\n      </block>\n    </scroll-view>\n\n    <!-- 蒙版层：回复状态或键盘弹出时显示 -->\n    <view v-if=\"isReplying || isKeyboardShow\" class=\"mask-layer\"\n      @tap=\"isReplying ? cancelReply() : hideMaskAndKeyboard()\"></view>\n\n    <!-- 底部输入框 -->\n    <view class=\"input-container\" :style=\"{ bottom: inputContainerBottom + 'px' }\">\n      <comment-input v-model=\"replyText\" :placeholder=\"inputPlaceholder\" :use-image-button=\"true\" @send=\"sendReply\"\n        ref=\"commentInput\" @focus=\"onInputFocus\" @blur=\"onInputBlur\" />\n    </view>\n\n    <!-- 更多操作弹窗 -->\n    <u-popup v-model=\"showMorePopup\" mode=\"bottom\" border-radius=\"30\">\n      <view class=\"action-popup\">\n        <view class=\"action-item reply\" @tap=\"replyFromMore\">\n          <view class=\"action-icon\">\n            <image src=\"/static/icon/chat-1.png\" mode=\"aspectFill\"></image>\n          </view>\n          <text>回复</text>\n        </view>\n        <view class=\"action-item copy\" @tap=\"copyComment\">\n          <view class=\"action-icon\">\n            <image src=\"/static/icon/copy.png\" mode=\"aspectFill\"></image>\n          </view>\n          <text>复制</text>\n        </view>\n        <view v-if=\"currentMoreComment && isReplyOwner(currentMoreComment)\" class=\"action-item report block\"\n          @tap=\"deleteReply\">\n          <view class=\"action-icon\">\n            <image src=\"/static/icon/delete.png\" mode=\"aspectFill\"></image>\n          </view>\n          <text>删除</text>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport commentApi from '@/config/comment.api.js'\nimport topicApi from '@/config/topic.api.js'\nimport dayjs from 'dayjs'\nimport relativeTime from 'dayjs/plugin/relativeTime'\nimport 'dayjs/locale/zh-cn'\nimport CommentInput from '../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue'\nimport ReplySkeleton from './components/ReplySkeleton.vue'\n\ndayjs.extend(relativeTime)\ndayjs.locale('zh-cn')\n\nexport default {\n  components: {\n    CommentInput,\n    ReplySkeleton\n  },\n  data() {\n    return {\n      commentId: null,\n      userId: '',\n      loading: true,\n      isRefreshing: false,\n      loadingMore: false,\n      hasMore: true,\n      page: 1,\n      limit: 10,\n      comment: {\n        id: '',\n        content: '',\n        created_at: '',\n        likes: 0,\n        is_liked: false,\n        user: {\n          id: '',\n          nickname: '',\n          avatar: '',\n          level: 0\n        }\n      },\n      replies: [],\n      replyCount: 0,\n      replyText: '',\n      currentReplyTo: null,\n      inputPlaceholder: '发表您的评论...',\n      sortBy: 'hot',\n      scrollViewHeight: 'calc(90vh - 110rpx)',\n      showFullContent: false,\n      showMorePopup: false, // 更多操作弹窗\n      currentMoreComment: null, // 当前操作的评论\n      isReplying: false, // 是否处于回复状态\n      keyboardHeight: 0, // 键盘高度\n      inputContainerBottom: 0, // 输入框容器底部距离\n      isKeyboardShow: false, // 键盘是否显示\n      scrollTop: 0, // scroll-view的滚动位置\n      scrollIntoView: '', // scroll-view的滚动到指定元素\n      // 分页相关数据\n      pagination: {\n        page: 1,\n        pageSize: 10,\n        hasMore: true,\n        loading: false\n      },\n      isLoadingMore: false, // 是否正在加载更多\n      loadingText: '加载中...' // 加载提示文本\n    }\n  },\n  computed: {\n    // 判断当前用户是否是评论所有者\n    isCommentOwner() {\n      console.log(this.comment.user, this.userId)\n      return this.comment.user && this.userId && String(this.comment.user.id) == String(this.userId);\n    }\n  },\n  onLoad(options) {\n    if (options.id) {\n      // 修复：将commentId转换为Long兼容的数字类型\n      this.commentId = Number(options.id);\n\n      // 修复：将userId转换为Long兼容的数字类型\n      const userIdStr = options.userId || uni.getStorageSync('userid') || '18';\n      this.userId = Number(userIdStr);\n\n      console.log('🔍 评论详情页参数:', {\n        commentId: this.commentId,\n        userId: this.userId,\n        commentIdType: typeof this.commentId,\n        userIdType: typeof this.userId,\n        commentIdValue: this.commentId,\n        userIdValue: this.userId\n      });\n      this.fetchCommentDetail();\n      this.setScrollViewHeight();\n      this.setupKeyboardListener();\n    } else {\n      uni.showToast({\n        title: '评论ID不存在',\n        icon: 'none'\n      });\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n    }\n  },\n\n  // 添加生命周期钩子来管理键盘监听\n  onShow() {\n    // 页面显示时，确保只有一个有效的键盘监听\n    uni.offKeyboardHeightChange(); // 先移除可能存在的监听\n    this.setupKeyboardListener();\n  },\n\n  onHide() {\n    // 页面隐藏时移除监听\n    uni.offKeyboardHeightChange();\n    console.log('页面隐藏，取消键盘高度监听');\n  },\n\n  onUnload() {\n    // 页面卸载时移除监听\n    uni.offKeyboardHeightChange();\n    console.log('页面卸载，取消键盘高度监听');\n  },\n  methods: {\n    // 兼容性时间戳函数 - 替代performance.now()\n    getTimestamp() {\n      // 微信小程序环境使用Date.now()\n      if (typeof performance !== 'undefined' && performance.now) {\n        return performance.now();\n      }\n      return Date.now();\n    },\n\n    // 设置键盘高度监听器\n    setupKeyboardListener() {\n      // #ifdef MP-WEIXIN\n      uni.onKeyboardHeightChange(res => {\n        console.log('键盘高度变化:', res.height);\n        this.keyboardHeight = res.height;\n        this.isKeyboardShow = res.height > 0;\n\n        if (res.height > 0) {\n          // 键盘弹出，调整输入框位置\n          this.inputContainerBottom = res.height;\n        } else {\n          // 键盘收起，恢复输入框位置\n          this.inputContainerBottom = 0;\n        }\n      });\n      // #endif\n\n      // #ifndef MP-WEIXIN\n      // 非微信小程序环境的兼容处理\n      console.log('非微信小程序环境，使用默认键盘处理');\n      // #endif\n\n      console.log('键盘高度监听器已设置');\n    },\n\n    // 输入框获取焦点\n    onInputFocus(e) {\n      console.log('输入框获取焦点');\n      this.isKeyboardShow = true;\n\n      // 微信小程序中，键盘弹出时的额外处理\n      // #ifdef MP-WEIXIN\n      // 延时获取键盘高度，因为键盘弹出需要时间\n      setTimeout(() => {\n        if (this.keyboardHeight === 0) {\n          // 如果监听器没有获取到键盘高度，使用默认值\n          this.keyboardHeight = 280; // 微信小程序默认键盘高度\n          this.inputContainerBottom = this.keyboardHeight;\n        }\n      }, 300);\n      // #endif\n    },\n\n    // 输入框失去焦点\n    onInputBlur(e) {\n      console.log('输入框失去焦点');\n      this.isKeyboardShow = false;\n\n      // 延时重置，确保键盘完全收起\n      setTimeout(() => {\n        if (!this.isKeyboardShow) {\n          this.keyboardHeight = 0;\n          this.inputContainerBottom = 0;\n        }\n      }, 100);\n    },\n\n    // 隐藏蒙版层并收起键盘\n    hideMaskAndKeyboard() {\n      console.log('点击蒙版层，收起键盘');\n\n      // 让输入框失去焦点\n      if (this.$refs.commentInput) {\n        this.$refs.commentInput.blur();\n      }\n\n      // 强制隐藏键盘\n      uni.hideKeyboard();\n\n      // 重置键盘状态\n      this.isKeyboardShow = false;\n      this.keyboardHeight = 0;\n      this.inputContainerBottom = 0;\n    },\n\n    // 取消回复\n    cancelReply() {\n      // 重置回复状态和相关数据\n      this.isReplying = false;\n      this.currentReplyTo = null;\n      this.replyText = '';\n      this.inputPlaceholder = '发表您的评论...';\n      // 确保键盘收起\n      uni.hideKeyboard();\n      // 模拟失去焦点，确保下次可以重新获取焦点\n      if (this.$refs.commentInput) {\n        this.$refs.commentInput.$emit('blur');\n      }\n    },\n    // 判断当前用户是否是回复所有者\n    isReplyOwner(reply) {\n      return reply && reply.user && this.userId && String(reply.user.id) == String(this.userId);\n    },\n\n    // 显示删除评论确认框\n    showDeleteCommentConfirm() {\n      uni.showModal({\n        title: '删除评论',\n        content: '确认要删除这条评论吗？删除后无法恢复',\n        confirmText: '删除',\n        confirmColor: '#f56c6c',\n        success: res => {\n          if (res.confirm) {\n            this.deleteComment();\n          }\n        }\n      });\n    },\n    // 显示更多操作弹窗\n    showMoreOptions(item) {\n      this.currentMoreComment = item;\n      this.showMorePopup = true;\n    },\n\n    // 从更多操作弹窗中点击回复\n    replyFromMore() {\n      if (this.currentMoreComment) {\n        this.showMorePopup = false;\n        // 等待更多操作弹窗关闭后再打开回复弹窗\n        setTimeout(() => {\n          // 先确保重置状态\n          if (this.$refs.commentInput) {\n            this.$refs.commentInput.autoFocus = false;\n          }\n\n          // 设置回复状态\n          this.replyToComment(this.currentMoreComment);\n          this.isReplying = true;\n\n          // 再次延时确保输入框聚焦，因为replyToComment中的聚焦可能因为弹窗动画而失效\n          setTimeout(() => {\n            if (this.$refs.commentInput) {\n              this.$refs.commentInput.focus();\n            }\n          }, 150);\n        }, 300);\n      }\n    },\n\n    // 复制评论内容\n    copyComment() {\n      if (!this.currentMoreComment) return;\n\n      uni.setClipboardData({\n        data: this.currentMoreComment.content,\n        success: () => {\n          uni.showToast({\n            title: '复制成功',\n            icon: 'success'\n          });\n          this.showMorePopup = false;\n        }\n      });\n    },\n\n    // 删除回复\n    deleteReply() {\n      if (!this.currentMoreComment) return;\n\n      uni.showModal({\n        title: '删除回复',\n        content: '确认要删除这条回复吗？删除后无法恢复',\n        confirmText: '删除',\n        confirmColor: '#f56c6c',\n        success: res => {\n          if (res.confirm) {\n            // 调用API删除回复\n            commentApi.deleteReply(Number(this.currentMoreComment.id), {\n              userId: this.userId\n            }).then(res => {\n              console.log('删除回复API返回数据:', JSON.stringify(res));\n              if (res.code === 0) {\n                // 从列表中移除已删除的回复\n                const index = this.replies.findIndex(item => item.id == this.currentMoreComment.id);\n                if (index > -1) {\n                  this.replies.splice(index, 1);\n                }\n                // 减少回复计数\n                this.replyCount--;\n\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                });\n\n                // 关闭弹窗\n                this.showMorePopup = false;\n              } else {\n                uni.showToast({\n                  title: res.message || '删除失败',\n                  icon: 'none'\n                });\n              }\n            }).catch(err => {\n              console.error('删除回复失败:', err);\n              uni.showToast({\n                title: '网络请求错误',\n                icon: 'none'\n              });\n            });\n          }\n        }\n      });\n    },\n\n    // 删除评论\n    deleteComment() {\n      console.log('删除评论ID:', this.commentId, '类型:', typeof this.commentId);\n      commentApi.deleteComment(Number(this.commentId), {\n        userId: this.userId\n      }).then(res => {\n        console.log('删除评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          });\n\n          // 删除成功后返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 1500);\n        } else {\n          uni.showToast({\n            title: res.message || '删除失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('删除评论失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n\n    // 显示删除回复确认框\n    showDeleteReplyConfirm(reply, index) {\n      uni.showModal({\n        title: '删除回复',\n        content: '确认要删除这条回复吗？删除后无法恢复',\n        confirmText: '删除',\n        confirmColor: '#f56c6c',\n        success: res => {\n          if (res.confirm) {\n            this.deleteReply(reply, index);\n          }\n        }\n      });\n    },\n\n    // 删除回复\n    deleteReply(reply, index) {\n      console.log('删除回复:', reply, index);\n      commentApi.deleteReply(Number(reply.id), {\n        userId: this.userId\n      }).then(res => {\n        console.log('删除回复API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          // 从列表中移除已删除的回复\n          this.replies.splice(index, 1);\n          // 减少回复计数\n          this.replyCount--;\n\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          });\n        } else {\n          uni.showToast({\n            title: res.message || '删除失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('删除回复失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n\n    setScrollViewHeight() {\n      const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 25;\n      const inputBoxHeight = 110; // 底部输入框高度（rpx转px）\n      const filterBarHeight = 100; // 筛选栏高度（rpx转px）\n\n      // 修改计算方式，不再减去输入框高度，因为输入框现在是固定定位\n      const scrollHeight = `calc(90vh - ${statusBarHeight}px - 20rpx)`;\n      this.scrollViewHeight = scrollHeight;\n      console.log('设置滚动视图高度:', scrollHeight);\n    },\n    goBack() {\n      uni.navigateBack();\n    },\n    fetchCommentDetail() {\n      this.loading = true;\n\n      // 调用API获取评论详情\n      commentApi.getCommentDetail(this.commentId, {\n        userId: this.userId,\n        sort: this.sortBy,\n        current: 1,\n        pageSize: 1 // 只获取评论信息，不包含回复\n      }).then(res => {\n        console.log('评论详情API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          // 设置评论详情\n          const commentData = res.data.comment || {};\n          console.log('原始评论数据:', JSON.stringify(commentData));\n\n          // 转换字段名\n          commentData.created_at = commentData.createdAt;\n          commentData.is_liked = commentData.isLiked;\n          commentData.reply_count = commentData.replyCount;\n\n          // 处理用户头像为空的情况\n          if (!commentData.user.avatar) {\n            commentData.user.avatar = '/static/images/toux.png';\n          }\n\n          console.log('处理后的评论数据:', JSON.stringify(commentData));\n\n          this.comment = commentData;\n          this.replyCount = commentData.replyCount || 0;\n\n          // 获取评论回复列表\n          this.fetchRepliesOnly();\n\n          // 设置loading为false，因为主评论已经加载完成\n          this.loading = false;\n        } else {\n          uni.showToast({\n            title: res.message || '获取评论详情失败',\n            icon: 'none'\n          });\n\n          // 失败后返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 1500);\n        }\n      }).catch(err => {\n        console.error('获取评论详情失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n\n        // 失败后返回上一页\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      });\n    },\n    fetchReplies() {\n      if (this.page === 1) {\n        // 第一页不显示加载更多\n        this.loading = true;\n      } else {\n        this.loadingMore = true;\n      }\n\n      // 调用API获取回复列表\n      commentApi.getCommentDetail(this.commentId, {\n        userId: this.userId,\n        sort: this.sortBy,\n        current: this.page,\n        pageSize: this.limit\n      }).then(res => {\n        console.log('评论回复API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          const data = res.data;\n\n          // 获取回复列表，可能在replies.items或replies.records中\n          const replyItems = data.replies.items || [];\n\n          console.log('原始回复列表:', JSON.stringify(replyItems));\n          if (replyItems.length === 0) {\n            this.replies = [];\n            console.log('replies.length=', this.replies.length)\n            return;\n          }\n\n          // 转换回复列表中的字段名\n          replyItems.forEach(reply => {\n            reply.created_at = reply.createdAt;\n            reply.is_liked = reply.isLiked;\n            // 添加控制内容展开/收起的属性\n            reply.showFullContent = false;\n\n            // 处理replyTo字段\n            if (reply.replyTo) {\n              reply.reply_to = {\n                id: reply.replyTo.id,\n                nickname: reply.replyTo.nickname\n              };\n            } else {\n              reply.reply_to = undefined;\n            }\n\n            // 处理用户头像为空的情况\n            if (!reply.user.avatar) {\n              reply.user.avatar = '/static/images/toux.png';\n            }\n          });\n\n          console.log('处理后的回复列表:', JSON.stringify(replyItems));\n\n          if (this.page === 1) {\n            // 第一页直接替换\n            this.replies = replyItems;\n            this.replyCount = (data.replies && data.replies.total) || 0;\n          } else {\n            // 加载更多，追加数据\n            this.replies = [...this.replies, ...replyItems];\n          }\n\n          // 是否还有更多数据\n          this.hasMore = data.replies && data.replies.has_more;\n          if (this.hasMore) {\n            this.page++;\n          }\n        } else {\n          uni.showToast({\n            title: res.message || '获取回复列表失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('获取回复列表失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      }).finally(() => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.isRefreshing = false;\n      });\n    },\n    // 懒加载更多回复（优化版）\n    loadMoreReplies() {\n      console.log('🔄 触发回复懒加载');\n\n      // 防抖处理，避免重复请求\n      if (this.pagination.loading || !this.pagination.hasMore) {\n        console.log('⚠️ 回复正在加载或已无更多数据，跳过请求');\n        return;\n      }\n\n      // 性能优化：增强防抖处理，减少低端设备的请求频率\n      const now = Date.now();\n      const lastRequestTime = this.lastRequestTime || 0;\n      if (now - lastRequestTime < 600) { // 增加到600ms，减少低端设备的负担\n        console.log('⚠️ 请求过于频繁，跳过回复懒加载');\n        return;\n      }\n      this.lastRequestTime = now;\n\n      // 设置加载状态\n      this.pagination.loading = true;\n      this.loadingText = '加载更多回复...';\n\n      // 计算下一页页码\n      const nextPage = this.pagination.page + 1;\n      const startTime = this.getTimestamp();\n\n      console.log(`📄 回复当前页码: ${this.pagination.page}, 请求页码: ${nextPage}`);\n\n      // 使用正确的API方法获取回复分页数据\n      // 修复：使用getCommentDetail而不是getCommentReplies，因为后者可能不存在\n      const params = {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        sort: this.sortBy,\n        current: nextPage,  // 使用current而不是page\n        pageSize: this.pagination.pageSize\n      };\n\n      console.log('📋 回复分页请求参数:', JSON.stringify(params));\n\n      // 调用API获取更多回复 - 修复数据类型为Long兼容\n      commentApi.getCommentDetail(Number(this.commentId), params).then(res => {\n        const endTime = this.getTimestamp();\n        const loadTime = endTime - startTime;\n\n        console.log(`✅ 回复分页API返回，耗时: ${loadTime.toFixed(2)}ms`);\n\n        if (res.code === 0) {\n          const data = res.data;\n\n          // 性能优化：减少日志输出\n          console.log('📊 回复分页数据概览:', {\n            repliesCount: data.replies && data.replies.items ? data.replies.items.length : 0,\n            total: data.replies ? data.replies.total : 0,\n            hasMore: data.replies ? data.replies.hasMore : false\n          });\n\n          // 获取回复列表，处理不同的数据结构（优化版）\n          let rawReplies = [];\n          if (data.replies) {\n            if (data.replies.items && Array.isArray(data.replies.items)) {\n              rawReplies = data.replies.items;\n            } else if (data.replies.records && Array.isArray(data.replies.records)) {\n              rawReplies = data.replies.records;\n            } else if (Array.isArray(data.replies)) {\n              rawReplies = data.replies;\n            }\n          }\n\n          const newReplies = this.processReplyDataOptimized(rawReplies);\n\n          if (newReplies && newReplies.length > 0) {\n            // 检查是否有重复数据（优化版）\n            const existingIds = new Set(this.replies.map(reply => reply.id));\n            const filteredReplies = newReplies.filter(reply => !existingIds.has(reply.id));\n\n            console.log(`🔄 回复去重: 原始${newReplies.length}条，去重后${filteredReplies.length}条`);\n\n            if (filteredReplies.length > 0) {\n              // 追加新回复到列表（优化：使用concat）\n              this.replies = this.replies.concat(filteredReplies);\n\n              // 更新分页信息\n              this.pagination.page = nextPage;\n\n              console.log(`✅ 回复加载成功，页码: ${nextPage}，新增: ${filteredReplies.length}条`);\n            }\n\n            // 检查是否还有更多数据\n            if (data.replies && data.replies.hasMore === false || newReplies.length < this.pagination.pageSize) {\n              this.pagination.hasMore = false;\n              console.log('🔚 回复已加载完毕');\n            }\n          } else {\n            // 没有更多数据\n            this.pagination.hasMore = false;\n            console.log('🔚 回复无更多数据');\n          }\n        } else {\n          // API返回错误\n          console.error('❌ 回复API返回错误:', res.message);\n          uni.showToast({\n            title: res.message || '加载失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('❌ 回复懒加载失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      }).finally(() => {\n        // 重置加载状态\n        this.pagination.loading = false;\n        this.loadingText = '加载中...';\n        console.log('🔄 回复加载状态重置');\n      });\n    },\n    onRefresh() {\n      this.isRefreshing = true;\n\n      // 重置分页状态\n      this.pagination = {\n        page: 1,\n        pageSize: 10,\n        hasMore: true,\n        loading: false\n      };\n\n      // 下拉刷新时仅刷新回复列表\n      this.fetchRepliesOnly();\n      setTimeout(() => {\n        this.isRefreshing = false;\n      }, 500);\n    },\n    likeComment() {\n      // 点赞/取消点赞主评论\n      const action = this.comment.is_liked ? 'unlike' : 'like';\n\n      commentApi.likeComment(Number(this.comment.id), {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        action\n      }).then(res => {\n        console.log('点赞主评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          // 更新评论点赞状态和数量\n          this.comment.is_liked = res.data.isLiked || res.data.is_liked;\n          this.comment.likes = res.data.likes;\n        } else {\n          uni.showToast({\n            title: res.message || '操作失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('点赞操作失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n    likeReply(reply, index) {\n      // 点赞/取消点赞回复\n      const action = reply.is_liked ? 'unlike' : 'like';\n\n      commentApi.likeReply(Number(reply.id), {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        action\n      }).then(res => {\n        console.log('点赞回复API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          // 更新回复点赞状态和数量\n          this.replies[index].is_liked = res.data.isLiked || res.data.is_liked;\n          this.replies[index].likes = res.data.likes;\n        } else {\n          uni.showToast({\n            title: res.message || '操作失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('点赞回复操作失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n    replyToMain() {\n      // 回复主评论\n      // 先重置当前状态\n      if (this.$refs.commentInput) {\n        this.$refs.commentInput.autoFocus = false;\n      }\n\n      this.currentReplyTo = null;\n      this.inputPlaceholder = '发表您的评论...';\n      this.isReplying = true; // 设置为回复状态\n\n      // 自动聚焦输入框 - 使用延时确保在DOM更新后执行\n      setTimeout(() => {\n        if (this.$refs.commentInput) {\n          this.$refs.commentInput.focus();\n        }\n      }, 150);\n    },\n    replyToComment(reply) {\n      // 回复某条回复\n      // 先重置当前状态\n      if (this.$refs.commentInput) {\n        this.$refs.commentInput.autoFocus = false;\n      }\n\n      this.currentReplyTo = reply;\n      this.isReplying = true; // 设置为回复状态\n      console.log('replyToComment:', JSON.stringify(reply));\n      console.log('currentReplyTo:', JSON.stringify(this.currentReplyTo));\n\n      // 确保reply.user存在且能访问\n      if (reply && reply.user) {\n        console.log('回复用户信息:', JSON.stringify(reply.user));\n        this.inputPlaceholder = `@ ${reply.user.nickname}:`;\n      } else {\n        console.log('回复用户信息不存在');\n        this.inputPlaceholder = '回复评论...';\n      }\n\n      // 自动聚焦输入框 - 使用延时确保在DOM更新后执行\n      setTimeout(() => {\n        if (this.$refs.commentInput) {\n          this.$refs.commentInput.focus();\n        }\n      }, 150);\n    },\n    sendReply() {\n      if (this.replyText.length > 1000) {\n        uni.showToast({\n          title: '评论字数不能超过1000字',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (!this.replyText.trim()) return;\n\n      const replyData = {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        commentId: Number(this.commentId), // 使用Number确保Long兼容性\n        content: this.replyText.trim(),\n        replyToId: this.currentReplyTo ? Number(this.currentReplyTo.userId) : null // 使用Number确保Long兼容性\n      };\n\n      console.log('🚀 发送回复数据:', JSON.stringify(replyData));\n      console.log('📊 回复数据类型检查:', {\n        userId: typeof replyData.userId,\n        userIdValue: replyData.userId,\n        commentId: typeof replyData.commentId,\n        commentIdValue: replyData.commentId,\n        replyToId: typeof replyData.replyToId,\n        replyToIdValue: replyData.replyToId,\n        content: typeof replyData.content\n      });\n\n      commentApi.replyComment(replyData.commentId, replyData).then(res => {\n        console.log('发送回复API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          uni.showToast({\n            title: '回复成功',\n            icon: 'success'\n          });\n\n          // 清空输入框\n          if (this.$refs.commentInput) {\n            this.$refs.commentInput.clear();\n          } else {\n            this.replyText = '';\n          }\n\n          // 重置回复状态\n          this.currentReplyTo = null;\n          this.inputPlaceholder = '发表您的评论...';\n          this.isReplying = false;\n\n          // 刷新回复列表 - 重置分页状态\n          this.pagination = {\n            page: 1,\n            pageSize: 10,\n            hasMore: true,\n            loading: false\n          };\n          this.fetchRepliesOnly();\n        } else {\n          uni.showToast({\n            title: res.message || '回复失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('发送回复失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n    changeSort(type) {\n      // 不再添加触觉反馈\n\n      if (this.sortBy === type) return;\n      this.sortBy = type;\n\n      // 重置分页状态\n      this.pagination = {\n        page: 1,\n        pageSize: 10,\n        hasMore: true,\n        loading: true\n      };\n\n      // 切换排序方式，只重新加载回复列表，不刷新整个页面\n      this.loading = false; // 确保不显示整页加载状态\n      this.fetchRepliesOnly();\n    },\n\n    // 只获取回复列表，不重新加载整个页面\n    fetchRepliesOnly() {\n      console.log('🔄 开始获取回复列表，当前页码:', this.pagination.page);\n\n      // 调用API获取回复列表 - 修复数据类型为Long兼容\n      commentApi.getCommentDetail(Number(this.commentId), {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        sort: this.sortBy,\n        current: this.pagination.page,  // 使用当前页码，不是page+1\n        pageSize: this.pagination.pageSize\n      }).then(res => {\n        console.log('✅ 评论回复API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          const data = res.data;\n\n          // 获取回复列表，处理不同的数据结构\n          let replyItems = [];\n          if (data.replies) {\n            if (data.replies.items && Array.isArray(data.replies.items)) {\n              replyItems = data.replies.items;\n            } else if (data.replies.records && Array.isArray(data.replies.records)) {\n              replyItems = data.replies.records;\n            } else if (Array.isArray(data.replies)) {\n              replyItems = data.replies;\n            }\n          }\n\n          console.log('📊 原始回复列表数量:', replyItems.length);\n\n          // 使用processReplyData处理回复数据\n          const processedReplies = this.processReplyData(replyItems);\n\n          if (this.pagination.page === 1) {\n            // 第一页，直接替换\n            this.replies = processedReplies;\n            console.log('📝 第一页回复数据已替换，数量:', processedReplies.length);\n          } else {\n            // 后续页，追加到现有列表\n            const existingIds = this.replies.map(reply => reply.id);\n            const filteredReplies = processedReplies.filter(reply => !existingIds.includes(reply.id));\n            this.replies = [...this.replies, ...filteredReplies];\n            console.log('📝 后续页回复数据已追加，去重后数量:', filteredReplies.length);\n          }\n\n          // 更新回复总数\n          this.replyCount = (data.replies && data.replies.total) || 0;\n\n          // 检查是否还有更多数据\n          if (processedReplies.length < this.pagination.pageSize) {\n            this.pagination.hasMore = false;\n            console.log('🔚 回复数据已加载完毕');\n          } else {\n            this.pagination.hasMore = true;\n            console.log('📄 还有更多回复数据');\n          }\n\n          console.log(`✅ 回复列表加载成功，当前页:${this.pagination.page}，总数:${this.replies.length}条`);\n        } else {\n          console.error('❌ 获取回复列表API错误:', res.message);\n          uni.showToast({\n            title: res.message || '获取回复列表失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('❌ 获取回复列表失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      }).finally(() => {\n        this.pagination.loading = false;\n        console.log('🔄 回复列表加载状态重置');\n      });\n    },\n    formatTime(timeString) {\n      if (!timeString) return '';\n      return dayjs(timeString).fromNow();\n    },\n    getLevelColor(level) {\n      const colors = {\n        0: '#cccbc8', // 灰色\n        1: '#c6ffe6',\n        2: '#61bc84', // 绿色\n        3: '#4d648d',\n        4: '#1F3A5F',\n        5: '#9c27b0',\n        6: '#6c35de',\n        7: '#ffd299', // 橙色\n        8: '#FF7F50', // 红色\n        9: '#f35d74', // 紫色\n        10: '#bb2649' // 粉色\n      };\n      return colors[level] || '#8dc63f';\n    },\n    // 切换评论内容的展开/收起状态\n    toggleContent() {\n      const wasExpanded = this.showFullContent;\n      this.showFullContent = !this.showFullContent;\n\n      // 如果是从展开状态收起，则滚动到主评论顶部\n      if (wasExpanded) {\n        this.scrollToMainComment();\n      }\n    },\n\n    // 切换回复内容的展开/收起状态\n    toggleReplyContent(reply, index) {\n      const wasExpanded = reply.showFullContent;\n\n      // Vue无法直接检测到通过索引设置的数组变化，需要使用Vue.set或$set方法\n      if (!reply.showFullContent) {\n        this.$set(reply, 'showFullContent', true);\n      } else {\n        this.$set(reply, 'showFullContent', false);\n      }\n\n      // 如果是从展开状态收起，则滚动到回复顶部\n      if (wasExpanded) {\n        this.scrollToReply(index);\n      }\n    },\n\n    // 滚动到主评论顶部位置\n    scrollToMainComment() {\n      console.log('🎯 开始滚动到主评论');\n\n      // 方法1: 使用scrollIntoView\n      this.scrollToElementByScrollIntoView('main-comment');\n\n      // 方法2: 备用方案\n      setTimeout(() => {\n        this.scrollToElementByScrollTop('main-comment');\n      }, 100);\n    },\n\n    // 滚动到指定回复的顶部位置\n    scrollToReply(index) {\n      const replyId = `reply-${index}`;\n      console.log(`🎯 开始滚动到回复 - ${replyId}`);\n\n      // 方法1: 使用scrollIntoView\n      this.scrollToElementByScrollIntoView(replyId);\n\n      // 方法2: 备用方案\n      setTimeout(() => {\n        this.scrollToElementByScrollTop(replyId);\n      }, 100);\n    },\n\n    // 方法1: 使用scrollIntoView滚动到元素\n    scrollToElementByScrollIntoView(elementId) {\n      console.log(`📍 使用scrollIntoView滚动到 - ${elementId}`);\n\n      this.$nextTick(() => {\n        setTimeout(() => {\n          // 设置scroll-into-view属性\n          this.scrollIntoView = elementId;\n\n          // 清除scrollIntoView，避免影响后续滚动\n          setTimeout(() => {\n            this.scrollIntoView = '';\n          }, 500);\n\n          console.log(`✅ scrollIntoView设置成功 - ${elementId}`);\n        }, 150);\n      });\n    },\n\n    // 方法2: 使用scroll-top属性滚动到元素\n    scrollToElementByScrollTop(elementId) {\n      console.log(`📍 使用scroll-top滚动到 - ${elementId}`);\n\n      this.$nextTick(() => {\n        const query = uni.createSelectorQuery().in(this);\n\n        query.select('.comment-container').boundingClientRect();\n        query.select(`#${elementId}`).boundingClientRect();\n\n        query.exec((res) => {\n          console.log(`📊 scroll-top查询结果 - ${elementId}:`, res);\n\n          if (res && res.length >= 2) {\n            const scrollViewRect = res[0];\n            const elementRect = res[1];\n\n            if (scrollViewRect && elementRect) {\n              const relativeTop = elementRect.top - scrollViewRect.top;\n              const topOffset = elementId === 'main-comment' ? 60 : 80;\n              const targetScrollTop = Math.max(0, relativeTop - topOffset);\n\n              console.log(`📐 scroll-top计算 - ${elementId}:`, {\n                scrollViewTop: scrollViewRect.top,\n                elementTop: elementRect.top,\n                relativeTop: relativeTop,\n                targetScrollTop: targetScrollTop\n              });\n\n              // 强制更新scrollTop\n              this.scrollTop = 0;\n              this.$nextTick(() => {\n                this.scrollTop = targetScrollTop;\n                console.log(`✅ scroll-top设置成功 - ${elementId}, 位置: ${targetScrollTop}`);\n              });\n            }\n          }\n        });\n      });\n    },\n\n    // 调试方法：检查DOM元素是否存在\n    debugScrollElements(elementId = 'main-comment') {\n      console.log(`🔍 调试滚动元素 - ${elementId}`);\n\n      const query = uni.createSelectorQuery().in(this);\n      query.select(`#${elementId}`).boundingClientRect();\n      query.select('.comment-container').boundingClientRect();\n\n      query.exec((res) => {\n        console.log('🔍 调试结果:', {\n          elementId: elementId,\n          targetElement: res[0],\n          scrollViewElement: res[1],\n          hasTarget: !!res[0],\n          hasScrollView: !!res[1]\n        });\n      });\n    },\n\n    // 处理回复数据\n    processReplyData(replies) {\n      if (!replies || !Array.isArray(replies)) {\n        console.warn('回复数据为空或格式错误');\n        return [];\n      }\n\n      console.log(`处理回复数据，回复数量:`, replies.length);\n\n      return replies.map(reply => {\n        if (!reply) return null;\n\n        // 转换字段名\n        reply.created_at = reply.createdAt || reply.created_at || new Date().toISOString();\n        reply.is_liked = reply.isLiked || reply.is_liked || false;\n        reply.likes = reply.likes || 0;\n\n        // 确保user对象存在\n        if (!reply.user) {\n          reply.user = {\n            id: 0,\n            nickname: '未知用户',\n            avatar: '/static/images/toux.png',\n            level: 0\n          };\n        } else {\n          // 处理用户头像为空的情况\n          if (!reply.user.avatar) {\n            reply.user.avatar = '/static/images/toux.png';\n          }\n\n          // 确保其他用户字段存在\n          reply.user.nickname = reply.user.nickname || '未知用户';\n          reply.user.level = reply.user.level || 0;\n        }\n\n        // 确保reply_to存在\n        if (reply.replyTo) {\n          reply.reply_to = reply.replyTo;\n        }\n\n        return reply;\n      }).filter(reply => reply !== null);\n    },\n\n    // 优化的回复数据处理方法\n    processReplyDataOptimized(replies) {\n      const startTime = this.getTimestamp();\n\n      if (!replies || !Array.isArray(replies)) {\n        console.warn('⚠️ 回复数据为空或格式错误');\n        return [];\n      }\n\n      console.log(`🔄 开始处理回复数据，数量: ${replies.length}`);\n\n      const processedReplies = replies.map(reply => {\n        if (!reply) return null;\n\n        // 优化：减少对象创建和属性复制\n        const processedReply = {\n          ...reply,\n          created_at: reply.createdAt || reply.created_at || new Date().toISOString(),\n          is_liked: reply.isLiked || reply.is_liked || false,\n          likes: reply.likes || 0,\n          showFullContent: false\n        };\n\n        // 确保user对象存在\n        if (!processedReply.user) {\n          processedReply.user = {\n            id: 0,\n            nickname: '未知用户',\n            avatar: '/static/images/toux.png',\n            level: 0\n          };\n        } else {\n          // 处理用户头像为空的情况\n          if (!processedReply.user.avatar) {\n            processedReply.user.avatar = '/static/images/toux.png';\n          }\n          processedReply.user.nickname = processedReply.user.nickname || '未知用户';\n          processedReply.user.level = processedReply.user.level || 0;\n        }\n\n        // 确保reply_to存在\n        if (reply.replyTo) {\n          processedReply.reply_to = reply.replyTo;\n        }\n\n        return processedReply;\n      }).filter(reply => reply !== null);\n\n      const endTime = this.getTimestamp();\n      console.log(`✅ 回复数据处理完成，耗时: ${(endTime - startTime).toFixed(2)}ms，处理数量: ${processedReplies.length}`);\n\n      return processedReplies;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.comment-detail {\n  display: flex;\n  flex-direction: column;\n  height: 94vh;\n  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);\n}\n\n/* 移除点击高亮效果 */\n.no-highlight {\n  -webkit-tap-highlight-color: transparent;\n}\n\n.filter-bar {\n  padding: 24rpx 32rpx;\n  background: rgba(255, 255, 255, 0.95);\n  /* 移除高消耗的backdrop-filter */\n  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);\n  /* 启用GPU加速 */\n  transform: translateZ(0);\n  will-change: transform;\n}\n\n.back-btn {\n  padding: 10rpx;\n}\n\n.page-title {\n  flex: 1;\n  font-size: 30rpx; /* 优化：从34rpx减小到30rpx，更符合移动端标准 */\n  font-weight: 600;\n  text-align: center;\n  margin-right: 44rpx;\n  /* 为了居中，右侧留出与左侧返回按钮相同的空间 */\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  letter-spacing: 0.5rpx;\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n.comment-container {\n  flex: 1;\n  position: relative;\n  padding: 24rpx;\n  width: auto;\n}\n\n.loading {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 120rpx 0;\n\n  text {\n    margin-top: 32rpx;\n    font-size: 30rpx;\n    color: #ff6b87;\n    font-weight: 500;\n    letter-spacing: 0.5rpx;\n  }\n}\n\n.main-comment {\n  background: rgba(255, 255, 255, 0.95);\n  /* 移除高消耗的backdrop-filter */\n  border-radius: 32rpx;\n  padding: 40rpx;\n  margin-bottom: 32rpx;\n  box-shadow: 0 6rpx 24rpx rgba(255, 105, 135, 0.08);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  /* 优化过渡效果，只对transform进行过渡 */\n  transition: transform 0.2s ease;\n  /* 启用GPU加速 */\n  transform: translateZ(0);\n  will-change: transform;\n\n  .user-info {\n    display: flex;\n    margin-bottom: 24rpx;\n\n    .avatar-wrap {\n      width: 88rpx;\n      height: 88rpx;\n      border-radius: 50%;\n      overflow: hidden;\n      margin-right: 28rpx;\n      border: 3rpx solid rgba(255, 107, 135, 0.2);\n      box-shadow: 0 6rpx 20rpx rgba(255, 107, 135, 0.15);\n      /* 优化过渡效果，只对transform进行过渡 */\n      transition: transform 0.2s ease;\n\n      &:active {\n        transform: scale(0.95);\n      }\n\n      .avatar {\n        width: 100%;\n        height: 100%;\n      }\n    }\n\n    .user-meta {\n      flex: 1;\n\n      .nickname {\n        font-size: 28rpx; /* 优化：从32rpx减小到28rpx，用户名更精致 */\n        font-weight: 600;\n        /* 备用颜色方案，确保在微信小程序中显示 */\n        color: #4a4a4a;\n        background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);\n        display: flex;\n        align-items: center;\n        letter-spacing: 0.3rpx;\n\n        /* 渐变文字效果（如果支持） */\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n        background-clip: text;\n\n        /* 微信小程序兼容性处理 */\n        @supports not (-webkit-background-clip: text) {\n          color: #4a4a4a !important;\n          background: none;\n        }\n\n        .user-level {\n          font-size: 22rpx;\n          color: #ffffff !important;\n          background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n          border-radius: 16rpx;\n          padding: 6rpx 16rpx;\n          margin-left: 16rpx;\n          font-weight: 600;\n          box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);\n          letter-spacing: 0.5rpx;\n          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n          /* 确保文字显示，移除可能导致兼容性问题的属性 */\n          -webkit-text-fill-color: initial;\n          -webkit-background-clip: initial;\n          background-clip: initial;\n        }\n      }\n\n      .time {\n        font-size: 26rpx;\n        color: #8a8a8a;\n        margin-top: 12rpx;\n        font-weight: 400;\n        opacity: 0.8;\n      }\n    }\n\n    .like-btn {\n      display: flex;\n      align-items: center;\n      padding: 12rpx 20rpx;\n      border-radius: 32rpx;\n      background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n      border: 1rpx solid rgba(255, 107, 135, 0.2);\n      transition: all 0.3s ease;\n      box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);\n      align-self: flex-start;\n      margin-top: 8rpx;\n\n      &:active {\n        background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);\n        transform: scale(0.95);\n        box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);\n      }\n\n      text {\n        font-size: 26rpx;\n        color: #ff6b87;\n        margin-left: 10rpx;\n        font-weight: 600;\n        letter-spacing: 0.3rpx;\n      }\n\n      /* 添加心形图标的样式 */\n      :deep(.u-icon__icon) {\n\n        &.uicon-heart-fill,\n        &.uicon-heart {\n          font-weight: bold;\n          transform: scale(1.1);\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        &.uicon-heart-fill {\n          /* 移除复杂的心跳动画和阴影效果，提升性能 */\n          color: #f56c6c;\n        }\n      }\n    }\n  }\n\n  .content {\n    font-size: 30rpx; /* 优化：从32rpx减小到30rpx，正文更符合移动端标准 */\n    line-height: 1.8;\n    color: #4a4a4a;\n    margin-bottom: 28rpx;\n    word-break: break-all;\n    font-weight: 400;\n    letter-spacing: 0.3rpx;\n\n    .expand-btn {\n      color: #ff6b87;\n      font-size: 28rpx;\n      display: inline-block;\n      font-weight: 600;\n      padding: 6rpx 12rpx;\n      border-radius: 16rpx;\n      background: rgba(255, 107, 135, 0.1);\n      transition: all 0.3s ease;\n      letter-spacing: 0.3rpx;\n\n      &:active {\n        background: rgba(255, 107, 135, 0.2);\n        transform: scale(0.95);\n      }\n    }\n  }\n\n  .actions {\n    display: flex;\n    margin-top: 24rpx;\n\n    .reply-btn,\n    .delete-btn {\n      display: flex;\n      align-items: center;\n      margin-right: 32rpx;\n      padding: 12rpx 20rpx;\n      border-radius: 28rpx;\n      background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n      border: 1rpx solid rgba(255, 107, 135, 0.2);\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);\n\n      &:active {\n        background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);\n        transform: scale(0.95);\n      }\n\n      text {\n        font-size: 26rpx;\n        color: #ff6b87;\n        margin-left: 10rpx;\n        font-weight: 600;\n        letter-spacing: 0.3rpx;\n      }\n\n      /* 添加图标的样式 */\n      :deep(.u-icon__icon) {\n\n        &.uicon-message-circle,\n        &.uicon-trash {\n          transform: scale(1.1);\n          transition: all 0.3s ease;\n          color: #ff6b87;\n        }\n      }\n    }\n  }\n}\n\n.replies-container {\n  background: rgba(255, 255, 255, 0.95);\n  /* 移除高消耗的backdrop-filter */\n  border-radius: 32rpx;\n  padding: 40rpx;\n  margin-bottom: 32rpx;\n  box-shadow: 0 6rpx 24rpx rgba(255, 105, 135, 0.08);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  /* 启用GPU加速 */\n  transform: translateZ(0);\n  will-change: transform;\n  position: relative;\n  overflow: hidden;\n\n  .replies-header {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 32rpx;\n    padding-bottom: 24rpx;\n    border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);\n\n    text {\n      font-size: 28rpx; /* 优化：从32rpx减小到28rpx，回复标题更精致 */\n      font-weight: 600;\n      /* 备用颜色方案，确保在微信小程序中显示 */\n      color: #ff6b87;\n      background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n      letter-spacing: 0.5rpx;\n\n      /* 渐变文字效果（如果支持） */\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n\n      /* 微信小程序兼容性处理 */\n      @supports not (-webkit-background-clip: text) {\n        color: #ff6b87 !important;\n        background: none;\n      }\n    }\n\n    .sort-options {\n      .van-tabs {\n        position: relative;\n        display: flex;\n        justify-content: flex-end;\n        -webkit-tap-highlight-color: transparent; // 移除整个标签区域的点击高亮效果\n\n        &__wrap {\n          overflow: hidden;\n          position: relative;\n          padding: 0;\n        }\n\n        &__nav {\n          position: relative;\n          display: flex;\n          background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n          backdrop-filter: blur(20rpx);\n          height: 72rpx;\n          border-radius: 36rpx;\n          user-select: none;\n          box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);\n          padding: 6rpx;\n          border: 1rpx solid rgba(255, 255, 255, 0.8);\n        }\n      }\n\n      .van-tab {\n        cursor: pointer;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 26rpx;\n        min-width: 80rpx;\n        margin: 0 2rpx;\n        -webkit-tap-highlight-color: transparent;\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n        &__text {\n          font-size: 24rpx;\n          color: #8a8a8a;\n          line-height: 1.2;\n          padding: 0 16rpx;\n          font-weight: 500;\n          transition: all 0.3s ease;\n          letter-spacing: 0.3rpx;\n        }\n\n        &--active {\n          background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n          box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);\n          transform: translateY(-2rpx) scale(1.02);\n\n          .van-tab__text {\n            color: #ffffff;\n            font-weight: 600;\n            text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);\n            transform: scale(1.05);\n          }\n        }\n      }\n    }\n  }\n}\n\n.reply-list {\n  .empty-image {\n    width: 462rpx;\n    height: 256rpx;\n    margin: 0 auto;\n  }\n\n  .empty-replies {\n    padding: 80rpx 0;\n    text-align: center;\n\n    .empty-text {\n      font-size: 32rpx; /* 优化：从36rpx减小到32rpx，空状态文字更合理 */\n      /* 备用颜色方案，确保在微信小程序中显示 */\n      color: #ff6b87;\n      background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n      font-weight: 600;\n      margin-top: 24rpx;\n      letter-spacing: 0.5rpx;\n\n      /* 渐变文字效果（如果支持） */\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n\n      /* 微信小程序兼容性处理 */\n      @supports not (-webkit-background-clip: text) {\n        color: #ff6b87 !important;\n        background: none;\n      }\n    }\n\n    .empty-subtext {\n      font-size: 28rpx;\n      color: #8a8a8a;\n      margin-top: 16rpx;\n      font-weight: 400;\n      opacity: 0.8;\n    }\n  }\n\n  .reply-item {\n    padding: 28rpx 0;\n    border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);\n    transition: all 0.3s ease;\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    .reply-user-info {\n      display: flex;\n      margin-bottom: 20rpx;\n\n      .reply-avatar {\n        width: 72rpx;\n        height: 72rpx;\n        border-radius: 50%;\n        margin-right: 24rpx;\n        border: 2rpx solid rgba(255, 107, 135, 0.2);\n        box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);\n        transition: all 0.3s ease;\n\n        &:active {\n          transform: scale(0.95);\n        }\n      }\n\n      .reply-user-meta {\n        flex: 1;\n\n        .reply-nickname {\n          font-size: 26rpx; /* 优化：从28rpx减小到26rpx，回复用户名更精致 */\n          font-weight: 600;\n          /* 备用颜色方案，确保在微信小程序中显示 */\n          color: #4a4a4a;\n          background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);\n          display: flex;\n          align-items: center;\n          flex-wrap: wrap;\n          letter-spacing: 0.3rpx;\n\n          /* 渐变文字效果（如果支持） */\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n\n          /* 微信小程序兼容性处理 */\n          @supports not (-webkit-background-clip: text) {\n            color: #4a4a4a !important;\n            background: none;\n          }\n\n          .reply-text {\n            color: #8a8a8a;\n            font-weight: normal;\n            margin: 0 10rpx;\n            font-size: 26rpx;\n          }\n\n          .reply-to {\n            /* 备用颜色方案，确保在微信小程序中显示 */\n            color: #ff6b87 !important;\n            font-weight: 600;\n            background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\n            /* 渐变文字效果（如果支持） */\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n\n            /* 微信小程序兼容性处理 */\n            @supports not (-webkit-background-clip: text) {\n              color: #ff6b87 !important;\n              background: none;\n            }\n          }\n        }\n\n        .reply-time {\n          font-size: 24rpx;\n          color: #8a8a8a;\n          margin-top: 12rpx;\n          font-weight: 400;\n          opacity: 0.8;\n        }\n      }\n\n      .reply-like {\n        display: flex;\n        align-items: center;\n        padding: 8rpx 16rpx;\n        border-radius: 24rpx;\n        background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n        border: 1rpx solid rgba(255, 107, 135, 0.2);\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        align-self: flex-start;\n        margin-top: 8rpx;\n        box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);\n\n        &:active {\n          background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);\n          transform: scale(0.95);\n          box-shadow: 0 1rpx 4rpx rgba(255, 107, 135, 0.15);\n        }\n\n        text {\n          font-size: 24rpx;\n          color: #ff6b87;\n          margin-left: 8rpx;\n          font-weight: 600;\n          letter-spacing: 0.3rpx;\n        }\n\n        /* 添加心形图标的样式 */\n        :deep(.u-icon__icon) {\n\n          &.uicon-heart-fill,\n          &.uicon-heart {\n            font-weight: bold;\n            transform: scale(1.1);\n            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          }\n\n          &.uicon-heart-fill {\n            /* 移除复杂的心跳动画和阴影效果，提升性能 */\n            color: #f56c6c;\n          }\n        }\n      }\n    }\n\n    .reply-content {\n      font-size: 28rpx; /* 优化：从30rpx减小到28rpx，回复内容更精致 */\n      line-height: 1.8;\n      color: #4a4a4a;\n      margin-bottom: 16rpx;\n      padding-left: 96rpx;\n      word-break: break-all;\n      cursor: pointer;\n      font-weight: 400;\n      letter-spacing: 0.3rpx;\n\n      /* 添加触摸反馈效果 */\n      &:active {\n        background-color: rgba(255, 107, 135, 0.05);\n        border-radius: 12rpx;\n      }\n\n      text {\n        display: block;\n      }\n\n      .expand-btn {\n        color: #ff6b87;\n        font-size: 26rpx;\n        display: inline-block;\n        font-weight: 600;\n        margin-top: 12rpx;\n        padding: 6rpx 12rpx;\n        border-radius: 16rpx;\n        background: rgba(255, 107, 135, 0.1);\n        transition: all 0.3s ease;\n        letter-spacing: 0.3rpx;\n\n        &:active {\n          background: rgba(255, 107, 135, 0.2);\n          transform: scale(0.95);\n        }\n      }\n    }\n\n    .reply-actions {\n      display: flex;\n      margin-top: 10rpx;\n      justify-content: space-between;\n\n      .reply-reply {\n  display: flex;\n  align-items: center;\n  padding: 12rpx 20rpx;\n  margin-left: 84rpx;\n  border-radius: 28rpx;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n  border: 1rpx solid rgba(255, 107, 135, 0.2);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);\n\n\n        image {\n          width: 28rpx;\n          height: 28rpx;\n        }\n\n  &:active {\n    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);\n    transform: scale(0.95);\n  }\n\n        text {\n          font-size: 22rpx;\n          color: #667eea;\n          margin-left: 8rpx;\n          font-weight: 500;\n        }\n\n        /* 添加回复图标的样式 */\n        :deep(.u-icon__icon) {\n          &.uicon-message-circle {\n            transform: scale(1.1);\n            transition: all 0.3s ease;\n          }\n        }\n      }\n\n      // .reply-delete {\n      //   display: flex;\n      //   align-items: center;\n      //   justify-content: flex-end;\n      //   margin-left: 20rpx;\n      //   padding: 4rpx 10rpx;\n      // }\n      .more-btn {\n  width: 48rpx;\n  height: 48rpx;\n  padding: 10rpx;\n  border-radius: 24rpx;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n  border: 1rpx solid rgba(255, 107, 135, 0.2);\n  transition: all 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);\n\n  &:active {\n    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);\n    transform: scale(0.9);\n  }\n\n        image {\n          width: 100%;\n          height: 100%;\n        }\n      }\n    }\n  }\n\n  .loading-more {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 48rpx 0;\n\n    text {\n      margin-left: 24rpx;\n      font-size: 30rpx;\n      color: #ff6b87;\n      font-weight: 500;\n      letter-spacing: 0.5rpx;\n    }\n  }\n\n  .no-more {\n    text-align: center;\n    padding: 48rpx 0;\n\n    text {\n      font-size: 30rpx;\n      color: #b0b0b0;\n      font-weight: 500;\n      letter-spacing: 0.5rpx;\n    }\n  }\n}\n\n.input-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 100;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  box-shadow: 0 -8rpx 32rpx rgba(255, 105, 135, 0.08);\n  border-top: 1rpx solid rgba(255, 105, 135, 0.1);\n  transition: bottom 0.3s ease-in-out;\n}\n\n/* 蒙版层样式 */\n.mask-layer {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 90;\n  opacity: 0;\n  animation: maskFadeIn 0.3s ease-out forwards;\n}\n\n/* 更多操作弹窗样式 - 小红书风格 */\n.action-popup {\n  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n  backdrop-filter: blur(20rpx);\n  border-top-left-radius: 32rpx;\n  border-top-right-radius: 32rpx;\n  padding: 32rpx 0;\n}\n\n.action-item {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 24rpx;\n  margin: 0 24rpx;\n  background: #ffffff;\n  transition: all 0.3s ease;\n\n  &:active {\n    background: #f8fafc;\n    transform: scale(0.98);\n  }\n\n  .action-icon {\n    width: 44rpx;\n    height: 44rpx;\n    margin: 0 20rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  /* 第一个action-item的样式 */\n  &.reply {\n    border-top-left-radius: 24rpx;\n    border-top-right-radius: 24rpx;\n    border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);\n  }\n\n  /* 第二个action-item的样式 */\n  &.copy {\n    margin-bottom: 24rpx;\n    border-bottom-left-radius: 24rpx;\n    border-bottom-right-radius: 24rpx;\n  }\n\n  /* 第三个action-item的样式 */\n  &.report {\n    border-top-left-radius: 24rpx;\n    border-top-right-radius: 24rpx;\n    border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);\n  }\n\n  /* 最后一个action-item的样式 */\n  &.block {\n    border-bottom-left-radius: 24rpx;\n    border-bottom-right-radius: 24rpx;\n  }\n\n  text {\n    font-size: 28rpx;\n    color: #334155;\n    font-weight: 500;\n  }\n}\n\n.action-cancel {\n  height: 90rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #fff;\n  margin-top: 20rpx;\n  border-radius: 20rpx;\n\n  text {\n    font-size: 32rpx;\n    font-weight: 500;\n    color: #333;\n  }\n\n  &:active {\n    background-color: #f5f5f5;\n  }\n}\n\n/* 加载更多状态样式 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n\n  .loading-text {\n    margin-left: 20rpx;\n    font-size: 28rpx;\n    color: #667eea;\n  }\n}\n\n/* 回复骨架屏加载状态样式 */\n.loading-more-skeleton {\n  padding: 0 32rpx;\n\n  /* 骨架屏淡入动画 */\n  animation: replySkeletonFadeIn 0.3s ease-out;\n}\n\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx 0;\n\n  text {\n    font-size: 28rpx;\n    color: #94a3b8;\n  }\n}\n\n/* 蒙版层淡入动画 */\n@keyframes maskFadeIn {\n  0% {\n    opacity: 0;\n  }\n\n  100% {\n    opacity: 1;\n  }\n}\n\n/* 小红书风格动画效果 */\n@keyframes heartBeat {\n  0% {\n    transform: scale(1.2);\n  }\n\n  14% {\n    transform: scale(1.4);\n  }\n\n  28% {\n    transform: scale(1.2);\n  }\n\n  42% {\n    transform: scale(1.4);\n  }\n\n  70% {\n    transform: scale(1.2);\n  }\n\n  100% {\n    transform: scale(1.2);\n  }\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30rpx);\n  }\n\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* 性能优化：移除入场动画，避免滚动时重复触发导致卡顿 */\n\n/* 渐变文字效果 - 微信小程序兼容版本 */\n.gradient-text {\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n/* 性能优化：移除消耗性能的毛玻璃效果 */\n.glass-effect {\n  /* backdrop-filter: blur(20rpx); */\n  /* -webkit-backdrop-filter: blur(20rpx); */\n  background: rgba(255, 255, 255, 0.9);\n}\n\n/* 悬浮阴影效果 */\n.floating-shadow {\n  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);\n  transition: box-shadow 0.3s ease;\n}\n\n.floating-shadow:hover {\n  box-shadow: 0 12rpx 48rpx rgba(255, 105, 135, 0.2);\n}\n\n/* 响应式设计优化 */\n@media (max-width: 750rpx) {\n  .main-comment {\n    padding: 32rpx;\n  }\n\n  .replies-container {\n    padding: 32rpx;\n  }\n\n  .reply-content {\n    font-size: 26rpx; /* 小屏幕优化：进一步减小回复内容字体 */\n  }\n}\n\n/* 回复骨架屏动画 */\n@keyframes replySkeletonFadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(15rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752117066333\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}