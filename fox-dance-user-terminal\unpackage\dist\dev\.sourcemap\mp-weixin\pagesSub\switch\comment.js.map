{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?4e81", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?c2fd", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?cba1", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?fcec", "uni-app:///pagesSub/switch/comment.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?5fea", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment.vue?c748"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "dayjs", "components", "CommentInput", "CommentSkeleton", "data", "activeFilter", "commentList", "commentText", "loading", "loadingMore", "isRefreshing", "page", "limit", "hasMore", "showMorePopup", "currentMoreComment", "isReplyMode", "currentReply", "inputPlaceholder", "pageHeight", "contentId", "contentType", "topicId", "storeId", "storeName", "storeImage", "userId", "topicInfo", "storeInfo", "keyboardHeight", "inputContainerBottom", "isKeyboardShow", "totalComments", "commentStats", "hotTotal", "newTotal", "myTotal", "commentListHot", "commentListNew", "commentListMy", "pagination", "hot", "pageSize", "new", "my", "isLoadingMore", "loadingText", "scrollTop", "scrollIntoView", "onLoad", "console", "currentPage", "onShow", "uni", "onHide", "onUnload", "methods", "getTimestamp", "setupStoreInfo", "id", "name", "title", "description", "commentUserCount", "createTime", "fetchStoreComments", "baseUrl", "url", "params", "filter", "current", "method", "header", "success", "resolve", "reject", "fail", "response", "getCurrentCommentCount", "getCurrentFilterTotal", "setupKeyboardListener", "isCommentOwner", "onInputFocus", "setTimeout", "onInputBlur", "focusInput", "hideMaskAndKeyboard", "debugKeyboardState", "setPageHeight", "goBack", "delta", "changeFilter", "fetchComments", "fetchCommentStats", "topicApi", "res", "fetchStoreCommentStats", "fetchCommentsByType", "total", "commentsCount", "pages", "commentApi", "handleApiError", "icon", "content", "confirmText", "loadMoreComments", "apiCall", "rawComments", "comments", "getExistingCommentIds", "existingComments", "processCommentDataOptimized", "created_at", "is_liked", "show<PERSON>ull<PERSON><PERSON>nt", "processedComment", "nickname", "avatar", "level", "onRefresh", "likeComment", "action", "goToDetail", "sendComment", "sendNormalComment", "sendTopicComment", "sendStoreComment", "sendRegularComment", "handleCommentSuccess", "handleCommentError", "postStoreComment", "replyComment", "showMoreOptions", "replyFromMore", "copyComment", "deleteComment", "confirmColor", "list", "removeFromList", "sendReply", "replyToId", "clearInputAndResetState", "cancelReplyMode", "toastTitle", "duration", "formatTime", "fetchTopicInfo", "processedImages", "value", "type", "isArray", "length", "handleTopicImageError", "previewTopicImage", "urls", "getLevelColor", "processImageUrl", "handleImageLoadError", "toggle<PERSON><PERSON>nt", "scrollToComment", "scrollToCommentByScrollIntoView", "query", "scrollToCommentByScrollTop", "topicInfoHeight", "scrollViewTop", "commentTop", "currentScrollTop", "commentAbsoluteTop", "topOffset", "targetScrollTop", "debugScrollElements", "commentId", "commentElement", "scrollViewElement", "hasComment", "hasScrollView", "testScroll", "forceScrollToComment", "testScrollToPosition", "getCurrentScrollStatus", "processCommentData", "commentsList", "item", "reply"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrPA;AAAA;AAAA;AAAA;AAAutB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyY3uB;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIAC;AACAA;AAAA,eAEA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;UAAA9B;UAAA+B;UAAA7B;UAAAL;QAAA;QACAmC;UAAAhC;UAAA+B;UAAA7B;UAAAL;QAAA;QACAoC;UAAAjC;UAAA+B;UAAA7B;UAAAL;QAAA;MACA;MACAqC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;IACA;;IAGA;IACA;IACA;;IAEA;IACA;IACA;IAEAC;MACA9B;MACAC;MACAC;MACAC;MACAC;MACAE;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;;IAEA;;IAQA;;IAEA;IACA;IACA;IACA;MACAyB;QAAA;MAAA;MACAD;IACA;EAEA;EAEA;EACAE;IACA;IACAC;IACA;EACA;EAEA;EACAC;IACA;IACAD;IACAH;EACA;EAEA;EACAK;IACA;IACAF;IACAH;EACA;EACAM;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACAR;;MAEA;MACA;QACAS;QACAC;QACAC;QACAC;QACArC;QACAsC;QAAA;QACAC;MACA;MAEAd;;MAEA;MACAG;QACAQ;MACA;;MAEA;MACA;IACA;IAEA;AACA;AACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAf;;gBAEA;gBACAgB;gBACAC;gBAEAC;kBACA1C;kBACA2C;kBACAC;kBACA5B;gBACA;gBAEAQ;gBACAA;;gBAEA;gBAAA;gBAAA,OACA;kBACAG;oBACAc;oBACAI;oBACAnE;oBACAoE;sBACA;sBACA;oBACA;oBACAC;sBACAvB;sBACA;wBACAwB;sBACA;wBAAA;wBACAC;sBACA;oBACA;oBACAC;sBACA1B;sBACAyB;oBACA;kBACA;gBACA;cAAA;gBAtBAE;gBAAA,iCAwBAA;cAAA;gBAAA;gBAAA;gBAEA3B;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACA4B;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MAEA3B;QACAH;QACAA;QACAA;QAEA;QACA;QAEA;UACA;UACA;UACAA;UACA;YACAA;UACA;QACA;UACA;UACA;UACAA;QACA;MACA;MAQAA;IACA;IAEA;IACA+B;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACAhC;MACA;QACAA;MACA;MACA;;MAEA;;MAEA;MACAiC;QACA;UACA;UACA;UACA;QACA;MACA;IAEA;IAEAC;MAAA;MACA;MACAlC;MACA;;MAEA;MACAiC;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAE;MACA;QACA;MACA;IACA;IAEA;IACAC;MACApC;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACAG;;MAEA;MACA;MACA;MACA;IACA;IAEA;IAEA;IACAkC;MACArC;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;IACA;IACAsC;MACA;MACA;;MAEA;MACA;MACA;MACAtC;IACA;IACAuC;MACApC;QACAqC;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA3C;gBAAA;cAAA;gBAAA;gBAKAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA4C;cAAA;gBAAAC;gBAAA;gBAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAA;cAAA;gBAGA;kBACA;kBACA7C;gBACA;kBACAA;kBACA;kBACA;oBACAhB;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAc;gBACA;gBACA;kBACAhB;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACA4D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA9C;gBAEAgB;gBACAC;gBAEAC;kBACA1C;gBACA;gBAEAwB;gBACAA;gBAAA;gBAAA,OAEA;kBACAG;oBACAc;oBACAI;oBACAnE;oBACAoE;sBACA;sBACA;oBACA;oBACAC;sBACAvB;sBACA;wBACAwB;sBACA;wBAAA;wBACAC;sBACA;oBACA;oBACAC;sBACA1B;sBACAyB;oBACA;kBACA;gBACA;cAAA;gBAtBAE;gBAAA,kCAwBAA;cAAA;gBAAA;gBAAA;gBAEA3B;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACA+C;MAAA;MACA/C;MAEA;;MAEA;MACA;QACAvC;QACA+B;QACA7B;QACAL;MACA;;MAEA;MACA;MACA;QACAkB;QAAA;QACAN;QACAC;QACAgD;QACAC;QAAA;QACA5B;MACA;MAEAQ;;MAEA;MACA;QACAA;QACAA;UACA5B;UACAI;UACA2C;UACAC;UACA5B;QACA;;QAEA;QACAoD;UACA;UACA;UAEA5C;UACA;YACA;;YAEA;YACAA;cACAgD;cACAC;cACAtF;cACAyD;cACA8B;YACA;;YAEA;YACA;;YAEA;YACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;YAAA;;YAGA;YACA;YACA;YAEAlD;UACA;YACA;YACA;UACA;QACA;UACAA;UACA;QACA;UACA;UACA;QACA;MACA;QACAA;QACAA;UACA3B;UACAG;UACA2C;UACAC;UACA5B;QACA;;QAEA;QACA;UACA;UACA;UAEAQ;UACA;YACA;YAEAA;cACAgD;cACAC;cACAtF;cACAyD;cACA8B;YACA;;YAEA;YACA;;YAEA;YACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;YAAA;;YAGA;YACA;YACA;YAEAlD;UACA;YACA;UACA;QACA;UACAA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACAA;QAEAmD;UACA;UACA;UAEAnD;UAEA;YACA;;YAEA;YACAA;cACAgD;cACAC;cACAtF;YACA;;YAEA;YACA;;YAEA;YACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;YAAA;;YAGA;YACA;YACA;YAEAqC;UACA;YACA;YACA;UACA;QACA;UACAA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAoD;MAAA;MACA;MACAjD;QACAQ;QACA0C;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;;MAGA;MACA;QACApB;UACA9B;YACAQ;YACA2C;YACAC;YACAhC;cACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;MACAxD;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;MACA;QAAA;QACAA;QACA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MAEAA;;MAEA;MACA;MAEA;QACA;QACAA;QACAyD;MACA;QACA;QACA;QACA;UACAjF;UAAA;UACAN;UACAC;UACAgD;UACAC;UAAA;UACA5B;QACA;QAEAQ;QACAyD;MACA;MAEAA;QACA;QACA;QAEAzD;QAEA;UACA;;UAEA;UACAA;YACAiD;YACAD;YACArF;UACA;;UAEA;UACA;UACA;YACA+F;UACA;YACAA;UACA;YACAA;UACA;UAEA;YAAAC;UAAA;UAEA;YACA;YACA;YACA;cAAA;YAAA;YAEA3D;YAEA;cACA;cACA;gBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;kBACA;kBACA;cAAA;;cAGA;cACA;cAEAA;YACA;;YAEA;YACA;cACA;cACAA;YACA;UACA;YACA;YACA;YACAA;UACA;QACA;UACA;UACAA;UACAG;YACAQ;YACA0C;UACA;QACA;MACA;QACArD;QACAG;UACAQ;UACA0C;QACA;MACA;QACA;QACA;QACA;QACArD;MACA;IACA;IAEA;IACA4D;MACA;MACA;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;MAAA;MAEA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;;MAEA;QACA9D;QACA;MACA;MAEA;MACAA;MAEA;QACA;;QAEA;QACA;UACA+D;UACAC;UACAC;QACA;;QAEA;QACA;UACAC;YACAzD;YACA0D;YACAC;YACAC;UACA;QACA;UACA;UACA;YACAH;UACA;UACAA;UACAA;QACA;QAEA;MACA;QAAA;MAAA;MAEA;MACAlE;MAEA;IACA;IAIAsE;MACA;;MAEA;MACA;QACA7G;QACA+B;QACA7B;QACAL;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IACAiH;MAAA;MACA;MACA;MAEApB;QACA3E;QAAA;QACAgG;MACA;QACAxE;QACA;UACA;UACA;UACA;;UAEA;UACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;UAAA;QAEA;UACAG;YACAQ;YACA0C;UACA;QACA;MACA;QACArD;QACAG;UACAQ;UACA0C;QACA;MACA;IACA;IACAoB;MACA;MACAtE;QACAc;MACA;IACA;IACAyD;MACA;QACAvE;UACAQ;UACA0C;QACA;QACA;MACA;MAEA;;MAEA;MACA;QACArD;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA2E;MACA;MACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACApG;QACAN;QACAE;QACAkF;MACA;MAEAtD;MAEAmD;QACAnD;QACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA6E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA3H;kBACAsB;kBACAH;kBACAiF;gBACA;gBAEAtD;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA6C;gBACA7C;gBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8E;MAAA;MACA;QACAtG;QACAN;QACAoF;MACA;MAEAtD;MAEAmD;QACAnD;QACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACA+E;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MAEA5E;QACAQ;QACA0C;MACA;IACA;IAEA;IACA2B;MACA7E;QACAQ;QACA0C;MACA;IACA;IAEA;IACA4B;MAAA;MAAA;QAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjE;gBACAC;gBAAA,kCAEA;kBACAd;oBACAc;oBACAI;oBACAnE;oBACAoE;sBACA;sBACA;oBACA;oBACAC;sBACAvB;sBACA;wBACAwB;sBACA;wBAAA;wBACAC;sBACA;oBACA;oBACAC;sBACA1B;sBACAyB;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAyD;MAAA;MACAlF;;MAEA;MACA;MACA;MACA;MAEAA;;MAEA;MACA;QACA;UACA;UACAA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAmF;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;QACAnD;UACA;QACA;MACA;IACA;IAEA;IACAoD;MAAA;MACA;MAEAlF;QACAjD;QACAqE;UACApB;YACAQ;YACA0C;UACA;UACA;QACA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;MAEAnF;QACAQ;QACA2C;QACAC;QACAgC;QACAhE;UACA;YACA;YACA4B;cACA3E;YACA;cACAwB;cACA;gBACAG;kBACAQ;kBACA0C;gBACA;;gBAEA;gBACA;;gBAEA;gBACA;kBACA;oBAAA;kBAAA;kBACA;oBACAmC;kBACA;gBACA;;gBAEA;gBACAC;gBACAA;gBACAA;;gBAEA;gBACA;cACA;gBACAtF;kBACAQ;kBACA0C;gBACA;cACA;YACA;cACArD;cACAG;gBACAQ;gBACA0C;cACA;YACA;UACA;QACA;MACA;IACA;IACAqC;MAAA;MACA;;MAEA;MACAvC;QACA3E;QAAA;QACA8E;QACAqC;MACA;QACA3F;QACA;UACA;UACA;UACA;;UAEA;UACA;UAEAG;YACAQ;YACA0C;UACA;QACA;UACAlD;YACAQ;YACA0C;UACA;QACA;MACA;QACArD;QACAG;UACAQ;UACA0C;QACA;MACA;IACA;IAEA;IACAuC;MAAA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;QACA5F;MACA;QACAA;MACA;;MAEA;MACA;MACA;MACA;MAEAA;IACA;IAEA;IACA6F;MAAA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MAEA;QACA7F;;QAEA;QACA;QACA;UACA8F;QACA;;QAEA;QACA3F;UACAQ;UACA0C;UACA0C;QACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAjG;gBAAA;gBAAA,OACA4C;cAAA;gBAAAC;gBAEA7C;gBAEA;kBACAA;kBACAA;kBACAA;kBACAA;kBAEA;kBACAA;kBACAA;;kBAEA;kBACA;oBACAA;oBACAA;;oBAEA;oBACAkG;sBACA;sBACAlG;sBACA;oBACA,IAEA;oBACAA;kBACA;oBACAA;oBACAA;sBACAmG;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;kBACAtG;kBACAA;kBACAG;oBACAQ;oBACA0C;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArD;gBACAG;kBACAQ;kBACA0C;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkD;MACAvG;MACA;QACA;QACA;QACAA;QACAA;;QAEA;QACA;MACA;IACA;IAEA;IACAwG;MAAA;MACAxG;MAEA;QACAG;UACAQ;UACA0C;QACA;QACA;MACA;MAEA;QACAlD;UACAQ;UACA0C;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACArD;MACAA;MAEAG;QACAiB;QAAA;QACAqF;QAAA;QACAlF;UACAvB;QACA;QACA0B;UACA1B;UACAG;YACAQ;YACA0C;UACA;QACA;MACA;IACA;IAEAqD;MACA;QACA;QAAA;QACA;QACA;QAAA;QACA;QACA;QACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;MAEA3G;;MAEA;MACA;QACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACAA;QACA;MACA;MAEAA;MACA;IACA;IAEA;IACA4G;MAAA;MACA5G;MACAG;QACAQ;QACA0C;MACA;IACA;IACA;IACAwD;MACA;;MAEA;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;;MAGA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA9G;;MAEA;MACAiC;QACA;MACA;;MAEA;MACAA;QACA;QACA;UACAjC;UACA;QACA;MACA;IACA;IAEA;IACA+G;MAAA;MACA/G;;MAEA;MACA;QACA;QACAgH;UACA;YACAhH;;YAEA;YACA;;YAEA;YACAiC;cACA;cACAjC;YACA;UACA;YACAA;YACA;YACAiC;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAgF;MAAA;MACAjH;MAEA;QACA;;QAEA;QACAgH;QACAA;QACA;QACAA;QACA;QACAA;QAEAA;UACAhH;UAEA;YACA;YACA;YACA;YACA;;YAEA;cACA;cACA;;cAEA;cACA;;cAEA;cACA;cACA;;cAEA;cACA;cACA;gBACAkH;gBACAlH;cACA;cAEAA;gBACAmH;gBACAC;gBACAC;gBACAC;gBACAJ;gBACAK;gBACAC;cACA;;cAEA;cACA;gBACA;gBACA;gBACA;kBACA;kBACAxH;gBACA;cACA;gBACAA;gBACA;gBACA;cACA;YACA;cACAA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAyH;MACA;MACAzH;MAEA;MACAgH;MACAA;MAEAA;QACAhH;UACA0H;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;MAAA;MACA/H;;MAEA;MACA;;MAEA;MACA;MACAiC;QACAjC;QACA;QAEAiC;UACA;UACAjC;QACA;MACA;IACA;IAEA;IACAgI;MAAA;MACA;MACAhI;;MAEA;MACA;;MAEA;MACA;QACA;QACAgH;UACA;YACAhH;YACA;YACA;YACA;YACAA;UACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAiI;MAAA;MACAjI;MACA;IACA;IAEA;IACAkI;MACAlI;QACAH;QACAC;MACA;;MAEA;MACA;MACAkH;QACAhH;MACA;IACA;IACA;IACAmI;MACA;MACA;QACAnI;QACA;MACA;MAEA;MACAA;MAEA;QACAoI;UACA;;UAEA;UACAC;UACAA;UACAA;UACAA;;UAEA;UACA;YACAA;cACA5H;cACA0D;cACAC;cACAC;YACA;UACA;YACA;YACA;cACAgE;YACA;;YAEA;YACAA;YACAA;UACA;;UAEA;UACA;YACAA;UACA;YACA;YACAA;cACA;cAEAC;cACAA;;cAEA;cACA;gBACAA;cACA;;cAEA;cACA;gBACAA;kBACA7H;kBACA0D;kBACAC;gBACA;cACA;gBACA;gBACA;kBACAkE;gBACA;;gBAEA;gBACAA;cACA;YACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5uEA;AAAA;AAAA;AAAA;AAAs3C,CAAgB,ixCAAG,EAAC,C;;;;;;;;;;;ACA14C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/switch/comment.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/switch/comment.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./comment.vue?vue&type=template&id=7270b30e&scoped=true&\"\nvar renderjs\nimport script from \"./comment.vue?vue&type=script&lang=js&\"\nexport * from \"./comment.vue?vue&type=script&lang=js&\"\nimport style0 from \"./comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7270b30e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/comment.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=template&id=7270b30e&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.topicInfo &&\n    _vm.topicInfo.topicImages &&\n    _vm.topicInfo.topicImages.length > 0\n  var g1 = g0 ? _vm.topicInfo.topicImages.length : null\n  var l0 = g0\n    ? _vm.__map(_vm.topicInfo.topicImages, function (image, index) {\n        var $orig = _vm.__get_orig(image)\n        var m0 = _vm.processImageUrl(image)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 =\n    _vm.topicInfo && _vm.topicInfo.createTime\n      ? _vm.formatTime(_vm.topicInfo.createTime)\n      : null\n  var m2 = _vm.getCurrentFilterTotal()\n  var g2 =\n    _vm.activeFilter === \"hot\" && !(_vm.loading && _vm.activeFilter === \"hot\")\n      ? _vm.commentListHot.length\n      : null\n  var l2 =\n    _vm.activeFilter === \"hot\" &&\n    !(_vm.loading && _vm.activeFilter === \"hot\") &&\n    !(g2 == 0)\n      ? _vm.__map(_vm.commentListHot, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.processImageUrl(item.user.avatar)\n          var m4 =\n            item.user.level >= 0 ? _vm.getLevelColor(item.user.level) : null\n          var m5 = _vm.formatTime(item.created_at)\n          var g3 = !item.showFullContent ? item.content.length : null\n          var g4 =\n            !item.showFullContent && g3 > 100\n              ? item.content.slice(0, 100)\n              : null\n          var g5 = item.content.length\n          var g6 = item.replies && item.replies.length > 0\n          var l1 = g6\n            ? _vm.__map(item.replies.slice(0, 2), function (reply, rIndex) {\n                var $orig = _vm.__get_orig(reply)\n                var g7 = reply.content.length\n                var g8 = g7 > 50 ? reply.content.slice(0, 50) : null\n                return {\n                  $orig: $orig,\n                  g7: g7,\n                  g8: g8,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n            m5: m5,\n            g3: g3,\n            g4: g4,\n            g5: g5,\n            g6: g6,\n            l1: l1,\n          }\n        })\n      : null\n  var g9 =\n    _vm.activeFilter === \"hot\" &&\n    !(_vm.loading && _vm.activeFilter === \"hot\") &&\n    !(g2 == 0) &&\n    !_vm.pagination.hot.loading\n      ? !_vm.pagination.hot.hasMore && _vm.commentListHot.length > 0\n      : null\n  var g10 =\n    _vm.activeFilter === \"new\" && !(_vm.loading && _vm.activeFilter === \"new\")\n      ? _vm.commentListNew.length\n      : null\n  var l4 =\n    _vm.activeFilter === \"new\" &&\n    !(_vm.loading && _vm.activeFilter === \"new\") &&\n    !(g10 == 0)\n      ? _vm.__map(_vm.commentListNew, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m6 = _vm.processImageUrl(item.user.avatar)\n          var m7 =\n            item.user.level >= 0 ? _vm.getLevelColor(item.user.level) : null\n          var m8 = _vm.formatTime(item.created_at)\n          var g11 = !item.showFullContent ? item.content.length : null\n          var g12 =\n            !item.showFullContent && g11 > 100\n              ? item.content.slice(0, 100)\n              : null\n          var g13 = item.content.length\n          var g14 = item.replies && item.replies.length > 0\n          var l3 = g14\n            ? _vm.__map(item.replies.slice(0, 2), function (reply, rIndex) {\n                var $orig = _vm.__get_orig(reply)\n                var g15 = reply.content.length\n                var g16 = g15 > 50 ? reply.content.slice(0, 50) : null\n                return {\n                  $orig: $orig,\n                  g15: g15,\n                  g16: g16,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m6: m6,\n            m7: m7,\n            m8: m8,\n            g11: g11,\n            g12: g12,\n            g13: g13,\n            g14: g14,\n            l3: l3,\n          }\n        })\n      : null\n  var g17 =\n    _vm.activeFilter === \"new\" &&\n    !(_vm.loading && _vm.activeFilter === \"new\") &&\n    !(g10 == 0) &&\n    !_vm.pagination.new.loading\n      ? !_vm.pagination.new.hasMore && _vm.commentListNew.length > 0\n      : null\n  var g18 =\n    _vm.activeFilter === \"my\" && !(_vm.loading && _vm.activeFilter === \"my\")\n      ? _vm.commentListMy.length == 0 || _vm.commentListMy === null\n      : null\n  var l6 =\n    _vm.activeFilter === \"my\" &&\n    !(_vm.loading && _vm.activeFilter === \"my\") &&\n    !g18\n      ? _vm.__map(_vm.commentListMy, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m9 = _vm.processImageUrl(item.user.avatar)\n          var m10 =\n            item.user.level >= 0 ? _vm.getLevelColor(item.user.level) : null\n          var m11 = _vm.formatTime(item.created_at)\n          var g19 = !item.showFullContent ? item.content.length : null\n          var g20 =\n            !item.showFullContent && g19 > 100\n              ? item.content.slice(0, 100)\n              : null\n          var g21 = item.content.length\n          var g22 = item.replies && item.replies.length > 0\n          var l5 = g22\n            ? _vm.__map(item.replies.slice(0, 2), function (reply, rIndex) {\n                var $orig = _vm.__get_orig(reply)\n                var g23 = reply.content.length\n                var g24 = g23 > 50 ? reply.content.slice(0, 50) : null\n                return {\n                  $orig: $orig,\n                  g23: g23,\n                  g24: g24,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m9: m9,\n            m10: m10,\n            m11: m11,\n            g19: g19,\n            g20: g20,\n            g21: g21,\n            g22: g22,\n            l5: l5,\n          }\n        })\n      : null\n  var g25 =\n    _vm.activeFilter === \"my\" &&\n    !(_vm.loading && _vm.activeFilter === \"my\") &&\n    !g18 &&\n    !_vm.pagination.my.loading\n      ? !_vm.pagination.my.hasMore && _vm.commentListMy.length > 0\n      : null\n  var m12 = _vm.isCommentOwner(_vm.currentMoreComment)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        g2: g2,\n        l2: l2,\n        g9: g9,\n        g10: g10,\n        l4: l4,\n        g17: g17,\n        g18: g18,\n        l6: l6,\n        g25: g25,\n        m12: m12,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"comment-page\">\n    <!-- 整页滚动容器 -->\n    <scroll-view\n      ref=\"commentScrollView\"\n      scroll-y\n      class=\"page-scroll-view\"\n      refresher-enabled\n      @refresherrefresh=\"onRefresh\"\n      :refresher-triggered=\"isRefreshing\"\n      @scrolltolower=\"loadMoreComments\"\n      :lower-threshold=\"100\"\n      :style=\"{ height: pageHeight }\"\n      :scroll-top=\"scrollTop\"\n      :scroll-into-view=\"scrollIntoView\"\n      :scroll-with-animation=\"true\">\n\n      <!-- 话题图片轮播 -->\n      <view class=\"topic-images-container\" v-if=\"topicInfo && topicInfo.topicImages && topicInfo.topicImages.length > 0\">\n        <swiper\n          class=\"topic-images-swiper\"\n          :indicator-dots=\"topicInfo.topicImages.length > 1\"\n          :autoplay=\"false\"\n          :circular=\"true\"\n          indicator-color=\"rgba(255, 255, 255, 0.5)\"\n          indicator-active-color=\"#ffffff\"\n        >\n        <swiper-item v-for=\"(image, index) in topicInfo.topicImages\" :key=\"index\">\n          <image\n            class=\"topic-image\"\n            :src=\"processImageUrl(image)\"\n            mode=\"aspectFill\"\n            @error=\"handleTopicImageError(index)\"\n            :lazy-load=\"true\"\n            @tap=\"previewTopicImage(index)\"\n          ></image>\n        </swiper-item>\n      </swiper>\n    </view>\n\n    <!-- 话题信息区域 -->\n    <view class=\"topic-info-section\" v-if=\"topicInfo\">\n      <view class=\"topic-header\">\n        <view class=\"topic-title\">{{ topicInfo.title }}</view>\n        <view class=\"topic-desc\" v-if=\"topicInfo.description\">{{ topicInfo.description }}</view>\n        <view class=\"topic-meta\">\n          <text class=\"participants\">{{ topicInfo.commentUserCount || 0 }}人参与此话题</text>\n          <text class=\"create-time\" v-if=\"topicInfo.createTime\">· {{ formatTime(topicInfo.createTime) }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 店铺信息区域 -->\n    <view class=\"store-info-section\" v-if=\"storeInfo\">\n      <view class=\"store-header\">\n        <view class=\"store-icon\">\n          <image :src=\"storeInfo.storeImage\" class=\"icon\" mode=\"aspectFit\"></image>\n        </view>\n        <view class=\"store-content\">\n          <view class=\"store-title\">{{ storeInfo.title }}</view>\n          <view class=\"store-desc\" v-if=\"storeInfo.description\">{{ storeInfo.description }}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <!-- 左侧评论数量 -->\n      <view class=\"comment-count-section\">\n        <text class=\"comment-count-text\">评论({{ getCurrentFilterTotal() }})</text>\n      </view>\n\n      <!-- 右侧筛选标签 -->\n      <view class=\"filter-tabs-section\">\n        <view class=\"van-tabs\">\n          <view class=\"van-tabs__wrap\">\n            <view class=\"van-tabs__nav\">\n              <view class=\"van-tab\" :class=\"{ 'van-tab--active': activeFilter === 'hot' }\" @tap=\"changeFilter('hot')\">\n                <view class=\"van-tab__text\">最热</view>\n              </view>\n              <view class=\"van-tab\" :class=\"{ 'van-tab--active': activeFilter === 'new' }\" @tap=\"changeFilter('new')\">\n                <view class=\"van-tab__text\">最新</view>\n              </view>\n              <view class=\"van-tab\" :class=\"{ 'van-tab--active': activeFilter === 'my' }\" @tap=\"changeFilter('my')\">\n                <view class=\"van-tab__text\">我的</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 评论列表容器 -->\n    <view class=\"comment-list-container\">\n      <!-- 当前激活的评论列表 -->\n      <view class=\"comment-list\" v-if=\"activeFilter === 'hot'\">\n          <view v-if=\"loading && activeFilter === 'hot'\" class=\"loading\">\n            <u-loading mode=\"flower\" size=\"50\" color=\"#667eea\"></u-loading>\n            <view class=\"loading-text\">正在加载热门评论...</view>\n          </view>\n          <view v-else-if=\"commentListHot.length == 0\" class=\"empty-tip\">\n            <image src=\"/static/icon/null.png\" mode=\"\" class=\"empty-image\"></image>\n            <view class=\"empty-text\">暂无热门评论</view>\n            <view class=\"empty-subtext\">快来发表第一条评论吧~</view>\n            <view class=\"empty-action\" @tap=\"focusInput\">\n              <text>立即评论</text>\n            </view>\n          </view>\n          <block v-else>\n            <view class=\"comment-item\" v-for=\"(item, index) in commentListHot\" :key=\"index\" :id=\"`comment-hot-${index}`\" @tap=\"goToDetail(item)\">\n              <view class=\"user-avatar\">\n                <image\n                  :src=\"processImageUrl(item.user.avatar)\"\n                  mode=\"aspectFill\"\n                  :lazy-load=\"true\"\n                  @error=\"handleImageLoadError(item.user.avatar, '用户头像')\"\n                ></image>\n              </view>\n              <view class=\"comment-content\">\n                <view class=\"user-info-row\">\n                  <view class=\"user-info\">\n                    <view class=\"user-name\">\n                      {{ item.user.nickname }}\n                      <view v-if=\"item.user.level >= 0\" class=\"user-level\"\n                        :style=\"{ backgroundColor: getLevelColor(item.user.level) }\">Lv.{{ item.user.level }}</view>\n                    </view>\n                    <view class=\"time\">{{ formatTime(item.created_at) }}</view>\n                  </view>\n                  <view class=\"like-btn\" @tap.stop=\"likeComment(item, index, 'hot')\">\n                    <u-icon :name=\"item.is_liked ? 'heart-fill' : 'heart'\" :color=\"item.is_liked ? '#f56c6c' : '#999'\"\n                      size=\"28\"></u-icon>\n                    <text>{{ item.likes }}</text>\n                  </view>\n                </view>\n                <view class=\"text\">\n                  <text>{{ item.showFullContent ? item.content : (item.content.length > 100 ? item.content.slice(0, 100) +\n                    '...' : item.content) }}</text>\n                  <view v-if=\"item.content.length > 100\" class=\"expand-btn\" @tap.stop=\"toggleContent(item, index, 'hot')\">\n                    {{ item.showFullContent ? '收起' : '展开' }}\n                  </view>\n                </view>\n                <view class=\"actions\">\n                  <view class=\"reply-btn\" @tap.stop=\"replyComment(item)\">\n                    <image src=\"/static/icon/chat.png\" mode=\"aspectFill\"></image>\n                    <text>回复</text>\n                  </view>\n                  <view class=\"more-btn\" @tap.stop=\"showMoreOptions(item)\">\n                    <image src=\"/static/icon/more.png\" mode=\"aspectFill\"></image>\n                  </view>\n                </view>\n                <view v-if=\"item.replies && item.replies.length > 0\" class=\"reply-preview\">\n                  <view class=\"reply-item\" v-for=\"(reply, rIndex) in item.replies.slice(0, 2)\" :key=\"rIndex\">\n                    <text class=\"reply-nickname\">{{ reply.user && reply.user.nickname }}</text>\n                    <text v-if=\"reply.reply_to\" class=\"reply-to\">@{{ reply.reply_to.nickname }}</text>\n                    <text class=\"reply-content\">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>\n                  </view>\n                  <view v-if=\"item.reply_count > 2\" class=\"view-more\" @tap.stop=\"goToDetail(item)\">\n                    查看全部{{ item.reply_count }}条回复 >\n                  </view>\n                </view>\n              </view>\n            </view>\n\n            <!-- 加载更多状态 - 使用骨架屏优化 -->\n            <view v-if=\"pagination.hot.loading\" class=\"loading-more-skeleton\">\n              <!-- 显示骨架屏而不是loading图标 -->\n              <comment-skeleton v-for=\"n in 3\" :key=\"n\"></comment-skeleton>\n            </view>\n            <view v-else-if=\"!pagination.hot.hasMore && commentListHot.length > 0\" class=\"no-more\">\n              <text>没有更多评论了</text>\n            </view>\n\n            <!-- 底部留白区域 -->\n            <view class=\"bottom-space\"></view>\n        </block>\n      </view>\n\n      <!-- 最新评论列表 -->\n      <view class=\"comment-list\" v-if=\"activeFilter === 'new'\">\n        <view v-if=\"loading && activeFilter === 'new'\" class=\"loading\">\n          <u-loading mode=\"flower\" size=\"50\" color=\"#ff6b87\"></u-loading>\n          <view class=\"loading-text\">正在加载最新评论...</view>\n        </view>\n          <view v-else-if=\"commentListNew.length == 0\" class=\"empty-tip\">\n            <image src=\"/static/icon/null.png\" mode=\"\" class=\"empty-image\"></image>\n            <view class=\"empty-text\">暂无最新评论</view>\n            <view class=\"empty-subtext\">快来发表第一条评论吧~</view>\n            <view class=\"empty-action\" @tap=\"focusInput\">\n              <text>立即评论</text>\n            </view>\n          </view>\n          <block v-else>\n            <view class=\"comment-item\" v-for=\"(item, index) in commentListNew\" :key=\"index\" :id=\"`comment-new-${index}`\" @tap=\"goToDetail(item)\">\n              <view class=\"user-avatar\">\n                <image\n                  :src=\"processImageUrl(item.user.avatar)\"\n                  mode=\"aspectFill\"\n                  :lazy-load=\"true\"\n                  @error=\"handleImageLoadError(item.user.avatar, '用户头像')\"\n                ></image>\n              </view>\n              <view class=\"comment-content\">\n                <view class=\"user-info-row\">\n                  <view class=\"user-info\">\n                    <view class=\"user-name\">\n                      {{ item.user.nickname }}\n                      <view v-if=\"item.user.level >= 0\" class=\"user-level\"\n                        :style=\"{ backgroundColor: getLevelColor(item.user.level) }\">Lv{{ item.user.level }}</view>\n                    </view>\n                    <view class=\"time\">{{ formatTime(item.created_at) }}</view>\n                  </view>\n                  <view class=\"like-btn\" @tap.stop=\"likeComment(item, index, 'new')\">\n                    <u-icon :name=\"item.is_liked ? 'heart-fill' : 'heart'\" :color=\"item.is_liked ? '#f56c6c' : '#999'\"\n                      size=\"28\"></u-icon>\n                    <text>{{ item.likes }}</text>\n                  </view>\n                </view>\n                <view class=\"text\">\n                  <text>{{ item.showFullContent ? item.content : (item.content.length > 100 ? item.content.slice(0, 100) +\n                    '...' : item.content) }}</text>\n                  <view v-if=\"item.content.length > 100\" class=\"expand-btn\" @tap.stop=\"toggleContent(item, index, 'new')\">\n                    {{ item.showFullContent ? '收起' : '展开' }}\n                  </view>\n                </view>\n                <view class=\"actions\">\n                  <view class=\"reply-btn\" @tap.stop=\"replyComment(item)\">\n                    <image src=\"/static/icon/chat.png\" mode=\"aspectFill\"></image>\n                    <text>回复</text>\n                  </view>\n                  <view class=\"more-btn\" @tap.stop=\"showMoreOptions(item)\">\n                    <image src=\"/static/icon/more.png\" mode=\"aspectFill\"></image>\n                  </view>\n                </view>\n                <view v-if=\"item.replies && item.replies.length > 0\" class=\"reply-preview\">\n                  <view class=\"reply-item\" v-for=\"(reply, rIndex) in item.replies.slice(0, 2)\" :key=\"rIndex\">\n                    <text class=\"reply-nickname\">{{ reply.user && reply.user.nickname }}</text>\n                    <text v-if=\"reply.reply_to\" class=\"reply-to\">@{{ reply.reply_to.nickname }}</text>\n                    <text class=\"reply-content\">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>\n                  </view>\n                  <view v-if=\"item.reply_count > 2\" class=\"view-more\" @tap.stop=\"goToDetail(item)\">\n                    查看全部{{ item.reply_count }}条回复 >\n                  </view>\n                </view>\n              </view>\n            </view>\n\n            <!-- 加载更多状态 - 使用骨架屏优化 -->\n            <view v-if=\"pagination.new.loading\" class=\"loading-more-skeleton\">\n              <!-- 显示骨架屏而不是loading图标 -->\n              <comment-skeleton v-for=\"n in 3\" :key=\"n\"></comment-skeleton>\n            </view>\n            <view v-else-if=\"!pagination.new.hasMore && commentListNew.length > 0\" class=\"no-more\">\n              <text>没有更多评论了</text>\n            </view>\n\n        </block>\n      </view>\n\n      <!-- 我的评论列表 -->\n      <view class=\"comment-list\" v-if=\"activeFilter === 'my'\">\n        <view v-if=\"loading && activeFilter === 'my'\" class=\"loading\">\n          <u-loading mode=\"flower\" size=\"50\" color=\"#ff6b87\"></u-loading>\n          <view class=\"loading-text\">正在加载我的评论...</view>\n        </view>\n          <view v-else-if=\"commentListMy.length == 0 || commentListMy === null\" class=\"empty-tip\">\n            <image src=\"/static/icon/null.png\" mode=\"\" class=\"empty-image\"></image>\n            <view class=\"empty-text\">您还没有发表过评论</view>\n            <view class=\"empty-subtext\">快来参与互动吧~</view>\n            <view class=\"empty-action\" @tap=\"focusInput\">\n              <text>立即评论</text>\n            </view>\n          </view>\n          <block v-else>\n            <view class=\"comment-item\" v-for=\"(item, index) in commentListMy\" :key=\"index\" :id=\"`comment-my-${index}`\" @tap=\"goToDetail(item)\">\n              <view class=\"user-avatar\">\n                <image\n                  :src=\"processImageUrl(item.user.avatar)\"\n                  mode=\"aspectFill\"\n                  :lazy-load=\"true\"\n                  @error=\"handleImageLoadError(item.user.avatar, '用户头像')\"\n                ></image>\n              </view>\n              <view class=\"comment-content\">\n                <view class=\"user-info-row\">\n                  <view class=\"user-info\">\n                    <view class=\"user-name\">\n                      {{ item.user.nickname }}\n                      <view v-if=\"item.user.level >= 0\" class=\"user-level\"\n                        :style=\"{ backgroundColor: getLevelColor(item.user.level) }\">Lv{{ item.user.level }}</view>\n                    </view>\n                    <view class=\"time\">{{ formatTime(item.created_at) }}</view>\n                  </view>\n                  <view class=\"like-btn\" @tap.stop=\"likeComment(item, index, 'my')\">\n                    <u-icon :name=\"item.is_liked ? 'heart-fill' : 'heart'\" :color=\"item.is_liked ? '#f56c6c' : '#999'\"\n                      size=\"28\"></u-icon>\n                    <text>{{ item.likes }}</text>\n                  </view>\n                </view>\n                <view class=\"text\">\n                  <text>{{ item.showFullContent ? item.content : (item.content.length > 100 ? item.content.slice(0, 100) +\n                    '...' : item.content) }}</text>\n                  <view v-if=\"item.content.length > 100\" class=\"expand-btn\" @tap.stop=\"toggleContent(item, index, 'my')\">\n                    {{ item.showFullContent ? '收起' : '展开' }}\n                  </view>\n                </view>\n                <view class=\"actions\">\n                  <view class=\"reply-btn\" @tap.stop=\"replyComment(item)\">\n                    <image src=\"/static/icon/chat.png\" mode=\"aspectFill\"></image>\n                    <text>回复</text>\n                  </view>\n                  <view class=\"more-btn\" @tap.stop=\"showMoreOptions(item)\">\n                    <image src=\"/static/icon/more.png\" mode=\"aspectFill\"></image>\n                  </view>\n                </view>\n                <view v-if=\"item.replies && item.replies.length > 0\" class=\"reply-preview\">\n                  <view class=\"reply-item\" v-for=\"(reply, rIndex) in item.replies.slice(0, 2)\" :key=\"rIndex\">\n                    <text class=\"reply-nickname\">{{ reply.user && reply.user.nickname }}</text>\n                    <text v-if=\"reply.reply_to\" class=\"reply-to\">@{{ reply.reply_to.nickname }}</text>\n                    <text class=\"reply-content\">: {{ reply.content.length > 50 ? reply.content.slice(0, 50) + '...' : reply.content }}</text>\n                  </view>\n                  <view v-if=\"item.reply_count > 2\" class=\"view-more\" @tap.stop=\"goToDetail(item)\">\n                    查看全部{{ item.reply_count }}条回复 >\n                  </view>\n                </view>\n              </view>\n            </view>\n\n            <!-- 加载更多状态 - 使用骨架屏优化 -->\n            <view v-if=\"pagination.my.loading\" class=\"loading-more-skeleton\">\n              <!-- 显示骨架屏而不是loading图标 -->\n              <comment-skeleton v-for=\"n in 3\" :key=\"n\"></comment-skeleton>\n            </view>\n            <view v-else-if=\"!pagination.my.hasMore && commentListMy.length > 0\" class=\"no-more\">\n              <text>没有更多评论了</text>\n            </view>\n\n        </block>\n      </view>\n\n      <!-- 底部留白区域 -->\n      <view class=\"bottom-space\"></view>\n    </view>\n    </scroll-view>\n\n    <!-- 蒙版层 -->\n    <view v-if=\"isKeyboardShow\" class=\"mask-layer\" @tap=\"hideMaskAndKeyboard\"></view>\n\n    <!-- 底部输入框 - 支持回复模式 -->\n    <view class=\"input-container\" :style=\"{ bottom: inputContainerBottom + 'px' }\">\n      <!-- 回复状态指示器 -->\n      <view v-if=\"isReplyMode\" class=\"reply-indicator\">\n        <view class=\"reply-info\">\n          <text class=\"reply-text\">回复 @{{ currentReply && currentReply.user && currentReply.user.nickname ? currentReply.user.nickname : '用户' }}</text>\n          <view class=\"cancel-reply-btn\" @tap=\"cancelReplyMode\">\n            <text>✕</text>\n          </view>\n        </view>\n      </view>\n\n      <comment-input v-model=\"commentText\" :placeholder=\"inputPlaceholder\" :use-image-button=\"true\" @send=\"sendComment\"\n        ref=\"mainCommentInput\" @focus=\"onInputFocus\" @blur=\"onInputBlur\" />\n    </view>\n\n    <!-- 回复弹窗已移除，改为使用底部主输入框进行回复 -->\n\n    <!-- 更多操作弹窗 -->\n    <u-popup v-model=\"showMorePopup\" mode=\"bottom\" border-radius=\"30\">\n      <view class=\"action-popup\">\n        <view class=\"action-item reply\" @tap=\"replyFromMore\">\n          <view class=\"action-icon\">\n            <image src=\"/static/icon/chat-1.png\" mode=\"aspectFill\"></image>\n          </view>\n          <text>回复</text>\n        </view>\n        <view class=\"action-item copy\" @tap=\"copyComment\">\n          <view class=\"action-icon\">\n            <image src=\"/static/icon/copy.png\" mode=\"aspectFill\"></image>\n          </view>\n          <text>复制</text>\n        </view>\n        <view v-if=\"isCommentOwner(currentMoreComment)\" class=\"action-item report block\" @tap=\"deleteComment\">\n          <view class=\"action-icon\">\n            <image src=\"/static/icon/delete.png\" mode=\"aspectFill\"></image>\n          </view>\n          <text>删除</text>\n        </view>\n      </view>\n    </u-popup>\n\n  </view>\n</template>\n\n<script>\nimport commentApi from '@/config/comment.api.js'\nimport topicApi from '@/config/topic.api.js'\nimport { getStoreById } from '@/config/store'\nimport dayjs from 'dayjs'\nimport relativeTime from 'dayjs/plugin/relativeTime'\nimport 'dayjs/locale/zh-cn'\nimport CommentInput from '../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue'\nimport CommentSkeleton from './components/CommentSkeleton.vue'\n\ndayjs.extend(relativeTime)\ndayjs.locale('zh-cn')\n\nexport default {\n  components: {\n    CommentInput,\n    CommentSkeleton\n  },\n  data() {\n    return {\n      activeFilter: 'hot',\n      commentList: [],\n      commentText: '',\n      loading: true,\n      loadingMore: false, // 加载更多状态\n      isRefreshing: false,\n      page: 1,\n      limit: 10,\n      hasMore: true,\n      showMorePopup: false, // 更多操作弹窗\n      currentMoreComment: null, // 当前操作的评论\n      // 回复状态管理\n      isReplyMode: false, // 是否处于回复模式\n      currentReply: null, // 当前被回复的评论\n      inputPlaceholder: '说点什么...', // 输入框提示文字\n      pageHeight: 'calc(100vh - 120rpx)', // 页面滚动容器高度\n      contentId: '', // 内容ID\n      contentType: '', // 内容类型\n      topicId: '', // 话题ID\n      storeId: '', // 店铺ID\n      storeName: '', // 店铺名称\n      storeImage: '',\n      userId: '', // 用户ID\n      topicInfo: null, // 话题信息（包含图片）\n      storeInfo: null, // 店铺信息\n      keyboardHeight: 0, // 键盘高度\n      inputContainerBottom: 0, // 输入框容器底部距离\n      isKeyboardShow: false, // 键盘是否显示\n      totalComments: 0, // 评论总数\n      // 各筛选条件的真实总数\n      commentStats: {\n        hotTotal: 0,    // 热门评论总数\n        newTotal: 0,    // 最新评论总数\n        myTotal: 0      // 我的评论总数\n      },\n      commentListHot: [],\n      commentListNew: [],\n      commentListMy: [],\n      // 分页相关数据\n      pagination: {\n        hot: { page: 1, pageSize: 10, hasMore: true, loading: false },\n        new: { page: 1, pageSize: 10, hasMore: true, loading: false },\n        my: { page: 1, pageSize: 10, hasMore: true, loading: false }\n      },\n      isLoadingMore: false, // 是否正在加载更多\n      loadingText: '加载中...', // 加载提示文本\n      scrollTop: 0, // scroll-view的滚动位置\n      scrollIntoView: '' // scroll-view的滚动到指定元素\n    }\n  },\n  onLoad(options) {\n    // 获取页面参数\n    this.contentId = options.content_id || '';\n    this.contentType = options.content_type || '';\n\n    // 修复：将topicId转换为Long类型（使用Number确保兼容性）\n    this.topicId = options.topicId ? Number(options.topicId) : null;\n\n    // 新增：店铺相关参数\n    this.storeId = options.storeId ? Number(options.storeId) : null;\n    this.storeName = options.storeName ? decodeURIComponent(options.storeName) : null;\n    this.storeImage = options.storeImage || null;\n\n\n    // 如果没有传入内容ID或类型，使用默认值\n    if (!this.contentId) this.contentId = 'default_content';\n    if (!this.contentType) this.contentType = 'video';\n\n    // 修复：将userId转换为Long类型（使用Number确保兼容性）\n    const userIdStr = uni.getStorageSync('userid') || '222';\n    this.userId = Number(userIdStr);\n\n    console.log('📱 评论页面参数:', {\n      contentId: this.contentId,\n      contentType: this.contentType,\n      topicId: this.topicId,\n      storeId: this.storeId,\n      storeName: this.storeName,\n      userId: this.userId\n    });\n\n    // 如果是话题页面，获取话题信息\n    if (this.topicId) {\n      this.fetchTopicInfo();\n    }\n\n    // 如果是店铺页面，设置店铺信息\n    if (this.storeId && this.storeName) {\n      this.setupStoreInfo();\n    }\n\n    // 获取评论统计信息\n    this.fetchCommentStats();\n\n    // 获取各标签的评论列表\n    this.fetchComments();\n    this.setPageHeight();\n\n    // 设置键盘监听\n    this.setupKeyboardListener();\n\n    // 将调试方法挂载到全局，方便控制台调用\n    // #ifdef H5\n    if (typeof window !== 'undefined') {\n      window.debugKeyboard = () => this.debugKeyboardState();\n      console.log('🔧 调试方法已挂载到全局: window.debugKeyboard()');\n    }\n    // #endif\n\n    // 微信小程序环境下的调试方法\n    // #ifdef MP-WEIXIN\n    // 在微信小程序中，可以通过 getCurrentPages() 获取当前页面实例\n    const pages = getCurrentPages();\n    const currentPage = pages[pages.length - 1];\n    if (currentPage) {\n      currentPage.debugKeyboard = () => this.debugKeyboardState();\n      console.log('🔧 调试方法已挂载到页面实例: getCurrentPages()[getCurrentPages().length-1].debugKeyboard()');\n    }\n    // #endif\n  },\n  \n  // 在页面显示时启用键盘监听\n  onShow() {\n    // 确保只有一个有效的键盘监听\n    uni.offKeyboardHeightChange(); // 先移除可能存在的监听\n    this.setupKeyboardListener();\n  },\n  \n  // 在页面隐藏时取消键盘监听\n  onHide() {\n    // 取消监听键盘高度变化\n    uni.offKeyboardHeightChange();\n    console.log('页面隐藏，取消键盘高度监听');\n  },\n  \n  // 在页面卸载时取消键盘监听\n  onUnload() {\n    // 取消监听键盘高度变化\n    uni.offKeyboardHeightChange();\n    console.log('页面卸载，取消键盘高度监听');\n  },\n  methods: {\n    // 兼容性时间戳函数 - 替代performance.now()\n    getTimestamp() {\n      // 微信小程序环境使用Date.now()\n      if (typeof performance !== 'undefined' && performance.now) {\n        return performance.now();\n      }\n      return Date.now();\n    },\n\n    /**\n     * 设置店铺信息\n     */\n    setupStoreInfo() {\n      console.log('🏪 设置店铺信息 - storeId:', this.storeId, 'storeName:', this.storeName, 'storeImage:', this.storeImage);\n\n      // 构建店铺信息对象\n      this.storeInfo = {\n        id: this.storeId,\n        name: this.storeName,\n        title: `${this.storeName}找搭子`,\n        description: '快来寻找你的搭子吧！',\n        storeImage: this.storeImage,\n        commentUserCount: 0, // 初始值，后续会通过API更新\n        createTime: new Date().toISOString()\n      };\n\n      console.log('🏪 店铺信息设置完成:', this.storeInfo);\n\n      // 设置页面标题\n      uni.setNavigationBarTitle({\n        title: this.storeInfo.title\n      });\n\n      // 加载店铺评论\n      this.fetchComments();\n    },\n\n    /**\n     * 获取店铺评论列表\n     */\n    async fetchStoreComments(storeId, userId, filter, current, pageSize) {\n      try {\n        console.log('🏪 调用店铺评论API - storeId:', storeId, 'filter:', filter);\n\n        // 构建请求URL和参数\n        const baseUrl = this.$config?.apis?.vote_baseUrl || 'https://vote.foxdance.com.cn';\n        const url = `${baseUrl}/api/comments/store/${storeId}`;\n\n        const params = {\n          userId: userId,\n          filter: filter,\n          current: current,\n          pageSize: pageSize\n        };\n\n        console.log('🏪 店铺评论请求URL:', url);\n        console.log('🏪 店铺评论请求参数:', params);\n\n        // 发送请求\n        const response = await new Promise((resolve, reject) => {\n          uni.request({\n            url: url,\n            method: 'GET',\n            data: params,\n            header: {\n              'Content-Type': 'application/json',\n              'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')\n            },\n            success: (res) => {\n              console.log('🏪 店铺评论API响应:', res);\n              if (res.statusCode === 200) {\n                resolve(res.data);\n              } else {\n                reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));\n              }\n            },\n            fail: (err) => {\n              console.error('🏪 店铺评论API请求失败:', err);\n              reject(err);\n            }\n          });\n        });\n\n        return response;\n      } catch (error) {\n        console.error('🏪 获取店铺评论失败:', error);\n        throw error;\n      }\n    },\n\n    // 获取当前筛选类型下的评论数量（已加载的数量）\n    getCurrentCommentCount() {\n      switch (this.activeFilter) {\n        case 'hot':\n          return this.commentListHot.length;\n        case 'new':\n          return this.commentListNew.length;\n        case 'my':\n          return this.commentListMy.length;\n        default:\n          return 0;\n      }\n    },\n\n    // 获取当前筛选条件对应的真实总数\n    getCurrentFilterTotal() {\n      switch (this.activeFilter) {\n        case 'hot':\n          return this.commentStats.hotTotal || 0;\n        case 'new':\n          return this.commentStats.newTotal || 0;\n        case 'my':\n          return this.commentStats.myTotal || 0;\n        default:\n          return 0;\n      }\n    },\n\n    // 设置键盘高度监听器\n    setupKeyboardListener() {\n      // #ifdef MP-WEIXIN\n      uni.onKeyboardHeightChange(res => {\n        console.log('🎹 键盘高度变化:', res.height);\n        console.log('📱 回复弹窗状态:', this.showReplyPopup);\n        console.log('🎯 回复输入框焦点状态:', this.isReplyInputFocused);\n\n        this.keyboardHeight = res.height;\n        this.isKeyboardShow = res.height > 0;\n\n        if (res.height > 0) {\n          // 键盘弹出，调整主输入框位置\n          this.inputContainerBottom = res.height;\n          console.log('🔧 调整主输入框位置:', this.inputContainerBottom);\n          if (this.isReplyMode) {\n            console.log('� 当前处于回复模式');\n          }\n        } else {\n          // 键盘收起，恢复主输入框位置\n          this.inputContainerBottom = 0;\n          console.log('📥 键盘收起，重置主输入框位置');\n        }\n      });\n      // #endif\n\n      // #ifndef MP-WEIXIN\n      // 非微信小程序环境的兼容处理\n      console.log('非微信小程序环境，使用默认键盘处理');\n      // #endif\n\n      console.log('键盘高度监听器已设置');\n    },\n    \n    // 判断当前用户是否是评论所有者\n    isCommentOwner(comment) {\n      if (!comment || !comment.user) return false;\n      return String(comment.user.id) == String(this.userId);\n    },\n    \n    onInputFocus(e) {\n      // 主输入框获取焦点，键盘弹出\n      console.log('📝 主输入框获取焦点');\n      if (this.isReplyMode) {\n        console.log('💬 当前处于回复模式，回复用户:', this.currentReply && this.currentReply.user ? this.currentReply.user.nickname : '未知用户');\n      }\n      this.isKeyboardShow = true;\n\n      // 微信小程序中，键盘弹出时的额外处理\n      // #ifdef MP-WEIXIN\n      // 延时获取键盘高度，因为键盘弹出需要时间\n      setTimeout(() => {\n        if (this.keyboardHeight === 0) {\n          // 如果监听器没有获取到键盘高度，使用默认值\n          this.keyboardHeight = 280; // 微信小程序默认键盘高度\n          this.inputContainerBottom = this.keyboardHeight;\n        }\n      }, 300);\n      // #endif\n    },\n\n    onInputBlur(e) {\n      // 输入框失去焦点，键盘收起\n      console.log('输入框失去焦点');\n      this.isKeyboardShow = false;\n\n      // 延时重置，确保键盘完全收起\n      setTimeout(() => {\n        if (!this.isKeyboardShow) {\n          this.keyboardHeight = 0;\n          this.inputContainerBottom = 0;\n        }\n      }, 100);\n    },\n\n    // 聚焦输入框\n    focusInput() {\n      if (this.$refs.mainCommentInput) {\n        this.$refs.mainCommentInput.focus();\n      }\n    },\n\n    // 隐藏蒙版层并收起键盘\n    hideMaskAndKeyboard() {\n      console.log('点击蒙版层，收起键盘');\n\n      // 检查是否处于回复模式，如果是则取消回复\n      if (this.isReplyMode) {\n        console.log('🚫 检测到回复模式，取消回复');\n        this.cancelReplyMode();\n      }\n\n      // 让输入框失去焦点\n      if (this.$refs.mainCommentInput) {\n        this.$refs.mainCommentInput.blur();\n      }\n\n      // 强制隐藏键盘\n      uni.hideKeyboard();\n\n      // 重置键盘状态\n      this.isKeyboardShow = false;\n      this.keyboardHeight = 0;\n      this.inputContainerBottom = 0;\n    },\n\n    // 回复弹窗相关方法已移除，改为使用底部主输入框进行回复\n\n    // 调试方法：检查当前键盘适配状态\n    debugKeyboardState() {\n      console.log('🔍 当前键盘适配状态:');\n      console.log('  键盘高度:', this.keyboardHeight);\n      console.log('  键盘显示状态:', this.isKeyboardShow);\n      console.log('  回复弹窗显示:', this.showReplyPopup);\n      console.log('  回复输入框焦点:', this.isReplyInputFocused);\n      console.log('  回复弹窗底部距离:', this.replyPopupBottom);\n      console.log('  主输入框底部距离:', this.inputContainerBottom);\n    },\n    setPageHeight() {\n      const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 25;\n      const inputBoxHeight = 120; // 底部输入框高度（rpx）\n\n      // 计算页面滚动容器高度，减去输入框高度\n      const pageHeight = `calc(100vh - ${inputBoxHeight}rpx)`;\n      this.pageHeight = pageHeight;\n      console.log('设置页面滚动高度:', pageHeight);\n    },\n    goBack() {\n      uni.navigateBack({\n        delta: 1\n      });\n    },\n\n    changeFilter(type) {\n      if (this.activeFilter === type) return;\n      this.activeFilter = type;\n      \n      // 如果该类型的评论列表为空，则加载数据\n      if (type === 'hot' && this.commentListHot.length === 0) {\n        this.fetchCommentsByType('hot');\n      } else if (type === 'new' && this.commentListNew.length === 0) {\n        this.fetchCommentsByType('new');\n      } else if (type === 'my' && this.commentListMy.length === 0) {\n        this.fetchCommentsByType('my');\n      }\n    },\n    \n    // 获取所有类型的评论\n    fetchComments() {\n      // 初始化加载热门评论\n      this.fetchCommentsByType('hot');\n    },\n\n    // 获取评论统计信息\n    async fetchCommentStats() {\n      if ((!this.topicId && !this.storeId) || !this.userId) {\n        console.warn('⚠️ 缺少必要参数，无法获取评论统计');\n        return;\n      }\n\n      try {\n        console.log('🔢 开始获取评论统计信息...');\n\n        let res;\n        if (this.topicId) {\n          // 话题评论统计\n          res = await topicApi.getTopicCommentStats(this.topicId, this.userId);\n        } else if (this.storeId) {\n          // 店铺评论统计\n          res = await this.fetchStoreCommentStats(this.storeId, this.userId);\n        }\n\n        if (res && res.code === 0) {\n          this.commentStats = res.data;\n          console.log('🔢 评论统计获取成功:', this.commentStats);\n        } else {\n          console.error('❌ 获取评论统计失败:', res?.message);\n          // 使用默认值\n          this.commentStats = {\n            hotTotal: 0,\n            newTotal: 0,\n            myTotal: 0\n          };\n        }\n      } catch (error) {\n        console.error('❌ 获取评论统计异常:', error);\n        // 使用默认值\n        this.commentStats = {\n          hotTotal: 0,\n          newTotal: 0,\n          myTotal: 0\n        };\n      }\n    },\n\n    /**\n     * 获取店铺评论统计信息\n     */\n    async fetchStoreCommentStats(storeId, userId) {\n      try {\n        console.log('🏪 调用店铺评论统计API - storeId:', storeId);\n\n        const baseUrl = this.$config?.apis?.vote_baseUrl || 'https://vote.foxdance.com.cn';\n        const url = `${baseUrl}/api/comments/store/${storeId}/stats`;\n\n        const params = {\n          userId: userId\n        };\n\n        console.log('🏪 店铺评论统计请求URL:', url);\n        console.log('🏪 店铺评论统计请求参数:', params);\n\n        const response = await new Promise((resolve, reject) => {\n          uni.request({\n            url: url,\n            method: 'GET',\n            data: params,\n            header: {\n              'Content-Type': 'application/json',\n              'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')\n            },\n            success: (res) => {\n              console.log('🏪 店铺评论统计API响应:', res);\n              if (res.statusCode === 200) {\n                resolve(res.data);\n              } else {\n                reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));\n              }\n            },\n            fail: (err) => {\n              console.error('🏪 店铺评论统计API请求失败:', err);\n              reject(err);\n            }\n          });\n        });\n\n        return response;\n      } catch (error) {\n        console.error('🏪 获取店铺评论统计失败:', error);\n        throw error;\n      }\n    },\n\n    // 根据类型获取评论\n    fetchCommentsByType(type) {\n      console.log(`请求${type}评论列表`);\n\n      this.loading = true;\n\n      // 重置分页状态\n      this.pagination[type] = {\n        page: 1,\n        pageSize: 10,\n        hasMore: true,\n        loading: false\n      };\n\n      // 准备分页参数 - 修复参数名和数据类型\n      const startTime = this.getTimestamp();\n      const params = {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        contentId: this.contentId,\n        contentType: this.contentType,\n        filter: type,\n        current: 1, // 修复：使用current而不是page\n        pageSize: this.pagination[type].pageSize\n      };\n\n      console.log(`🚀 开始请求${type}评论列表，参数:`, params);\n\n      // 判断是否为话题类型的评论\n      if (this.topicId) {\n        console.log(`🎯 检测到topicId: ${this.topicId}，使用话题API获取评论`);\n        console.log(`📋 话题API调用参数:`, {\n          topicId: Number(this.topicId),\n          userId: Number(this.userId),\n          filter: type,\n          current: params.current,\n          pageSize: params.pageSize\n        });\n\n        // 使用话题API获取评论 - 修复数据类型为Long兼容\n        topicApi.getTopicComments(Number(this.topicId), Number(this.userId), type, params.current, params.pageSize).then(res => {\n          const endTime = this.getTimestamp();\n          const loadTime = endTime - startTime;\n\n          console.log(`✅ 话题${type}评论列表API返回，耗时: ${loadTime.toFixed(2)}ms`);\n          if (res.code === 0) {\n            const data = res.data;\n\n            // 性能优化：减少JSON序列化\n            console.log(`📊 话题${type}评论列表数据概览:`, {\n              total: data.total,\n              commentsCount: data.comments ? data.comments.length : 0,\n              hasMore: data.hasMore,\n              current: data.current,\n              pages: data.pages\n            });\n\n            // 处理评论数据（优化版）\n            const processedData = this.processCommentDataOptimized(data);\n\n            // 根据类型更新对应的评论列表\n            switch (type) {\n              case 'hot':\n                this.commentListHot = processedData;\n                break;\n              case 'new':\n                this.commentListNew = processedData;\n                break;\n              case 'my':\n                this.commentListMy = processedData;\n                break;\n            }\n\n            // 更新总评论数和分页信息\n            this.totalComments = (data && data.total) || 0;\n            this.pagination[type].hasMore = data.hasMore !== false;\n\n            console.log(`🎯 话题${type}评论加载完成，总数: ${this.totalComments}, 当前显示: ${processedData.length}`);\n          } else {\n            // 请求失败，显示错误信息\n            this.handleApiError(type, res.message || '获取评论失败');\n          }\n        }).catch(err => {\n          console.error(`获取${type}评论列表失败:`, err);\n          this.handleApiError(type, '网络请求错误');\n        }).finally(() => {\n          this.loading = false;\n          this.isRefreshing = false;\n        });\n      } else if (this.storeId) {\n        console.log(`🏪 检测到storeId: ${this.storeId}，使用店铺API获取评论`);\n        console.log(`📋 店铺API调用参数:`, {\n          storeId: Number(this.storeId),\n          userId: Number(this.userId),\n          filter: type,\n          current: params.current,\n          pageSize: params.pageSize\n        });\n\n        // 使用店铺API获取评论\n        this.fetchStoreComments(Number(this.storeId), Number(this.userId), type, params.current, params.pageSize).then(res => {\n          const endTime = this.getTimestamp();\n          const loadTime = endTime - startTime;\n\n          console.log(`✅ 店铺${type}评论列表API返回，耗时: ${loadTime.toFixed(2)}ms`);\n          if (res.code === 0) {\n            const data = res.data;\n\n            console.log(`📊 店铺${type}评论列表数据概览:`, {\n              total: data.total,\n              commentsCount: data.comments ? data.comments.length : 0,\n              hasMore: data.hasMore,\n              current: data.current,\n              pages: data.pages\n            });\n\n            // 处理评论数据\n            const processedData = this.processCommentDataOptimized(data);\n\n            // 根据类型更新对应的评论列表\n            switch (type) {\n              case 'hot':\n                this.commentListHot = processedData;\n                break;\n              case 'new':\n                this.commentListNew = processedData;\n                break;\n              case 'my':\n                this.commentListMy = processedData;\n                break;\n            }\n\n            // 更新总评论数和分页信息\n            this.totalComments = (data && data.total) || 0;\n            this.pagination[type].hasMore = data.hasMore !== false;\n\n            console.log(`🎯 店铺${type}评论加载完成，总数: ${this.totalComments}, 当前显示: ${processedData.length}`);\n          } else {\n            this.handleApiError(type, res.message || '获取店铺评论失败');\n          }\n        }).catch(err => {\n          console.error(`获取店铺${type}评论列表失败:`, err);\n          this.handleApiError(type, '网络请求错误');\n        }).finally(() => {\n          this.loading = false;\n          this.isRefreshing = false;\n        });\n      } else {\n        // 使用普通评论API获取评论\n        console.log('请求参数:', JSON.stringify(params));\n\n        commentApi.getCommentList(params).then(res => {\n          const endTime = this.getTimestamp();\n          const loadTime = endTime - startTime;\n\n          console.log(`✅ ${type}评论列表API返回，耗时: ${loadTime.toFixed(2)}ms`);\n\n          if (res.code === 0) {\n            const data = res.data;\n\n            // 性能优化：减少JSON序列化\n            console.log(`📊 ${type}评论列表数据概览:`, {\n              total: data.total,\n              commentsCount: data.comments ? data.comments.length : 0,\n              hasMore: data.hasMore\n            });\n\n            // 处理评论数据（优化版）\n            const processedData = this.processCommentDataOptimized(data);\n\n            // 根据类型更新对应的评论列表\n            switch (type) {\n              case 'hot':\n                this.commentListHot = processedData;\n                break;\n              case 'new':\n                this.commentListNew = processedData;\n                break;\n              case 'my':\n                this.commentListMy = processedData;\n                break;\n            }\n\n            // 更新总评论数和分页信息\n            this.totalComments = (data && data.total) || 0;\n            this.pagination[type].hasMore = data.hasMore !== false;\n\n            console.log(`🎯 ${type}评论加载完成，总数: ${this.totalComments}, 当前显示: ${processedData.length}`);\n          } else {\n            // 请求失败，显示错误信息\n            this.handleApiError(type, res.message || '获取评论失败');\n          }\n        }).catch(err => {\n          console.error(`获取${type}评论列表失败:`, err);\n          this.handleApiError(type, '网络请求错误');\n        }).finally(() => {\n          this.loading = false;\n          this.isRefreshing = false;\n        });\n      }\n    },\n    \n    // 处理API错误\n    handleApiError(type, message) {\n      // 显示错误提示\n      uni.showToast({\n        title: message,\n        icon: 'none'\n      });\n      \n      // 确保评论列表不为undefined\n      switch (type) {\n        case 'hot':\n          if (!this.commentListHot) this.commentListHot = [];\n          break;\n        case 'new':\n          if (!this.commentListNew) this.commentListNew = [];\n          break;\n        case 'my':\n          if (!this.commentListMy) this.commentListMy = [];\n          break;\n      }\n      \n      // 如果是刷新操作，提供重试选项\n      if (this.isRefreshing) {\n        setTimeout(() => {\n          uni.showModal({\n            title: '提示',\n            content: '获取评论失败，是否重试？',\n            confirmText: '重试',\n            success: (res) => {\n              if (res.confirm) {\n                this.fetchCommentsByType(type);\n              }\n            }\n          });\n        }, 500);\n      }\n    },\n\n    // 懒加载更多评论（优化版）\n    loadMoreComments() {\n      const type = this.activeFilter; // 使用当前激活的筛选类型\n      console.log(`🔄 触发${type}评论懒加载`);\n\n      // 防抖处理，避免重复请求\n      if (this.pagination[type].loading || !this.pagination[type].hasMore) {\n        console.log(`⚠️ ${type}评论正在加载或已无更多数据，跳过请求`);\n        return;\n      }\n\n      // 性能优化：增强防抖处理，减少低端设备的请求频率\n      const now = Date.now();\n      const lastRequestTime = this.lastRequestTime || 0;\n      if (now - lastRequestTime < 800) { // 增加到800ms，减少低端设备的负担\n        console.log(`⚠️ 请求过于频繁，跳过${type}评论懒加载`);\n        return;\n      }\n      this.lastRequestTime = now;\n\n      // 设置加载状态\n      this.pagination[type].loading = true;\n      this.loadingText = '加载更多评论...';\n\n      // 计算下一页页码\n      const nextPage = this.pagination[type].page + 1;\n      const startTime = this.getTimestamp();\n\n      console.log(`📄 ${type}评论当前页码: ${this.pagination[type].page}, 请求页码: ${nextPage}`);\n\n      // 准备分页参数 - 修复参数传递问题\n      let apiCall;\n\n      if (this.topicId) {\n        // 话题评论API调用 - 修复数据类型为Long兼容\n        console.log(`🎯 调用话题评论API: topicId=${this.topicId}, type=${type}, page=${nextPage}`);\n        apiCall = topicApi.getTopicComments(Number(this.topicId), Number(this.userId), type, nextPage, this.pagination[type].pageSize);\n      } else {\n        // 普通评论API调用 - 修复分页\n        // 参数和数据类型为Long兼容\n        const params = {\n          userId: Number(this.userId), // 使用Number确保Long兼容性\n          contentId: this.contentId,\n          contentType: this.contentType,\n          filter: type,\n          current: nextPage,  // 修复：使用current而不是page\n          pageSize: this.pagination[type].pageSize\n        };\n\n        console.log(`📋 调用普通评论API，参数:`, JSON.stringify(params));\n        apiCall = commentApi.getCommentList(params);\n      }\n\n      apiCall.then(res => {\n        const endTime = this.getTimestamp();\n        const loadTime = endTime - startTime;\n\n        console.log(`✅ ${type}评论分页API返回，耗时: ${loadTime.toFixed(2)}ms`);\n\n        if (res.code === 0) {\n          const data = res.data;\n\n          // 性能优化：减少日志输出\n          console.log(`📊 ${type}评论分页数据概览:`, {\n            commentsCount: data.comments ? data.comments.length : 0,\n            total: data.total,\n            hasMore: data.hasMore\n          });\n\n          // 处理不同的数据结构（优化版）\n          let rawComments = [];\n          if (data.comments && Array.isArray(data.comments)) {\n            rawComments = data.comments;\n          } else if (data.items && Array.isArray(data.items)) {\n            rawComments = data.items;\n          } else if (Array.isArray(data)) {\n            rawComments = data;\n          }\n\n          const newComments = this.processCommentDataOptimized({ comments: rawComments });\n\n          if (newComments && newComments.length > 0) {\n            // 检查是否有重复数据（优化版）\n            const existingIds = new Set(this.getExistingCommentIds(type));\n            const filteredComments = newComments.filter(comment => !existingIds.has(comment.id));\n\n            console.log(`� ${type}评论去重: 原始${newComments.length}条，去重后${filteredComments.length}条`);\n\n            if (filteredComments.length > 0) {\n              // 追加新评论到对应列表（优化：使用concat而不是展开运算符）\n              switch (type) {\n                case 'hot':\n                  this.commentListHot = this.commentListHot.concat(filteredComments);\n                  break;\n                case 'new':\n                  this.commentListNew = this.commentListNew.concat(filteredComments);\n                  break;\n                case 'my':\n                  this.commentListMy = this.commentListMy.concat(filteredComments);\n                  break;\n              }\n\n              // 更新分页信息\n              this.pagination[type].page = nextPage;\n\n              console.log(`✅ ${type}评论加载成功，页码: ${nextPage}，新增: ${filteredComments.length}条`);\n            }\n\n            // 检查是否还有更多数据\n            if (data.hasMore === false || newComments.length < this.pagination[type].pageSize) {\n              this.pagination[type].hasMore = false;\n              console.log(`🔚 ${type}评论已加载完毕`);\n            }\n          } else {\n            // 没有更多数据\n            this.pagination[type].hasMore = false;\n            console.log(`🔚 ${type}评论无更多数据`);\n          }\n        } else {\n          // API返回错误\n          console.error(`❌ ${type}评论API返回错误:`, res.message);\n          uni.showToast({\n            title: res.message || '加载失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error(`❌ ${type}评论懒加载失败:`, err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      }).finally(() => {\n        // 重置加载状态\n        this.pagination[type].loading = false;\n        this.loadingText = '加载中...';\n        console.log(`🔄 ${type}评论加载状态重置`);\n      });\n    },\n\n    // 获取已存在的评论ID列表，用于去重\n    getExistingCommentIds(type) {\n      let existingComments = [];\n      switch (type) {\n        case 'hot':\n          existingComments = this.commentListHot;\n          break;\n        case 'new':\n          existingComments = this.commentListNew;\n          break;\n        case 'my':\n          existingComments = this.commentListMy;\n          break;\n      }\n      return existingComments.map(comment => comment.id);\n    },\n\n    // 优化的评论数据处理方法 - 增强渲染性能\n    processCommentDataOptimized(data) {\n      const startTime = this.getTimestamp();\n\n      // 性能优化：减少批处理大小，避免滚动时卡顿\n      const batchSize = 5; // 减少到5条评论，确保滚动流畅\n\n      if (!data || !data.comments) {\n        console.warn('⚠️ 评论数据为空或格式错误');\n        return [];\n      }\n\n      const comments = Array.isArray(data.comments) ? data.comments : [];\n      console.log(`� 开始处理评论数据，数量: ${comments.length}`);\n\n      const processedComments = comments.map(comment => {\n        if (!comment) return null;\n\n        // 优化：减少对象创建和属性复制\n        const processedComment = Object.assign({}, comment, {\n          created_at: comment.createdAt || comment.created_at || new Date().toISOString(),\n          is_liked: comment.isLiked || comment.is_liked || false,\n          showFullContent: false\n        });\n\n        // 确保用户对象存在\n        if (!processedComment.user) {\n          processedComment.user = {\n            id: 0,\n            nickname: '未知用户',\n            avatar: '/static/images/toux.png',\n            level: 0\n          };\n        } else {\n          // 处理用户头像为空的情况\n          if (!processedComment.user.avatar) {\n            processedComment.user.avatar = '/static/images/toux.png';\n          }\n          processedComment.user.nickname = processedComment.user.nickname || '未知用户';\n          processedComment.user.level = processedComment.user.level || 0;\n        }\n\n        return processedComment;\n      }).filter(comment => comment !== null);\n\n      const endTime = this.getTimestamp();\n      console.log(`✅ 评论数据处理完成，耗时: ${(endTime - startTime).toFixed(2)}ms，处理数量: ${processedComments.length}`);\n\n      return processedComments;\n    },\n\n\n\n    onRefresh() {\n      this.isRefreshing = true;\n\n      // 重置当前激活标签的分页状态\n      this.pagination[this.activeFilter] = {\n        page: 1,\n        pageSize: 10,\n        hasMore: true,\n        loading: false\n      };\n\n      // 刷新评论统计信息\n      this.fetchCommentStats();\n\n      // 刷新当前激活的评论列表\n      this.fetchCommentsByType(this.activeFilter);\n    },\n    likeComment(item, index, type) {\n      // 调用点赞/取消点赞API\n      const action = item.is_liked ? 'unlike' : 'like';\n\n      commentApi.likeComment(Number(item.id), {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        action\n      }).then(res => {\n        console.log('点赞评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          // 更新评论点赞状态和数量\n          const updatedIsLiked = res.data.isLiked || res.data.is_liked;\n          const updatedLikes = res.data.likes;\n          \n          // 根据类型更新对应的评论列表\n          switch (type) {\n            case 'hot':\n              this.commentListHot[index].is_liked = updatedIsLiked;\n              this.commentListHot[index].likes = updatedLikes;\n              break;\n            case 'new':\n              this.commentListNew[index].is_liked = updatedIsLiked;\n              this.commentListNew[index].likes = updatedLikes;\n              break;\n            case 'my':\n              this.commentListMy[index].is_liked = updatedIsLiked;\n              this.commentListMy[index].likes = updatedLikes;\n              break;\n          }\n        } else {\n          uni.showToast({\n            title: res.message || '操作失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('点赞操作失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n    goToDetail(item) {\n      // 跳转到评论详情页面\n      uni.navigateTo({\n        url: `/pagesSub/switch/comment-detail?id=${item.id}&userId=${this.userId}`\n      });\n    },\n    sendComment() {\n      if (this.commentText.length > 1000) {\n        uni.showToast({\n          title: '评论字数不能超过1000字',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (!this.commentText.trim()) return;\n\n      // 根据当前状态决定是发送评论还是回复\n      if (this.isReplyMode && this.currentReply) {\n        console.log('📤 发送回复给用户:', this.currentReply.user.nickname);\n        this.sendReply();\n      } else {\n        console.log('📤 发送普通评论');\n        this.sendNormalComment();\n      }\n    },\n\n    // 发送普通评论\n    sendNormalComment() {\n      // 判断是话题评论还是店铺评论\n      if (this.topicId) {\n        this.sendTopicComment();\n      } else if (this.storeId) {\n        this.sendStoreComment();\n      } else {\n        // 普通评论\n        this.sendRegularComment();\n      }\n    },\n\n    // 发送话题评论\n    sendTopicComment() {\n      const data = {\n        userId: Number(this.userId),\n        contentId: this.contentId,\n        topicId: Number(this.topicId),\n        content: this.commentText.trim()\n      };\n\n      console.log('🎯 发送话题评论请求数据:', JSON.stringify(data));\n\n      commentApi.postComment(data).then(res => {\n        console.log('发送话题评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          this.handleCommentSuccess();\n        } else {\n          this.handleCommentError(res.message || '话题评论失败');\n        }\n      }).catch(err => {\n        console.error('发送话题评论失败:', err);\n        this.handleCommentError('网络错误，请重试');\n      });\n    },\n\n    // 发送店铺评论\n    async sendStoreComment() {\n      const data = {\n        userId: Number(this.userId),\n        storeId: Number(this.storeId),\n        content: this.commentText.trim()\n      };\n\n      console.log('🏪 发送店铺评论请求数据:', JSON.stringify(data));\n\n      try {\n        const res = await this.postStoreComment(data);\n        console.log('发送店铺评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          this.handleCommentSuccess();\n        } else {\n          this.handleCommentError(res.message || '店铺评论失败');\n        }\n      } catch (err) {\n        console.error('发送店铺评论失败:', err);\n        this.handleCommentError('网络错误，请重试');\n      }\n    },\n\n    // 发送普通评论\n    sendRegularComment() {\n      const data = {\n        userId: Number(this.userId),\n        contentId: this.contentId,\n        content: this.commentText.trim()\n      };\n\n      console.log('📝 发送普通评论请求数据:', JSON.stringify(data));\n\n      commentApi.postComment(data).then(res => {\n        console.log('发送普通评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          this.handleCommentSuccess();\n        } else {\n          this.handleCommentError(res.message || '评论失败');\n        }\n      }).catch(err => {\n        console.error('发送普通评论失败:', err);\n        this.handleCommentError('网络错误，请重试');\n      });\n    },\n\n    // 处理评论成功\n    handleCommentSuccess() {\n      // 清空输入框并重置状态\n      this.clearInputAndResetState();\n\n      // 重新加载评论列表 - 发布后切换到\"最新\"标签\n      this.fetchCommentsByType('new');\n      this.activeFilter = 'new';\n      this.currentTabIndex = 1;\n\n      // 刷新评论统计\n      this.fetchCommentStats();\n\n      uni.showToast({\n        title: '评论成功',\n        icon: 'success'\n      });\n    },\n\n    // 处理评论错误\n    handleCommentError(message) {\n      uni.showToast({\n        title: message,\n        icon: 'none'\n      });\n    },\n\n    // 发送店铺评论API调用\n    async postStoreComment(data) {\n      const baseUrl = this.$config?.apis?.vote_baseUrl || 'https://vote.foxdance.com.cn';\n      const url = `${baseUrl}/api/comments/store`;\n\n      return new Promise((resolve, reject) => {\n        uni.request({\n          url: url,\n          method: 'POST',\n          data: data,\n          header: {\n            'Content-Type': 'application/json',\n            'bausertoken': uni.getStorageSync('bausertoken') || uni.getStorageSync('token')\n          },\n          success: (res) => {\n            console.log('🏪 店铺评论API响应:', res);\n            if (res.statusCode === 200) {\n              resolve(res.data);\n            } else {\n              reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));\n            }\n          },\n          fail: (err) => {\n            console.error('🏪 店铺评论API请求失败:', err);\n            reject(err);\n          }\n        });\n      });\n    },\n    replyComment(item) {\n      console.log('💬 进入回复模式，回复用户:', item.user.nickname);\n\n      // 设置回复状态\n      this.isReplyMode = true;\n      this.currentReply = item;\n      this.inputPlaceholder = `@${item.user.nickname}`;\n\n      console.log('🔄 已设置回复模式，placeholder:', this.inputPlaceholder);\n\n      // 聚焦主输入框\n      this.$nextTick(() => {\n        if (this.$refs.mainCommentInput) {\n          this.$refs.mainCommentInput.focus();\n          console.log('🎯 主输入框已聚焦，准备回复');\n        } else {\n          console.warn('⚠️ 主输入框引用不存在');\n        }\n      });\n    },\n    \n    // 显示更多操作弹窗\n    showMoreOptions(item) {\n      this.currentMoreComment = item;\n      this.showMorePopup = true;\n    },\n    \n    // 从更多操作弹窗中点击回复\n    replyFromMore() {\n      if (this.currentMoreComment) {\n        this.showMorePopup = false;\n        // 等待更多操作弹窗关闭后再打开回复弹窗\n        setTimeout(() => {\n          this.replyComment(this.currentMoreComment);\n        }, 300);\n      }\n    },\n    \n    // 复制评论内容\n    copyComment() {\n      if (!this.currentMoreComment) return;\n      \n      uni.setClipboardData({\n        data: this.currentMoreComment.content,\n        success: () => {\n          uni.showToast({\n            title: '复制成功',\n            icon: 'success'\n          });\n          this.showMorePopup = false;\n        }\n      });\n    },\n    \n    // 删除评论\n    deleteComment() {\n      if (!this.currentMoreComment) return;\n      \n      uni.showModal({\n        title: '删除评论',\n        content: '确认要删除这条评论吗？删除后无法恢复',\n        confirmText: '删除',\n        confirmColor: '#f56c6c',\n        success: res => {\n          if (res.confirm) {\n            // 调用API删除评论 - 修复数据类型为Long兼容\n            commentApi.deleteComment(Number(this.currentMoreComment.id), {\n              userId: Number(this.userId) // 使用Number确保Long兼容性\n            }).then(res => {\n              console.log('删除评论API返回数据:', JSON.stringify(res));\n              if (res.code === 0) {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'success'\n                });\n                \n                // 关闭弹窗\n                this.showMorePopup = false;\n                \n                // 从列表中移除已删除的评论\n                const removeFromList = (list, commentId) => {\n                  const index = list.findIndex(item => item.id == commentId);\n                  if (index > -1) {\n                    list.splice(index, 1);\n                  }\n                };\n                \n                // 根据当前标签页删除对应列表中的评论\n                removeFromList(this.commentListHot, this.currentMoreComment.id);\n                removeFromList(this.commentListNew, this.currentMoreComment.id);\n                removeFromList(this.commentListMy, this.currentMoreComment.id);\n                \n                // 更新评论总数\n                this.totalComments--;\n              } else {\n                uni.showToast({\n                  title: res.message || '删除失败',\n                  icon: 'none'\n                });\n              }\n            }).catch(err => {\n              console.error('删除评论失败:', err);\n              uni.showToast({\n                title: '网络请求错误',\n                icon: 'none'\n              });\n            });\n          }\n        }\n      });\n    },\n    sendReply() {\n      if (!this.commentText.trim() || !this.currentReply) return;\n\n      // 调用回复评论API - 修复数据类型为Long兼容\n      commentApi.replyComment(Number(this.currentReply.id), {\n        userId: Number(this.userId), // 使用Number确保Long兼容性\n        content: this.commentText.trim(),\n        replyToId: null // 直接回复评论，不是回复其他回复\n      }).then(res => {\n        console.log('回复评论API返回数据:', JSON.stringify(res));\n        if (res.code === 0) {\n          // 回复成功\n          // 清空输入框并重置回复状态\n          this.clearInputAndResetState();\n\n          // 重新加载当前标签页的评论列表\n          this.fetchCommentsByType(this.activeFilter);\n          \n          uni.showToast({\n            title: '回复成功',\n            icon: 'success'\n          });\n        } else {\n          uni.showToast({\n            title: res.message || '回复失败',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        console.error('回复评论失败:', err);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      });\n    },\n\n    // 清空输入框并重置回复状态\n    clearInputAndResetState(clearInput = true) {\n      // 根据参数决定是否清空输入框\n      if (clearInput) {\n        if (this.$refs.mainCommentInput) {\n          this.$refs.mainCommentInput.clear();\n        } else {\n          this.commentText = '';\n        }\n        console.log('🔄 输入框已清空');\n      } else {\n        console.log('🔄 保留输入内容');\n      }\n\n      // 重置回复状态\n      this.isReplyMode = false;\n      this.currentReply = null;\n      this.inputPlaceholder = '说点什么...';\n\n      console.log('🔄 回复状态已重置');\n    },\n\n    // 取消回复模式\n    cancelReplyMode(clearInput = false) {\n      const wasInReplyMode = this.isReplyMode;\n      const replyTargetName = this.currentReply && this.currentReply.user ? this.currentReply.user.nickname : '未知用户';\n      const hasInputContent = this.commentText && this.commentText.trim();\n\n      // 重置回复状态，但可以选择保留输入内容\n      this.isReplyMode = false;\n      this.currentReply = null;\n      this.inputPlaceholder = '说点什么...';\n\n      // 根据参数决定是否清空输入框\n      if (clearInput) {\n        if (this.$refs.mainCommentInput) {\n          this.$refs.mainCommentInput.clear();\n        } else {\n          this.commentText = '';\n        }\n      }\n\n      if (wasInReplyMode) {\n        console.log(`❌ 已取消回复模式，原回复对象: ${replyTargetName}`);\n\n        // 根据是否有输入内容给出不同的提示\n        let toastTitle = '已取消回复';\n        if (hasInputContent && !clearInput) {\n          toastTitle = '已取消回复，内容转为评论';\n        }\n\n        // 给用户视觉反馈\n        uni.showToast({\n          title: toastTitle,\n          icon: 'none',\n          duration: 1500\n        });\n      }\n    },\n\n    formatTime(timeString) {\n      if (!timeString) return '';\n      return dayjs(timeString).fromNow();\n    },\n\n    // 获取话题信息\n    async fetchTopicInfo() {\n      if (!this.topicId) return;\n\n      try {\n        console.log('🎯 获取话题信息，topicId:', this.topicId, 'userId:', this.userId);\n        const res = await topicApi.getTopicById(this.topicId, this.userId);\n\n        console.log('🔍 API响应完整数据:', JSON.stringify(res, null, 2));\n\n        if (res.code === 0 && res.data) {\n          console.log('🔍 API响应data字段:', JSON.stringify(res.data, null, 2));\n          console.log('🔍 topicImages字段类型:', typeof res.data.topicImages);\n          console.log('🔍 topicImages字段值:', res.data.topicImages);\n          console.log('🔍 topicImages是否为数组:', Array.isArray(res.data.topicImages));\n\n          this.topicInfo = res.data;\n          console.log('✅ 话题信息获取成功，赋值后的topicInfo:', this.topicInfo);\n          console.log('✅ 赋值后的topicImages:', this.topicInfo.topicImages);\n\n          // 如果话题有图片，记录详细日志并验证URL\n          if (this.topicInfo.topicImages && this.topicInfo.topicImages.length > 0) {\n            console.log('🖼️ 话题包含图片，数量:', this.topicInfo.topicImages.length);\n            console.log('🖼️ 原始图片URL列表:', this.topicInfo.topicImages);\n\n            // 验证和处理图片URL\n            const processedImages = this.topicInfo.topicImages.map((url, index) => {\n              const processedUrl = this.processImageUrl(url);\n              console.log(`🖼️ 图片${index + 1}: ${url} -> ${processedUrl}`);\n              return processedUrl;\n            });\n\n            // 更新处理后的图片URL（可选，保持原始数据不变）\n            console.log('🖼️ 处理后图片URL列表:', processedImages);\n          } else {\n            console.warn('📷 话题不包含图片或topicImages为null/empty');\n            console.warn('📷 topicImages详细信息:', {\n              value: this.topicInfo.topicImages,\n              type: typeof this.topicInfo.topicImages,\n              isArray: Array.isArray(this.topicInfo.topicImages),\n              length: this.topicInfo.topicImages ? this.topicInfo.topicImages.length : 'N/A'\n            });\n          }\n        } else {\n          console.warn('⚠️ 获取话题信息失败:', res.message);\n          console.warn('⚠️ 完整响应:', res);\n          uni.showToast({\n            title: res.message || '获取话题信息失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('❌ 获取话题信息异常:', error);\n        uni.showToast({\n          title: '网络请求错误',\n          icon: 'none'\n        });\n      }\n    },\n\n    // 处理话题图片加载错误\n    handleTopicImageError(index) {\n      console.error('❌ 话题图片加载失败，索引:', index);\n      if (this.topicInfo && this.topicInfo.topicImages && this.topicInfo.topicImages[index]) {\n        const originalUrl = this.topicInfo.topicImages[index];\n        const processedUrl = this.processImageUrl(originalUrl);\n        console.error('❌ 原始URL:', originalUrl);\n        console.error('❌ 处理后URL:', processedUrl);\n\n        // 尝试使用备用图片或提示用户\n        this.handleImageLoadError(processedUrl, '话题图片');\n      }\n    },\n\n    // 预览话题图片\n    previewTopicImage(index) {\n      console.log('🔥 开始预览话题图片，索引:', index);\n\n      if (!this.topicInfo || !this.topicInfo.topicImages || this.topicInfo.topicImages.length === 0) {\n        uni.showToast({\n          title: '没有可预览的图片',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (index < 0 || index >= this.topicInfo.topicImages.length) {\n        uni.showToast({\n          title: '图片索引错误',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 处理图片URL，确保可访问性\n      const processedUrls = this.topicInfo.topicImages.map(url => {\n        return this.processImageUrl(url);\n      });\n\n      const processedCurrentUrl = processedUrls[index];\n      console.log('🔥 处理后的话题图片URL数组:', processedUrls);\n      console.log('🔥 当前预览URL:', processedCurrentUrl);\n\n      uni.previewImage({\n        current: processedCurrentUrl,  // 修复：使用图片URL而不是索引\n        urls: processedUrls,           // 修复：使用处理后的URL数组\n        success: () => {\n          console.log('✅ 话题图片预览成功');\n        },\n        fail: (error) => {\n          console.error('❌ 话题图片预览失败:', error);\n          uni.showToast({\n            title: '图片预览失败: ' + (error.errMsg || '未知错误'),\n            icon: 'none'\n          });\n        }\n      });\n    },\n\n    getLevelColor(level) {\n      const colors = {\n        0: '#cccbc8', // 灰色\n        1: '#c6ffe6',\n        2: '#61bc84', // 绿色\n        3: '#4d648d',\n        4: '#1F3A5F',\n        5: '#9c27b0',\n        6: '#6c35de',\n        7: '#ffd299', // 橙色\n        8: '#FF7F50', // 红色\n        9: '#f35d74', // 紫色\n        10: '#bb2649' // 粉色\n      };\n      return colors[level] || '#8dc63f';\n    },\n\n    // 处理图片URL，确保可访问性\n    processImageUrl(url) {\n      if (!url) return '';\n\n      console.log('🔥 处理图片URL:', url);\n\n      // 如果URL是相对路径，转换为绝对路径\n      if (url.startsWith('/')) {\n        const processedUrl = 'https://file.foxdance.com.cn' + url;\n        console.log('🔥 相对路径转换:', url, '->', processedUrl);\n        return processedUrl;\n      }\n\n      // 如果URL不包含协议，添加https\n      if (!url.startsWith('http://') && !url.startsWith('https://')) {\n        const processedUrl = 'https://' + url;\n        console.log('🔥 添加协议:', url, '->', processedUrl);\n        return processedUrl;\n      }\n\n      console.log('🔥 URL无需处理:', url);\n      return url;\n    },\n\n    // 处理图片加载错误\n    handleImageLoadError(url, context = '图片') {\n      console.error('❌ 图片加载失败:', context, url);\n      uni.showToast({\n        title: context + '加载失败',\n        icon: 'none'\n      });\n    },\n    // 切换评论内容的展开/收起状态\n    toggleContent(item, index, type) {\n      const wasExpanded = item.showFullContent;\n\n      // 根据类型更新对应的评论列表\n      switch (type) {\n        case 'hot':\n          this.$set(this.commentListHot[index], 'showFullContent', !item.showFullContent);\n          break;\n        case 'new':\n          this.$set(this.commentListNew[index], 'showFullContent', !item.showFullContent);\n          break;\n        case 'my':\n          this.$set(this.commentListMy[index], 'showFullContent', !item.showFullContent);\n          break;\n      }\n\n      // 如果是从展开状态收起，则滚动到评论顶部\n      if (wasExpanded) {\n        this.scrollToComment(index, type);\n      }\n    },\n\n    // 滚动到指定评论的顶部位置\n    scrollToComment(index, type) {\n      const commentId = `comment-${type}-${index}`;\n      console.log(`🎯 开始滚动到评论 - ${commentId}, 索引: ${index}, 类型: ${type}`);\n\n      // 优先使用scroll-top方法，因为它更可控\n      setTimeout(() => {\n        this.scrollToCommentByScrollTop(commentId);\n      }, 200);\n\n      // 如果scroll-top失败，再尝试scrollIntoView\n      setTimeout(() => {\n        // 检查scroll-top是否成功，如果scrollTop仍然很小，说明可能失败了\n        if (this.scrollTop < 50) {\n          console.log(`🔄 scroll-top可能失败，尝试scrollIntoView - ${commentId}`);\n          this.scrollToCommentByScrollIntoView(commentId);\n        }\n      }, 600);\n    },\n\n    // 方法1: 使用scrollIntoView滚动到评论\n    scrollToCommentByScrollIntoView(commentId) {\n      console.log(`📍 使用scrollIntoView滚动到 - ${commentId}`);\n\n      // 先验证目标元素是否存在\n      this.$nextTick(() => {\n        const query = uni.createSelectorQuery().in(this);\n        query.select(`#${commentId}`).boundingClientRect((rect) => {\n          if (rect) {\n            console.log(`📍 找到目标元素 - ${commentId}:`, rect);\n\n            // 设置scroll-into-view属性\n            this.scrollIntoView = commentId;\n\n            // 清除scrollIntoView，避免影响后续滚动\n            setTimeout(() => {\n              this.scrollIntoView = '';\n              console.log(`✅ scrollIntoView设置成功 - ${commentId}`);\n            }, 800); // 增加清除延时\n          } else {\n            console.warn(`⚠️ 未找到目标元素 - ${commentId}`);\n            // 如果scrollIntoView失败，尝试使用scroll-top方法\n            setTimeout(() => {\n              this.scrollToCommentByScrollTop(commentId);\n            }, 100);\n          }\n        }).exec();\n      });\n    },\n\n    // 方法2: 使用scroll-top属性滚动到评论\n    scrollToCommentByScrollTop(commentId) {\n      console.log(`📍 使用scroll-top滚动到 - ${commentId}`);\n\n      this.$nextTick(() => {\n        const query = uni.createSelectorQuery().in(this);\n\n        // 获取scroll-view容器和目标元素的位置信息\n        query.select('.page-scroll-view').boundingClientRect();\n        query.select(`#${commentId}`).boundingClientRect();\n        // 同时获取话题信息区域的高度，用于更精确的计算\n        query.select('.topic-info-section').boundingClientRect();\n        // 获取当前scroll-view的滚动信息\n        query.select('.page-scroll-view').scrollOffset();\n\n        query.exec((res) => {\n          console.log(`📊 scroll-top查询结果 - ${commentId}:`, res);\n\n          if (res && res.length >= 3) {\n            const scrollViewRect = res[0];  // scroll-view容器\n            const commentRect = res[1];     // 目标评论元素\n            const topicInfoRect = res[2];   // 话题信息区域\n            const scrollInfo = res[3];      // 当前滚动信息\n\n            if (scrollViewRect && commentRect) {\n              // 获取当前真实的滚动位置\n              const currentScrollTop = scrollInfo ? scrollInfo.scrollTop : (this.scrollTop || 0);\n\n              // 计算评论元素在整个内容中的绝对位置\n              const commentAbsoluteTop = currentScrollTop + (commentRect.top - scrollViewRect.top);\n\n              // 设置合理的顶部偏移量\n              const topOffset = 120;\n              const targetScrollTop = Math.max(0, commentAbsoluteTop - topOffset);\n\n              // 如果话题信息区域存在，记录其高度用于调试\n              let topicInfoHeight = 0;\n              if (topicInfoRect) {\n                topicInfoHeight = topicInfoRect.height || 0;\n                console.log(`📏 话题信息区域高度: ${topicInfoHeight}`);\n              }\n\n              console.log(`📐 scroll-top详细计算 - ${commentId}:`, {\n                scrollViewTop: scrollViewRect.top,\n                commentTop: commentRect.top,\n                currentScrollTop: currentScrollTop,\n                commentAbsoluteTop: commentAbsoluteTop,\n                topicInfoHeight: topicInfoRect ? topicInfoRect.height : 0,\n                topOffset: topOffset,\n                targetScrollTop: targetScrollTop\n              });\n\n              // 使用更可靠的滚动方式\n              if (targetScrollTop > 0) {\n                // 先重置到一个不同的值，强制触发更新\n                this.scrollTop = targetScrollTop + 1;\n                this.$nextTick(() => {\n                  this.scrollTop = targetScrollTop;\n                  console.log(`✅ scroll-top设置成功 - ${commentId}, 位置: ${targetScrollTop}`);\n                });\n              } else {\n                console.warn(`⚠️ 计算的滚动位置为0或负数 - ${commentId}, 位置: ${targetScrollTop}`);\n                // 如果计算结果为0，尝试一个最小的滚动位置\n                this.scrollTop = 50;\n              }\n            } else {\n              console.warn(`⚠️ 获取元素位置失败 - scrollView: ${!!scrollViewRect}, comment: ${!!commentRect}`);\n            }\n          } else {\n            console.warn(`⚠️ 查询结果不完整 - ${commentId}:`, res);\n          }\n        });\n      });\n    },\n\n    // 调试方法：检查DOM元素是否存在\n    debugScrollElements(index, type) {\n      const commentId = `comment-${type}-${index}`;\n      console.log(`🔍 调试滚动元素 - ${commentId}`);\n\n      const query = uni.createSelectorQuery().in(this);\n      query.select(`#${commentId}`).boundingClientRect();\n      query.select('.page-scroll-view').boundingClientRect();\n\n      query.exec((res) => {\n        console.log('🔍 调试结果:', {\n          commentId: commentId,\n          commentElement: res[0],\n          scrollViewElement: res[1],\n          hasComment: !!res[0],\n          hasScrollView: !!res[1]\n        });\n      });\n    },\n\n    // 测试方法：手动触发滚动\n    testScroll(index = 0, type = 'hot') {\n      console.log(`🧪 测试滚动功能 - ${type}-${index}`);\n\n      // 先检查元素是否存在\n      this.debugScrollElements(index, type);\n\n      // 测试scrollIntoView\n      const commentId = `comment-${type}-${index}`;\n      setTimeout(() => {\n        console.log(`🧪 设置scrollIntoView - ${commentId}`);\n        this.scrollIntoView = commentId;\n\n        setTimeout(() => {\n          this.scrollIntoView = '';\n          console.log(`🧪 测试完成 - ${commentId}`);\n        }, 1000);\n      }, 500);\n    },\n\n    // 强制滚动方法：用于测试\n    forceScrollToComment(index, type) {\n      const commentId = `comment-${type}-${index}`;\n      console.log(`🚀 强制滚动到评论 - ${commentId}`);\n\n      // 直接设置scrollIntoView，不使用延时\n      this.scrollIntoView = commentId;\n\n      // 同时尝试scroll-top方式\n      this.$nextTick(() => {\n        const query = uni.createSelectorQuery().in(this);\n        query.select(`#${commentId}`).boundingClientRect((rect) => {\n          if (rect) {\n            console.log(`🚀 强制滚动 - 找到元素:`, rect);\n            // 计算一个简单的滚动位置\n            const targetScrollTop = Math.max(0, rect.top - 100);\n            this.scrollTop = targetScrollTop;\n            console.log(`🚀 强制滚动 - 设置scrollTop: ${targetScrollTop}`);\n          } else {\n            console.warn(`🚀 强制滚动 - 未找到元素: ${commentId}`);\n          }\n        }).exec();\n      });\n    },\n\n    // 简单滚动测试：直接滚动到指定位置\n    testScrollToPosition(position = 300) {\n      console.log(`🧪 测试滚动到位置: ${position}`);\n      this.scrollTop = position;\n    },\n\n    // 获取当前滚动状态\n    getCurrentScrollStatus() {\n      console.log(`📊 当前滚动状态:`, {\n        scrollTop: this.scrollTop,\n        scrollIntoView: this.scrollIntoView\n      });\n\n      // 获取实际的滚动位置\n      const query = uni.createSelectorQuery().in(this);\n      query.select('.page-scroll-view').scrollOffset((scrollInfo) => {\n        console.log(`📊 实际滚动位置:`, scrollInfo);\n      }).exec();\n    },\n    // 处理评论数据的方法\n    processCommentData(data) {\n      // 处理数据，转换字段名称以适配前端展示\n      if (!data) {\n        console.warn('评论数据为空');\n        return [];\n      }\n      \n      const commentsList = data.comments || [];\n      console.log(`处理评论数据，评论数量:`, commentsList.length);\n      \n      if (commentsList.length > 0) {\n        commentsList.forEach(item => {\n          if (!item) return;\n          \n          // 转换字段名\n          item.created_at = item.createdAt || new Date().toISOString();\n          item.is_liked = item.isLiked || false;\n          item.reply_count = item.replyCount || 0;\n          item.likes = item.likes || 0;\n          \n          // 确保user对象存在\n          if (!item.user) {\n            item.user = {\n              id: 0,\n              nickname: '未知用户',\n              avatar: '/static/images/toux.png',\n              level: 0\n            };\n          } else {\n            // 处理用户头像为空的情况\n            if (!item.user.avatar) {\n              item.user.avatar = '/static/images/toux.png';\n            }\n            \n            // 确保其他用户字段存在\n            item.user.nickname = item.user.nickname || '未知用户';\n            item.user.level = item.user.level || 0;\n          }\n          \n          // 确保replies字段存在\n          if (!item.replies) {\n            item.replies = [];\n          } else if (item.replies.length > 0) {\n            // 转换回复中的字段名\n            item.replies.forEach(reply => {\n              if (!reply) return;\n              \n              reply.created_at = reply.createdAt || new Date().toISOString();\n              reply.is_liked = reply.isLiked || false;\n              \n              // 确保reply_to存在\n              if (reply.replyTo) {\n                reply.reply_to = reply.replyTo;\n              }\n              \n              // 确保user对象存在\n              if (!reply.user) {\n                reply.user = {\n                  id: 0,\n                  nickname: '未知用户',\n                  avatar: '/static/images/toux.png'\n                };\n              } else {\n                // 处理用户头像为空的情况\n                if (!reply.user.avatar) {\n                  reply.user.avatar = '/static/images/toux.png';\n                }\n                \n                // 确保其他用户字段存在\n                reply.user.nickname = reply.user.nickname || '未知用户';\n              }\n            });\n          }\n        });\n      }\n      \n      return commentsList;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.comment-page {\n  height: 100vh;\n  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);\n  position: relative;\n}\n\n/* 页面滚动容器 */\n.page-scroll-view {\n  width: 94%;\n  padding: 0 24rpx;\n}\n\n/* 话题图片轮播样式 - 小红书风格 */\n.topic-images-container {\n  position: relative;\n  width: 100%;\n  height: 520rpx;\n  border-radius: 32rpx;\n  overflow: hidden;\n  box-shadow: 0 16rpx 48rpx rgba(255, 105, 135, 0.15);\n  margin-top: 24rpx;\n  background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);\n}\n\n.topic-images-swiper {\n  width: 100%;\n  height: 100%;\n  border-radius: 32rpx;\n}\n\n.topic-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n  border-radius: 32rpx;\n}\n\n.topic-image:active {\n  transform: scale(0.98);\n}\n\n/* 话题信息区域样式 - 性能优化版本 */\n.topic-info-section {\n  background: rgba(255, 255, 255, 0.95);\n  /* 移除高消耗的backdrop-filter */\n  margin: 24rpx 0;\n  border-radius: 32rpx;\n  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  overflow: hidden;\n  /* 启用GPU加速 */\n  transform: translateZ(0);\n  will-change: transform;\n}\n\n.topic-header {\n  padding: 40rpx 32rpx 32rpx;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 245, 240, 0.9) 100%);\n}\n\n.topic-title {\n  font-size: 32rpx; /* 优化：从38rpx减小到32rpx，更符合移动端标准 */\n  font-weight: 600;\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  margin-bottom: 20rpx;\n  line-height: 1.4;\n  letter-spacing: 0.5rpx;\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n.topic-desc {\n  font-size: 28rpx; /* 优化：从30rpx减小到28rpx，提升精致感 */\n  color: #4a4a4a;\n  line-height: 1.7;\n  margin-bottom: 24rpx;\n  letter-spacing: 0.3rpx;\n}\n\n.topic-meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 26rpx;\n  color: #8a8a8a;\n  padding: 20rpx 0 0;\n  border-top: 1rpx solid rgba(255, 105, 135, 0.1);\n\n  .participants {\n    /* 备用颜色方案，确保在微信小程序中显示 */\n    color: #ff6b87 !important;\n    font-weight: 600;\n    background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\n    /* 渐变文字效果（如果支持） */\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n\n    /* 微信小程序兼容性处理 */\n    @supports not (-webkit-background-clip: text) {\n      color: #ff6b87 !important;\n      background: none;\n    }\n  }\n\n  .create-time {\n    margin-left: 16rpx;\n    opacity: 0.8;\n  }\n}\n\n/* 店铺信息区域样式 */\n.store-info-section {\n  background: rgba(255, 255, 255, 0.95);\n  margin: 24rpx 0;\n  border-radius: 32rpx;\n  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  overflow: hidden;\n  transform: translateZ(0);\n  will-change: transform;\n}\n\n.store-header {\n  padding: 40rpx 32rpx 32rpx;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 245, 240, 0.9) 100%);\n  display: flex;\n  // align-items: center;\n}\n\n.store-icon {\n  margin-right: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 80rpx;\n  height: 80rpx;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);\n}\n\n.store-icon .icon {\n  font-size: 40rpx;\n    width: 80rpx;\n  height: 80rpx;\n  border-radius: 20rpx;\n  color: #ffffff;\n}\n\n.store-content {\n  flex: 1;\n}\n\n.store-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: 12rpx;\n  line-height: 1.4;\n  letter-spacing: 0.3rpx;\n}\n\n.store-desc {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.5;\n  margin-bottom: 16rpx;\n  letter-spacing: 0.2rpx;\n}\n\n.store-meta {\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n  color: #8a8a8a;\n  letter-spacing: 0.2rpx;\n}\n\n.store-meta .participants {\n  color: #ff6b87;\n  font-weight: 500;\n}\n\n.store-meta .store-name {\n  color: #8a8a8a;\n  font-size: 26rpx;\n  margin-left: 8rpx;\n}\n\n.filter-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 32rpx; /* 减少垂直padding，与comment-detail.vue保持一致 */\n  background: rgba(255, 255, 255, 0.95);\n  /* 移除高消耗的backdrop-filter */\n  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);\n  margin: 0 0 24rpx 0;\n  border-radius: 32rpx;\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  left: 0;\n  /* 启用GPU加速 */\n  transform: translateZ(0);\n  will-change: transform;\n  right: 0;\n  z-index: 10;\n}\n\n/* 评论数量区域样式 */\n.comment-count-section {\n  flex: 0 0 auto;\n}\n\n.comment-count-text {\n  font-size: 28rpx; /* 优化：从30rpx减小到28rpx，更符合移动端标准 */\n  font-weight: 600;\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  letter-spacing: 0.5rpx;\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n/* 筛选标签区域样式 */\n.filter-tabs-section {\n  flex: 0 0 auto;\n}\n\n.van-tabs__wrap {\n  padding: 0;\n  border-radius: 24rpx;\n  overflow: hidden;\n}\n\n.van-tabs__nav {\n  display: flex;\n  justify-content: flex-end;\n  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n  border-radius: 48rpx;\n  padding: 6rpx; /* 减少内边距 */\n  height: 70rpx; /* 减少高度 */\n  box-shadow: inset 0 2rpx 8rpx rgba(255, 105, 135, 0.1);\n  width: auto;\n  min-width: 100rpx;\n}\n\n.van-tab {\n  flex: 0 0 auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  border-radius: 32rpx;\n  margin: 0 4rpx;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  height: 58rpx; /* 减少高度 */\n  min-width: 80rpx;\n  padding: 0 14rpx; /* 减少水平内边距 */\n}\n\n.van-tab--active {\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);\n  transform: translateY(-2rpx) scale(1.02);\n}\n\n.van-tab--active .van-tab__text {\n  color: #ffffff;\n  font-weight: 600;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n  transform: scale(1.05);\n}\n\n.van-tab__text {\n  color: #8a8a8a;\n  font-size: 24rpx; /* 减小字体大小 */\n  font-weight: 500;\n  transition: all 0.3s ease;\n  letter-spacing: 0.3rpx;\n  white-space: nowrap;\n}\n\n\n\n/* 评论列表容器 */\n.comment-list-container {\n  width: 100%;\n}\n\n.comment-list {\n  width: 100%;\n  padding: 0 0 32rpx 0;\n\n  .empty-image {\n    width: 280rpx;\n    height: 280rpx;\n    opacity: 0.6;\n    border-radius: 24rpx;\n  }\n}\n\n.comment-item {\n  background: rgba(255, 255, 255, 0.95);\n  /* 移除高消耗的backdrop-filter */\n  padding: 32rpx;\n  margin-bottom: 20rpx;\n  display: flex;\n  border-radius: 28rpx;\n  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  /* 优化过渡效果，只对transform进行过渡 */\n  transition: transform 0.2s ease;\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  /* 启用GPU加速 */\n  transform: translateZ(0);\n  will-change: transform;\n\n  &:active {\n    transform: translateY(-2rpx) scale(0.98);\n    box-shadow: 0 12rpx 36rpx rgba(255, 105, 135, 0.15);\n  }\n}\n\n.user-avatar {\n  position: relative;\n  margin-right: 28rpx;\n}\n\n.user-avatar image {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  border: 3rpx solid rgba(255, 107, 135, 0.2);\n  box-shadow: 0 6rpx 20rpx rgba(255, 107, 135, 0.15);\n  transition: all 0.3s ease;\n}\n\n.user-avatar:active image {\n  transform: scale(0.95);\n}\n\n.comment-content {\n  flex: 1;\n}\n\n.user-info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 28rpx; /* 优化：从30rpx减小到28rpx，更符合移动端用户名标准 */\n  font-weight: 600;\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #4a4a4a;\n  background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);\n  display: flex;\n  align-items: center;\n  letter-spacing: 0.3rpx;\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #4a4a4a !important;\n    background: none;\n  }\n}\n\n.user-level {\n  font-size: 22rpx;\n  color: #ffffff !important;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  border-radius: 16rpx;\n  padding: 6rpx 16rpx;\n  margin-left: 16rpx;\n  font-weight: 600;\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);\n  letter-spacing: 0.5rpx;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n  /* 确保文字显示，移除可能导致兼容性问题的属性 */\n  -webkit-text-fill-color: initial;\n  -webkit-background-clip: initial;\n  background-clip: initial;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #8a8a8a;\n  margin-top: 8rpx;\n  font-weight: 400;\n  opacity: 0.8;\n}\n\n.like-btn {\n  display: flex;\n  align-items: center;\n  padding: 12rpx 20rpx;\n  border-radius: 32rpx;\n  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);\n  border: 1rpx solid rgba(255, 107, 135, 0.2);\n  transition: all 0.3s ease;\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);\n\n  &:active {\n    background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);\n    transform: scale(0.95);\n    box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);\n  }\n}\n\n.like-btn text {\n  font-size: 26rpx;\n  color: #ff6b87;\n  margin-left: 10rpx;\n  font-weight: 600;\n  letter-spacing: 0.3rpx;\n}\n\n/* 心形图标样式 - 性能优化版本 */\n.u-icon__icon.uicon-heart-fill,\n.u-icon__icon.uicon-heart {\n  font-weight: bold;\n  transform: scale(1.1);\n  /* 优化过渡效果，缩短时长并只对transform进行过渡 */\n  transition: transform 0.2s ease;\n  color: #ff6b87;\n}\n\n.u-icon__icon.uicon-heart-fill {\n  /* 移除复杂的心跳动画，提升性能 */\n  color: #ff6b87;\n}\n\n.text {\n  font-size: 30rpx; /* 优化：从32rpx减小到30rpx，更符合移动端正文标准 */\n  line-height: 1.8;\n  margin-bottom: 20rpx;\n  color: #4a4a4a;\n  word-break: break-all;\n  font-weight: 400;\n  letter-spacing: 0.3rpx;\n}\n\n.expand-btn {\n  color: #ff6b87;\n  font-size: 26rpx; /* 优化：从28rpx减小到26rpx，按钮文字更精致 */\n  display: inline-block;\n  font-weight: 600;\n  padding: 6rpx 12rpx;\n  border-radius: 16rpx;\n  background: rgba(255, 107, 135, 0.1);\n  transition: all 0.3s ease;\n  letter-spacing: 0.3rpx;\n\n  &:active {\n    background: rgba(255, 107, 135, 0.2);\n    transform: scale(0.95);\n  }\n}\n\n.actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16rpx;\n}\n\n.reply-btn {\n  display: flex;\n  align-items: center;\n  padding: 12rpx 20rpx;\n  border-radius: 28rpx;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n  border: 1rpx solid rgba(255, 107, 135, 0.2);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);\n\n  &:active {\n    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);\n    transform: scale(0.95);\n  }\n}\n\n.reply-btn image {\n  width: 36rpx;\n  height: 36rpx;\n  filter: hue-rotate(320deg) saturate(1.2);\n}\n\n.reply-btn text {\n  font-size: 26rpx;\n  color: #ff6b87;\n  margin-left: 10rpx;\n  font-weight: 600;\n  letter-spacing: 0.3rpx;\n}\n\n.more-btn {\n  width: 48rpx;\n  height: 48rpx;\n  padding: 10rpx;\n  border-radius: 24rpx;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n  border: 1rpx solid rgba(255, 107, 135, 0.2);\n  transition: all 0.3s ease;\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);\n\n  &:active {\n    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);\n    transform: scale(0.9);\n  }\n}\n\n.more-btn image {\n  width: 100%;\n  height: 100%;\n  filter: hue-rotate(320deg) saturate(1.2);\n}\n\n.reply-preview {\n  margin-top: 24rpx;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);\n  border-radius: 20rpx;\n  padding: 28rpx;\n  border: 1rpx solid rgba(255, 107, 135, 0.1);\n  backdrop-filter: blur(10rpx);\n}\n\n.reply-item {\n  font-size: 26rpx;\n  margin-bottom: 16rpx;\n  line-height: 1.7;\n  color: #6a6a6a;\n  letter-spacing: 0.3rpx;\n}\n\n.reply-nickname {\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87 !important;\n  font-weight: 600;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n.reply-to {\n  color: #ff8e53;\n  margin: 0 8rpx;\n  font-weight: 600;\n}\n\n.reply-content {\n  color: #4a4a4a;\n  font-weight: 400;\n}\n\n.view-more {\n  font-size: 26rpx;\n  color: #ff6b87;\n  text-align: right;\n  margin-top: 16rpx;\n  font-weight: 600;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\n  border: 1rpx solid rgba(255, 107, 135, 0.2);\n  display: inline-block;\n  transition: all 0.3s ease;\n  letter-spacing: 0.3rpx;\n\n  &:active {\n    background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);\n    transform: scale(0.95);\n  }\n}\n\n/* 确保评论列表内容底部有足够空间 */\n.comment-list-bottom-space {\n  height: 160rpx; /* 比输入框高度大一些 */\n}\n\n/* 加载更多状态样式 - 小红书风格 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 48rpx 0;\n\n  .loading-text {\n    margin-left: 24rpx;\n    font-size: 30rpx;\n    color: #ff6b87;\n    font-weight: 500;\n    letter-spacing: 0.5rpx;\n  }\n}\n\n/* 骨架屏加载状态样式 */\n.loading-more-skeleton {\n  padding: 0;\n\n  /* 骨架屏淡入动画 */\n  animation: skeletonFadeIn 0.3s ease-out;\n}\n\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 48rpx 0;\n\n  text {\n    font-size: 30rpx;\n    color: #b0b0b0;\n    font-weight: 500;\n    letter-spacing: 0.5rpx;\n  }\n}\n\n/* 评论列表的底部留白空间 */\n.bottom-space {\n  height: 200rpx;\n  width: 100%;\n}\n\n.loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 0;\n\n  .loading-text {\n    margin-top: 24rpx;\n    font-size: 30rpx;\n    color: #ff6b87;\n    font-weight: 500;\n    letter-spacing: 0.5rpx;\n  }\n}\n\n.empty-tip {\n  padding: 160rpx 40rpx;\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);\n  border-radius: 32rpx;\n  margin: 24rpx;\n}\n\n.empty-text {\n  font-size: 32rpx; /* 优化：从36rpx减小到32rpx，空状态文字更合理 */\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  margin-top: 32rpx;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n.empty-subtext {\n  font-size: 28rpx;\n  color: #8a8a8a;\n  margin-top: 16rpx;\n  font-weight: 400;\n}\n\n.empty-action {\n  margin-top: 32rpx;\n  padding: 16rpx 32rpx;\n  background: linear-gradient(135deg, #ff6b87 0%, #a8727e 100%);\n  border-radius: 24rpx;\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n  &:active {\n    transform: translateY(2rpx);\n    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\n  }\n\n  text {\n    color: #ffffff;\n    font-size: 28rpx;\n    font-weight: 600;\n  }\n}\n\n/* 蒙版层样式 */\n.mask-layer {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 90;\n  opacity: 0;\n  animation: maskFadeIn 0.3s ease-out forwards;\n}\n\n.input-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 100;\n  background: #ffffff;\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\n  border-top: 1rpx solid #f0f0f0;\n  transition: bottom 0.3s ease-in-out;\n}\n\n/* 回复状态指示器样式 - 性能优化版本 */\n.reply-indicator {\n  background: rgba(255, 107, 135, 0.1);\n  border-bottom: 1rpx solid rgba(255, 107, 135, 0.15);\n  padding: 16rpx 32rpx;\n  /* 移除复杂动画，使用简单的透明度过渡 */\n  opacity: 1;\n  transition: opacity 0.2s ease;\n}\n\n.reply-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.reply-text {\n  font-size: 28rpx;\n  color: #ff6b87;\n  font-weight: 500;\n  letter-spacing: 0.3rpx;\n  /* 小红书风格渐变文字 */\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n.cancel-reply-btn {\n  width: 48rpx;\n  height: 48rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 107, 135, 0.15);\n  border-radius: 50%;\n  font-size: 24rpx;\n  color: #ff6b87;\n  font-weight: bold;\n  transition: all 0.3s ease;\n\n  &:active {\n    background: rgba(255, 107, 135, 0.25);\n    transform: scale(0.9);\n  }\n}\n\n/* 回复指示器入场动画 */\n@keyframes replyIndicatorSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-20rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.reply-popup {\n  padding: 30rpx;\n  background-color: #fff;\n  /* 确保弹窗能够平滑跟随键盘调整 - 使用transform */\n  transition: transform 0.3s ease-in-out;\n  /* 确保transform生效 */\n  position: relative;\n  z-index: 1;\n}\n\n.reply-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.reply-header text {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 自定义回复弹窗样式已移除，改为使用底部主输入框进行回复 */\n\n/* 弹窗入场动画已移除，保留键盘适配过渡效果 */\n\n/* 蒙版层淡入动画 */\n@keyframes maskFadeIn {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* 添加心跳动画 */\n@keyframes heartBeat {\n  0% {\n    transform: scale(1.1);\n  }\n  25% {\n    transform: scale(1.4) rotate(-5deg);\n  }\n  50% {\n    transform: scale(1.6) rotate(5deg);\n  }\n  75% {\n    transform: scale(1.3) rotate(-2deg);\n  }\n  100% {\n    transform: scale(1.1) rotate(0deg);\n  }\n}\n\n/* 更多操作弹窗样式 */\n.action-popup {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-top-left-radius: 32rpx;\n  border-top-right-radius: 32rpx;\n  padding: 32rpx 0;\n}\n\n.action-item {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 24rpx;\n  margin: 0 24rpx;\n  background: #ffffff;\n  transition: all 0.3s ease;\n\n  &:active {\n    background: #f8fafc;\n    transform: scale(0.98);\n  }\n}\n\n.action-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin: 0 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 第一个action-item的样式 */\n.action-item.reply {\n  border-top-left-radius: 24rpx;\n  border-top-right-radius: 24rpx;\n  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);\n}\n\n/* 第二个action-item的样式 */\n.action-item.copy {\n  margin-bottom: 24rpx;\n  border-bottom-left-radius: 24rpx;\n  border-bottom-right-radius: 24rpx;\n}\n\n/* 第三个action-item的样式 */\n.action-item.report {\n  border-top-left-radius: 24rpx;\n  border-top-right-radius: 24rpx;\n  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);\n}\n\n/* 最后一个action-item的样式 */\n.action-item.block {\n  border-bottom-left-radius: 24rpx;\n  border-bottom-right-radius: 24rpx;\n}\n\n.action-item text {\n  font-size: 28rpx;\n  color: #334155;\n  font-weight: 500;\n}\n\n.action-cancel {\n  height: 90rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #fff;\n  margin-top: 20rpx;\n  border-radius: 20rpx;\n}\n\n.action-cancel text {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.action-cancel:active {\n  background-color: #f5f5f5;\n}\n\n/* 小红书风格动画效果 */\n@keyframes heartBeat {\n  0% {\n    transform: scale(1.2);\n  }\n  14% {\n    transform: scale(1.4);\n  }\n  28% {\n    transform: scale(1.2);\n  }\n  42% {\n    transform: scale(1.4);\n  }\n  70% {\n    transform: scale(1.2);\n  }\n  100% {\n    transform: scale(1.2);\n  }\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* 性能优化：移除评论项入场动画，避免滚动时重复触发导致卡顿 */\n\n/* 骨架屏动画 */\n@keyframes skeletonFadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 渐变文字效果 - 微信小程序兼容版本 */\n.gradient-text {\n  /* 备用颜色方案，确保在微信小程序中显示 */\n  color: #ff6b87;\n  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\n\n  /* 渐变文字效果（如果支持） */\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* 微信小程序兼容性处理 */\n  @supports not (-webkit-background-clip: text) {\n    color: #ff6b87 !important;\n    background: none;\n  }\n}\n\n/* 毛玻璃效果 */\n.glass-effect {\n  backdrop-filter: blur(20rpx);\n  -webkit-backdrop-filter: blur(20rpx);\n}\n\n/* 悬浮阴影效果 */\n.floating-shadow {\n  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);\n  transition: box-shadow 0.3s ease;\n}\n\n.floating-shadow:hover {\n  box-shadow: 0 12rpx 48rpx rgba(255, 105, 135, 0.2);\n}\n\n/* 响应式设计优化 */\n@media (max-width: 750rpx) {\n  .comment-item {\n    padding: 28rpx;\n    margin-bottom: 16rpx;\n  }\n\n  .topic-title {\n    font-size: 30rpx; /* 小屏幕优化：进一步减小标题字体 */\n  }\n\n  .text {\n    font-size: 28rpx; /* 小屏幕优化：进一步减小正文字体 */\n  }\n\n  /* 小屏幕下筛选栏适配 */\n  .filter-bar {\n    padding: 16rpx 24rpx; /* 进一步减少小屏幕下的padding */\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12rpx; /* 减少间距 */\n  }\n\n  .comment-count-text {\n    font-size: 24rpx; /* 小屏幕优化：进一步减小评论数量字体 */\n  }\n\n  .van-tabs__nav {\n    min-width: 240rpx;\n    height: 60rpx; /* 小屏幕下进一步减少高度 */\n    padding: 4rpx; /* 减少内边距 */\n  }\n\n  .van-tab {\n    height: 52rpx; /* 小屏幕下减少标签高度 */\n    padding: 0 12rpx; /* 减少水平内边距 */\n  }\n\n  .van-tab__text {\n    font-size: 22rpx; /* 小屏幕下进一步减小字体 */\n  }\n}\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment.vue?vue&type=style&index=0&id=7270b30e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114332599\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}