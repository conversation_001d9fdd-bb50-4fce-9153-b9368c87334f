{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue?6b65", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue?7a31", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue?20c5", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue?5eb0", "uni-app:///pagesSub/switch/components/CommentInput.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue?f1a4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue?1fff"], "names": ["name", "props", "placeholder", "type", "default", "buttonText", "useImageButton", "max<PERSON><PERSON><PERSON>", "value", "minHeight", "maxHeight", "data", "inputText", "textareaHeight", "baseLineHeight", "autoFocus", "watch", "mounted", "methods", "adjustHeight", "query", "newHeight", "onInput", "uni", "title", "icon", "onSend", "clear", "focus", "setTimeout", "properties", "context", "res", "success", "console", "fail", "inputComponent", "blur", "onFocus", "onBlur"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA2uB,CAAgB,2sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmC/vB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;EACA;EACAO;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAR;MAAA;MACA;MACA;QACA;MACA;IACA;EACA;EACAS;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;UACA;UACA;;UAEA;UACA;UACAC;;UAEA;UACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACA;QACAC;UACAC;UACAC;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;;MAEA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;;QAEA;QACAC;UACA;;UAEA;UACAT;YACAU;YACAC;UACA;YACA;cACAC;gBACAC;kBACAC;gBACA;gBACAC;kBACAD;kBACA;kBACA;kBACA;oBACAE;kBACA;gBACA;cACA;YACA;UACA;;UAGA;QAQA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;;MAEA;MACAjB;QACAU;QACAC;MACA;QACA;UACAC;YACAC;cACAC;YACA;YACAC;cACAD;YACA;UACA;QACA;MACA;;MAGA;IAOA;IACA;IACAI;MACAJ;MACA;IACA;IACA;IACAK;MACA;MACA;MACA;IACA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAMA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxRA;AAAA;AAAA;AAAA;AAAs5C,CAAgB,sxCAAG,EAAC,C;;;;;;;;;;;ACA16C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/switch/components/CommentInput.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./CommentInput.vue?vue&type=template&id=73134684&scoped=true&\"\nvar renderjs\nimport script from \"./CommentInput.vue?vue&type=script&lang=js&\"\nexport * from \"./CommentInput.vue?vue&type=script&lang=js&\"\nimport style0 from \"./CommentInput.vue?vue&type=style&index=0&id=73134684&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"73134684\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/components/CommentInput.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CommentInput.vue?vue&type=template&id=73134684&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.useImageButton ? _vm.inputText.trim() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CommentInput.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CommentInput.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"comment-input-box\">\n    <view class=\"input-container\">\n              <textarea \n        v-model=\"inputText\" \n        :placeholder=\"placeholder\" \n        @input=\"onInput\"\n        @confirm=\"onSend\"\n        @focus=\"onFocus\"\n        @blur=\"onBlur\"\n        :maxlength=\"maxLength\"\n        :focus=\"autoFocus\"\n        :auto-blur=\"true\"\n        :show-confirm-bar=\"false\"\n        :cursor-spacing=\"10\"\n        class=\"input-textarea\"\n        :adjust-position=\"false\"\n        confirm-type=\"send\"\n        ref=\"textareaRef\"\n        :style=\"{ height: textareaHeight + 'px' }\"\n      />\n      <!-- 隐藏的镜像div，用于测量文本高度 -->\n      <view class=\"measure-box\" ref=\"measureBox\">{{inputText || placeholder}}</view>\n      <template v-if=\"useImageButton\">\n        <!-- <image src=\"/static/icon/biaoqingbao.png\" mode=\"aspectFill\" class=\"biaoqingbao-btn-inside\" @tap=\"showEmoji\"></image> -->\n        <image src=\"/static/icon/send.png\" mode=\"aspectFill\" class=\"send-btn-inside\" @tap=\"onSend\"></image>\n      </template>\n      <template v-else>\n        <text class=\"text-btn-inside\" :class=\"{'disabled': !inputText.trim()}\" @tap=\"onSend\">{{ buttonText }}</text>\n      </template>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'CommentInput',\n  props: {\n    // 输入框提示文字\n    placeholder: {\n      type: String,\n      default: '说点什么...'\n    },\n    // 按钮文本\n    buttonText: {\n      type: String,\n      default: '发送'\n    },\n    // 是否使用图片按钮\n    useImageButton: {\n      type: Boolean,\n      default: false\n    },\n    // 最大字符数\n    maxLength: {\n      type: Number,\n      default: 1000\n    },\n    // 初始值\n    value: {\n      type: String,\n      default: ''\n    },\n    // 最小高度\n    minHeight: {\n      type: Number,\n      default: 40\n    },\n    // 最大高度\n    maxHeight: {\n      type: Number,\n      default: 120\n    }\n  },\n  data() {\n    return {\n      inputText: this.value,\n      textareaHeight: this.minHeight,\n      baseLineHeight: 40, // 基础行高，单位px\n      autoFocus: false // 控制自动获取焦点\n    }\n  },\n  watch: {\n    value(newVal) {\n      this.inputText = newVal;\n      this.$nextTick(() => {\n        this.adjustHeight();\n      });\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.adjustHeight();\n    });\n  },\n  methods: {\n    // 调整输入框高度\n    adjustHeight() {\n      // 使用选择器查询获取镜像div的高度\n      const query = uni.createSelectorQuery().in(this);\n      query.select('.measure-box').boundingClientRect(data => {\n        if (data) {\n          // 获取文本实际高度\n          const scrollHeight = data.height;\n          \n          // 计算应该设置的高度（不小于最小高度，不大于最大高度）\n          let newHeight = Math.max(this.minHeight, scrollHeight);\n          newHeight = Math.min(newHeight, this.maxHeight);\n          \n          // 如果高度有变化，则更新\n          if (this.textareaHeight !== newHeight) {\n            this.textareaHeight = newHeight;\n          }\n        }\n      }).exec();\n    },\n    \n    onInput() {\n      // 限制字符长度\n      if (this.inputText.length > this.maxLength) {\n        this.inputText = this.inputText.slice(0, this.maxLength);\n        uni.showToast({\n          title: `评论字数不能超过${this.maxLength}字`,\n          icon: 'none'\n        });\n      }\n      \n      // 调整高度\n      this.$nextTick(() => {\n        this.adjustHeight();\n      });\n      \n      // 向父组件发送输入内容\n      this.$emit('input', this.inputText);\n    },\n    \n    onSend() {\n      if (!this.inputText.trim()) return;\n      \n      // 向父组件发送提交事件\n      this.$emit('send', this.inputText);\n    },\n    // 清空输入框\n    clear() {\n      this.inputText = '';\n      this.textareaHeight = this.minHeight;\n      this.$emit('input', '');\n    },\n    // 设置焦点\n    focus() {\n      // 先重置再设置 autoFocus，确保每次都能触发 focus 变更\n      this.autoFocus = false;\n\n      // 使用 nextTick 确保视图已更新\n      this.$nextTick(() => {\n        this.autoFocus = true;\n\n        // 微信小程序需要使用额外方法确保聚焦\n        setTimeout(() => {\n          // 微信小程序使用选择器进行聚焦\n          // #ifdef MP-WEIXIN\n          const query = uni.createSelectorQuery().in(this);\n          query.select('.input-textarea').fields({\n            properties: ['focus'],\n            context: true\n          }, res => {\n            if (res && res.context) {\n              res.context.focus({\n                success: () => {\n                  console.log('微信小程序设置焦点成功');\n                },\n                fail: (err) => {\n                  console.error('微信小程序设置焦点失败:', err);\n                  // 失败后尝试原生方法\n                  const inputComponent = this.$refs.textareaRef;\n                  if (inputComponent) {\n                    inputComponent.focus();\n                  }\n                }\n              });\n            }\n          }).exec();\n          // #endif\n\n          // H5、App 和其他平台\n          // #ifndef MP-WEIXIN\n          const inputComponent = this.$refs.textareaRef;\n          if (inputComponent) {\n            // 使用 focus() 方法\n            inputComponent.focus();\n          }\n          // #endif\n        }, 150);\n      });\n    },\n\n    // 失去焦点\n    blur() {\n      // 重置autoFocus状态\n      this.autoFocus = false;\n\n      // 微信小程序使用选择器进行失焦\n      // #ifdef MP-WEIXIN\n      const query = uni.createSelectorQuery().in(this);\n      query.select('.input-textarea').fields({\n        properties: ['blur'],\n        context: true\n      }, res => {\n        if (res && res.context) {\n          res.context.blur({\n            success: () => {\n              console.log('微信小程序失去焦点成功');\n            },\n            fail: (err) => {\n              console.error('微信小程序失去焦点失败:', err);\n            }\n          });\n        }\n      }).exec();\n      // #endif\n\n      // H5、App 和其他平台\n      // #ifndef MP-WEIXIN\n      const inputComponent = this.$refs.textareaRef;\n      if (inputComponent) {\n        inputComponent.blur();\n      }\n      // #endif\n    },\n    // 输入框获取焦点\n    onFocus(e) {\n      console.log('输入框获得焦点');\n      this.$emit('focus', e);\n    },\n    // 输入框失去焦点\n    onBlur(e) {\n      this.$emit('blur', e);\n      // 重置autoFocus，确保下次可以再次触发聚焦\n      this.autoFocus = false;\n    }\n    // // 显示表情选择器\n    // showEmoji() {\n    //   // #ifdef MP-WEIXIN\n    //   // 微信小程序中，直接调用表情选择器API\n    //   const that = this;\n    //   wx.showEmojiPanel({\n    //     success: function(res) {\n    //       console.log('表情面板显示成功');\n    //       // 监听表情选择\n    //       wx.onEmojiSelected(function(emojiRes) {\n    //         // 将选中的表情添加到输入框\n    //         that.inputText += emojiRes.emoji;\n    //         that.$emit('input', that.inputText);\n    //       });\n    //     },\n    //     fail: function(err) {\n    //       console.error('显示表情面板失败', err);\n    //       // 如果新API不可用，尝试使用旧版API\n    //       wx.getSystemInfo({\n    //         success: function(sysInfo) {\n    //           if (sysInfo.SDKVersion >= '2.9.0') {\n    //             uni.showToast({\n    //               title: '表情功能暂不可用',\n    //               icon: 'none'\n    //             });\n    //           }\n    //         }\n    //       });\n    //     }\n    //   });\n    //   // #endif\n      \n    //   // #ifndef MP-WEIXIN\n    //   uni.showToast({\n    //     title: '表情功能仅在微信小程序中可用',\n    //     icon: 'none'\n    //   });\n    //   // #endif\n    // }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.comment-input-box {\n  display: flex;\n  padding: 0 30rpx;\n  background-color: #fff;\n  //border-top: 1rpx solid #eee;\n  //box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);\n  //border-radius: 20rpx;\n  \n  .input-container {\n    flex: 1;\n    position: relative;\n    display: flex;\n    align-items: center;\n    width: 100%;\n    \n    .input-textarea {\n      flex: 1;\n      min-height: 80rpx;\n      background: #f8fafc;\n      border-radius: 24rpx;\n      padding: 20rpx 120rpx 20rpx 30rpx;\n      margin: 20rpx 0;\n      font-size: 28rpx;\n      border: 1rpx solid #e2e8f0;\n      width: 100%;\n      box-sizing: border-box;\n      line-height: 40rpx;\n      overflow-y: auto;\n      color: #1e293b;\n      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n      transition: all 0.3s ease;\n\n      &:focus {\n        border-color: #667eea;\n        box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);\n        background: #ffffff;\n      }\n    }\n    \n    // 隐藏的镜像div，用于测量文本高度\n    .measure-box {\n      position: absolute;\n      visibility: hidden;\n      width: calc(100% - 150rpx); // 减去按钮和padding的空间\n      font-size: 28rpx;\n      line-height: 40rpx;\n      padding: 20rpx 0;\n      white-space: pre-wrap; // 保留换行符和空格\n      word-wrap: break-word; // 允许长单词换行\n      box-sizing: border-box;\n      top: -9999px;\n      left: -9999px;\n      border: 1rpx solid transparent;\n    }\n    \n    .send-btn-inside {\n      position: absolute;\n      right: 10rpx;\n      top: 25rpx;\n      width: 70rpx;\n      height: 70rpx;\n      border-radius: 30rpx;\n      padding: 0;\n      z-index: 2;\n    }\n    \n    .text-btn-inside {\n      position: absolute;\n      right: 20rpx;\n      top: 45rpx;\n      font-size: 28rpx;\n      color: #667eea;\n      font-weight: 600;\n      z-index: 2;\n      padding: 8rpx 16rpx;\n      border-radius: 16rpx;\n      background: rgba(102, 126, 234, 0.1);\n      transition: all 0.3s ease;\n\n      &:active {\n        background: rgba(102, 126, 234, 0.2);\n        transform: scale(0.95);\n      }\n\n      &.disabled {\n        color: #94a3b8;\n        background: rgba(148, 163, 184, 0.1);\n      }\n    }\n  }\n  .biaoqingbao-btn-inside {\n    position: absolute;\n    right: 80rpx;\n    width: 55rpx;\n    height: 55rpx;\n    border-radius: 30rpx;\n    margin-right: 10rpx;\n  }\n  \n  // 移除原来的外部按钮样式\n  // .send-btn {\n  //   margin-left: 20rpx;\n  //   \n  //   &[disabled] {\n  //     background: linear-gradient(135deg, #a0cfbb, #a0cfbb);\n  //     color: #fff;\n  //     box-shadow: none;\n  //   }\n  // }\n  // \n  // button.send-btn {\n  //   width: 140rpx;\n  //   height: 80rpx;\n  //   border-radius: 40rpx;\n  //   font-size: 28rpx;\n  //   padding: 0;\n  //   line-height: 80rpx;\n  //   background: linear-gradient(135deg, #4b8df8, #3b7ff2);\n  //   color: #fff;\n  //   box-shadow: 0 4rpx 8rpx rgba(75, 141, 248, 0.3);\n  //   transition: all 0.3s;\n  //   \n  //   &:active {\n  //     transform: translateY(2rpx);\n  //     box-shadow: 0 2rpx 4rpx rgba(75, 141, 248, 0.3);\n  //   }\n  // }\n  // \n  // image.send-btn {\n  //   width: 80rpx;\n  //   height: 80rpx;\n  //   border-radius: 40rpx;\n  //   padding: 0;\n  //   line-height: 80rpx;\n  // }\n}\n</style> ", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CommentInput.vue?vue&type=style&index=0&id=73134684&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CommentInput.vue?vue&type=style&index=0&id=73134684&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752030104637\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}