{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/ReplySkeleton.vue?bcb6", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/ReplySkeleton.vue?436b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/ReplySkeleton.vue?6425", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/ReplySkeleton.vue?2c55", "uni-app:///pagesSub/switch/components/ReplySkeleton.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/ReplySkeleton.vue?b157", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/ReplySkeleton.vue?ec2e"], "names": ["name"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4uB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+BhwB;EACAA;AACA;AAAA,2B;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAu5C,CAAgB,uxCAAG,EAAC,C;;;;;;;;;;;ACA36C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/switch/components/ReplySkeleton.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ReplySkeleton.vue?vue&type=template&id=d8ebce58&scoped=true&\"\nvar renderjs\nimport script from \"./ReplySkeleton.vue?vue&type=script&lang=js&\"\nexport * from \"./ReplySkeleton.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ReplySkeleton.vue?vue&type=style&index=0&id=d8ebce58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d8ebce58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/components/ReplySkeleton.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ReplySkeleton.vue?vue&type=template&id=d8ebce58&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ReplySkeleton.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ReplySkeleton.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"reply-skeleton\">\n    <!-- 用户头像骨架 -->\n    <view class=\"skeleton-avatar\">\n      <view class=\"skeleton-circle\"></view>\n    </view>\n    \n    <!-- 回复内容骨架 -->\n    <view class=\"skeleton-content\">\n      <!-- 用户名和回复对象骨架 -->\n      <view class=\"skeleton-header\">\n        <view class=\"skeleton-line skeleton-name\"></view>\n        <view class=\"skeleton-line skeleton-reply-to\"></view>\n        <view class=\"skeleton-line skeleton-time\"></view>\n      </view>\n      \n      <!-- 回复文本骨架 -->\n      <view class=\"skeleton-text\">\n        <view class=\"skeleton-line skeleton-text-line1\"></view>\n        <view class=\"skeleton-line skeleton-text-line2\"></view>\n      </view>\n      \n      <!-- 操作按钮骨架 -->\n      <view class=\"skeleton-actions\">\n        <view class=\"skeleton-line skeleton-action\"></view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'ReplySkeleton'\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.reply-skeleton {\n  padding: 28rpx 0;\n  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);\n  display: flex;\n  animation: skeletonPulse 1.5s ease-in-out infinite;\n}\n\n.skeleton-avatar {\n  margin-right: 24rpx;\n  \n  .skeleton-circle {\n    width: 72rpx;\n    height: 72rpx;\n    border-radius: 50%;\n    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n    background-size: 200% 100%;\n    animation: skeletonShimmer 1.5s infinite;\n  }\n}\n\n.skeleton-content {\n  flex: 1;\n}\n\n.skeleton-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n  gap: 12rpx;\n  flex-wrap: wrap;\n}\n\n.skeleton-line {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeletonShimmer 1.5s infinite;\n  border-radius: 6rpx;\n}\n\n.skeleton-name {\n  width: 100rpx;\n  height: 28rpx;\n}\n\n.skeleton-reply-to {\n  width: 80rpx;\n  height: 24rpx;\n}\n\n.skeleton-time {\n  width: 60rpx;\n  height: 20rpx;\n}\n\n.skeleton-text {\n  margin-bottom: 16rpx;\n}\n\n.skeleton-text-line1 {\n  width: 90%;\n  height: 28rpx;\n  margin-bottom: 10rpx;\n}\n\n.skeleton-text-line2 {\n  width: 70%;\n  height: 28rpx;\n}\n\n.skeleton-actions {\n  display: flex;\n  gap: 16rpx;\n}\n\n.skeleton-action {\n  width: 60rpx;\n  height: 24rpx;\n}\n\n/* 骨架屏动画 */\n@keyframes skeletonShimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n@keyframes skeletonPulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.8;\n  }\n}\n\n/* 小红书风格优化 */\n.reply-skeleton {\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 107, 135, 0.08),\n      transparent\n    );\n    animation: skeletonSweep 2.5s infinite;\n  }\n}\n\n@keyframes skeletonSweep {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n\n/* 响应式适配 */\n@media (max-width: 750rpx) {\n  .skeleton-avatar .skeleton-circle {\n    width: 64rpx;\n    height: 64rpx;\n  }\n  \n  .skeleton-text-line1,\n  .skeleton-text-line2 {\n    height: 26rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ReplySkeleton.vue?vue&type=style&index=0&id=d8ebce58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ReplySkeleton.vue?vue&type=style&index=0&id=d8ebce58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114223817\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}