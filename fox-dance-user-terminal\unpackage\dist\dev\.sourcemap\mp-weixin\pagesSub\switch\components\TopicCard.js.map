{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue?28f7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue?ffb4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue?5ca4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue?1201", "uni-app:///pagesSub/switch/components/TopicCard.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/components/TopicCard.vue?e131"], "names": ["dayjs", "name", "props", "topic", "type", "required", "computed", "hasCoverImage", "methods", "goToDetail", "uni", "url", "formatTime", "handleImageError", "console"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAwuB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiC5vB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEAA;AACAA;AAAA,eAEA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA,gCACA,uCACA,oCACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAm5C,CAAgB,mxCAAG,EAAC,C", "file": "pagesSub/switch/components/TopicCard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./TopicCard.vue?vue&type=template&id=4c84b6dc&scoped=true&\"\nvar renderjs\nimport script from \"./TopicCard.vue?vue&type=script&lang=js&\"\nexport * from \"./TopicCard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./TopicCard.vue?vue&type=style&index=0&id=4c84b6dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4c84b6dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/components/TopicCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TopicCard.vue?vue&type=template&id=4c84b6dc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.topic.createTime ? _vm.formatTime(_vm.topic.createTime) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TopicCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TopicCard.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"topic-card\" @click=\"goToDetail\">\n\t\t<view class=\"topic-content\" :class=\"{ 'no-image': !hasCoverImage }\">\n\t\t\t<!-- 左侧内容区域 -->\n\t\t\t<view class=\"topic-left\">\n\t\t\t\t<view class=\"topic-title\">\n\t\t\t\t\t<image class=\"topic-icon\" src=\"/static/icon/topic-title.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<text class=\"title-text\">{{ topic.title }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"topic-desc\">{{ topic.description }}</view>\n\t\t\t\t<view class=\"topic-stat\">\n\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t\t<text class=\"participants\">{{ topic.participants }}人参与此话题</text>\n\t\t\t\t\t<text class=\"time\" v-if=\"topic.createTime\">· {{ formatTime(topic.createTime) }}</text>\n\t\t\t\t\t<image class=\"arrow\" src=\"/static/icon/right.png\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 右侧封面图区域（仅在有图片时显示） -->\n\t\t\t<view class=\"topic-right\" v-if=\"hasCoverImage\">\n\t\t\t\t<image\n\t\t\t\t\tclass=\"cover-image\"\n\t\t\t\t\t:src=\"topic.coverImage\"\n\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t@error=\"handleImageError\"\n\t\t\t\t\t:lazy-load=\"true\"\n\t\t\t\t></image>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport dayjs from 'dayjs';\n\timport relativeTime from 'dayjs/plugin/relativeTime';\n\timport 'dayjs/locale/zh-cn';\n\t\n\tdayjs.extend(relativeTime);\n\tdayjs.locale('zh-cn');\n\t\n\texport default {\n\t\tname: 'TopicCard',\n\t\tprops: {\n\t\t\ttopic: {\n\t\t\t\ttype: Object,\n\t\t\t\trequired: true\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 判断是否有封面图\n\t\t\thasCoverImage() {\n\t\t\t\treturn this.topic.coverImage &&\n\t\t\t\t\t   this.topic.coverImage.trim() !== '' &&\n\t\t\t\t\t   this.topic.coverImage !== 'null' &&\n\t\t\t\t\t   this.topic.coverImage !== 'undefined';\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tgoToDetail() {\n\t\t\t\tif(this.topic.id == 9) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pagesSub/store/store-list'\n\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pagesSub/switch/comment?topicId=' + this.topic.id + '&content_type=topic'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tformatTime(timeString) {\n\t\t\t\tif (!timeString) return '';\n\t\t\t\treturn dayjs(timeString).fromNow();\n\t\t\t},\n\t\t\thandleImageError() {\n\t\t\t\tconsole.warn('话题封面图加载失败:', this.topic.coverImage);\n\t\t\t\t// 可以在这里设置默认图片或隐藏图片\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.topic-card {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 32rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n\t\tborder: 1rpx solid #f0f0f0;\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: translateY(-4rpx);\n\t\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);\n\t\t}\n\n\t\t.topic-content {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: flex-start;\n\t\t\tgap: 24rpx;\n\n\t\t\t// 无图片时的布局优化\n\t\t\t&.no-image {\n\t\t\t\t.topic-left {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tflex: none;\n\n\t\t\t\t\t.topic-desc {\n\t\t\t\t\t\t// 无图片时描述文字可以显示更多行\n\t\t\t\t\t\t-webkit-line-clamp: 4;\n\t\t\t\t\t\tmax-height: 120rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.topic-stat {\n\t\t\t\t\t\tmargin-top: 24rpx; // 增加间距，让布局更舒适\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.topic-left {\n\t\t\t\tflex: 1;\n\t\t\t\tmin-width: 0; // 防止文字溢出\n\t\t\t\t\n\t\t\t\t.topic-title {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\t\n\t\t\t\t\t.topic-icon {\n\t\t\t\t\t\twidth: 45rpx;\n\t\t\t\t\t\theight: 45rpx;\n\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.title-text {\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tline-height: 1.3;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.topic-desc {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #64748b;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\tline-height: 1.6;\n\t\t\t\t\tdisplay: -webkit-box;\n\t\t\t\t\t-webkit-box-orient: vertical;\n\t\t\t\t\t-webkit-line-clamp: 2;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tword-break: break-all;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.topic-stat {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t\n\t\t\t\t\t.dot {\n\t\t\t\t\t\twidth: 10rpx;\n\t\t\t\t\t\theight: 10rpx;\n\t\t\t\t\t\tbackground-color: #ff6b87;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.participants {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #999999;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.time {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #999999;\n\t\t\t\t\t\tmargin-left: 8rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.arrow {\n\t\t\t\t\t\twidth: 20rpx;\n\t\t\t\t\t\theight: 20rpx;\n\t\t\t\t\t\tmargin-left: 4rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 右侧封面图区域\n\t\t\t.topic-right {\n\t\t\t\twidth: 200rpx;\n\t\t\t\theight: 150rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\tbackground: #f8fafc;\n\t\t\t\tborder: 1rpx solid #e2e8f0;\n\n\t\t\t\t.cover-image {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tobject-fit: cover;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style> ", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TopicCard.vue?vue&type=style&index=0&id=4c84b6dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./TopicCard.vue?vue&type=style&index=0&id=4c84b6dc&lang=scss&scoped=true&\""], "sourceRoot": ""}