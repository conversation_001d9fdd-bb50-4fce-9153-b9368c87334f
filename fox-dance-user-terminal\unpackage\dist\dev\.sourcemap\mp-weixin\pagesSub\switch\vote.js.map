{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/vote.vue?8661", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/vote.vue?7f08", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/vote.vue?4d4c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/vote.vue?fa07", "uni-app:///pagesSub/switch/vote.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/vote.vue?d658", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/vote.vue?e68f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "metroLines", "selectedLineIndex", "selectedLineId", "currentLineData", "selected", "remainingVotes", "voteRecords", "debugShown", "locationAuth", "voteTitle", "voteInfo", "voteTips", "allStationsData", "isAllStationsSelected", "displayMode", "voteSuccess", "animatingStations", "computed", "currentStations", "currentVoteCounts", "votesMap", "sortedStations", "station", "votes", "mounted", "methods", "toggleShowMoreStations", "getVoteInfo", "BASE_URL", "uni", "url", "method", "response", "console", "getBaseUrl", "getUserInfo", "token", "userId", "checkLogin", "title", "icon", "duration", "setTimeout", "getRemainingVotes", "getAllMetroLines", "allOption", "id", "lineName", "index", "getMetroLineById", "buildAllStationsRanking", "linesResponse", "lines", "allStations", "line", "lineDetailResponse", "lineData", "stations", "voteCounts", "handleLineChange", "select", "vote", "selectedStation", "authStatus", "content", "success", "type", "resolve", "fail", "reject", "location", "lineId", "latitude", "longitude", "requestUrl", "header", "getLocationAuth", "handleVoteSuccess", "handleVoteError", "errorMsg", "getPercent", "isVotedStation", "getUserVoteRecords", "onShareTimeline", "onShareAppMessage", "path", "checkLocationPermission", "requestLocationPermission", "scope", "getBtnText", "getTotalVotes"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAotB,CAAgB,msBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyIxuB;EACAC;IACA;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IACA;IACAC;MACA;QACA;UAAA;QAAA;MACA;MAEA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;UACA;YACAC;UACA;QACA;QACA;MACA;MAEA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QAAA;UACAC;UACAC;QACA;MAAA;;MAEA;MACA;QAAA;MAAA;IACA;EACA;EAEAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;IACA;MACA;MACA;IACA;;IAEA;IACA;MACA;MACA;;MAEA;MACA;IACA;;IAEA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAIAC;gBACA;kBACAvB,+BACA;kBACA;kBACA;kBACA;gBACA;kBACAuB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAEA;;MAGA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QAAAF;MACA;QACAP;UACAU;UACAC;UACAC;QACA;;QAEA;QACAC;UACAb;YACAC;UACA;QACA;QAEA;MACA;MACA;IACA;IAEA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,qBAEA;gBAAA,IACAN;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAT;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;kBACA;gBACA;kBACAC;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAhB;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;kBACA;kBACAa;oBACAC;oBAAA;oBACAC;kBACA;kBACA;;kBAEA;kBACA;oBACAC;sBAAA;oBAAA;oBACA;sBACA;sBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACAnB;oBACAU;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;kBACAU;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACArB;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;kBACA;gBACA;kBACAH;oBACAU;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;kBACAU;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACArB;kBACAU;gBACA;gBAAA;gBAGAX,gCACA;gBAAA;gBAAA,OACAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAoB;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAC,kBAEA;gBAAA,uCACAD;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAE;gBAAA;gBAAA,OACAzB;kBACAC;kBACAC;gBACA;cAAA;gBAHAwB;gBAKA;kBACAC;kBACAC;kBACAC,wCAEA;kBAAA,wCACAD;kBAAA;oBAAA;sBAAA;sBACA;sBACA;sBACA;wBAAA;sBAAA;sBAEA;wBACA;wBACAJ;wBACA;wBACA;0BACAA;wBACA;sBACA;wBACA;wBACAA;0BACA/B;0BACAC;0BACA6B;wBACA;sBACA;oBAAA;oBAnBA;sBAAA;oBAoBA;kBAAA;oBAAA;kBAAA;oBAAA;kBAAA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBACA;gBACApB;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;cAAA;gBAAA;gBAEAJ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8B;MAAA;MACA;MACA;MAEA;QACA;;QAEA;QACA;UACA;UACA;UACA;UACA;YACA;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBACAhC;kBACAU;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAZ;kBACAU;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,qBAIA;gBAAA,IACAJ;kBAAA;kBAAA;gBAAA;gBACAR;kBACAU;kBACAC;gBACA;gBAAA;cAAA;gBAIAsB;gBACAlC;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAmC;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAlC;kBACAU;kBACAyB;kBACAC;oBACA;sBACApC;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAA;kBACAU;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBACAV;oBACAqC;oBAAA;oBACAD;sBACAE;oBACA;oBACAC;sBACAC;oBACA;kBACA;gBACA;kBACApC;kBACAJ;kBACAA;oBACAU;oBACAC;oBACAC;kBACA;kBACA;gBACA;cAAA;gBAnBA6B;gBAqBAzC;;gBAEA;gBACAI;kBACAsC;kBACAjD;kBACAe;kBACAmC;kBACAC;gBACA;;gBAEA;gBACAC,6OAGA;gBAAA;gBAAA,OACA7C;kBACAC;kBACAC;kBACA4C;oBACA;kBACA;gBACA;cAAA;gBANA3C;gBAQA;gBACAC;gBAEA;kBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;kBAAA;kBACAJ;oBACAU;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAoC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACA/C;oBACAoC;sBACA;wBACAE;sBACA;wBACAA;sBACA;oBACA;oBACAC;sBACAD;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAU;MAAA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACAnC;QACAT;QACA;QACA;QACA;;QAEA;QACA;QACA;UACA;QACA;MACA;;MAEA;MACAS;QACA;MACA;MAEAb;QACAU;QACAC;MACA;IACA;IAEA;IACAsC;MACA;MAEA;QACA;QACA7C;QACAA;;QAEA;QACA8C;;QAEA;QACA;UACA;UACA;YACA;YACA;;YAEA;cACAA;YACA;cACAA;YACA;YACA;YACA;YACA;YACA;UACA;QACA;UACAA;QACA;MACA;MAEAlD;QACAU;QACAC;QACAC;MACA;IACA;IAEAuC;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACAhD;QACAA;QACAA;QACA;MACA;;MAEA;MACA;QACA;UAAA;QAAA;MACA;;MAEA;MACA;QACA;QACA;QACA;QAEA;MACA;IACA;IAEA;IACAiD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA,sBACA;gBAAA,IACA7C;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAT;gBAAA;gBAEAC;kBACAU;gBACA;gBAAA;gBAAA,OAEAV;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKAH;gBAEA;kBACA;kBACA;kBACAI;;kBAEA;kBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACAI;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkD;MACA;QACA5C;MACA;IACA;IAEA;IACA6C;MAEA;QACA7C;QACA8C;MACA;IACA;IAEA;IACAC;MAAA;MACAzD;QACAoC;UACA;UACA;UAEA;YACA;YACApC;cACAU;cACAyB;cACAC;gBACA;kBACApC;oBACAoC;sBACA;sBACA;sBAEA;wBACApC;0BACAU;0BACAC;wBACA;sBACA;wBACAX;0BACAU;0BACAC;wBACA;sBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;QACA4B;UACA;QACA;MACA;IACA;IAEA;IACAmB;MAAA;MACA;MACA1D;QACA2D;QACAvB;UACA;UACApC;YACAU;YACAC;UACA;QACA;QACA4B;UACA;UACAvC;YACAU;YACAyB;YACAC;cACA;gBACApC;kBACAoC;oBACA;oBACA;sBACApC;wBACAU;wBACAC;sBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiD;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACz8BA;AAAA;AAAA;AAAA;AAAm3C,CAAgB,8wCAAG,EAAC,C;;;;;;;;;;;ACAv4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/switch/vote.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/switch/vote.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./vote.vue?vue&type=template&id=de3fba80&scoped=true&\"\nvar renderjs\nimport script from \"./vote.vue?vue&type=script&lang=js&\"\nexport * from \"./vote.vue?vue&type=script&lang=js&\"\nimport style0 from \"./vote.vue?vue&type=style&index=0&id=de3fba80&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"de3fba80\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/switch/vote.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vote.vue?vue&type=template&id=de3fba80&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.metroLines.length\n  var g1 = _vm.sortedStations.length\n  var m0 = g1 > 0 ? _vm.getTotalVotes() : null\n  var l0 =\n    g1 > 0\n      ? _vm.__map(_vm.sortedStations, function (item, idx) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.isVotedStation(item.station)\n          var m2 = _vm.getPercent(item.votes)\n          var m3 = _vm.getPercent(item.votes)\n          var g2 = _vm.animatingStations.includes(item.station)\n          return {\n            $orig: $orig,\n            m1: m1,\n            m2: m2,\n            m3: m3,\n            g2: g2,\n          }\n        })\n      : null\n  var g3 =\n    g1 > 0\n      ? _vm.isAllStationsSelected &&\n        _vm.displayMode !== \"all\" &&\n        _vm.allStationsData &&\n        _vm.allStationsData.length > (_vm.displayMode === \"more\" ? 30 : 15)\n      : null\n  var m4 = _vm.getBtnText()\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      _vm.select(_vm.currentStations.indexOf(item.station))\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        l0: l0,\n        g3: g3,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vote.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vote.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"vote-container\">\r\n    <!-- 头部区域 -->\r\n    <view class=\"header-section\">\r\n      <view class=\"vote-title\">{{ voteTitle }}</view>\r\n      <view class=\"vote-info\">{{ voteInfo }}</view>\r\n    </view>\r\n\r\n    <!-- 线路选择卡片 -->\r\n    <view class=\"card dropdown-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">选择地铁线路</text>\r\n      </view>\r\n      <picker @change=\"handleLineChange\" :value=\"selectedLineIndex\" :range=\"metroLines\" range-key=\"lineName\">\r\n        <view class=\"picker\">\r\n          <text v-if=\"metroLines.length > 0\" class=\"picker-text\">{{ metroLines[selectedLineIndex].lineName }}</text>\r\n          <text v-else class=\"picker-loading\">加载中...</text>\r\n          <text class=\"picker-arrow\">▼</text>\r\n        </view>\r\n      </picker>\r\n    </view>\r\n\r\n    <!-- 提示信息卡片 -->\r\n    <view class=\"card notice-card\">\r\n      <view class=\"notice-content\">\r\n        <image src=\"/static/images/icon78.png\" class=\"notice-icon\"></image>\r\n        <view class=\"notice-text\">\r\n          <text class=\"notice-title\">投票须知</text>\r\n          <text class=\"notice-desc\">仅限广州地区用户参与投票，需要获取位置权限</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 投票列表 -->\r\n    <view class=\"card vote-list-card\" v-if=\"sortedStations.length > 0\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">站点排行榜</text>\r\n        <text class=\"total-votes\">总票数: {{ getTotalVotes() }}</text>\r\n      </view>\r\n\r\n      <view class=\"vote-list\">\r\n        <view\r\n          v-for=\"(item, idx) in sortedStations\"\r\n          :key=\"item.station\"\r\n          class=\"vote-item\"\r\n          :class=\"{\r\n            voted: isVotedStation(item.station),\r\n            selected: selected !== -1 && currentStations[selected] === item.station,\r\n            'rank-top': idx < 3\r\n          }\"\r\n          @click=\"select(currentStations.indexOf(item.station))\"\r\n        >\r\n          <!-- 排名标识 -->\r\n          <view class=\"rank-badge\" v-if=\"idx < 3\">\r\n            <text class=\"rank-number\">{{ idx + 1 }}</text>\r\n          </view>\r\n          <view class=\"rank-number-normal\" v-else>{{ idx + 1 }}</view>\r\n\r\n          <!-- 站点信息 -->\r\n          <view class=\"station-info\">\r\n            <text class=\"station-name\">{{ item.station }}</text>\r\n            <view class=\"vote-progress\">\r\n              <view class=\"vote-bar\">\r\n                <view\r\n                  class=\"vote-bar-inner\"\r\n                  :style=\"{\r\n                    width: getPercent(item.votes) + '%',\r\n                    animationDelay: (idx * 0.1) + 's'\r\n                  }\"\r\n                ></view>\r\n              </view>\r\n              <text class=\"vote-percentage\">{{ getPercent(item.votes) }}%</text>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 票数显示 -->\r\n          <view class=\"vote-count-container\">\r\n            <text class=\"vote-count\" :class=\"{ 'count-animate': animatingStations.includes(item.station) }\">\r\n              {{ item.votes }}\r\n            </text>\r\n            <text class=\"vote-unit\">票</text>\r\n          </view>\r\n\r\n          <!-- 选中状态指示器 -->\r\n          <view class=\"selection-indicator\" v-if=\"selected !== -1 && currentStations[selected] === item.station\">\r\n            <text class=\"check-icon\">✓</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 更多按钮 -->\r\n        <view\r\n          v-if=\"isAllStationsSelected && displayMode !== 'all' && allStationsData && allStationsData.length > (displayMode === 'more' ? 30 : 15)\"\r\n          class=\"more-button\"\r\n          @click=\"toggleShowMoreStations\"\r\n        >\r\n          <text class=\"more-text\">{{ displayMode === 'more' ? '查看全部' : '查看更多' }}</text>\r\n          <text class=\"more-icon\" :class=\"{ 'icon-rotate': displayMode === 'more' }\">▼</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载状态 -->\r\n    <view class=\"card loading-card\" v-else>\r\n      <view class=\"loading-content\">\r\n        <view class=\"loading-spinner\"></view>\r\n        <text class=\"loading-text\">加载站点信息中...</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部操作区域 -->\r\n    <view class=\"bottom-section\">\r\n      <view class=\"vote-tip\">{{ voteTips }}</view>\r\n\r\n      <view class=\"vote-action\">\r\n        <button\r\n          class=\"vote-btn\"\r\n          :class=\"{\r\n            'btn-disabled': remainingVotes <= 0 || selected === -1,\r\n            'btn-success': voteSuccess\r\n          }\"\r\n          :disabled=\"remainingVotes <= 0 || selected === -1\"\r\n          @click=\"vote\"\r\n        >\r\n          <text class=\"btn-text\">{{ getBtnText() }}</text>\r\n          <view class=\"btn-ripple\" v-if=\"voteSuccess\"></view>\r\n        </button>\r\n\r\n        <view class=\"remaining-votes\">\r\n          <text class=\"votes-label\">剩余投票次数</text>\r\n          <text class=\"votes-count\">{{ remainingVotes }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 地铁线路信息\r\n      metroLines: [],\r\n      selectedLineIndex: 0, // 默认选择第一项，即\"全部线路\"\r\n      selectedLineId: -1, // 默认选择ID为-1的线路，表示\"全部线路\"\r\n      currentLineData: null,\r\n      selected: -1,\r\n      remainingVotes: 0, // 剩余投票次数\r\n      voteRecords: [], // 存储从服务器获取的用户投票记录\r\n      debugShown: false, // 调试标记\r\n      locationAuth: false, // 位置授权状态\r\n      // 投票基本信息\r\n      voteTitle: '你希望下一家Fox新店开在哪里',\r\n      voteInfo: '排名靠前的投票会看地铁附近是否有合适的场地才会最终确定开',\r\n      voteTips: '每人仅限投票一次，fox会员可投两次',\r\n      // 添加全部站点数据\r\n      allStationsData: null,\r\n      isAllStationsSelected: true, // 默认选中全部站点\r\n      displayMode: 'initial', // 显示模式: initial(初始15条), more(30条), all(全部)\r\n      // 新增UI状态\r\n      voteSuccess: false, // 投票成功状态\r\n      animatingStations: [], // 正在动画的站点列表\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 计算当前选中线路的站点列表\r\n    currentStations() {\r\n      if (this.isAllStationsSelected) {\r\n        return this.allStationsData ? this.allStationsData.map(item => item.station) : []\r\n      }\r\n      \r\n      if (this.currentLineData) {\r\n        return this.currentLineData.stations || []\r\n      }\r\n      return []\r\n    },\r\n\r\n    // 计算当前选中线路的投票数据\r\n    currentVoteCounts() {\r\n      if (this.isAllStationsSelected) {\r\n        // 如果选择了全部，构建一个站点到投票数的映射对象\r\n        const votesMap = {}\r\n        if (this.allStationsData) {\r\n          this.allStationsData.forEach(item => {\r\n            votesMap[item.station] = item.votes\r\n          })\r\n        }\r\n        return votesMap\r\n      }\r\n      \r\n      if (this.currentLineData) {\r\n        return this.currentLineData.voteCounts || {}\r\n      }\r\n      return {}\r\n    },\r\n    \r\n    // 按投票数量排序的站点列表\r\n    sortedStations() {\r\n      if (this.isAllStationsSelected) {\r\n        // 在全部线路视图中，根据displayMode决定展示多少条数据\r\n        if (this.allStationsData && this.allStationsData.length > 0) {\r\n          if (this.displayMode === 'initial' && this.allStationsData.length > 15) {\r\n            return this.allStationsData.slice(0, 15) // 显示前15条\r\n          } else if (this.displayMode === 'more' && this.allStationsData.length > 30) {\r\n            return this.allStationsData.slice(0, 30) // 显示前30条\r\n          }\r\n        }\r\n        return this.allStationsData || []\r\n      }\r\n      \r\n      // 如果没有站点数据，返回空数组\r\n      if (!this.currentStations.length) return []\r\n      \r\n      // 创建站点和投票数的组合数组\r\n      const stationsWithVotes = this.currentStations.map(station => ({\r\n        station: station,\r\n        votes: this.currentVoteCounts[station] || 0\r\n      }))\r\n      \r\n      // 按投票数降序排序\r\n      return stationsWithVotes.sort((a, b) => b.votes - a.votes)\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 获取投票信息\r\n    this.getVoteInfo(1)\r\n    \r\n    // 获取所有地铁线路\r\n    this.getAllMetroLines()\r\n\r\n    // 检查URL参数是否带有指定的线路ID\r\n    const query = uni.getLaunchOptionsSync().query;\r\n    if (query && query.lineId && query.lineId !== '-1') {\r\n      this.selectedLineId = parseInt(query.lineId) || -1;\r\n      this.isAllStationsSelected = false;\r\n    }\r\n\r\n    // 根据选择的线路ID决定加载哪种数据\r\n    if (this.selectedLineId === -1) {\r\n      // 加载全部站点排名\r\n      this.buildAllStationsRanking();\r\n    } else {\r\n      // 加载特定线路\r\n      this.getMetroLineById(this.selectedLineId);\r\n    }\r\n\r\n    // 检查用户是否登录\r\n    if (this.checkLogin()) {\r\n      // 获取用户剩余投票次数\r\n      this.getRemainingVotes()\r\n\r\n      // 获取用户投票记录\r\n      this.getUserVoteRecords()\r\n    }\r\n    \r\n    // 检查位置权限\r\n    this.checkLocationPermission()\r\n  },\r\n\r\n  methods: {\r\n    // 切换显示更多站点\r\n    toggleShowMoreStations() {\r\n      if (this.displayMode === 'initial') {\r\n        // 从初始状态切换到显示更多\r\n        this.displayMode = 'more'\r\n      } else if (this.displayMode === 'more') {\r\n        // 从显示更多切换到显示全部\r\n        this.displayMode = 'all'\r\n      }\r\n    },\r\n    \r\n    // 获取投票信息\r\n    async getVoteInfo(id) {\r\n      const BASE_URL = this.getBaseUrl()\r\n      try {\r\n        const response = await uni.request({\r\n          url: `${BASE_URL}/api/vote-info/${id}`,\r\n          method: 'GET'\r\n        })\r\n        console.log('获取投票信息:', response)\r\n        if (response.data && response.data.code === 0) {\r\n          const voteInfo = response.data.data\r\n          // 更新页面显示内容\r\n          this.voteTitle = voteInfo.title || this.voteTitle\r\n          this.voteInfo = voteInfo.info || this.voteInfo\r\n          this.voteTips = voteInfo.tips || this.voteTips\r\n        } else {\r\n          console.error('获取投票信息失败:', response.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取投票信息异常:', error)\r\n      }\r\n    },\r\n\r\n    // 根据环境配置API基础URL\r\n    getBaseUrl() {\r\n      // #ifdef MP-WEIXIN\r\n      return 'https://vote.foxdance.com.cn' // 替换为实际的HTTPS域名\r\n      // #endif\r\n\r\n      // 非小程序环境使用本地开发地址\r\n      return 'https://vote.foxdance.com.cn'\r\n    },\r\n\r\n    // 获取用户ID和token\r\n    getUserInfo() {\r\n      const token = uni.getStorageSync('token')\r\n      const userId = uni.getStorageSync('userid')\r\n      return {\r\n        token,\r\n        userId\r\n      }\r\n    },\r\n\r\n    // 检查用户是否已登录\r\n    checkLogin() {\r\n      const { token } = this.getUserInfo()\r\n      if (!token) {\r\n        uni.showToast({\r\n          title: '请先登录',\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n\r\n        // 延迟跳转到登录页\r\n        setTimeout(() => {\r\n          uni.navigateTo({\r\n            url: '/pages/login/login'\r\n          })\r\n        }, 1500)\r\n\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取用户剩余投票次数\r\n    async getRemainingVotes() {\r\n      // 检查用户是否登录\r\n      if (!this.checkLogin()) return\r\n\r\n      const { userId } = this.getUserInfo()\r\n      if (!userId) return\r\n\r\n      const BASE_URL = this.getBaseUrl()\r\n      try {\r\n        const response = await uni.request({\r\n          url: `${BASE_URL}/api/ba-user/remaining-votes/${userId}`,\r\n          method: 'GET'\r\n        })\r\n\r\n        if (response.data && response.data.code === 0) {\r\n          this.remainingVotes = response.data.data\r\n        } else {\r\n          console.log('userId:', userId)\r\n          console.error('获取剩余投票次数失败:', response.data)\r\n          this.remainingVotes = 0\r\n        }\r\n      } catch (error) {\r\n        console.error('获取剩余投票次数异常:', error)\r\n        this.remainingVotes = 0\r\n      }\r\n    },\r\n\r\n    // 获取所有地铁线路\r\n    async getAllMetroLines() {\r\n      const BASE_URL = this.getBaseUrl()\r\n      try {\r\n        const response = await uni.request({\r\n          url: `${BASE_URL}/api/metro-lines`,\r\n          method: 'GET'\r\n        })\r\n\r\n        if (response.data && response.data.code === 0) {\r\n          // 添加\"全部\"选项\r\n          const allOption = {\r\n            id: -1, // 使用一个特殊值表示全部\r\n            lineName: '全部线路'\r\n          }\r\n          this.metroLines = [allOption, ...response.data.data]\r\n          \r\n          // 如果有已选择的线路ID，找到对应的索引\r\n          if (this.selectedLineId !== -1) {\r\n            const index = this.metroLines.findIndex(line => line.id === this.selectedLineId)\r\n            if (index !== -1) {\r\n              this.selectedLineIndex = index\r\n              this.isAllStationsSelected = false\r\n            }\r\n          }\r\n          \r\n          // 获取所有站点的排名数据（无论是否默认选择全部）\r\n          this.buildAllStationsRanking()\r\n        } else {\r\n          uni.showToast({\r\n            title: '获取地铁线路失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '网络请求异常',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 根据ID获取地铁线路详情\r\n    async getMetroLineById(id) {\r\n      const BASE_URL = this.getBaseUrl()\r\n      try {\r\n        const response = await uni.request({\r\n          url: `${BASE_URL}/api/metro-lines/${id}`,\r\n          method: 'GET'\r\n        })\r\n\r\n        if (response.data && response.data.code === 0) {\r\n          this.currentLineData = response.data.data\r\n        } else {\r\n          uni.showToast({\r\n            title: '获取线路详情失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '网络请求异常',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 获取所有站点的排名数据\r\n    async buildAllStationsRanking() {\r\n      // 显示加载提示\r\n      uni.showLoading({\r\n        title: '处理数据...'\r\n      })\r\n      \r\n      try {\r\n        const BASE_URL = this.getBaseUrl()\r\n        // 先获取所有线路\r\n        const linesResponse = await uni.request({\r\n          url: `${BASE_URL}/api/metro-lines`,\r\n          method: 'GET'\r\n        })\r\n        \r\n        if (linesResponse.data && linesResponse.data.code === 0) {\r\n          const lines = linesResponse.data.data\r\n          let allStations = []\r\n          \r\n          // 对每条线路获取详细信息\r\n          for (const line of lines) {\r\n            const lineDetailResponse = await uni.request({\r\n              url: `${BASE_URL}/api/metro-lines/${line.id}`,\r\n              method: 'GET'\r\n            })\r\n            \r\n            if (lineDetailResponse.data && lineDetailResponse.data.code === 0) {\r\n              const lineData = lineDetailResponse.data.data\r\n              const stations = lineData.stations || []\r\n              const voteCounts = lineData.voteCounts || {}\r\n              \r\n              // 将站点和对应投票数添加到总列表\r\n              for (const station of stations) {\r\n                const votes = voteCounts[station] || 0\r\n                // 检查是否已存在同名站点\r\n                const existingIndex = allStations.findIndex(s => s.station === station)\r\n                \r\n                if (existingIndex >= 0) {\r\n                  // 如果存在，累加投票\r\n                  allStations[existingIndex].votes += votes\r\n                  // 记录该站点属于多条线路\r\n                  if (!allStations[existingIndex].lines.includes(line.lineName)) {\r\n                    allStations[existingIndex].lines.push(line.lineName)\r\n                  }\r\n                } else {\r\n                  // 如果不存在，添加新站点\r\n                  allStations.push({\r\n                    station,\r\n                    votes,\r\n                    lines: [line.lineName]\r\n                  })\r\n                }\r\n              }\r\n            }\r\n          }\r\n          \r\n          // 按投票数排序\r\n          allStations.sort((a, b) => b.votes - a.votes)\r\n          this.allStationsData = allStations\r\n          console.log('手动构建的所有站点数据:', allStations)\r\n        }\r\n      } catch (error) {\r\n        console.error('手动构建站点数据失败:', error)\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    },\r\n\r\n    // 处理线路选择变化\r\n    handleLineChange(e) {\r\n      this.selectedLineIndex = e.detail.value\r\n      const selectedLine = this.metroLines[this.selectedLineIndex]\r\n      \r\n      if (selectedLine) {\r\n        this.selectedLineId = selectedLine.id\r\n        \r\n        // 判断是否选择了\"全部\"选项\r\n        if (selectedLine.id === -1) {\r\n          this.isAllStationsSelected = true\r\n          this.displayMode = 'initial' // 切换到全部线路时，重置为只显示前15个\r\n          // 确保已加载全部站点数据\r\n          if (!this.allStationsData || this.allStationsData.length === 0) {\r\n            this.buildAllStationsRanking()\r\n          }\r\n        } else {\r\n          this.isAllStationsSelected = false\r\n          this.getMetroLineById(selectedLine.id)\r\n        }\r\n\r\n        // 重置选择状态\r\n        this.selected = -1\r\n\r\n        // 在线路切换后刷新投票记录\r\n        this.$nextTick(() => {\r\n          this.getUserVoteRecords()\r\n        })\r\n      }\r\n    },\r\n\r\n    select(idx) {\r\n      if (this.remainingVotes <= 0) return\r\n      this.selected = idx\r\n    },\r\n\r\n    // 初始投票方法\r\n    async vote() {\r\n      if (this.remainingVotes <= 0 || this.selected === -1) return\r\n      \r\n      // 如果选择的是全部站点视图，不允许投票\r\n      if (this.isAllStationsSelected) {\r\n        uni.showToast({\r\n          title: '请先选择具体线路再投票',\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n        return\r\n      }\r\n\r\n      // 验证用户是否已登录\r\n      if (!this.checkLogin()) return\r\n\r\n      // 检查用户是否还有剩余投票次数\r\n      if (this.remainingVotes <= 0) {\r\n        uni.showToast({\r\n          title: '您的投票次数已用完',\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n        return\r\n      }\r\n\r\n      const { token, userId } = this.getUserInfo()\r\n      if (!userId) {\r\n        uni.showToast({\r\n          title: '获取用户信息失败',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      const selectedStation = this.currentStations[this.selected]\r\n      const BASE_URL = this.getBaseUrl()\r\n\r\n      try {\r\n        // 先检查位置权限状态\r\n        const authStatus = await this.getLocationAuth()\r\n        \r\n        if (!authStatus) {\r\n          // 如果没有权限，提示用户\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '投票需要获取您的位置信息，是否前往设置页面授权？',\r\n            success: (modalRes) => {\r\n              if (modalRes.confirm) {\r\n                uni.openSetting()\r\n              }\r\n            }\r\n          })\r\n          return\r\n        }\r\n        \r\n        // 获取用户位置\r\n        uni.showLoading({\r\n          title: '获取位置中...'\r\n        })\r\n\r\n        // 使用Promise包装getLocation\r\n        const location = await new Promise((resolve, reject) => {\r\n          uni.getLocation({\r\n            type: 'gcj02', // 国内使用gcj02坐标系\r\n            success: function(res) {\r\n              resolve(res)\r\n            },\r\n            fail: function(err) {\r\n              reject(err)\r\n            }\r\n          })\r\n        }).catch(err => {\r\n          console.error('获取位置失败:', err)\r\n          uni.hideLoading()\r\n          uni.showToast({\r\n            title: '获取位置失败，请允许位置权限',\r\n            icon: 'none',\r\n            duration: 2000\r\n          })\r\n          throw new Error('获取位置失败')\r\n        })\r\n\r\n        uni.hideLoading()\r\n\r\n        // 打印调试信息\r\n        console.log('开始投票', {\r\n          lineId: this.selectedLineId,\r\n          station: selectedStation,\r\n          userId,\r\n          latitude: location.latitude,\r\n          longitude: location.longitude\r\n        })\r\n\r\n        // 构建请求参数 - 使用基于位置的投票接口\r\n        const requestUrl = `${BASE_URL}/api/metro-lines/${this.selectedLineId}/location-vote/${selectedStation}?latitude=${location.latitude}&longitude=${location.longitude}&userId=${userId}`\r\n\r\n\r\n        // 调用投票接口\r\n        const response = await uni.request({\r\n          url: requestUrl,\r\n          method: 'POST',\r\n          header: {\r\n            'bausertoken': token\r\n          }\r\n        })\r\n\r\n        // 打印响应\r\n        console.log('投票响应', response)\r\n\r\n        if (response.data && response.data.code === 0) {\r\n          // 投票成功，更新本地数据\r\n          this.handleVoteSuccess(selectedStation)\r\n\r\n          // 更新剩余投票次数\r\n          this.remainingVotes--\r\n        } else {\r\n          // 处理错误情况\r\n          this.handleVoteError(response.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('投票异常', error)\r\n        if (error.message !== '获取位置失败') { // 避免重复显示错误提示\r\n          uni.showToast({\r\n            title: '网络请求异常',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      }\r\n    },\r\n\r\n    // 检查位置授权状态\r\n    async getLocationAuth() {\r\n      return new Promise((resolve) => {\r\n        uni.getSetting({\r\n          success: (res) => {\r\n            if (res.authSetting['scope.userLocation']) {\r\n              resolve(true)\r\n            } else {\r\n              resolve(false)\r\n            }\r\n          },\r\n          fail: () => {\r\n            resolve(false)\r\n          }\r\n        })\r\n      })\r\n    },\r\n\r\n    // 处理投票成功\r\n    handleVoteSuccess(selectedStation) {\r\n      // 设置投票成功状态\r\n      this.voteSuccess = true\r\n\r\n      // 添加动画效果\r\n      this.animatingStations.push(selectedStation)\r\n\r\n      // 更新本地数据\r\n      if (this.currentVoteCounts[selectedStation] !== undefined) {\r\n        this.currentVoteCounts[selectedStation]++\r\n      } else {\r\n        this.currentVoteCounts[selectedStation] = 1\r\n      }\r\n\r\n      // 重置选择状态\r\n      this.selected = -1\r\n\r\n      // 更新投票记录（从服务器获取最新数据）\r\n      this.getUserVoteRecords()\r\n\r\n      // 立即刷新线路数据以获取最新票数\r\n      setTimeout(() => {\r\n        console.log('刷新线路数据以获取最新票数')\r\n        this.getMetroLineById(this.selectedLineId)\r\n        // 刷新全部站点排名数据\r\n        this.buildAllStationsRanking()\r\n\r\n        // 移除动画状态\r\n        const index = this.animatingStations.indexOf(selectedStation)\r\n        if (index > -1) {\r\n          this.animatingStations.splice(index, 1)\r\n        }\r\n      }, 800)\r\n\r\n      // 重置投票成功状态\r\n      setTimeout(() => {\r\n        this.voteSuccess = false\r\n      }, 1000)\r\n\r\n      uni.showToast({\r\n        title: '投票成功',\r\n        icon: 'success'\r\n      })\r\n    },\r\n\r\n    // 处理投票错误\r\n    handleVoteError(errorData) {\r\n      let errorMsg = '投票失败'\r\n\r\n      if (errorData) {\r\n        // 输出更详细的错误信息用于调试\r\n        console.error('投票失败详情:', errorData)\r\n        console.error('错误信息内容:', errorData.message)\r\n\r\n        // 直接显示原始错误信息\r\n        errorMsg = errorData.message || '投票失败'\r\n\r\n        // 检测特定错误类型并设置友好提示\r\n        if (errorData.code === 50000) {\r\n          // 记录实际消息内容以便调试\r\n          if (errorData.message) {\r\n            // 移除24小时投票限制的检查，因为该限制已取消\r\n            // console.log('检查24小时内投票条件:', errorData.message.indexOf('已在24小时内对该站点投过票') !== -1)\r\n\r\n            if (errorData.message.indexOf('仅限广州地区') !== -1) {\r\n              errorMsg = '仅限广州地区用户参与投票'\r\n            } else if (errorData.message.indexOf('异常投票行为') !== -1) {\r\n              errorMsg = '检测到异常投票行为，请稍后再试'\r\n            }\r\n            // 注释掉24小时投票限制的处理，因为该限制已取消\r\n            // else if (errorData.message.indexOf('已在24小时内对该站点投过票') !== -1) {\r\n            //   errorMsg = '您已在24小时内对该站点投过票'\r\n            // }\r\n          }\r\n        } else if (errorData.code === 40000) {\r\n          errorMsg = errorData.message || '参数错误'\r\n        }\r\n      }\r\n\r\n      uni.showToast({\r\n        title: errorMsg,\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n    },\r\n\r\n    getPercent(count) {\r\n      if (!this.currentVoteCounts) return 0\r\n\r\n      // 计算当前所有站点的总票数\r\n      const total = Object.values(this.currentVoteCounts).reduce((sum, count) => sum + Number(count), 0)\r\n\r\n      // 防止除以零\r\n      if (total === 0) return 0\r\n\r\n      // 计算百分比\r\n      return ((count / total) * 100).toFixed(1)\r\n    },\r\n\r\n    // 检查当前站点是否是已投票的站点 - 仅用于UI显示\r\n    isVotedStation(stationName) {\r\n      // 确保voteRecords是数组且不为空\r\n      if (!this.voteRecords || !Array.isArray(this.voteRecords) || this.voteRecords.length === 0) {\r\n        return false\r\n      }\r\n\r\n      // 打印调试信息\r\n      if (!this.debugShown) {\r\n        console.log('调试投票记录信息:')\r\n        console.log('当前选择的线路ID:', this.selectedLineId, '类型:', typeof this.selectedLineId)\r\n        console.log('投票记录:', this.voteRecords)\r\n        this.debugShown = true\r\n      }\r\n\r\n      // 如果选择了\"全部\"视图，检查所有线路中是否有投过该站点\r\n      if (this.isAllStationsSelected) {\r\n        return this.voteRecords.some(record => record.stationName === stationName)\r\n      }\r\n\r\n      // 确保类型一致进行比较 - 将两边都转为字符串再比较\r\n      return this.voteRecords.some(record => {\r\n        // 类型转换再比较\r\n        const recordLineId = String(record.metroLineId)\r\n        const currentLineId = String(this.selectedLineId)\r\n\r\n        return recordLineId === currentLineId && record.stationName === stationName\r\n      })\r\n    },\r\n\r\n    // 获取用户投票记录\r\n    async getUserVoteRecords() {\r\n      // 检查用户是否登录\r\n      const { userId } = this.getUserInfo()\r\n      if (!userId) return\r\n\r\n      const BASE_URL = this.getBaseUrl()\r\n      try {\r\n        uni.showLoading({\r\n          title: '加载投票记录...'\r\n        })\r\n\r\n        const response = await uni.request({\r\n          url: `${BASE_URL}/api/vote-records/user/${userId}`,\r\n          method: 'GET'\r\n        })\r\n\r\n        uni.hideLoading()\r\n\r\n        if (response.data && response.data.code === 0) {\r\n          // 更新投票记录\r\n          this.voteRecords = response.data.data || []\r\n          console.log('获取到投票记录:', this.voteRecords)\r\n\r\n          // 强制更新视图\r\n          this.$forceUpdate()\r\n        } else {\r\n          console.error('获取投票记录失败:', response.data)\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        console.error('获取投票记录异常:', error)\r\n      }\r\n    },\r\n\r\n    // 添加分享到朋友圈的方法\r\n    onShareTimeline() {\r\n      return {\r\n        title: 'Fox Dance新店投票'\r\n      }\r\n    },\r\n    \r\n    // 分享给好友\r\n    onShareAppMessage() {\r\n      \r\n      return {\r\n        title: 'Fox Dance新店投票，快来投票吧！',\r\n        path: '/pagesSub/switch/vote'\r\n      }\r\n    },\r\n\r\n    // 添加检查位置权限的方法\r\n    checkLocationPermission() {\r\n      uni.getSetting({\r\n        success: (res) => {\r\n          // 更新位置授权状态\r\n          this.locationAuth = !!res.authSetting['scope.userLocation']\r\n          \r\n          if (!res.authSetting['scope.userLocation']) {\r\n            // 用户未授权位置信息\r\n            uni.showModal({\r\n              title: '提示',\r\n              content: '投票需要获取您的位置信息，是否前往设置页面授权？',\r\n              success: (modalRes) => {\r\n                if (modalRes.confirm) {\r\n                  uni.openSetting({\r\n                    success: (settingRes) => {\r\n                      // 更新位置授权状态\r\n                      this.locationAuth = !!settingRes.authSetting['scope.userLocation']\r\n                      \r\n                      if (settingRes.authSetting['scope.userLocation']) {\r\n                        uni.showToast({\r\n                          title: '授权成功',\r\n                          icon: 'success'\r\n                        })\r\n                      } else {\r\n                        uni.showToast({\r\n                          title: '授权失败，无法参与投票',\r\n                          icon: 'none'\r\n                        })\r\n                      }\r\n                    }\r\n                  })\r\n                }\r\n              }\r\n            })\r\n          }\r\n        },\r\n        fail: () => {\r\n          this.locationAuth = false\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 添加请求位置权限的方法\r\n    requestLocationPermission() {\r\n      // 先尝试直接请求位置权限\r\n      uni.authorize({\r\n        scope: 'scope.userLocation',\r\n        success: () => {\r\n          this.locationAuth = true\r\n          uni.showToast({\r\n            title: '授权成功',\r\n            icon: 'success'\r\n          })\r\n        },\r\n        fail: () => {\r\n          // 如果直接请求失败，打开设置页面\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '需要位置权限才能参与投票，是否前往设置页面授权？',\r\n            success: (res) => {\r\n              if (res.confirm) {\r\n                uni.openSetting({\r\n                  success: (settingRes) => {\r\n                    this.locationAuth = !!settingRes.authSetting['scope.userLocation']\r\n                    if (this.locationAuth) {\r\n                      uni.showToast({\r\n                        title: '授权成功',\r\n                        icon: 'success'\r\n                      })\r\n                    }\r\n                  }\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 获取按钮文本\r\n    getBtnText() {\r\n      if (this.voteSuccess) {\r\n        return '投票成功'\r\n      }\r\n      if (this.remainingVotes <= 0) {\r\n        return '投票次数已用完'\r\n      }\r\n      if (this.selected === -1) {\r\n        return '请选择站点'\r\n      }\r\n      return '立即投票'\r\n    },\r\n\r\n    // 获取总票数\r\n    getTotalVotes() {\r\n      if (!this.sortedStations || this.sortedStations.length === 0) {\r\n        return 0\r\n      }\r\n      return this.sortedStations.reduce((total, station) => total + station.votes, 0)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// 主题色彩变量\r\n$primary-gradient: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);\r\n$secondary-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\r\n$success-gradient: linear-gradient(135deg, #00b894 0%, #00cec9 100%);\r\n$background-color: #f8f9fa;\r\n$card-background: #ffffff;\r\n$text-primary: #2d3436;\r\n$text-secondary: #636e72;\r\n$text-light: #b2bec3;\r\n$border-color: #e9ecef;\r\n$shadow-light: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n$shadow-medium: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);\r\n\r\n// 动画变量\r\n$transition-fast: 0.2s ease-out;\r\n$transition-medium: 0.3s ease-out;\r\n$transition-slow: 0.5s ease-out;\r\n\r\n.vote-container {\r\n  background: $background-color;\r\n  min-height: 100vh;\r\n  padding: 0 24rpx 120rpx;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\r\n}\r\n\r\n// 头部区域\r\n.header-section {\r\n  padding: 40rpx 0 32rpx;\r\n  text-align: center;\r\n  // background: $primary-gradient;\r\n  margin: 0 -24rpx 32rpx;\r\n  border-radius: 0 0 32rpx 32rpx;\r\n  color: $text-primary;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"40\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"80\" r=\"1.5\" fill=\"rgba(255,255,255,0.1)\"/></svg>');\r\n    opacity: 0.3;\r\n  }\r\n}\r\n\r\n.vote-title {\r\n  font-size: 38rpx;\r\n  font-weight: 700;\r\n  margin-bottom: 16rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.vote-info {\r\n  font-size: 20rpx;\r\n  opacity: 0.9;\r\n  line-height: 1.5;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n// 卡片基础样式\r\n.card {\r\n  background: $card-background;\r\n  border-radius: 24rpx;\r\n  margin-bottom: 24rpx;\r\n  box-shadow: $shadow-light;\r\n  overflow: hidden;\r\n  transition: transform $transition-fast, box-shadow $transition-fast;\r\n\r\n  &:active {\r\n    transform: translateY(2rpx);\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);\r\n  }\r\n}\r\n\r\n.card-header {\r\n  padding: 32rpx 32rpx 16rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1rpx solid $border-color;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: $text-primary;\r\n}\r\n\r\n.total-votes {\r\n  font-size: 24rpx;\r\n  color: $text-secondary;\r\n  background: linear-gradient(135deg, #ff6b87, #ff8e53);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  font-weight: 600;\r\n}\r\n\r\n// 下拉选择器\r\n.dropdown-card {\r\n  .picker {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24rpx 32rpx;\r\n    min-height: 80rpx;\r\n  }\r\n\r\n  .picker-text {\r\n    font-size: 32rpx;\r\n    color: $text-primary;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .picker-loading {\r\n    font-size: 32rpx;\r\n    color: $text-light;\r\n  }\r\n\r\n  .picker-arrow {\r\n    color: $text-secondary;\r\n    font-size: 28rpx;\r\n    transition: transform $transition-fast;\r\n  }\r\n}\r\n\r\n// 提示卡片\r\n.notice-card {\r\n  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 20%, #ff7675 100%);\r\n\r\n  .notice-content {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 24rpx 32rpx;\r\n  }\r\n\r\n  .notice-icon {\r\n    width: 48rpx;\r\n    height: 48rpx;\r\n    margin-right: 16rpx;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .notice-text {\r\n    flex: 1;\r\n  }\r\n\r\n  .notice-title {\r\n    display: block;\r\n    font-size: 28rpx;\r\n    font-weight: 600;\r\n    color: white;\r\n    margin-bottom: 4rpx;\r\n  }\r\n\r\n  .notice-desc {\r\n    display: block;\r\n    font-size: 24rpx;\r\n    color: rgba(255, 255, 255, 0.9);\r\n    line-height: 1.4;\r\n  }\r\n}\r\n\r\n// 投票列表\r\n.vote-list-card {\r\n  .vote-list {\r\n    padding: 0;\r\n    margin-bottom: 120rpx;\r\n  }\r\n}\r\n\r\n.vote-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 24rpx 32rpx;\r\n  border-bottom: 1rpx solid $border-color;\r\n  transition: all $transition-fast;\r\n  position: relative;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  &:active {\r\n    background: #f8f9fa;\r\n  }\r\n\r\n  &.selected {\r\n    background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\r\n    border-left: 6rpx solid #ff6b87;\r\n\r\n    .station-name {\r\n      color: #ff6b87;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  &.voted {\r\n    background: linear-gradient(135deg, rgba(255, 234, 167, 0.3) 0%, rgba(250, 177, 160, 0.3) 100%);\r\n\r\n    &::after {\r\n      content: '已投票';\r\n      position: absolute;\r\n      top: 8rpx;\r\n      right: 32rpx;\r\n      font-size: 20rpx;\r\n      color: #ff8e53;\r\n      background: rgba(255, 142, 83, 0.2);\r\n      padding: 4rpx 12rpx;\r\n      border-radius: 12rpx;\r\n    }\r\n  }\r\n\r\n  &.rank-top {\r\n    .rank-badge {\r\n      background: $primary-gradient;\r\n    }\r\n  }\r\n}\r\n\r\n// 排名标识\r\n.rank-badge {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: $secondary-gradient;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 24rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);\r\n\r\n  .rank-number {\r\n    font-size: 24rpx;\r\n    font-weight: 700;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.rank-number-normal {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: #e9ecef;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 24rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 600;\r\n  color: $text-secondary;\r\n}\r\n\r\n// 站点信息\r\n.station-info {\r\n  flex: 1;\r\n  margin-right: 24rpx;\r\n}\r\n\r\n.station-name {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: $text-primary;\r\n  margin-bottom: 12rpx;\r\n  transition: color $transition-fast;\r\n}\r\n\r\n.vote-progress {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n}\r\n\r\n.vote-bar {\r\n  flex: 1;\r\n  height: 12rpx;\r\n  background: #e9ecef;\r\n  border-radius: 6rpx;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.vote-bar-inner {\r\n  height: 100%;\r\n  background: $primary-gradient;\r\n  border-radius: 6rpx;\r\n  transition: width $transition-medium;\r\n  animation: progressSlide 0.8s ease-out;\r\n  position: relative;\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);\r\n    animation: shimmer 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes progressSlide {\r\n  from {\r\n    width: 0 !important;\r\n  }\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    transform: translateX(-100%);\r\n  }\r\n  100% {\r\n    transform: translateX(100%);\r\n  }\r\n}\r\n\r\n.vote-percentage {\r\n  font-size: 24rpx;\r\n  color: $text-secondary;\r\n  font-weight: 500;\r\n  min-width: 60rpx;\r\n  text-align: right;\r\n}\r\n\r\n// 票数显示\r\n.vote-count-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-width: 80rpx;\r\n}\r\n\r\n.vote-count {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n  color: #ff6b87;\r\n  transition: all $transition-fast;\r\n\r\n  &.count-animate {\r\n    animation: countPulse 0.6s ease-out;\r\n  }\r\n}\r\n\r\n@keyframes countPulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n    color: #ff8e53;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.vote-unit {\r\n  font-size: 20rpx;\r\n  color: $text-light;\r\n  margin-top: 4rpx;\r\n}\r\n\r\n// 选中指示器\r\n.selection-indicator {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  background: $success-gradient;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 16rpx;\r\n  animation: checkBounce 0.3s ease-out;\r\n}\r\n\r\n@keyframes checkBounce {\r\n  0% {\r\n    transform: scale(0);\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.check-icon {\r\n  font-size: 20rpx;\r\n  color: white;\r\n  font-weight: 700;\r\n}\r\n\r\n// 更多按钮\r\n.more-button {\r\n  padding: 32rpx;\r\n  text-align: center;\r\n  border-top: 1rpx solid $border-color;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  transition: background-color $transition-fast;\r\n\r\n  &:active {\r\n    background-color: #f8f9fa;\r\n  }\r\n}\r\n\r\n.more-text {\r\n  font-size: 28rpx;\r\n  color: #ff6b87;\r\n  font-weight: 500;\r\n}\r\n\r\n.more-icon {\r\n  margin-left: 12rpx;\r\n  font-size: 24rpx;\r\n  color: #ff6b87;\r\n  transition: transform $transition-fast;\r\n\r\n  &.icon-rotate {\r\n    transform: rotate(180deg);\r\n  }\r\n}\r\n\r\n// 加载状态\r\n.loading-card {\r\n  .loading-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 60rpx 32rpx;\r\n  }\r\n\r\n  .loading-spinner {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border: 4rpx solid #e9ecef;\r\n    border-top: 4rpx solid #ff6b87;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 24rpx;\r\n  }\r\n\r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: $text-secondary;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n// 底部操作区域\r\n.bottom-section {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: $card-background;\r\n  padding: 24rpx;\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 100;\r\n}\r\n\r\n.vote-tip {\r\n  text-align: center;\r\n  color: $text-secondary;\r\n  font-size: 24rpx;\r\n  margin-bottom: 24rpx;\r\n  line-height: 1.4;\r\n}\r\n\r\n.vote-action {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.vote-btn {\r\n  width: 100%;\r\n  height: 88rpx;\r\n  background: $primary-gradient;\r\n  color: white;\r\n  font-size: 32rpx;\r\n  border: none;\r\n  border-radius: 44rpx;\r\n  font-weight: 600;\r\n  box-shadow: $shadow-medium;\r\n  transition: all $transition-fast;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &:not(.btn-disabled):active {\r\n    transform: translateY(2rpx);\r\n    box-shadow: 0 4rpx 15rpx rgba(255, 107, 135, 0.4);\r\n  }\r\n\r\n  &.btn-disabled {\r\n    background: #e9ecef;\r\n    color: $text-light;\r\n    box-shadow: none;\r\n  }\r\n\r\n  &.btn-success {\r\n    background: $success-gradient;\r\n    animation: successPulse 0.6s ease-out;\r\n  }\r\n}\r\n\r\n@keyframes successPulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n.btn-text {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.btn-ripple {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 0;\r\n  height: 0;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translate(-50%, -50%);\r\n  animation: ripple 0.6s ease-out;\r\n}\r\n\r\n@keyframes ripple {\r\n  to {\r\n    width: 300rpx;\r\n    height: 300rpx;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.remaining-votes {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n}\r\n\r\n.votes-label {\r\n  font-size: 24rpx;\r\n  color: $text-secondary;\r\n}\r\n\r\n.votes-count {\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n  color: #ff6b87;\r\n  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 16rpx;\r\n  min-width: 48rpx;\r\n  text-align: center;\r\n}\r\n\r\n// 响应式适配\r\n@media screen and (max-width: 750rpx) {\r\n  .vote-item {\r\n    padding: 20rpx 24rpx;\r\n  }\r\n\r\n  .station-name {\r\n    font-size: 30rpx;\r\n  }\r\n\r\n  .vote-count {\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vote.vue?vue&type=style&index=0&id=de3fba80&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./vote.vue?vue&type=style&index=0&id=de3fba80&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752117065985\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}