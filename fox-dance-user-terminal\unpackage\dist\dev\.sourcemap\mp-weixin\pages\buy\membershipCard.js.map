{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/membershipCard.vue?7594", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/membershipCard.vue?2d84", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/membershipCard.vue?1af7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/membershipCard.vue?faec", "uni-app:///pages/buy/membershipCard.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/membershipCard.vue?f9f8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/membershipCard.vue?b19c"], "names": ["data", "cardType", "isLogined", "loding", "tqLists", "name", "hyLists", "hyType", "imgbaseUrl", "cardsInfo", "ck_selectStores", "sck_selectStores", "mdText", "xyCont", "xyToggle", "scrollViewHeight", "isScrollToBottom", "onShow", "console", "methods", "onScroll", "query", "that", "ljktTap", "uni", "icon", "title", "setTimeout", "url", "mdid", "tyTap", "xzmdTap", "onLoadData", "mdTextData", "cardsData", "card_id", "type", "xyGbTap", "cardTab", "yhTap", "navTo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA8tB,CAAgB,6sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6ElvB;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC,UACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC,UACA,IACA,IACA,IACA,IACA,GACA;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAEAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MAAA;MACA;MACA;QAEA;QACAC;QACAA;QACAA;UACAH;UACA;YACA;YACA;YACA;YACA;cACAA;cACAI;YACA;UACA;QACA;MAEA;IACA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACAC;UACAH;YACAI;UACA;QACA;QACA;MACA;MACA;QACAJ;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;YACAG;UACA;UACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;YACAA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;MACA;AACA;AACA;AACA;AACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;UACA;UACA;UACA;YACAD;UACA;UACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;YACAA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACAL;QACAI;MACA;IACA;IACA;IACAG;MACAP;QACAI;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAV;QACAE;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;YACAG;UACA;UACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACA;UACA;UACA;UACA;YACAA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MAEA;MACA;QACAM;QACAC;MACA;QACAlB;QACA;UACAI;UACAA;UACA;;UAEAA;UACAE;QACA;MACA;IACA;IACA;IACAa;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAhB;QACAI;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACrVA;AAAA;AAAA;AAAA;AAAq2C,CAAgB,gwCAAG,EAAC,C;;;;;;;;;;;ACAz3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/membershipCard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./membershipCard.vue?vue&type=template&id=13fc1b68&\"\nvar renderjs\nimport script from \"./membershipCard.vue?vue&type=script&lang=js&\"\nexport * from \"./membershipCard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./membershipCard.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/membershipCard.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./membershipCard.vue?vue&type=template&id=13fc1b68&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loding ? _vm.cardsInfo.card.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./membershipCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./membershipCard.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"membershipCard\">\r\n\t\t<view class=\"memk_one\">\r\n\t\t\t<view :class=\"cardType == 0 ? 'memk_one_ac' : ''\" @click=\"cardTab(0)\">次卡<text></text></view>\r\n\t\t\t<view :class=\"cardType == 1 ? 'memk_one_ac' : ''\" @click=\"cardTab(1)\">时长卡<text></text></view>\r\n\t\t</view>\r\n\t\t<template v-if=\"loding\">\r\n\t\t<view class=\"memk_ban\">\r\n\t\t\t<!-- <swiper class=\"swiper\" autoplay=\"1500\" :indicator-dots=\"true\" :circular='true'\r\n\t\t\t\tindicator-active-color=\"#ffffff\" indicator-color=\"#cccccc\">\r\n\t\t\t\t<swiper-item class=\"swiper-wrap\" v-for=\"(item,index) in 3\" :key=\"index\">\r\n\t\t\t\t\t<image src=\"/static/images/index1111.png\" mode=\"aspectFill\" @click=\"goBannerTap(item.url)\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper> -->\r\n\t\t\t<image :src=\"cardType == 0 ? imgbaseUrl + cardsInfo.secondary_card_image : imgbaseUrl + cardsInfo.duration_chart\" mode=\"aspectFill\" class=\"memk_ban_bj\"></image>\r\n\t\t\t<view class=\"memk_ban_n\"><view class=\"memk_ban_n_v\" @click=\"xzmdTap\"><view>{{mdText}}</view><text></text></view></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"memk_two\">\r\n\t\t\t<view class=\"memk_two_t\"><view class=\"kcxq_two_t_n\"><text>尊享特权</text><text style=\"display:none;\"></text></view></view>\r\n\t\t\t<view class=\"memk_two_b\">\r\n\t\t\t\t<view class=\"memk_two_b_li\" v-for=\"(item,index) in cardsInfo.privilege\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"memk_two_b_li_l\"><template v-if=\"index <= 8\">0</template>{{index+1}}</view>\r\n\t\t\t\t\t<view class=\"memk_two_b_li_r\"><view>{{item}}</view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 0=次卡,1=年卡,2=月卡 -->\r\n\t\t<view class=\"memk_thr\" v-if=\"cardsInfo.card.length > 0\">\r\n\t\t\t<view class=\"memk_thr_li\" v-for=\"(item,index) in cardsInfo.card\" :class=\"hyType == index ? 'memk_thr_li_ac' : ''\" :key=\"index\" @click=\"yhTap(index)\">\r\n\t\t\t\t<view class=\"memk_thr_li_a\"><text>{{item.number}}</text>{{item.type*1 == 0 ? '次' : item.type*1 == 1 ? '年' : item.type*1 == 2 ? '个月' : ''}}</view>\r\n\t\t\t\t<view class=\"memk_thr_li_b\">{{item.average_price}}</view>\r\n\t\t\t\t<!-- <view class=\"memk_thr_li_b\">{{item.proportion*1}}元/{{item.type*1 == 0 ? '次' : item.type*1 == 1 ? '年' : item.type*1 == 2 ? '月' : ''}}</view> -->\r\n\t\t\t\t<view class=\"memk_thr_li_c\"><text>￥{{item.price*1}}</text></view>\r\n\t\t\t\t<image src=\"/static/images/icon28-1.png\" class=\"memk_thr_li_xz\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"memk_fou\" @click=\"ljktTap\">立即开通</view>\r\n\t\t<view class=\"memk_fiv\">开通会员前请阅读<text @click.stop=\"navTo('/pages/login/xieYi?type=99')\">《会员服务协议》</text></view>\r\n\t\t<view class=\"memk_six\">\r\n\t\t\t<view class=\"memk_six_a\"><image src=\"/static/images/icon29.png\"></image>约课说明</view>\r\n\t\t\t<view class=\"memk_six_b\">{{cardsInfo.appointment_instructions}}</view>\r\n\t\t</view>\r\n\t\t</template>\r\n\t\t\r\n\t\t<view class=\"xytc\" v-if=\"xyToggle\">\r\n\t\t\t<view class=\"xytcCon\">\r\n\t\t\t\t<view class=\"xytcCon_a\">会员服务协议提示</view>\r\n\t\t\t\t<view class=\"xytcCon_b\">欢迎使用Fox舞蹈小程序!为了更好的保您的个人权益，在使用本产品前，请先阅读并同意以下内容:</view>\r\n\t\t\t\t<!-- <view class=\"xytcCon_c\"><view><rich-text :nodes=\"xyCont\"></rich-text></view></view> -->\r\n\t\t\t\t<view class=\"xytcCon_c\">\r\n\t\t\t\t\t<scroll-view\r\n\t\t\t\t\t    class=\"scroll-view\"\r\n\t\t\t\t\t    :style=\"{ height: scrollViewHeight + 'px' }\"\r\n\t\t\t\t\t    scroll-y\r\n\t\t\t\t\t    @scroll=\"onScroll\"\r\n\t\t\t\t\t\tid=\"myScrollView\"\r\n\t\t\t\t\t    ref=\"scrollViewRef\"\r\n\t\t\t\t\t  >\r\n\t\t\t\t\t    <!-- 这里放置需要滚动的内容 -->\r\n\t\t\t\t\t\t<view><rich-text :nodes=\"xyCont\"></rich-text></view>\r\n\t\t\t\t\t  </scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"xytcCon_b\">如您同意以上内容，请点击同意并继续，开始使用我们的产品和服务!</view>\r\n\t\t\t\t<view class=\"xytcCon_f\">\r\n\t\t\t\t\t<view class=\"ty\" @click=\"tyTap\" :style=\"isScrollToBottom ? '' : 'opacity:.7'\">{{isScrollToBottom ? '同意并继续' : '请仔细阅读下滑查看完毕'}}</view>\r\n\t\t\t\t\t<view class=\"noty\" @click=\"xyGbTap\">不同意</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tcardsApi,\r\n\tbuyCardsApi\r\n} from '@/config/http.achieve.js'\r\nimport util from '@/utils/utils.js';\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcardType:0,\r\n\t\t\tisLogined:false,\r\n\t\t\tloding:false,\r\n\t\t\ttqLists:[\r\n\t\t\t\t{name:'特权'},\r\n\t\t\t\t{name:'特权特权特权'},\r\n\t\t\t\t{name:'特权特权特权特权特权'},\r\n\t\t\t\t{name:'特权'},\r\n\t\t\t],\r\n\t\t\thyLists:[\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t],\r\n\t\t\thyType:0,\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\tcardsInfo:{},\r\n\t\t\tck_selectStores:[],//次卡\r\n\t\t\tsck_selectStores:[],//时长卡\r\n\t\t\tmdText:'',\r\n\t\t\t\r\n\t\t\txyCont:'',\r\n\t\t\txyToggle:false,\r\n\t\t\tscrollViewHeight: 300, // 可根据实际情况调整滚动视图的高度\r\n\t\t\tisScrollToBottom: false // 标记是否滚动到底部\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tconsole.log('走吗1五')\r\n\t},\r\n\tmethods: {\r\n\t\tonScroll(e) {\r\n\t\t\tvar that = this;\r\n\t\t  this.$nextTick(() => {\r\n\t\t\t\t\r\n\t\t\t  const query = uni.createSelectorQuery().in(this);\r\n\t\t\t  query.select('.scroll-view').boundingClientRect();\r\n\t\t\t  query.select('.scroll-view').scrollOffset();\r\n\t\t\t  query.exec((res) => {\r\n\t\t\t\t  console.log(res,'res')\r\n\t\t\t\tif (res && res[0] && res[1]) {\r\n\t\t\t\t  const scrollViewHeight = res[0].height;\r\n\t\t\t\t  const scrollTop = res[1].scrollTop;\r\n\t\t\t\t  const scrollHeight = e.detail.scrollHeight;\r\n\t\t\t\t  if (scrollTop + scrollViewHeight >= scrollHeight-20) {\r\n\t\t\t\t\tconsole.log('已经滚动到底部');\r\n\t\t\t\t\tthat.isScrollToBottom = true;\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\t  });\r\n\t\t\t\r\n\t\t  });\r\n\t\t},\r\n\t\t//立即开通\r\n\t\tljktTap(){\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif(this.cardsInfo.card.length == 0){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '暂无要开通的卡'\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t//次卡\r\n\t\t\tif(this.cardType == 0){\r\n\t\t\t\tif(uni.getStorageSync('ck_selectStores')){\r\n\t\t\t\t\t//取选择门店\r\n\t\t\t\t\tvar mdid = [];\r\n\t\t\t\t\tfor(var i=0;i<uni.getStorageSync('ck_selectStores').length;i++){\r\n\t\t\t\t\t\tmdid.push(uni.getStorageSync('ck_selectStores')[i].id)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar card_id = mdid.join(',')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//取默认门店\r\n\t\t\t\t\tvar card_id = uni.getStorageSync('storeInfo').id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t//时长卡\r\n\t\t\tif(this.cardType == 1){\r\n\t\t\t\tif(uni.getStorageSync('sck_selectStores')){\r\n\t\t\t\t\t//取选择门店\r\n\t\t\t\t\tvar mdid = [];\r\n\t\t\t\t\tfor(var j=0;j<uni.getStorageSync('sck_selectStores').length;j++){\r\n\t\t\t\t\t\tmdid.push(uni.getStorageSync('sck_selectStores')[j].id)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar card_id = mdid.join(',')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//取默认门店\r\n\t\t\t\t\tvar card_id = uni.getStorageSync('storeInfo').id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.xyToggle = true;\r\n\t\t\t/*var price = this.cardsInfo.card[this.hyType].price\r\n\t\t\tvar ids = this.cardsInfo.card[this.hyType].id\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/buy/cardsPayment?price=' + price + '&id=' + ids + '&storeid=' + card_id\r\n\t\t\t})*/\r\n\t\t},\r\n\t\t//同意弹窗\r\n\t\ttyTap(){\r\n\t\t\tif(!this.isScrollToBottom){\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t//次卡\r\n\t\t\tif(this.cardType == 0){\r\n\t\t\t\tif(uni.getStorageSync('ck_selectStores')){\r\n\t\t\t\t\t//取选择门店\r\n\t\t\t\t\tvar mdid = [];\r\n\t\t\t\t\tfor(var i=0;i<uni.getStorageSync('ck_selectStores').length;i++){\r\n\t\t\t\t\t\tmdid.push(uni.getStorageSync('ck_selectStores')[i].id)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar card_id = mdid.join(',')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//取默认门店\r\n\t\t\t\t\tvar card_id = uni.getStorageSync('storeInfo').id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t//时长卡\r\n\t\t\tif(this.cardType == 1){\r\n\t\t\t\tif(uni.getStorageSync('sck_selectStores')){\r\n\t\t\t\t\t//取选择门店\r\n\t\t\t\t\tvar mdid = [];\r\n\t\t\t\t\tfor(var j=0;j<uni.getStorageSync('sck_selectStores').length;j++){\r\n\t\t\t\t\t\tmdid.push(uni.getStorageSync('sck_selectStores')[j].id)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar card_id = mdid.join(',')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//取默认门店\r\n\t\t\t\t\tvar card_id = uni.getStorageSync('storeInfo').id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.xyToggle = false;\r\n\t\t\tthis.isScrollToBottom = false;\r\n\t\t\tvar price = this.cardsInfo.card[this.hyType].price\r\n\t\t\tvar ids = this.cardsInfo.card[this.hyType].id\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/buy/cardsPayment?price=' + price + '&id=' + ids + '&storeid=' + card_id\r\n\t\t\t})\r\n\t\t},\r\n\t\t//选择门店\r\n\t\txzmdTap(){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/buy/selectStores?type=' + this.cardType\r\n\t\t\t})\r\n\t\t},\r\n\t\tonLoadData(){\r\n\t\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\t\tthis.mdTextData();//赋值\r\n\t\t\tthis.cardsData();//会员卡\r\n\t\t},\r\n\t\t//赋值\r\n\t\tmdTextData(){\r\n\t\t\tif(this.cardType == 0){\r\n\t\t\t\tif(uni.getStorageSync('ck_selectStores')){\r\n\t\t\t\t\tthis.mdText = uni.getStorageSync('ck_selectStores').length == 1 ? uni.getStorageSync('ck_selectStores')[0].name : uni.getStorageSync('ck_selectStores')[0].name + ' 等' + uni.getStorageSync('ck_selectStores').length + '个门店'\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.mdText = '选择门店'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(this.cardType == 1){\r\n\t\t\t\tif(uni.getStorageSync('sck_selectStores')){\r\n\t\t\t\t\tthis.mdText = uni.getStorageSync('sck_selectStores').length == 1 ? uni.getStorageSync('sck_selectStores')[0].name : uni.getStorageSync('sck_selectStores')[0].name + ' 等' + uni.getStorageSync('sck_selectStores').length + '个门店'\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.mdText = '选择门店'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t//会员卡\r\n\t\tcardsData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t//次卡\r\n\t\t\tif(this.cardType == 0){\r\n\t\t\t\tif(uni.getStorageSync('ck_selectStores')){\r\n\t\t\t\t\t//取选择门店\r\n\t\t\t\t\tvar mdid = [];\r\n\t\t\t\t\tfor(var i=0;i<uni.getStorageSync('ck_selectStores').length;i++){\r\n\t\t\t\t\t\tmdid.push(uni.getStorageSync('ck_selectStores')[i].id)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar card_id = mdid.join(',')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//取默认门店\r\n\t\t\t\t\tvar card_id = uni.getStorageSync('storeInfo').id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t//时长卡\r\n\t\t\tif(this.cardType == 1){\r\n\t\t\t\tif(uni.getStorageSync('sck_selectStores')){\r\n\t\t\t\t\t//取选择门店\r\n\t\t\t\t\tvar mdid = [];\r\n\t\t\t\t\tfor(var j=0;j<uni.getStorageSync('sck_selectStores').length;j++){\r\n\t\t\t\t\t\tmdid.push(uni.getStorageSync('sck_selectStores')[j].id)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvar card_id = mdid.join(',')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//取默认门店\r\n\t\t\t\t\tvar card_id = uni.getStorageSync('storeInfo').id\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tlet that = this;\r\n\t\t\tcardsApi({\r\n\t\t\t\tcard_id:card_id,\r\n\t\t\t\ttype:that.cardType*1 + 1\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('会员卡',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.cardsInfo = res.data;\r\n\t\t\t\t\t// res.data.member_service_agreement =  \"<p style='margin-top:100px'>会议会员服务协议</p><p style='margin-top:100px'>会议会员服务协议</p><p style='margin-top:100px'>会议会员服务协议</p><p style='margin-top:100px'>会议会员服务协议</p>\"\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.xyCont = util.formatRichText(res.data.member_service_agreement)\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//协议关闭\r\n\t\txyGbTap(){\r\n\t\t\tthis.xyToggle = false;\r\n\t\t\tthis.isScrollToBottom = false;\r\n\t\t},\r\n\t\tcardTab(index){\r\n\t\t\tthis.cardType = index;\r\n\t\t\tthis.mdTextData();//赋值\r\n\t\t\tthis.cardsData();//会员卡\r\n\t\t},\r\n\t\tyhTap(index){\r\n\t\t\tthis.hyType = index;\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage{background:#F6F6F6;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./membershipCard.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./membershipCard.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752116123576\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}