{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/confirmOrder.vue?46f0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/confirmOrder.vue?0978", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/confirmOrder.vue?1a17", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/confirmOrder.vue?ae0c", "uni-app:///pages/buy/pointsMall/confirmOrder.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/confirmOrder.vue?5f9c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/confirmOrder.vue?983b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "productxq", "id", "imgbaseUrl", "remark", "s<PERSON><PERSON><PERSON><PERSON>", "area", "qj<PERSON>ton", "jinzLd", "onShow", "onLoad", "console", "methods", "dhSubTap", "uni", "icon", "title", "duration", "that", "goods_id", "addr_id", "sku_id", "num", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "url", "fail", "goToAddr", "addressData", "page", "size", "arrdArr", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2uB,CAAgB,2sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0D/vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;QAAAH;MAAA;IACA;EACA;EACAI;IACA;IACA;IACA;IACA;IACAC;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACAC;MACAJ;QACAE;MACA;MACA;QACAG;QACAC;QACAC;QACAC;QACAlB;MACA;QACAO;QAMA;UACA;AACA;AACA;AACA;AACA;AACA;UACA;UACAG;YACAS;YACAC;YACAC;YACAC;YACAC;YACAC;cACA;cACAd;cACA;AACA;AACA;AACA;AACA;cACAI;cACAJ;gBACAe;cACA;YACA;YACAC;cACAnB;cACA;gBACAO;gBACAJ;gBACAA;kBACAC;kBACAC;kBACAC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;YACA;UACA;QAEA;UACAC;UACAJ;UACAA;YACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAc;MACAjB;QACAe;MACA;IACA;IACA;IACAG;MAAA;MACA;QAAAC;QAAAC;MAAA;QACA;UACAvB;UACA;UACA;YACA;cACAwB;YACA;UACA;UACA;YACA;cAAA7B;YAAA;UACA;YACA;YACAQ;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAsB;MACAtB;QACAe;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC1NA;AAAA;AAAA;AAAA;AAA83C,CAAgB,8vCAAG,EAAC,C;;;;;;;;;;;ACAl5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/pointsMall/confirmOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/buy/pointsMall/confirmOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./confirmOrder.vue?vue&type=template&id=6b4facf4&\"\nvar renderjs\nimport script from \"./confirmOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./confirmOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./confirmOrder.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/pointsMall/confirmOrder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=template&id=6b4facf4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"confirmOrder\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"productxq.id\">\r\n\t\t\r\n\t\t<view class=\"qrdd_a\" @click=\"goToAddr('diancan')\" v-if=\"shouAddr.area == ''\"><image src=\"/static/images/icon33.png\"></image>添加收货地址</view>\r\n\t\t<view class=\"qrdd_b\" @click=\"goToAddr('diancan')\" v-else>\r\n\t\t\t<view class=\"qrdd_b_a\">{{shouAddr.name}} {{shouAddr.phone}}</view>\r\n\t\t\t<view class=\"qrdd_b_b\">{{shouAddr.area+shouAddr.detail}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"qrdd_c\" v-if=\"false\">\r\n\t\t\t<view class=\"qrdd_c_li\">\r\n\t\t\t\t<image :src=\"imgbaseUrl + productxq.image\" mode=\"aspectFill\" class=\"qrdd_c_li_l\"></image>\r\n\t\t\t\t<view class=\"qrdd_c_li_r\">\r\n\t\t\t\t\t<view class=\"qrdd_c_li_r_a\">{{productxq.name}}</view>\r\n\t\t\t\t\t<!-- <view class=\"qrdd_c_li_r_b\">已选：420g</view> -->\r\n\t\t\t\t\t<view class=\"qrdd_c_li_r_c\"><view>￥{{productxq.redeem_points*1}}</view><text>x1</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"qrdd_c\">\r\n\t\t\t<view class=\"spe_n_a\">\r\n\t\t\t\t<view class=\"spe_n_a_l\"><image :src=\"imgbaseUrl + productxq.image\" mode=\"aspectFill\"></image></view>\r\n\t\t\t\t<view class=\"spe_n_a_r\">\r\n\t\t\t\t\t<view class=\"spe_n_a_r_a\">\r\n\t\t\t\t\t\t<view>{{productxq.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"spe_n_a_r_b\">已选：{{productxq.xuanztext}}</view>\r\n\t\t\t\t\t<view class=\"spe_n_a_r_c\"><view>￥{{productxq.redeem_points*1}}</view><text>x{{productxq.num}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"qrdd_d\">\r\n\t\t\t<view>运费</view>\r\n\t\t\t<input type=\"text\" :disabled=\"true\" value=\"包邮\"  placeholder-style=\"color:#999999;\" />\r\n\t\t</view>\r\n\t\t<view class=\"qrdd_d qrdd_bz\">\r\n\t\t\t<view>备注</view>\r\n\t\t\t<input type=\"text\" placeholder=\"如有特殊要求，请填写\" v-model=\"remark\" placeholder-style=\"color: #999999;\" />\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<view class=\"peodex_foo\">\r\n\t\t\t<view class=\"peodex_foo_l\">总计：<text>￥{{(productxq.redeem_points*1)*(productxq.num)}}</text></view>\r\n\t\t\t<view class=\"peodex_foo_r\" @click=\"dhSubTap\">立即购买</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\texchangeSubApi,\r\n\taddrList\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tproductxq:{id:0},\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tremark:'',\r\n\t\t\tshouAddr:{area:''},\r\n\t\t\tqjbutton:'#131315',\r\n\t\t\tjinzLd:true\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tif (uni.getStorageSync('diancan')) {\r\n\t\t\tthis.shouAddr = uni.getStorageSync('diancan')\r\n\t\t}else{\r\n\t\t\tthis.shouAddr = {area:''}\r\n\t\t}\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.productxq = JSON.parse(option.productxq);\r\n\t\tthis.addressData();\r\n\t\tconsole.log(this.productxq)\r\n\t},\r\n\tmethods: {\r\n\t\t//提交兑换\r\n\t\tdhSubTap(){\r\n\t\t\tif(this.shouAddr.area == ''){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t\ttitle:'请选择收货地址~',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tif(!that.jinzLd){\r\n\t\t\t  uni.showToast({\r\n\t\t\t\ticon:'none',\r\n\t\t\t\ttitle:'您点击的太快了~',\r\n\t\t\t\tduration: 2000\r\n\t\t\t  });\r\n\t\t\t  return false;\r\n\t\t\t}\r\n\t\t\tthat.jinzLd = false;\r\n\t\t\tuni.showLoading({\r\n\t\t\t  title:'支付中...',\r\n\t\t\t});\r\n\t\t\texchangeSubApi({\r\n\t\t\t\tgoods_id:that.productxq.id,\r\n\t\t\t\taddr_id:that.shouAddr.id,\r\n\t\t\t\tsku_id:that.productxq.skuid,\r\n\t\t\t\tnum:that.productxq.num,\r\n\t\t\t\tremark:that.remark\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('提交兑换',res)\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t/*uni.hideLoading();\r\n\t\t\t\t\tthat.jinzLd = true\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl:'/pages/buy/coursePackage/success'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false;*/\r\n\t\t\t\t\t//调取微信支付\r\n\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\ttimeStamp:res.data.timeStamp,\r\n\t\t\t\t\t\tnonceStr:res.data.nonceStr,\r\n\t\t\t\t\t\tpackage:res.data.package,\r\n\t\t\t\t\t\tsignType:res.data.signType,\r\n\t\t\t\t\t\tpaySign:res.data.paySign,\r\n\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t//支付成功\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t/*uni.showToast({\r\n\t\t\t\t\t\t\t  icon:'success',\r\n\t\t\t\t\t\t\t  title: '支付成功',\r\n\t\t\t\t\t\t\t  duration: 2000\r\n\t\t\t\t\t\t\t});*/\r\n\t\t\t\t\t\t\tthat.jinzLd = true\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl:'/pages/buy/pointsMall/success'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function (err) {\r\n\t\t\t\t\t\t\tconsole.log('fail:' + JSON.stringify(err),'不回话接口');\r\n\t\t\t\t\t\t\tif (err.errMsg == \"requestPayment:fail cancel\") {\r\n\t\t\t\t\t\t\t\tthat.jinzLd = true\r\n\t\t\t\t\t\t\t\t  uni.hideLoading();\r\n\t\t\t\t\t\t\t\t  uni.showToast({\r\n\t\t\t\t\t\t\t\t\t  icon:'none',\r\n\t\t\t\t\t\t\t\t\t  title: '支付取消',\r\n\t\t\t\t\t\t\t\t\t  duration: 2000\r\n\t\t\t\t\t\t\t\t  });\r\n\t\t\t\t\t\t\t\t  // setTimeout(function(){\r\n\t\t\t\t\t\t\t\t  // \tuni.switchTab({\r\n\t\t\t\t\t\t\t\t  // \t\turl:'/pages/order/order'\r\n\t\t\t\t\t\t\t\t  // \t})\r\n\t\t\t\t\t\t\t\t  // },1500)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.jinzLd = true\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//收货地址\r\n\t\tgoToAddr(type) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/mine/address?type=${type}`\r\n\t\t\t})\r\n\t\t},\r\n\t\t//收货地址\r\n\t\taddressData(){\r\n\t\t\taddrList({page: 1,size: 999,}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tconsole.log(res,'地址列表')\r\n\t\t\t\t\tvar arrdArr = [];\r\n\t\t\t\t\tfor(var i=0;i<res.data.length;i++){\r\n\t\t\t\t\t\tif(res.data[i].is_default == 1){\r\n\t\t\t\t\t\t\tarrdArr.push(res.data[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(arrdArr.length == 0){\r\n\t\t\t\t\t\tthis.shouAddr = {area:''}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.shouAddr = arrdArr[0]\r\n\t\t\t\t\t\tuni.setStorageSync('diancan',arrdArr[0])\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.mescroll.endErr()\r\n\t\t\t\t\t// this.mescroll.endSuccess();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.confirmOrder{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752113447766\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}