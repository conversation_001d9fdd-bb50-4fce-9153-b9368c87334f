{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/pointsMall.vue?9e9f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/pointsMall.vue?c026", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/pointsMall.vue?e6dc", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/pointsMall.vue?5bf0", "uni-app:///pages/buy/pointsMall/pointsMall.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/pointsMall.vue?b08a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/pointsMall.vue?337b"], "names": ["components", "shoppingselect", "data", "isLogined", "bgColor", "shopCateIndex", "shopCate", "mallLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "scrollTop", "score", "imgbaseUrl", "onShow", "methods", "onLoadData", "console", "mallData", "uni", "title", "cate_id", "size", "that", "onReachBottomData", "onReachBottom", "onPullDownRefresh", "categoryData", "userData", "onPageScrollData", "shopCateTap", "dhTap", "url", "icon", "duration", "setTimeout", "navTo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgE7vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACA;MACA;QACA;QACAC;MACA;QACA;MACA;MACA;MACAA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAC;QACAjB;QACAkB;MACA;QACAL;QACA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA;UACAM;UACAA;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAJ;UACAA;QACA;MACA;IAEA;IACAK;MACAP;MACA;QACA;UACA;QACA;MACA;IACA;IACAQ;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;AACA;AACA;MACA;MACA;QACAV;QACA;UACA;AACA;AACA;AACA;UACAM;UACA;UACA;QACA;MACA;IACA;IACA;IACAK;MACA;AACA;AACA;MACA;MACA;QACAX;QACA;UACAM;UACA;QACA;MACA;IACA;IACAM;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;QACAZ;UACAa;QACA;MACA;QACAb;UACAc;UACAb;UACAc;QACA;QACAC;UACAhB;YACAa;UACA;QACA;MACA;IACA;IACAI;MACA;QACAjB;UACAa;QACA;QACA;MACA;MACA;MACA;QACAb;UACAc;UACAb;QACA;QACAe;UACAhB;YACAa;UACA;QACA;MACA;QACAb;UACAa;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAA43C,CAAgB,4vCAAG,EAAC,C;;;;;;;;;;;ACAh5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/pointsMall/pointsMall.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./pointsMall.vue?vue&type=template&id=252e191d&\"\nvar renderjs\nimport script from \"./pointsMall.vue?vue&type=script&lang=js&\"\nexport * from \"./pointsMall.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pointsMall.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/pointsMall/pointsMall.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pointsMall.vue?vue&type=template&id=252e191d&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pointsMall.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pointsMall.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"pointsMall\">\r\n\t\t\r\n\t\t\r\n\t\t<!-- <view class=\"poi_one\" style=\"-margin-top: 1000rpx;\">\r\n\t\t\t<view class=\"poi_one_l\"><view>{{score}}</view><text>可用积分</text></view>\r\n\t\t\t<view class=\"poi_one_r\" @click=\"navTo('/pages/mine/integral')\"><image src=\"/static/images/icon30.png\"></image><text>积分明细</text></view>\r\n\t\t\t<view class=\"poi_one_r\" @click=\"navTo('/pages/mine/order/order')\"><image src=\"/static/images/icon31.png\"></image><text>商城订单</text></view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<view class=\"poi_two\">\r\n\t\t\t<view class=\"poi_two_l\" @click=\"navTo('/pages/mine/order/order')\">商城订单</view>\r\n\t\t\t<view class=\"poi_two_r\">\r\n\t\t\t\t<view class=\"les_search_l\" @click=\"navTo('/pages/buy/pointsMall/search','1')\"><image src=\"/static/images/search.png\"></image><input type=\"text\" :disabled=\"true\" placeholder-style=\"color:#999999\" placeholder=\"请搜索你想要的商品\" /></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"poi_thr\" :class=\"scrollTop >= 225 ? 'poi_thr_fixed' : ''\">\r\n\t\t\t<view class=\"poi_thr_l\">\r\n\t\t\t\t<!-- <view class=\"poi_thr_l_li\" :class=\"shopCateIndex == -1 ? 'poi_thr_l_li_ac' : ''\" @click=\"shopCateTap(-1)\"><text></text><view>全部商品</view></view> -->\r\n\t\t\t\t<view class=\"poi_thr_l_li\" :class=\"shopCateIndex == index ? 'poi_thr_l_li_ac' : ''\" v-for=\"(item,index) in shopCate\" :key=\"index\" @click=\"shopCateTap(index)\">\r\n\t\t\t\t\t<text></text>\r\n\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t<image src=\"/static/images/yj1.png\" class=\"yj1\" v-if=\"shopCateIndex == index\"></image>\r\n\t\t\t\t\t<image src=\"/static/images/yj2.png\" class=\"yj2\" v-if=\"shopCateIndex == index\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"poi_thr_r\">\r\n\t\t\t\t<view class=\"poi_thr_r_li\" v-for=\"(item,index) in mallLists\" :key=\"index\" @click=\"navTo('/pages/buy/pointsMall/productDetails?id=' + item.id,'1')\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"aspectFill\" class=\"poi_thr_r_li_l\"></image>\r\n\t\t\t\t\t<view class=\"poi_thr_r_li_r\">\r\n\t\t\t\t\t\t<view class=\"poi_thr_r_li_r_a\">{{item.name}}</view>\r\n\t\t\t\t\t\t<view class=\"poi_thr_r_li_r_b\">\r\n\t\t\t\t\t\t\t<text>￥{{item.redeem_points}}</text>\r\n\t\t\t\t\t\t\t<view @click.stop=\"dhTap(item)\">购买</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_loding\" v-if=\"!zanwsj\">\r\n\t\t\t\t\t<view class=\"loader-inner ball-clip-rotate\" v-if=\"status == 'loading'\">\r\n\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t<text>加载中</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gg_loding_wusj\" v-else>─── 没有更多数据了 ───</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_zwsj\" v-if=\"zanwsj\">\r\n\t\t\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t<text>暂无数据</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<shoppingselect ref=\"shopCar\" />\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tuserInfoApi,\r\n\tmallCategoryApi,\r\n\tmallListsApi,\r\n} from '@/config/http.achieve.js'\r\nimport shoppingselect from \"@/pages/buy/specification.vue\"\r\nexport default {\r\n\tcomponents: {\r\n\t\tshoppingselect,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tbgColor:'#fff',\r\n\t\t\tshopCateIndex:0,\r\n\t\t\tshopCate:[],\r\n\t\t\tmallLists:[],\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\tscrollTop:0,\r\n\t\t\tscore:0,\r\n\t\t\timgbaseUrl:''\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tmethods: {\r\n\t\tonLoadData(){\r\n\t\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\t\tif(this.isLogined){\r\n\t\t\t\tthis.userData();//个人信息\r\n\t\t\t\tconsole.log(this.$baseUrl,'this.$baseUrl')\r\n\t\t\t}else{\r\n\t\t\t\tthis.loding = true;\r\n\t\t\t}\r\n\t\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\t\tconsole.log('onshow哈哈');\r\n\t\t\tthis.categoryData();//分类\r\n\t\t\tthis.mallLists = [];\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.mallData();//积分商城\r\n\t\t},\r\n\t\t//积分商城\r\n\t\tmallData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmallListsApi({\r\n\t\t\t\tcate_id:that.shopCateIndex == 0 ? 0 : that.shopCate[that.shopCateIndex].id,\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tsize:10,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('积分商城',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t/*var obj = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\t\timage: \"/storage/default/20241023/O1CN01v2BipQ1Pk522c5aa07389ab876c7f9c9d4fd52c8fc26d9a76.png\",\r\n\t\t\t\t\t\t\tname: \"印尼进口营多捞面速食泡面夜宵食品网红拉面拌面方便面整箱\",\r\n\t\t\t\t\t\t\tredeem_points: \"100\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t]*/\r\n\t\t\t\t\tvar obj = res.data.data;\r\n\t\t\t\t\tthat.score = res.data.score\r\n\t\t\t\t\tthat.mallLists = that.mallLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.mallLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.mallLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottomData() {\r\n\t\t\tconsole.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.mallData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t// console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.mallData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    // console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t\tthis.mallLists = [];\r\n\t\t\tthis.mallData();//积分商城\r\n\t\t},\r\n\t\t//分类\r\n\t\tcategoryData(){\r\n\t\t\t/*uni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});*/\r\n\t\t\tlet that = this;\r\n\t\t\tmallCategoryApi({}).then(res => {\r\n\t\t\t\tconsole.log('分类',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t/*var obj = [\r\n\t\t\t\t\t\t{id: 100,name: \"水果1\"},\r\n\t\t\t\t\t\t{id: 100,name: \"水果2\"},\r\n\t\t\t\t\t]*/\r\n\t\t\t\t\tthat.shopCate = res.data;\r\n\t\t\t\t\t// that.shopCate = obj;\r\n\t\t\t\t\t// uni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\t/*uni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});*/\r\n\t\t\tlet that = this;\r\n\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\tconsole.log('个人中心',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.score = res.data.score;\r\n\t\t\t\t\t// uni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tonPageScrollData(e){\r\n\t\t\tthis.scrollTop = e;\r\n\t\t\t// console.log(e,'执行')\r\n\t\t},\r\n\t\tshopCateTap(index){\r\n\t\t\tthis.shopCateIndex = index;\r\n\t\t\tthis.mallLists = [];\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.mallData();//积分商城\r\n\t\t},\r\n\t\t//兑换\r\n\t\tdhTap(item){\r\n\t\t\t// console.log(item.spec_data)\r\n\t\t\tif(this.isLogined){\r\n\t\t\t\tthis.$refs.shopCar.startTanc(item);\r\n\t\t\t\treturn false;\r\n\t\t\t\t/*if(this.score < item.redeem_points*1){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '积分不足',\r\n\t\t\t\t\t\tduration:2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}*/\r\n\t\t\t\tvar productxq = JSON.stringify(item.spec_data)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/buy/pointsMall/confirmOrder?productxq=' + productxq\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\tduration:2000\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t}\r\n\t\t},\r\n\t\tnavTo(url,ismd){\r\n\t\t\tif(ismd){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.pointsMall{overflow: hidden;}\r\npage{padding-bottom: 0;background:#fff;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pointsMall.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pointsMall.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752030104380\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}