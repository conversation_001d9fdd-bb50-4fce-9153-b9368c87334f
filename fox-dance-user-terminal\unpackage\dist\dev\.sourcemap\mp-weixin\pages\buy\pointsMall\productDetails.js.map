{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/productDetails.vue?db1a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/productDetails.vue?dbb0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/productDetails.vue?e18c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/productDetails.vue?2a34", "uni-app:///pages/buy/pointsMall/productDetails.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/productDetails.vue?c555", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/productDetails.vue?4f9d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "shoppingselect", "data", "loding", "isLogined", "score", "imgbaseUrl", "swiperIndex", "bannerLists", "kcjsDetail", "goodsDetail", "id", "qj<PERSON>ton", "onShow", "onLoad", "methods", "goodsData", "uni", "title", "goods_id", "console", "that", "userData", "dhTap", "name", "image", "redeem_points", "url", "icon", "duration", "setTimeout", "swiper<PERSON><PERSON>e", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA6uB,CAAgB,6sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkDjwB;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;;IACA;EACA;EACAC;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAC;MACA;QACAC;QACA;UACAC;UACAA;UACAJ;QACA;MACA;IACA;IACA;IACAK;MACA;AACA;AACA;MACA;MACA;QACAF;QACA;UACAC;UACA;QACA;MACA;IACA;IACA;IACAE;MACA;QACA;QACA;QACA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;UAAAZ;UAAAa;UAAAC;UAAAC;QAAA;QACAT;UACAU;QACA;MACA;QACAV;UACAW;UACAV;UACAW;QACA;QACAC;UACAb;YACAU;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;IACA;IACAC;MACAf;QACAU;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC9JA;AAAA;AAAA;AAAA;AAAg4C,CAAgB,gwCAAG,EAAC,C;;;;;;;;;;;ACAp5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/pointsMall/productDetails.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/buy/pointsMall/productDetails.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./productDetails.vue?vue&type=template&id=288ae8d9&\"\nvar renderjs\nimport script from \"./productDetails.vue?vue&type=script&lang=js&\"\nexport * from \"./productDetails.vue?vue&type=script&lang=js&\"\nimport style0 from \"./productDetails.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/pointsMall/productDetails.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productDetails.vue?vue&type=template&id=288ae8d9&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goodsDetail.id ? _vm.goodsDetail.images.length : null\n  var g1 = _vm.goodsDetail.id ? _vm.goodsDetail.parameter.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productDetails.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productDetails.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"productDetails\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"goodsDetail.id\">\r\n\t\t\r\n\t\t<view class=\"pro_ban\">\r\n\t\t\t<swiper class=\"swiper\" circular :autoplay=\"true\" @change=\"swiperChange\">\r\n\t\t\t\t<swiper-item v-for=\"(item,index) in goodsDetail.images\" :key=\"index\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + item\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"pro_ban_xf\"><text>{{swiperIndex+1}}/{{goodsDetail.images.length}}</text></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"pro_one\">\r\n\t\t\t<view class=\"pro_one_a\">￥{{goodsDetail.redeem_points*1}}</view>\r\n\t\t\t<view class=\"pro_one_b\">\r\n\t\t\t\t<view class=\"pro_one_b_t\">{{goodsDetail.name}}</view>\r\n\t\t\t\t<!-- <view class=\"pro_one_b_b\" v-if=\"goodsDetail.parameter.length > 0\">\r\n\t\t\t\t\t<view class=\"pro_one_b_b_li\" v-for=\"(item,index) in goodsDetail.parameter\" :key=\"index\"><view>{{item.value}}</view><text>{{item.key}}</text></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"pro_two\">送至<text>中原国家广告产业园</text><image src=\"/static/images/icon18.png\"></image></view> -->\r\n\t\t<view class=\"pro_thr\" style=\"margin-bottom:0;\">\r\n\t\t\t<view class=\"pro_thr_t\">商品信息</view>\r\n\t\t\t<view class=\"pro_thr_c\" v-if=\"goodsDetail.parameter.length > 0\">\r\n\t\t\t\t<view class=\"pro_thr_c_li\" v-for=\"(item,index) in goodsDetail.parameter\" :key=\"index\"><view>{{item.key}}</view><text>{{item.value}}</text></view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"pro_thr_b\">\r\n\t\t\t\t<rich-text :nodes=\"kcjsDetail\"></rich-text>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"pro_fou\">\r\n\t\t\t<rich-text :nodes=\"kcjsDetail\"></rich-text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"peode_foo\"><view @click=\"dhTap()\">立即购买</view></view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<shoppingselect ref=\"shopCar\" />\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmallListsxqApi,\r\n\tuserInfoApi,\r\n} from '@/config/http.achieve.js'\r\nimport util from '@/utils/utils.js';\r\nimport shoppingselect from \"@/pages/buy/specification.vue\"\r\nexport default {\r\n\tcomponents: {\r\n\t\tshoppingselect,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloding:false,\r\n\t\t\tisLogined:true,\r\n\t\t\tscore:0,\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tswiperIndex:0,\r\n\t\t\tbannerLists:[],\r\n\t\t\tkcjsDetail:'',\r\n\t\t\tgoodsDetail:{id:0},\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tif(this.isLogined){\r\n\t\t\tthis.userData();//个人信息\r\n\t\t}\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.goodsData(option.id);//商品详情\r\n\t},\r\n\tmethods: {\r\n\t\t//商品详情\r\n\t\tgoodsData(id){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmallListsxqApi({\r\n\t\t\t\tgoods_id:id\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('商品详情',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.kcjsDetail = util.formatRichText(res.data.details)\r\n\t\t\t\t\tthat.goodsDetail = res.data;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\t/*uni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});*/\r\n\t\t\tlet that = this;\r\n\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\tconsole.log('个人中心',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.score = res.data.score;\r\n\t\t\t\t\t// uni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//兑换\r\n\t\tdhTap(){\r\n\t\t\tif(this.isLogined){\r\n\t\t\t\tthis.goodsDetail.image = this.goodsDetail.images.length == 0 ? '' : this.goodsDetail.images[0]\r\n\t\t\t\tthis.$refs.shopCar.startTanc(this.goodsDetail);\r\n\t\t\t\treturn false;\r\n\t\t\t\t/*if(this.score < this.goodsDetail.redeem_points*1){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '积分不足',\r\n\t\t\t\t\t\tduration:2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}*/\r\n\t\t\t\tvar productxq = JSON.stringify({id:this.goodsDetail.id,name:this.goodsDetail.name,image:this.goodsDetail.images[0],redeem_points:this.goodsDetail.redeem_points*1})\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/buy/pointsMall/confirmOrder?productxq=' + productxq\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\tduration:2000\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// swiper 监听\r\n\t\tswiperChange(e){\r\n\t\t\tthis.swiperIndex = e.detail.current\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.productDetails{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productDetails.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./productDetails.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751964162465\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}