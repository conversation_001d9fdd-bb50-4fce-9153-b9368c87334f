{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/search.vue?e17e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/search.vue?8dfc", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/search.vue?05a8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/search.vue?e4f2", "uni-app:///pages/buy/pointsMall/search.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/search.vue?9752", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/pointsMall/search.vue?b18c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "hotLists", "keywords", "keywordsLists", "onLoad", "onShow", "methods", "searchTap", "uni", "url", "clearTap", "title", "content", "success", "that", "console", "isyou", "name"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAquB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2BzvB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACAF;QACAG;QACAC;QACAC;UACA;YACAL;YACAM;UACA;YACAC;UACA;QACA;MACA;IAGA;EAAA,2EAEAb;IACA;IACA;IACA;MACA;QACAc;MACA;IACA;IACA;MACAd;IACA;IACA;MAAAe;IAAA;IACAT;IAEAO;IACAP;MACAC;IACA;EACA,oEACAA;IACAD;MACAC;IACA;EACA;AAGA;AAAA,2B;;;;;;;;;;;;;AC7FA;AAAA;AAAA;AAAA;AAAw3C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACA54C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/pointsMall/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/buy/pointsMall/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=5605c7ae&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/pointsMall/search.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=5605c7ae&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.keywordsLists.length\n  var g1 = _vm.keywordsLists.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"search\">\r\n\t\t\r\n\t\t<view class=\"les_search\">\r\n\t\t\t<view class=\"les_search_l\"><image src=\"/static/images/search.png\"></image><input type=\"text\" placeholder-style=\"color:#999999\" placeholder=\"请搜索你想要的商品\" v-model=\"keywords\" confirm-type=\"search\" @confirm=\"searchTap(keywords)\" /></view>\r\n\t\t\t<view class=\"les_search_r\" @click=\"searchTap(keywords)\">搜索</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"sear_one\" v-if=\"keywordsLists.length > 0\">\r\n\t\t\t<view class=\"sear_one_t\">历史搜索<image src=\"/static/images/icon32.png\" @click=\"clearTap\"></image></view>\r\n\t\t\t<view class=\"sear_one_b\"><text v-for=\"(item,index) in keywordsLists\" :key=\"index\" @click=\"searchTap(item.name)\">{{item.name}}</text></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"sear_one\" v-if=\"keywordsLists.length == 0\">\r\n\t\t\t<view class=\"sear_one_t\">历史搜索</view>\r\n\t\t\t<view class=\"sear_one_b\" style=\"text-align:center;font-size:26rpx;color:#999;\">暂无历史记录</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\thotLists:['大蒜','胡萝卜','大蒜','胡萝卜','大蒜','胡萝卜'],\r\n\t\t\tkeywords:'',\r\n\t\t\tkeywordsLists:[],\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.keywordsLists = uni.getStorageSync(\"keywordsLists\") == '' ? [] : JSON.parse(uni.getStorageSync(\"keywordsLists\"))\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tmethods: {\r\n\t\tsearchTap(){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/buy/pointsMall/searchResults?keywords=' + this.keywords\r\n\t\t\t})\r\n\t\t},\r\n\t\t//清空历史记录\r\n\t\tclearTap(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认要清空历史记录吗？',\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tuni.removeStorageSync(\"keywordsLists\")\r\n\t\t\t\t\t\tthat.keywordsLists = [];\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t\r\n\t\t},\r\n\t\t//搜索\r\n\t\tsearchTap(keywords){\r\n\t\t\t// var keywordsLists = this.keywordsLists;\r\n\t\t\tvar isyou = []\r\n\t\t\tfor(var i=0;i<this.keywordsLists.length;i++){\r\n\t\t\t\tif(this.keywordsLists[i].name == keywords){\r\n\t\t\t\t\tisyou.push(i)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(keywords.split(' ').join('').length == 0){\r\n\t\t\t\tkeywords = ''\r\n\t\t\t}\r\n\t\t\tthis.keywordsLists = (isyou.length == 0 && keywords.length != 0)  ? this.keywordsLists.concat({name:keywords}) : this.keywordsLists;\r\n\t\t\tuni.setStorageSync(\"keywordsLists\",JSON.stringify(this.keywordsLists))\r\n\t\t\t\r\n\t\t\tconsole.log(keywords.length,'keywords',this.keywordsLists)\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/buy/pointsMall/searchResults?keywords=' + keywords\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.search{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120227463\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}