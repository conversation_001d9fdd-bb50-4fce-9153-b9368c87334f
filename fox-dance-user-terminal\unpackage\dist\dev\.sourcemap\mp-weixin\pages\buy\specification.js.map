{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/specification.vue?2cef", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/specification.vue?00da", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/specification.vue?085f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/specification.vue?6bea", "uni-app:///pages/buy/specification.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/specification.vue?fb16", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/buy/specification.vue?43b5"], "names": ["data", "isLogined", "gwcToggle", "selnum", "xuanzText", "guige", "goodsDetial", "carData", "price", "imgbaseUrl", "dqggItem", "id", "onShow", "methods", "ljdhTap", "uni", "icon", "title", "duration", "name", "image", "redeem_points", "num", "xuanztext", "skuid", "url", "success", "that", "startTanc", "console", "selguigClick", "arr", "newsArr", "goodspriceApi", "jian", "add", "navTo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClCA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0CjvB;EACAA;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAjBA,CAkBA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QAAAC;MAAA;IACA;EACA;EACAC,2BAEA;EACAC;IACA;IACAC;MACA;MACA;QACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAAP;QAAAQ;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAT;QACAU;QACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QAAAjB;MAAA;MACA;QACAL;MACA;MACA;MACA;MACA;MACA;MACA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA;MACAuB;IACA;IACAC;MACAD;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;UACAE;UACAC;QACA;MACA;MACA;MACA;MAGA;QACA;QACA;UAAArB;QAAA;MACA;QACA;MACA;IAEA;IACA;IACAsB;MACAJ;MACA;MACA;MAEA;QACA;UACA;QACA;MACA;MACAA;IACA;IACA;IACAK;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA;IACA;IACAC;MACArB;QACAU;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC7MA;AAAA;AAAA;AAAA;AAAo2C,CAAgB,+vCAAG,EAAC,C;;;;;;;;;;;ACAx3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/buy/specification.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./specification.vue?vue&type=template&id=57215911&\"\nvar renderjs\nimport script from \"./specification.vue?vue&type=script&lang=js&\"\nexport * from \"./specification.vue?vue&type=script&lang=js&\"\nimport style0 from \"./specification.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/buy/specification.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./specification.vue?vue&type=template&id=57215911&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.gwcToggle\n    ? _vm.__map(_vm.guige, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = _vm.guige.length\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.gwcToggle = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.gwcToggle = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./specification.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./specification.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"specification\"  v-if=\"gwcToggle\" @click=\"gwcToggle = false\">\r\n\t\t\r\n\t\t<view class=\"spe_n\" @click.stop>\r\n\t\t\t<view class=\"spe_n_a\">\r\n\t\t\t\t<view class=\"spe_n_a_l\"><image :src=\"imgbaseUrl + (dqggItem.id == 0 ? goodsDetial.image : dqggItem.image)\" mode=\"aspectFill\"></image></view>\r\n\t\t\t\t<view class=\"spe_n_a_r\">\r\n\t\t\t\t\t<view class=\"spe_n_a_r_a\">\r\n\t\t\t\t\t\t<view>{{goodsDetial.name}}</view>\r\n\t\t\t\t\t\t<image src=\"/static/images/gb1.png\" @click=\"gwcToggle = false\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"spe_n_a_r_b\">已选：{{xuanzText == '' ? '请选择' : xuanzText}}</view>\r\n\t\t\t\t\t<view class=\"spe_n_a_r_c\"><view>￥{{price}}</view><text v-if=\"false\">x1</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"spe_n_b\">\r\n\t\t\t\t<view class=\"spe_n_b_l\">数量</view>\r\n\t\t\t\t<view class=\"spe_n_b_r\">\r\n\t\t\t\t\t<view @click=\"jian(index)\">-</view>\r\n\t\t\t\t\t<input type=\"number\" :disabled=\"false\" maxlength=\"3\" v-model=\"selnum\" />\r\n\t\t\t\t\t<view @click=\"add(index)\">+</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"spe_n_c\">\r\n\t\t\t\t<view class=\"spe_n_c_li\" v-for=\"(item,index) in guige\" :key=\"index\" v-if=\"guige.length != 0\">\r\n\t\t\t\t\t<view class=\"spe_n_c_t\">{{item.name}}</view>\r\n\t\t\t\t\t<view class=\"spe_n_c_t_b\">\r\n\t\t\t\t\t\t<view v-for=\"(erjitem,erjindex) in item.value\" :key=\"erjindex\" :class=\"item.indexSel == erjindex ? 'sho_con_b_a_b_ac' : ''\" @click=\"selguigClick(index,erjindex)\">{{erjitem}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"spe_n_d\" @click=\"ljdhTap\">立即购买</view>\r\n\t\t</view>\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tgwcToggle:false,//购物车弹窗是否显示\r\n\t\t\tselnum:1,\r\n\t\t\txuanzText:'',\r\n\t\t\tguige:[\r\n\t\t\t\t/*{\r\n\t\t\t\t\ttitle:'颜色',\r\n\t\t\t\t\tindexSel:-1,\r\n\t\t\t\t\tlist:[\r\n\t\t\t\t\t\t{name:'黄色',id:'1-1'},\r\n\t\t\t\t\t\t{name:'白色',id:'1-2'},\r\n\t\t\t\t\t\t{name:'蓝色',id:'1-3'},\r\n\t\t\t\t\t]\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\ttitle:'尺码',\r\n\t\t\t\t\tindexSel:-1,\r\n\t\t\t\t\tlist:[\r\n\t\t\t\t\t\t{name:'L',id:'2-1'},\r\n\t\t\t\t\t\t{name:'XL',id:'2-2'},\r\n\t\t\t\t\t\t{name:'2XL',id:'2-3'}\r\n\t\t\t\t\t]\r\n\t\t\t\t}*/\r\n\t\t\t],\r\n\t\t\tgoodsDetial:{},\r\n\t\t\tcarData:{},\r\n\t\t\tprice:0,//价格\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\tdqggItem:{id:0},//选中的规格\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tmethods: {\r\n\t\t//立即兑换\r\n\t\tljdhTap(){\r\n\t\t\tvar that = this;\r\n\t\t\tif(this.dqggItem.id == 0){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请选择完整的规格',\r\n\t\t\t\t\tduration:2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif(this.dqggItem.stock == 0){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '当前规格暂无库存',\r\n\t\t\t\t\tduration:2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif(this.selnum > this.dqggItem.stock){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '暂无更多库存',\r\n\t\t\t\t\tduration:2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar productxq = JSON.stringify({id:this.goodsDetial.id,name:this.goodsDetial.name,image:this.dqggItem.image,redeem_points:this.price,num:this.selnum,xuanztext:this.xuanzText,skuid:this.dqggItem.id})\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/buy/pointsMall/confirmOrder?productxq=' + productxq,\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.gwcToggle = false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//开启弹窗\r\n\t\tstartTanc(goodsDetial){\r\n\t\t\tthis.selnum = 1;\r\n\t\t\tthis.dqggItem = {id:0}\r\n\t\t\tfor(var i=0;i<goodsDetial.spec_list.length;i++){\r\n\t\t\t\tgoodsDetial.spec_list[i].indexSel = -1;\r\n\t\t\t}\r\n\t\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\t\tthis.gwcToggle = true;\r\n\t\t\tthis.goodsDetial = goodsDetial;\r\n\t\t\tthis.guige = goodsDetial.spec_list\r\n\t\t\tthis.price = goodsDetial.redeem_points;\r\n\t\t\t/*\r\n\t\t\tthis.price = goodsDetial.price;\r\n\t\t\tthis.goodsDetial = goodsDetial;\r\n\t\t\tvar arrnews = goodsDetial.param;\r\n\t\t\tfor(var i = 0;i<arrnews.length;i++){\r\n\t\t\t\tarrnews[i].indexSel = -1\r\n\t\t\t}\r\n\t\t\tthis.guige = arrnews;\r\n\t\t\t*/\r\n\t\t\tthis.xuanzText = '请选择';\r\n\t\t\tconsole.log(goodsDetial);\r\n\t\t},\r\n\t\tselguigClick(index,erjIndex){\r\n\t\t\tconsole.log(index,erjIndex)\r\n\t\t\tif(this.guige[index].indexSel == erjIndex){\r\n\t\t\t\tthis.guige[index].indexSel = -1\r\n\t\t\t}else{\r\n\t\t\t\tthis.guige[index].indexSel = erjIndex\r\n\t\t\t}\r\n\t\t\tvar arr = [];\r\n\t\t\tvar newsArr = []\r\n\t\t\tfor(var i=0;i<this.guige.length;i++){\r\n\t\t\t\tif(this.guige[i].indexSel != -1){\r\n\t\t\t\t\tarr.push(this.guige[i].value[this.guige[i].indexSel])\r\n\t\t\t\t\tnewsArr.push(this.guige[i].name + ':' +this.guige[i].value[this.guige[i].indexSel])\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// console.log(arr,newsArr,'哈哈123');\r\n\t\t\tthis.xuanzText = arr.join(',')\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tif(newsArr.length == 0 || newsArr.length != this.guige.length){\r\n\t\t\t\tthis.price = this.goodsDetial.redeem_points\r\n\t\t\t\tthis.dqggItem = {id:0}\r\n\t\t\t}else{\r\n\t\t\t\tthis.goodspriceApi(newsArr);//获取商品价格\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\t//获取商品价格\r\n\t\tgoodspriceApi(newsArr){\r\n\t\t\tconsole.log(newsArr,'newsArr')\r\n\t\t\tvar ggName = newsArr.join(';');\r\n\t\t\tvar skuList = this.goodsDetial.spec_data.skuList;\r\n\t\t\t\r\n\t\t\tfor(var i=0;i<skuList.length;i++){\r\n\t\t\t\tif(skuList[i].spec == ggName){\r\n\t\t\t\t\tthis.dqggItem = skuList[i]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log(this.dqggItem,'this.dqggItem')\r\n\t\t},\r\n\t\t//减商品\r\n\t\tjian(index){\r\n\t\t\t if(this.selnum == 1){\r\n\t\t\t\t return false;\r\n\t\t\t }\r\n\t\t\t this.selnum --;\r\n\t\t},\r\n\t\t//增加商品\r\n\t\tadd(index){\r\n\t\t\t/*if(this.selnum >= this.dqggItem.stock){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '暂无更多库存',\r\n\t\t\t\t\tduration:2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}*/\r\n\t\t\tthis.selnum ++;\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.specification{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./specification.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./specification.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752116123139\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}