{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/foxDetail.vue?c9a1", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/foxDetail.vue?d7c1", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/foxDetail.vue?c006", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/foxDetail.vue?5f52", "uni-app:///pages/index/foxDetail.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/foxDetail.vue?7bf4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/foxDetail.vue?6d68"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loding", "imgbaseUrl", "foxInfo", "qj<PERSON>ton", "onShow", "onLoad", "methods", "dhTap", "uni", "name", "latitude", "longitude", "success", "console", "foxDetail", "title", "that", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAytB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgH7uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAN;QACAO;MACA;MACA;MACA;QACAJ;QACAD;MACA;QACAG;QACA;UACAL;UACAQ;UACAA;QACA;MACA;IAEA;IACAC;MACAT;QACAU;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzKA;AAAA;AAAA;AAAA;AAAg2C,CAAgB,2vCAAG,EAAC,C;;;;;;;;;;;ACAp3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/foxDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/foxDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./foxDetail.vue?vue&type=template&id=0b63f81a&\"\nvar renderjs\nimport script from \"./foxDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./foxDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./foxDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/foxDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./foxDetail.vue?vue&type=template&id=0b63f81a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loding ? _vm.foxInfo.teacher.length : null\n  var g1 = _vm.loding ? _vm.foxInfo.store.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./foxDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./foxDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"loding\" style=\"overflow: hidden;\">\r\n\t\t<view class=\"fox\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"title_text\">\r\n\t\t\t\t\tFox介绍\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title_bottom\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"fox_cont flex col-top\">\r\n\t\t\t\t<view class=\"fox_cont_text\">{{foxInfo.introduce}}</view>\r\n\t\t\t\t<view class=\"fox_cont_img\">\r\n\t\t\t\t\t<image mode=\"aspectFill\" :src=\"imgbaseUrl + foxInfo.introduce_image\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"fox\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"title_text\">\r\n\t\t\t\t\t主理人介绍\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title_bottom\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"fox_cont flex col-top\">\r\n\t\t\t\t<view class=\"fox_cont_img\" style=\"margin-left: 0;margin-right: 32rpx;\">\r\n\t\t\t\t\t<image mode=\"aspectFill\" :src=\"imgbaseUrl + foxInfo.host_image\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fox_cont_text\">{{foxInfo.host}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"fox\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"title_text\">\r\n\t\t\t\t\t成立背景\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title_bottom\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"fox_cont flex col-top\">\r\n\t\t\t\t<view class=\"fox_cont_text\" style=\"padding-top: 0;\">{{foxInfo.establish_background}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 旗下师资 -->\r\n\t\t<view class=\"fox teach\" v-if=\"foxInfo.teacher.length > 0\">\r\n\t\t\t<view class=\"flex row-between\" style=\"padding:26rpx ;\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view class=\"title_text\">\r\n\t\t\t\t\t\t旗下师资\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title_bottom\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"more flex\" @click=\"navTo('/pages/index/teacherDetail')\">\r\n\t\t\t\t\t查看更多 <image src=\"/static/images/introduce_more.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"teach_list\">\r\n\t\t\t\t<view class=\"teach_li flex\" v-for=\"(item,index) in foxInfo.teacher\" :key=\"index\" @click=\"navTo('/pages/index/teacherDetail')\">\r\n\t\t\t\t\t<view class=\"teach_li_l\">\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teach_li_r flex-1\">\r\n\t\t\t\t\t\t<view class=\"teach_li_r_t flex\">\r\n\t\t\t\t\t\t\t<view class=\"teach_li_r_name\">{{item.name}}老师</view>\r\n\t\t\t\t\t\t\t<view class=\"teach_li_r_tag\" v-for=\"(itemerj,indexerj) in item.levelTable.name\" :key=\"indexerj\">{{itemerj}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"teach_li_r_c\">擅长舞种：{{item.skilled_dance}}</view>\r\n\t\t\t\t\t\t<view class=\"teach_li_r_c\" v-if=\"item.work_year*1 > 0\">工作年限：{{item.work_year}}年</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 所有门店 -->\r\n\t\t<view class=\"fox teach  store\" v-if=\"foxInfo.store.length > 0\">\r\n\t\t\t<view class=\"flex row-between\" style=\"padding:26rpx ;\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<view class=\"title_text\">\r\n\t\t\t\t\t\t所有门店\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title_bottom\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"more flex\" @click=\"navTo('/pages/index/switchStores?mdlist=1')\">\r\n\t\t\t\t\t查看更多 <image src=\"/static/images/introduce_more.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"store_list\">\r\n\t\t\t\t<view class=\"store_li \" v-for=\"(item,index) in foxInfo.store\" :key=\"index\" @click=\"navTo('/pages/index/storesDetail?id=' + item.id)\">\r\n\t\t\t\t\t<view class=\"store_li_t flex\">\r\n\t\t\t\t\t\t<view class=\"store_li_t_l\">\r\n\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"store_li_t_r flex-1\">\r\n\t\t\t\t\t\t\t<view class=\"\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"\">{{item.introduce}}</view>\r\n\t\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/images/store_map_icon.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t\t距离你{{item.distance}}km\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"store_li_d flex row-between\" @click.stop=\"dhTap(item)\">\r\n\t\t\t\t\t\t<view class=\"line-1\">{{item.address}}</view>\r\n\t\t\t\t\t\t<view class=\"btn\">导航前往</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tfoxJsApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloding:false,\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\tfoxInfo:{},\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.foxDetail();//FOX介绍\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t},\r\n\tmethods: {\r\n\t\t//导航\r\n\t\tdhTap(item){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.openLocation({\r\n\t\t\t\tname:item.address,\r\n\t\t\t\tlatitude: item.latitude*1,\r\n\t\t\t\tlongitude: item.longitude*1,\r\n\t\t\t\tsuccess: function () {\r\n\t\t\t\t\tconsole.log('success');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//FOX介绍\r\n\t\tfoxDetail(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tfoxJsApi({\r\n\t\t\t\tlongitude:uni.getStorageSync('postion').longitude,\r\n\t\t\t\tlatitude:uni.getStorageSync('postion').latitude,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('FOX介绍',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.foxInfo = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tnavTo(url) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.title {\r\n\t\tposition: relative;\r\n\r\n\t\t.title_text {\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 11;\r\n\t\t\tfont-family: Maoken Glitch Sans, Maoken Glitch Sans;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 38rpx;\r\n\t\t}\r\n\r\n\t\t.title_bottom {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom: 6rpx;\r\n\t\t\twidth: 126rpx;\r\n\t\t\theight: 10rpx;\r\n\t\t\tbackground: linear-gradient(90deg, #131315 28%, rgba(255, 255, 255, 0) 100%);\r\n\t\t}\r\n\t}\r\n\r\n\t.fox {\r\n\t\tmargin: 20rpx auto 0;\r\n\t\twidth: 698rpx;\r\n\t\tpadding: 26rpx;\r\n\t\t// height: 298rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\r\n\t\t.fox_cont {\r\n\t\t\tmargin-top: 26rpx;\r\n\r\n\t\t\t.fox_cont_text {\r\n\t\t\t\tpadding-top: 16rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tline-height: 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.fox_cont_img {\r\n\t\t\t\tmargin-left: 32rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 266rpx;\r\n\t\t\t\t\theight: 174rpx;\r\n\t\t\t\t\tborder-radius: 10rpx 10rpx 10rpx 10rpx;\r\n\t\t\t\t\tbackground-color: pink;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.teach {\r\n\t\tbackground-color: transparent;\r\n\t\tpadding: 0;\r\n\r\n\t\t.more {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #999999;\r\n\t\t\tline-height: 30rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 10.46rpx;\r\n\t\t\t\theight: 16rpx;\r\n\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.teach_list {\r\n\t\t\t.teach_li {\r\n\t\t\t\tmargin: 20rpx auto 0;\r\n\t\t\t\tpadding: 42rpx 26rpx 40rpx;\r\n\t\t\t\twidth: 698rpx;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\r\n\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\tmargin-top: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.teach_li_l {\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 118rpx;\r\n\t\t\t\t\t\theight: 118rpx;\r\n\t\t\t\t\t\tborder-radius: 10rpx 10rpx 10rpx 10rpx;\r\n\t\t\t\t\t\tbackground-color: pink;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.teach_li_r {\r\n\t\t\t\t\tmargin-left: 26rpx;\r\n\r\n\t\t\t\t\t.teach_li_r_t {\r\n\t\t\t\t\t\t.teach_li_r_name {\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\t\tline-height: 38rpx;\r\n\t\t\t\t\t\t\tmargin-right: 28rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.teach_li_r_tag {\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\tpadding: 0 6rpx;\r\n\t\t\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\t\t\tbackground: #131315;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx 10rpx 10rpx 10rpx;\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.teach_li_r_c {\r\n\t\t\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.store {\r\n\t\tmargin-top: 28rpx;\r\n\t\tmargin-bottom: 28rpx;\r\n\t\t.store_list {\r\n\t\t\t.store_li {\r\n\t\t\t\tmargin: 22rpx auto 0;\r\n\t\t\t\twidth: 698rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius:20rpx;\r\n\r\n\t\t\t\t// border-radius: 0rpx 0rpx 0rpx 0rpx;\r\n\t\t\t\t// border: 2rpx solid rgba(153, 153, 153, 0.2);\r\n\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\tmargin-top: 0rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.store_li_t {\r\n\t\t\t\t\tpadding: 18rpx 26rpx;\r\n\t\t\t\t\tborder-bottom: 2rpx solid rgba(153, 153, 153, 0.2);\r\n\r\n\t\t\t\t\t.store_li_t_l {\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx 10rpx 10rpx 10rpx;\r\n\t\t\t\t\t\t\tbackground-color: pink;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.store_li_t_r {\r\n\t\t\t\t\t\tmargin-left: 26rpx;\r\n\r\n\t\t\t\t\t\tview {\r\n\t\t\t\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\t\tline-height: 38rpx;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t\t\tline-height: 36rpx;\r\n\t\t\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\t\t\t-webkit-line-clamp:2;\r\n\t\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:nth-child(3) {\r\n\t\t\t\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\t\t\t\theight: 32rpx;\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.store_li_d {\r\n\t\t\t\t\twidth: 698rpx;\r\n\t\t\t\t\theight: 112rpx;\r\n\t\t\t\t\tpadding: 0 26rpx;\r\n\r\n\t\t\t\t\tview {\r\n\t\t\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\t\t\tmax-width: 470rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\t\t\twidth: 144rpx;\r\n\t\t\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\t\tborder-radius: 82rpx 82rpx 82rpx 82rpx;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./foxDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./foxDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120226630\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}