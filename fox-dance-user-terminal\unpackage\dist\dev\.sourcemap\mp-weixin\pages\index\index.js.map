{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/index.vue?d58b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/index.vue?8bac", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/index.vue?61bc", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/index.vue?6404", "uni-app:///pages/index/index.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/index.vue?ea9d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/index.vue?71c0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "isLogined", "navBg", "notice_text", "showConcat", "imgbaseUrl", "userInfo", "avatar", "frequency", "nickname", "notice", "score", "poster", "store", "address", "name", "id", "luck_draw_frequency", "level_name", "experience_value", "upgrade", "storesLists", "kczxBan", "kczxIndex", "topBan", "topBanBj", "topBanIndex", "navHeight", "pageBj", "cssVariables", "image2", "loding", "onShow", "uni", "onPageScroll", "scrollTop", "e", "onLoad", "console", "methods", "getContractData", "that", "url", "qmTap", "toPop1", "sctxTap", "title", "driver", "goBannerTap", "changePidData", "pid", "topChange", "kczxChange", "teacher<PERSON><PERSON>", "storeData", "type", "limit", "openImg", "arr", "current", "urls", "homeData", "longitude", "latitude", "store_id", "getPosition", "flag", "scope", "fail", "showCancel", "success", "confirm", "navTo", "icon", "onShareAppMessage", "path"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAqtB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyLzuB;AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UAAAC;UAAAC;UAAAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;;IACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACA,IACAC,YACAC,EADAD;IAEA;IACA;EACA;EACAE;IACA;IACAJ;IACA;IACA;MACA;IAAA,CACA;MACA;IAAA;IAEA;IAEA;IACAA;IAGA;MACAA;MACA;QACA;MACA;;MACAK;IACA;IACA;AACA;AACA;AACA;EAEA;;EACAC;IACA;IACAC;MACA;MACA,kCAEA;QACAF;QACA;UACA;UACAG;UACA;YACAR;cACAS;YACA;UACA;UACA;AACA;AACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAP;MACA;MACAL;QACAa;MACA;MACA;QAAAC;MAAA;QACAT;QACA;UACAL;UACA;QACA;MACA;IACA;IACA;IACAe;MACA;QACA;UACAf;YACAS;UACA;QACA;UACAT;YACAS;UACA;QACA;MACA;IACA;IACA;IACAO;MACA;QACAC;MACA;QACAZ;QACA,oBACA;MACA;IAEA;IACA;IACAa;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAf;QACA;UACAL;UACA;UACA;QACA;MACA;IAEA;IACA;IACAqB;MACA;MACA;QACAC;QACAC;MACA;QACAlB;QACA;UACAL;UACAQ;QACA;MACA;IAEA;IACA;IACAgB;MACA;MACA;QACAC;MACA;MACApB;MACAL;QACA0B;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACA5B;QACAa;MACA;MACA;MACA;QACAgB;QACAC;QACAC;MACA;QACA;UACA1B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAL;UACAQ;UACAA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;UAEAR;QACA;MACA;IACA;IACA;IACAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAxB;gBAAA;gBAAA,OACA;cAAA;gBAAAyB;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAGA5B;gBACA;kBACA;kBACAL;oBACAkC;oBACAC;sBACAnC;wBACAa;wBACAuB;wBACAC,gCAEA;0BAAA,IADAC;0BAEA;4BACAtC;8BACAqC;gCACArC;kCACAsB;kCACAe;oCACAhC;oCACA;sCACAyB;sCACAD;oCACA;oCACA7B;oCACAQ;kCACA;kCACA2B,0BAEA;gCACA;gCACA9B;8BACA;8BACA8B;gCACA9B;8BACA;4BACA;0BACA;wBACA;sBACA;oBACA;oBACAgC;sBACArC;wBACAsB;wBACAe;0BACAhC;0BACA;4BACAyB;4BACAD;0BACA;0BACA7B;0BACAQ;wBACA;wBACA2B,0BAEA;sBACA;oBACA;kBACA;gBACA;kBACAnC;oBACAsB;oBACAe;sBACAhC;sBACA;wBACAyB;wBACAD;sBACA;sBACA7B;sBACAQ;oBACA;oBACA2B,0BACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MACA;QACAvC;UACAS;QACA;QACA;MACA;MACA;MACA;QACAT;UACAwC;UACA3B;QACA;QACA;QACAb;UACAS;QACA;QACA;MACA;QACAT;UACAS;QACA;MACA;IACA;EACA;EACA;EACAgC;IACA;IACA;MACA5B;MACA6B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtjBA;AAAA;AAAA;AAAA;AAA41C,CAAgB,uvCAAG,EAAC,C;;;;;;;;;;;ACAh3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uniNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar\" */ \"@/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.kczxBan.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showConcat = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"index\" :style=\"'background:' + pageBj\">\r\n\t\t<!-- <u-navbar :is-back=\"false\" title=\"FOX舞蹈\" :background=\"{ background:'none' }\"\r\n\t\t\t:border-bottom=\"false\" :title-color=\"navBg==1?'#fff':'#fff' \" title-size=\"32\">\r\n\t\t</u-navbar> -->\r\n\t\t <!-- :style=\"'margin-top:-'+navHeight+'px'\" -->\r\n\t\t<view class=\"ind_ban\">\r\n\t\t\t<view class=\"ind_swiper_bj\">\r\n\t\t\t\t<!-- <image :src=\"bannerLists[swiperIndex].bigimg\" mode=\"scaleToFill\"></image> -->\r\n\t\t\t\t<image  @click=\"goBannerTap(item.jump_address)\" v-for=\"(item,index) in topBanBj\" :key=\"index\" :src=\"imgbaseUrl + item.carousel_background\" :style=\"topBanIndex == index ? 'opacity:1;' : 'opacity:0;'\" mode=\"aspectFill\"></image>\r\n\t\t\t</view>\r\n\t\t\t<swiper circular=\"true\" @change=\"topChange\">\r\n\t\t\t\t<swiper-item v-for=\"(item,index) in topBan\" :key=\"index\">\r\n\t\t\t\t\t<view><image :src=\"imgbaseUrl + item.carousel\" mode=\"aspectFill\"  @click=\"goBannerTap(item.jump_address)\"></image></view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<!-- <view class=\"ind_ban_box\" :style=\"'box-shadow:0 0 16rpx '+pageBj+';background:'+pageBj\"></view> -->\r\n\t\t\t<view class=\"ind_ban_box\" :style=\"'background: linear-gradient(to top, '+pageBj+' 0%, transparent 100%)'\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ind_one\">\r\n\t\t\t<view style=\"width: 100%;height:auto;overflow:hidden;\"></view>\r\n\t\t\t<view class=\"ind_one_tx\">\r\n\t\t\t\t<image src=\"/static/images/toux.png\" mode=\"aspectFill\" v-if=\"!isLogined\"></image>\r\n\t\t\t\t<image :src=\"userInfo.avatar == '' ? '/static/images/toux.png' : imgbaseUrl + userInfo.avatar\"\r\n\t\t\t\t\tmode=\"aspectFill\" v-if=\"isLogined\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ind_one_a\"><view>HI，{{isLogined ? (userInfo.nickname == '' ? '微信用户' : userInfo.nickname) : '请先登录'}}</view><text>{{userInfo.level_name}}</text></view>\r\n\t\t\t<view class=\"ind_one_b\">\r\n\t\t\t\t<!-- <view class=\"ind_one_b_li\">\r\n\t\t\t\t\t<view class=\"ind_one_b_li_a\">当前积分</view>\r\n\t\t\t\t\t<view class=\"ind_one_b_li_b\">{{isLogined ? userInfo.score*1 : 0}}</view>\r\n\t\t\t\t\t<view class=\"xian\"></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"ind_one_b_li\" @click=\"navTo('/pages/prizedraw/prizedraw')\" style=\"width: 50%;\">\r\n\t\t\t\t\t<view class=\"ind_one_b_li_a\">抽奖次数</view>\r\n\t\t\t\t\t<view class=\"ind_one_b_li_b\">{{isLogined ? userInfo.luck_draw_frequency*1 : 0}}次</view>\r\n\t\t\t\t\t<view class=\"xian\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ind_one_b_li\" @click=\"navTo('/pages/prizedraw/prizedraw')\" style=\"width: 50%;\">\r\n\t\t\t\t\t<view class=\"ind_one_b_li_a\">惊喜抽奖</view>\r\n\t\t\t\t\t<view class=\"ind_one_b_li_b\">可抽奖</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ind_one_c\">\r\n\t\t\t\t<view class=\"ind_one_c_l\" @click=\"navTo('/pages/prizedraw/dengji')\">经验值<image src=\"/static/images/icon72.png\"></image></view>\r\n\t\t\t\t<view class=\"ind_one_c_c\"><text>{{userInfo.experience_value}}</text>/{{userInfo.upgrade}}</view>\r\n\t\t\t\t<view class=\"ind_one_c_r\"><view :style=\"'width:'+(userInfo.experience_value*1)/userInfo.upgrade*100+'%;'\"></view></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t\r\n\t\t<view class=\"ind_two\">\r\n\t\t\t<view class=\"ind_two_li\" @click=\"navTo('/pages/index/teacherDetail',1)\">\r\n\t\t\t\t<image src='/static/images/icon73.png'></image>\r\n\t\t\t\t<view class=\"ind_two_li_a\">导师介绍</view>\r\n\t\t\t\t<view class=\"ind_two_li_b\">130+位广州实力导师</view>\r\n\t\t\t\t<text></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ind_two_li\" @click=\"navTo('/pages/index/foxDetail',1)\">\r\n\t\t\t\t<image src='/static/images/icon74.png'></image>\r\n\t\t\t\t<view class=\"ind_two_li_a\">FOX介绍</view>\r\n\t\t\t\t<view class=\"ind_two_li_b\">F5.周年品牌沉跑</view>\r\n\t\t\t\t<text></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ind_two_li\" @click=\"navTo('/pages/index/storesDetail?id=' + userInfo.store.id,1)\">\r\n\t\t\t\t<image src='/static/images/icon75.png'></image>\r\n\t\t\t\t<view class=\"ind_two_li_a\">门店介绍</view>\r\n\t\t\t\t<view class=\"ind_two_li_b\">灵动舞之坊</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ind_thr\">\r\n\t\t\t<view class=\"ind_thr_l\"><view>当前门店</view><text>ADDRE55</text><text class=\"xian\"></text></view>\r\n\t\t\t<view class=\"ind_thr_c\">\r\n\t\t\t\t<!-- <image src=\"/static/images/icon76.png\"></image> -->\r\n\t\t\t\t<u-icon name=\"map-fill\" :color=\"pageBj\" size=\"40\"></u-icon>\r\n\t\t\t\t<view :style=\"'color:'+pageBj\">{{userInfo.store.name}}</view>\r\n\t\t\t\t<text class=\"xian\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ind_thr_r\" @click=\"navTo('/pages/index/switchStores')\">\r\n\t\t\t\t<view class=\"ind_thr_r_a\"><image src=\"/static/images/icon77.png\"></image>切换门店<image src=\"/static/images/icon77.png\"></image></view>\r\n\t\t\t\t<view class=\"ind_thr_r_b\">在此切换门店</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ind_fou\">\r\n\t\t\t<image src=\"/static/images/icon78.png\" class=\"ind_fou_l\"></image>\r\n\t\t\t<view class=\"ind_fou_r\">\r\n\t\t\t\t<uni-notice-bar scrollable single :text=\"userInfo.notice\" background-color=\"transparent\" color=\"#333\"\r\n\t\t\t\t\t:single=\"true\"></uni-notice-bar>\r\n\t\t\t\t<!-- <u-notice-bar mode=\"horizontal\" :list=\"notice_text\"></u-notice-bar> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ind_fiv\" v-if=\"kczxBan.length > 0\">\r\n\t\t\t<view class=\"ind_fiv_t\"><view>最新课程咨询</view><text>不定期福利等你领取</text></view>\r\n\t\t\t<view class=\"ind_fiv_b\">\r\n\t\t\t\t<swiper circular=\"true\" @change=\"kczxChange\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item,index) in kczxBan\" :key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.carousel\" mode=\"aspectFill\" @click=\"goBannerTap(item.jump_address)\"></image>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<view class=\"ind_fiv_b_yd\"><text v-for=\"(item,index) in kczxBan\" :class=\"kczxIndex == index ? 'ind_fiv_b_yd_ac' : ''\"></text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"\" style=\"height: 300rpx;\"></view>\r\n\t\t\r\n\r\n\t\t<view class=\"use\">\r\n\t\t\t<view class=\"share\">\r\n\t\t\t\t<image src=\"/static/images/index_share.png\" mode=\"\"></image>\r\n\t\t\t\t<button open-type=\"share\"></button>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"concat\" @click=\"showConcat = false\"> -->\r\n\t\t\t<view class=\"concat\" @click=\"navTo('/pages/mine/tzgl')\">\r\n\t\t\t\t<image src=\"/static/images/index_concat_kf1.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t<view><text>消息</text><text>推送</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\r\n\t\t<u-popup v-model=\"showConcat\" border-radius=\"20\" mode=\"center\">\r\n\t\t\t<view class=\"concat_box\">\r\n\t\t\t\t<view class=\"concat_box_title flex col-top row-between\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t选择门店\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/images/popup_close.png\" mode=\"scaleToFill\" @click=\"showConcat = false\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"concat_box_list\">\r\n\t\t\t\t\t<view class=\"concat_box_li flex row-between\" v-for=\"(item,index) in storesLists\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"concat_box_li_l flex\">\r\n\t\t\t\t\t\t\t<view class=\"concat_box_li_l_img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"concat_box_li_l_name\">\r\n\t\t\t\t\t\t\t\t<view class=\"text\">{{item.name}}<view class=\"title_bottom\"></view></view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"concat_box_li_r btn\" @click=\"navTo('/pages/index/service?id=' + item.id)\">联系客服</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"concat_box_li flex row-between\" v-for=\"(item,index) in storesLists\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"concat_box_li_l flex\">\r\n\t\t\t\t\t\t\t<view class=\"concat_box_li_l_img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"concat_box_li_l_name\">\r\n\t\t\t\t\t\t\t\t<view class=\"text\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"title_bottom\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"concat_box_li_r btn\" @click=\"navTo('/pages/index/service',1)\">\r\n\t\t\t\t\t\t\t联系客服\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"concat_box_li flex row-between\" v-for=\"(item,index) in storesLists\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"concat_box_li_l flex\">\r\n\t\t\t\t\t\t\t<view class=\"concat_box_li_l_img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"concat_box_li_l_name\">\r\n\t\t\t\t\t\t\t\t<view class=\"text\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"title_bottom\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"concat_box_li_r btn\" @click=\"navTo('/pages/index/service',1)\">\r\n\t\t\t\t\t\t\t联系客服\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\t\t<tabbar ref=\"tabbar\" :current=\"0\"></tabbar>\r\n\t\t\r\n\t\t<view class=\"lodingg\" v-if=\"loding\"></view>\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tuserInfoApi,\r\n\thomeDataApi,\r\n\tstoreListsApi,\r\n\tchangePidApi,\r\n\tteacherApi,\r\n\tupImg,\r\n\tgetContractApi\r\n} from '@/config/http.achieve.js'\r\nimport {\r\n\t\tauthIsPass\r\n\t} from '@/utils/auth.js'\r\nimport tabbar from '@/components/tabbar.vue'\r\nexport default {\r\n\tcomponents: {\r\n\t\ttabbar,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tnavBg: '',\r\n\t\t\tnotice_text: [],\r\n\t\t\tshowConcat: false, //联系客服弹窗\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\tuserInfo:{\r\n\t\t\t\tavatar: \"\",\r\n\t\t\t\tfrequency: 0,\r\n\t\t\t\tnickname: \"\",\r\n\t\t\t\tnotice:'',\r\n\t\t\t\tscore: 0,\r\n\t\t\t\tposter:[],\r\n\t\t\t\tstore:{address:'',name:'',id:0},\r\n\t\t\t\tluck_draw_frequency:0,\r\n\t\t\t\tlevel_name:'LV0',\r\n\t\t\t\texperience_value:0,\r\n\t\t\t\tupgrade:0,\r\n\t\t\t},\r\n\t\t\tstoresLists:[],\r\n\t\t\tkczxBan:[],//底部轮播图\r\n\t\t\tkczxIndex:0,//轮播图索引\r\n\t\t\t\r\n\t\t\ttopBan:[],//顶部轮播图\r\n\t\t\ttopBanBj:[],//顶部轮播图背景\r\n\t\t\ttopBanIndex:0,//顶部轮播图索引\r\n\t\t\tnavHeight:0,\r\n\t\t\tpageBj:'#fff',\r\n\t\t\tcssVariables:{},\r\n\t\t\t\r\n\t\t\timage2:'',\r\n\t\t\tloding:true\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t// this.cssVariables = {'--main-color':uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').button : '#131315'}\r\n\t\t// const page = this.$scope || this.$mp.page;\r\n\t\t// console.log(this.cssVariables,'this.cssVariables',page.$el)\r\n\t\t// uni.setStorageSync('token','49e2e458-2c96-489d-a19a-9aacf70b3c13')\r\n\t\tthis.$refs.tabbar.setColor();\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t\tif(this.isLogined){\r\n\t\t\t// this.userData();//个人信息\r\n\t\t\tthis.getContractData();//获取未签署的合同\r\n\t\t}else{\r\n\t\t\tthis.loding = false;\r\n\t\t}\r\n\t\tif(uni.getStorageSync('postion')){\r\n\t\t\tthis.homeData();//首页数据\r\n\t\t}\r\n\t\tuni.hideTabBar()\r\n\t\tif(this.isLogined && uni.getStorageSync('pid')){\r\n\t\t\tthis.changePidData();//更改邀请人\r\n\t\t}\r\n\t},\r\n\tonPageScroll(e) {\r\n\t\tconst top = uni.upx2px(100)\r\n\t\tconst {\r\n\t\t\tscrollTop\r\n\t\t} = e\r\n\t\tlet percent = scrollTop / top > 1 ? 1 : scrollTop / top\r\n\t\tthis.navBg = percent\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// this.teacherData();//老师\r\n\t\tuni.setStorageSync('qhwc',1)\r\n\t\tthis.navHeight = (uni.getSystemInfoSync().statusBarHeight + 44);\r\n\t\tif (!uni.getStorageSync('postion')) {\r\n\t\t\t//this.getPosition()\r\n\t\t} else {\r\n\t\t\t// this.areaInfo = uni.getStorageSync('postion')\r\n\t\t}\r\n\t\tthis.getPosition()\r\n\t\t\r\n\t\tthis.storeData();//门店列表\r\n\t\tuni.hideTabBar();\r\n\t\t\r\n\t\t\r\n\t\tif(options.pid){\r\n\t\t\tuni.setStorageSync('pid',options.pid);\r\n\t\t\tif(this.isLogined){\r\n\t\t\t\tthis.changePidData();//更改邀请人\r\n\t\t\t}\r\n\t\t\tconsole.log('options进去了？userid',uni.getStorageSync('pid'))\r\n\t\t}\r\n\t\t/*if(options.q){\r\n\t\t\tconst scene = decodeURIComponent(options.q);\r\n\t\t\tuni.setStorageSync('pid',scene.split('=')[1])\r\n\t\t}*/\r\n\t\t\r\n\t},\r\n\tmethods: {\r\n\t\t//获取未签署的合同\r\n\t\tgetContractData(){\r\n\t\t\tvar that = this;\r\n\t\t\tgetContractApi({\r\n\t\t\t\t\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('获取未签署的合同',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t// res.data = 22;\r\n\t\t\t\t\tthat.loding = res.data ? true : false;\r\n\t\t\t\t\tif(res.data){\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl:'/pages/index/signing?id=' + res.data\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t/*uni.reLaunch({\r\n\t\t\t\t\t\turl:'/pages/index/signing'\r\n\t\t\t\t\t})*/\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//签名提交\r\n\t\tqmTap(){\r\n\t\t\tthis.sctxTap(this.image2)\r\n\t\t},\r\n\t\ttoPop1(){\r\n\t\t\tthis.$refs.signature1.toPop()\r\n\t\t},\r\n\t\t//上传头像\r\n\t\tsctxTap(tempFilePaths){\r\n\t\t\tconsole.log(tempFilePaths,'tempFilePaths')\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle:'加载中'\r\n\t\t\t})\r\n\t\t\tupImg(tempFilePaths, 'file',{driver:'cos'}).then(ress => {\r\n\t\t\t\tconsole.log('上传图片',ress)\r\n\t\t\t\tif (ress.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// that.avatar = ress.data.file.url\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//跳转tabview\r\n\t\tgoBannerTap(url){\r\n\t\t\tif(url != ''){\r\n\t\t\t\tif (url.includes(\"http\")) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/webView/webView?url=' + url\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t//更改邀请人\r\n\t\tchangePidData(){\r\n\t\t\tchangePidApi({\r\n\t\t\t\tpid:uni.getStorageSync('pid')\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('更改邀请人',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\t//顶部轮播图\r\n\t\ttopChange(e){\r\n\t\t\tthis.topBanIndex = e.detail.current;\r\n\t\t\t// this.pageBj = e.detail.current == 0 || e.detail.current == 2  ? '#FADAFF' : '#EDB1D0'\r\n\t\t},\r\n\t\t//底部轮播图\r\n\t\tkczxChange(e){\r\n\t\t\tthis.kczxIndex = e.detail.current;\r\n\t\t},\r\n\t\t//老师-本地缓存\r\n\t\tteacherData(){\r\n\t\t\tlet that = this;\r\n\t\t\tteacherApi({}).then(res => {\r\n\t\t\t\tconsole.log('老师',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// uni.setStorageSync('teacherlist',res.data.data);\r\n\t\t\t\t\t// that.storesLists = res.data.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\t//门店列表\r\n\t\tstoreData(){\r\n\t\t\tlet that = this;\r\n\t\t\tstoreListsApi({\r\n\t\t\t\ttype:1,\r\n\t\t\t\tlimit:9999,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店列表',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.storesLists = res.data.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\t//打开图片\r\n\t\topenImg(idx, imgs) {\r\n\t\t\tlet arr = []\r\n\t\t\tfor (let i = 0; i < imgs.length; i++) {\r\n\t\t\t\tarr.push(this.imgbaseUrl + imgs[i])\r\n\t\t\t}\r\n\t\t\tconsole.log(idx, imgs);\r\n\t\t\tuni.previewImage({\r\n\t\t\t\tcurrent: idx,\r\n\t\t\t\turls: arr\r\n\t\t\t})\r\n\t\t},\r\n\t\t//首页数据\r\n\t\thomeData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\thomeDataApi({\r\n\t\t\t\tlongitude:uni.getStorageSync('postion').longitude,\r\n\t\t\t\tlatitude:uni.getStorageSync('postion').latitude,\r\n\t\t\t\tstore_id:uni.getStorageSync('storeInfo') ? uni.getStorageSync('storeInfo').id : 0\r\n\t\t\t}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tconsole.log('首页',res);\r\n\t\t\t\t\t// uni.setStorageSync('storeInfo',res.data.store);\r\n\t\t\t\t\t// res.data.store.carousel = ['/storage/default/20250208/微信截图_20250208152c336795f3a876e19e0480fdee90d32329932868.png','/storage/default/20250122/微信图片_2025012222d3cc6c1e11de20f706d6f9f4382f51c5fff11900.jpg']\r\n\t\t\t\t\t// res.data.store.carousel_background = ['/storage/default/20250208/icon701013d4966a6214114e903c141e4155dc8fe24204.png','/storage/default/20250122/微信图片_2025012222d3cc6c1e11de20f706d6f9f4382f51c5fff11900.jpg']\r\n\t\t\t\t\tthis.pageBj = res.data.store.background;//首页背景图\r\n\t\t\t\t\tthis.kczxBan = res.data.course_carousel;//底部课程轮播图\r\n\t\t\t\t\tthis.topBan = res.data.carousel;//顶部轮播\r\n\t\t\t\t\tthis.topBanBj = res.data.carousel;\r\n\t\t\t\t\tuni.setStorageSync('storeInfo',res.data.store);\r\n\t\t\t\t\tthat.userInfo = res.data;\r\n\t\t\t\t\tthat.$refs.tabbar.setColor(res.data.ecology);\r\n\t\t\t\t\t/*if(uni.getStorageSync('storeInfo')){\r\n\t\t\t\t\t\tthat.userInfo = res.data;\r\n\t\t\t\t\t\tthis.userInfo.store = uni.getStorageSync('storeInfo');\r\n\t\t\t\t\t\tuni.setStorageSync('server_token',res.data.server_token)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.setStorageSync('storeInfo',res.data.store);\r\n\t\t\t\t\t\tthat.userInfo = res.data;\r\n\t\t\t\t\t}*/\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//获取自身位置\r\n\t\tasync getPosition() {\r\n\t\t\t\r\n\t\t\tlet that = this\r\n\t\t\tconst flag = await authIsPass('scope.userLocation')\r\n\t\t\tif (this.IsOpenMap == false) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tconsole.log(flag);\r\n\t\t\tif (!flag) {\r\n\t\t\t\tthis.IsOpenMap = false\r\n\t\t\t\tuni.authorize({\r\n\t\t\t\t\tscope: 'scope.userLocation',\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '使用该功能必须允许位置服务，是否重新授权？',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: ({\r\n\t\t\t\t\t\t\t\tconfirm\r\n\t\t\t\t\t\t\t}) => {\r\n\t\t\t\t\t\t\t\tif (confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.openSetting({\r\n\t\t\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.getLocation({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype: 'wgs84',\r\n\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('定位1',res)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlatitude:res.latitude,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlongitude:res.longitude\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('postion',data)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat.homeData();\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('开启权限成功')\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail() {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('开启权限失败')\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.getLocation({\r\n\t\t\t\t\t\t\ttype: 'wgs84',\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tconsole.log('定位2',res)\r\n\t\t\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\t\t\tlatitude:res.latitude,\r\n\t\t\t\t\t\t\t\t\tlongitude:res.longitude\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('postion',data)\r\n\t\t\t\t\t\t\t\tthat.homeData();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'wgs84',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconsole.log('定位3',res)\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\tlatitude:res.latitude,\r\n\t\t\t\t\t\t\tlongitude:res.longitude\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.setStorageSync('postion',data)\r\n\t\t\t\t\t\tthat.homeData();\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tnavTo(url,ismd){\r\n\t\t\tif(ismd){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tif(uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\t//setTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t//},1000)\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\t// 分享到微信好友 \r\n\tonShareAppMessage() {\r\n\t  var that = this;\r\n\t  return {\r\n\t\ttitle:'FOX舞蹈，快来和我一起体验吧，超多福利等你来拿!',\r\n\t\tpath: '/pages/index/index?pid=' + uni.getStorageSync('userid') ? uni.getStorageSync('userid') : 0,\r\n\t\t// imageUrl:that.bannerLists[0].images,\r\n\t  }\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t\r\n\tpage {\r\n\t\tpadding-bottom: 0;\r\n\t\tbackground:#FADAFF;\r\n\t\t\r\n\t}\r\n\r\n\t.index {\r\n\t\t// background-image: url(/static/login/login_top_bgi.png);\r\n\t\t-background-image: url(https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/userreport/login_top_bgi.png);\r\n\t\t\r\n\t\tbackground-size: 100% auto;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tmin-height: 100vh;\r\n\t\ttransition:0.8s;\r\n\t}\r\n\r\n\t.use {\r\n\t\tposition: fixed;\r\n\t\tright: 14rpx;\r\n\t\tbottom: 30vh;\r\n\t\tz-index: 11;\r\n\t\t.share{\r\n\t\t\tposition: relative;\r\n\t\t\tbutton{\r\n\t\t\t\tdisplay:block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tposition:absolute;\r\n\t\t\t\ttop: 0;left:0;\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.concat{\r\n\t\t\tposition: relative;\r\n\t\t\tview{\r\n\t\t\t\twidth:100%;\r\n\t\t\t\theight:82rpx;\r\n\t\t\t\toverflow:hidden;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content:center;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tposition:absolute;\r\n\t\t\t\ttop:0;left:0;\r\n\t\t\t\ttext{\r\n\t\t\t\t\tdisplay:block;\r\n\t\t\t\t\tfont-size:22rpx;\r\n\t\t\t\t\tcolor:#333;\r\n\t\t\t\t\tletter-spacing:1px;\r\n\t\t\t\t\tposition:relative;\r\n\t\t\t\t\tleft:1px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tbutton{\r\n\t\t\t\tdisplay:block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tposition:absolute;\r\n\t\t\t\ttop: 0;left:0;\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\timage {\r\n\t\t\twidth: 106rpx;\r\n\t\t\theight: 106rpx;\r\n\r\n\t\t\t&:nth-child(1) {\r\n\t\t\t\tmargin-bottom: 34rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.userInfo {\r\n\t\tmargin: 494rpx auto 0;\r\n\t\tpadding: 42rpx 32rpx 32rpx;\r\n\t\twidth: 700rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 30rpx 30rpx 30rpx 30rpx;\r\n\t\tposition: relative;\r\n\r\n\t\t.userInfo_t {\r\n\t\t\t.userInfo_t_l {\r\n\t\t\t\tpadding-left: 20rpx;\r\n\r\n\t\t\t\t.userInfo_t_l_t {\r\n\t\t\t\t\tfont-family: Maoken Glitch Sans;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.userInfo_t_l_d {\r\n\t\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\t\tfont-weight: Medium;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 33rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.userInfo_t_r {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 52rpx;\r\n\t\t\t\ttop: -44rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\theight: 140rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tborder: 4rpx solid #FFFFFF;\r\n\t\t\t\t\tbackground-color: pink;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.userInfo_count {\r\n\t\t\tmargin-top: 26rpx;\r\n\t\t\tpadding-bottom: 24rpx;\r\n\t\t\tborder-bottom: 2rpx solid rgba(160, 160, 160, 0.2);\r\n\r\n\t\t\t.userInfo_count_li {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t.userInfo_count_li_num {\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 47rpx;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tline-height: 33rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.userInfo_count_li_text {\r\n\t\t\t\t\tmargin-top: 26rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.userInfo_shop {\r\n\t\t\tpadding-top: 32rpx;\r\n\r\n\t\t\t.userInfo_shop_l {\r\n\t\t\t\t.userInfo_shop_l_t {\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\t// margin-left: 24rpx;\r\n\t\t\t\t\t\twidth: 10.46rpx;\r\n\t\t\t\t\t\theight: 16rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.userInfo_shop_l_d {\r\n\t\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.userInfo_shop_r {\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\twidth: 144rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\tbackground: #945048;\r\n\t\t\t\t\tborder-radius: 124rpx 124rpx 124rpx 124rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.notice {\r\n\t\tmargin: 24rpx auto 0;\r\n\t\twidth: 670rpx;\r\n\t\theight: 80rpx;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 0 26rpx;\r\n\t\theight: 72rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 90rpx 90rpx 90rpx 90rpx;\r\n\r\n\t\t.notice_l {\r\n\t\t\timage {\r\n\t\t\t\twidth: 32rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.notice_r {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t/deep/ .uni-noticebar {\r\n\t\t\t\tmargin-bottom: 0 !important;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n\r\n\t.nav {\r\n\t\tpadding: 26rpx 22rpx 0 26rpx;\r\n\r\n\t\t.title_bottom {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom: 6rpx;\r\n\t\t\twidth: 126rpx;\r\n\t\t\theight: 10rpx;\r\n\t\t\tbackground: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);\r\n\t\t}\r\n\r\n\t\t.nav_l {\r\n\t\t\twidth: 362rpx;\r\n\t\t\theight: 308rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\r\n\t\t\t.nav_l_t {\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.nav_l_text {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tz-index: 11;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 38rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nav_l_d {\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 202rpx;\r\n\t\t\t\t\theight: 202rpx;\r\n\t\t\t\t\tmargin: 30rpx auto 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.nav_r {\r\n\t\t\twidth: 314rpx;\r\n\t\t\theight: 308rpx;\r\n\r\n\t\t\t.nav_r_t {\r\n\t\t\t\twidth: 314rpx;\r\n\t\t\t\theight: 136rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\r\n\t\t\t\t.nav_r_t_text {\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.text {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tz-index: 11;\r\n\t\t\t\t\t\tfont-family: Maoken Glitch Sans;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\tline-height: 38rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.nav_r_t_img {\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 108rpx;\r\n\t\t\t\t\t\theight: 108rpx;\r\n\t\t\t\t\t\tmargin-left: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.poster {\r\n\t\timage {\r\n\t\t\twidth: 698rpx;\r\n\t\t\theight: 238rpx;\r\n\t\t\tmargin: 20rpx auto 0;\r\n\r\n\t\t\t&:nth-child(1) {\r\n\t\t\t\tmargin-top: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.concat_box {\r\n\t\tpadding: 26rpx 66rpx;\r\n\t\twidth: 662rpx;\r\n\t\theight: 770rpx;\r\n\t\theight:auto;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\t\toverflow: auto;\r\n\t\t.concat_box_title {\r\n\t\t\tpadding-bottom: 26rpx;\r\n\t\t\tborder-bottom: 2rpx solid rgba(148, 80, 72, 0.2);\r\n\r\n\t\t\tview {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tline-height: 38rpx;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t\t.concat_box_list{\r\n\t\t\t-height:calc(100% - 100rpx);\r\n\t\t\t-overflow: auto;\r\n\t\t\t.concat_box_li{\r\n\t\t\t\tpadding: 14rpx 30rpx;\r\n\t\t\t\tpadding: 14rpx 0;\r\n\t\t\t\tmargin: 26rpx 0  0;\r\n\t\t\t\t.concat_box_li_l{\r\n\t\t\t\t\t.concat_box_li_l_img{\r\n\t\t\t\t\t\timage{\r\n\t\t\t\t\t\t\twidth: 108rpx;\r\n\t\t\t\t\t\t\theight: 108rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.concat_box_li_l_name{\r\n\t\t\t\t\t\twidth:200rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tmargin-left: 40rpx;\r\n\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\t-webkit-line-clamp:1;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\t.text {\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\tz-index: 11;\r\n\t\t\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\t\t\tfont-family: Maoken Glitch Sans;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\t\t\tfloat:left;\r\n\t\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\t\t-webkit-line-clamp:1;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.title_bottom {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\tbottom: 6rpx;\r\n\t\t\t\t\t\t\t-width: 126rpx;\r\n\t\t\t\t\t\t\theight: 10rpx;\r\n\t\t\t\t\t\t\tbackground: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);\r\n\t\t\t\t\t\t\tfloat:left;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.concat_box_li_r{\r\n\t\t\t\t\twidth: 152rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\tbackground: #945048;\r\n\t\t\t\t\tborder-radius: 92rpx 92rpx 92rpx 92rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751964161350\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}