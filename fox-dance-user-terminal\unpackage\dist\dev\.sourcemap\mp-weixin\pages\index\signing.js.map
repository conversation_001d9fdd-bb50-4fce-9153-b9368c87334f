{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/signing.vue?c105", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/signing.vue?efb6", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/signing.vue?67ca", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/signing.vue?b07f", "uni-app:///pages/index/signing.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/signing.vue?fa5e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/signing.vue?80f2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "imgbaseUrl", "image2", "url", "htImgsrc", "htId", "onShow", "onLoad", "console", "methods", "cesTap", "uni", "title", "content", "showCancel", "confirmText", "success", "htconData", "id", "that", "qmTap", "icon", "duration", "toPop1", "sctxTap", "qshtSub", "image", "getContractData", "setTimeout", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAutB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC4B3uB;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACA;YACAR;YACAG;cACAR;YACA;UACA;YACAK;UACA;QACA;MACA;IACA;IACA;IACAS;MACAN;QACAC;MACA;MACA;MACA;QAAAM;MAAA;QACA;UACAP;UACAQ;QACA;MACA;IACA;IACA;IACAC;MACA;QACAT;UACAU;UACAT;UACAU;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAhB;MACA;MACAG;QACAC;MACA;MACA;QACAJ;QACA;UACAG;UACA;UACAQ;QACA;MACA;IACA;IACA;IACAM;MACAd;QACAC;MACA;MACA;MACA;QAAAM;QAAAQ;MAAA;QACA;UACAf;UACAQ;UACAR;YACAU;YACAT;YACAU;UACA;QAEA;MACA;IACA;IACA;IACA;IACAK;MACA;MACA;QACAnB;QACA;UACA;UACA;YACAG;cACAC;cACAC;cACAC;cACAC;cACAC;gBACA;kBACAR;kBACAG;oBACAR;kBACA;gBACA;kBACAK;gBACA;cACA;YACA;UACA;YACAoB;cACAjB;gBACAR;cACA;YACA;UACA;QAEA;MACA;IACA;IACA0B;MACAlB;QACAR;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACvLA;AAAA;AAAA;AAAA;AAA81C,CAAgB,yvCAAG,EAAC,C;;;;;;;;;;;ACAl3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/signing.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/signing.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./signing.vue?vue&type=template&id=2b64f11f&\"\nvar renderjs\nimport script from \"./signing.vue?vue&type=script&lang=js&\"\nexport * from \"./signing.vue?vue&type=script&lang=js&\"\nimport style0 from \"./signing.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/signing.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signing.vue?vue&type=template&id=2b64f11f&\"", "var components\ntry {\n  components = {\n    jpSignaturePopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup\" */ \"@/uni_modules/jp-signature/components/jp-signature-popup/jp-signature-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signing.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signing.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"signing\">\r\n\t\t\r\n\t\t<view class=\"sig_one\" style=\"margin:0;margin-bottom:600rpx;\">\r\n\t\t\t<!-- 合同内容 -->\r\n\t\t\t<image :src=\"imgbaseUrl + htImgsrc\" mode=\"widthFix\" style=\"width:100%;\" :show-menu-by-longpress=\"true\"></image>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"sig_two sig_two_fixed\">\r\n\t\t\t<view class=\"sig_two_t\" @click=\"cesTap\">签字区域（必填）</view>\r\n\t\t\t<view class=\"sig_two_b\" @click=\"toPop1\">\r\n\t\t\t\t<text v-if=\"image2 == ''\">点击签字</text>\r\n\t\t\t\t<image :src=\"image2\" mode=\"heightFix\" v-else></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"sig_two_f\" @click=\"qmTap\">提交</view>\r\n\t\t\t<view class=\"aqjlViw\"></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t<jp-signature-popup ref=\"signature1\" popup v-model=\"image2\" :required=\"true\" />\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tgetContractApi,\r\n\tupImg,\r\n\thqhtnrApi,\r\n\tsignContractApi\r\n} from '@/config/http.achieve.js'\r\nimport {\r\n\tapis\r\n} from '@/config/http.api.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\timgbaseUrl:'',\r\n\t\t\timage2:'',\r\n\t\t\turl: '',\r\n\t\t\thtImgsrc:'',\r\n\t\t\thtId:0\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tonLoad(options) {\r\n\t\tconsole.log('options12',options,apis)\r\n\t\tthis.imgbaseUrl = this.$baseUrl_ht;\r\n\t\tthis.htId = options.id ? options.id : 0;\r\n\t\tthis.htconData();//获取合同内容\r\n\t},\r\n\tmethods: {\r\n\t\t//测试跳转\r\n\t\tcesTap(){\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '查询到您有未签署的合同，请点击下一步继续签署',\r\n\t\t\t\tshowCancel:false,\r\n\t\t\t\tconfirmText:'下一步',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl:'/pages/index/signing?id=23'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//获取合同内容\r\n\t\thtconData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '生成合同中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\thqhtnrApi({id:that.htId}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.htImgsrc = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//签名提交\r\n\t\tqmTap(){\r\n\t\t\tif(this.image2 == ''){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先进行签字',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tthis.sctxTap(this.image2)\r\n\t\t},\r\n\t\ttoPop1(){\r\n\t\t\tthis.$refs.signature1.toPop()\r\n\t\t},\r\n\t\t//上传头像\r\n\t\tsctxTap(tempFilePaths){\r\n\t\t\tconsole.log(tempFilePaths,'tempFilePaths')\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle:'加载中'\r\n\t\t\t})\r\n\t\t\tupImg(tempFilePaths, 'file').then(ress => {\r\n\t\t\t\tconsole.log('上传图片',ress)\r\n\t\t\t\tif (ress.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// that.avatar = ress.data.file.url\r\n\t\t\t\t\tthat.qshtSub(ress.data.file.url);//签署合同提交\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//签署合同提交\r\n\t\tqshtSub(image){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tsignContractApi({id:that.htId,image:image}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.getContractData(res.data);//继续查询是否有未签署完的合同，获取未签署的合同\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\ttitle: '签署成功',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//继续查询是否有未签署完的合同\r\n\t\t//获取未签署的合同\r\n\t\tgetContractData(num){\r\n\t\t\tvar that = this;\r\n\t\t\tgetContractApi({}).then(res => {\r\n\t\t\t\tconsole.log('获取未签署的合同',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t// res.data = 22;\r\n\t\t\t\t\tif(res.data){\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: '查询到您还有'+num+'份未签署的合同，请点击下一步继续签署',\r\n\t\t\t\t\t\t\tshowCancel:false,\r\n\t\t\t\t\t\t\tconfirmText:'下一步',\r\n\t\t\t\t\t\t\tsuccess: function (ress) {\r\n\t\t\t\t\t\t\t\tif (ress.confirm) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\t\turl:'/pages/index/signing?id=' + res.data\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t} else if (ress.cancel) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl:'/pages/index/index'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},1500)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.signing{overflow: hidden;\r\n\t-width: 100%;\r\n\t-height:100vh;\r\n}\r\npage{padding-bottom: 0;background:#fff;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signing.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signing.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752030103457\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}