{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/teacherDetail.vue?b895", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/teacherDetail.vue?30ca", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/teacherDetail.vue?7969", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/teacherDetail.vue?ba17", "uni-app:///pages/index/teacherDetail.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/teacherDetail.vue?080d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/index/teacherDetail.vue?0c50"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "format", "isLogined", "loding", "jibLists", "jibIndex", "jibText", "jb<PERSON><PERSON><PERSON>", "imgbaseUrlOss", "wuzLists", "wuzIndex", "wuzText", "wuzToggle", "laosLists", "laosIndex", "laosText", "laosToggle", "keywords", "keywords_cunc", "searchToggle", "currentIndex", "type", "sjsxLists", "sjsxIndex", "scrollLeft", "date_sx", "array_md", "array_md_cunc", "index_md", "dateText", "uswiperIndex", "teacherList", "ljtkToggle", "storeCourseLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "imgbaseUrl", "kcxqTeachId", "controlsToggle", "speedState", "speedNum", "speedRate", "qj<PERSON>ton", "<PERSON><PERSON><PERSON><PERSON>", "onShow", "onLoad", "computed", "startDate", "endDate", "methods", "speedTap", "handleFullScreen", "handleControlstoggle", "handleSetSpeedRate", "videoContext", "uni", "icon", "title", "duration", "changeSwiper", "console", "searchStore", "teacher_id", "that", "storeCourseData", "id", "level_id", "dance_id", "date", "name", "onReachBottom", "onPullDownRefresh", "storesxqTap", "setTimeout", "url", "yypdTo", "kqhyts", "ljktTap", "searchTap", "bindPickerChange_md", "dateDatasx", "getDateArrayWithWeekday", "newDate", "dateArray", "week", "day", "getFormattedCurrentDate", "cIndex", "scrollJt", "bindDateChange_sx", "getDate", "year", "month", "sjsxTap", "navTap", "teachersData", "limit", "res", "categoryData", "gbTcTap", "jbStartTap", "jibTap", "jibSubTap", "jib<PERSON><PERSON><PERSON>", "wuzStartTap", "wuzTap", "wuzSubTap", "wuzReact", "laosStartTap", "laosTap", "laosSubTap", "laosReact", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqOjvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACA;MACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEAC;MACAC;MACAC;MAEAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEAC;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACAN;QACAE;MACA;MACA;MACA;QACA;QACAK;MACA;QACAF;QACA;UACAL;UACA;UACA;UACA;YACAlC;UACA;UACA0C;UACAA;UACA;YACAA;YACAA;UACA;YACAA;YACAA;YACAA;UACA;QAEA;MACA;IACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACAC;MAEA;MACAT;QACAE;MACA;MACA;QACA5B;QACAoC;QACAC;QACAC;QACAL;QACA;QACAM;QACAC;MACA;QACAT;QACA;AACA;AACA;AACA;QACA;UACA;UACAG;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAR;UACAA;QACA;MACA;IACA;IACAe;MACA;MACA;QACA;UACA;QACA;MACA;IACA;;IACAC;MACAX;MACA;MACA;MACA;IACA;IACA;IACAY;MACAZ;MACA;QACAL;UACAC;UACAC;QACA;QACAgB;UACAlB;YACAmB;UACA;QACA;QACA;MACA;MACA;MACA;QACA;MACA;QACAnB;UACA;UACAmB;QACA;MACA;IACA;IACA;IACAC;MACA;QACApB;UACAC;UACAC;QACA;QACAgB;UACAlB;YACAmB;UACA;QACA;QACA;MACA;MAEAnB;QACAmB;MACA;IACA;IACA;IACAE;MACArB;QACAE;QACAD;QACAE;MACA;IACA;IACA;IACAmB;MACA;MACAtB;QACAmB;MACA;IACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;AACA;AACA;IACA;;IACA;IACAC;MACAnB;MACA;MACA;MACA;MACA;IACA;IACA;IACAoB;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACAC;UAAAC;UAAAC;UAAAjB;QAAA;MACA;MACA;IACA;IACA;IACAkB;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC,gCACA;IACAC;MACA;MACA;MACA;MACA;MACA1B;MACA;MACA;MACA;MACAU;QACAV;MACA;IAEA;IACA2B;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;QACAA;MACA;MACAC;MACAP;MACA;IACA;IACA;IACAQ;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAxC;QACAE;MACA;MACA;MACA;QACA5B;QACAmE;QACA;QACA;QACA;MACA;QACApC;QACA;UACA;YACAqC;YACA;YACA;cACAA;YACA;YACA;YACA;cACA;gBACAlC;cACA;YACA;UACA;UACA;UACAA;UACAA;UACAR;UACAQ;QACA;MACA;IACA;IACA;IACAmC;MACA;MACA;QACAtC;QACA;UACAG;UACAA;UACAA;QACA;MACA;IACA;IACA;IACAoC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;MACA;;MAEA;QACA;QACA;QACA/C;UACAE;QACA;QACAgB;UACAlB;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAgD;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;MACA;MACA;QACA;QACAnD;UACAE;QACA;QACAgB;UACAlB;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAoD;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAlD;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACAL;UACAE;QACA;QACAgB;UACAlB;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAwD;MACA;IACA;IACAC;MACAzD;QACAmB;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACpyBA;AAAA;AAAA;AAAA;AAAo2C,CAAgB,+vCAAG,EAAC,C;;;;;;;;;;;ACAx3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/teacherDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/teacherDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./teacherDetail.vue?vue&type=template&id=06552a6d&\"\nvar renderjs\nimport script from \"./teacherDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./teacherDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./teacherDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/teacherDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetail.vue?vue&type=template&id=06552a6d&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-swiper/u-swiper\" */ \"@/components/uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loding ? _vm.type == 0 && _vm.array_md_cunc.length > 0 : null\n  var g1 = _vm.loding ? _vm.type == 0 && _vm.array_md_cunc.length > 0 : null\n  var g2 = _vm.loding ? _vm.storeCourseLists.length == 0 && _vm.type == 0 : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.searchToggle = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.searchToggle = false\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      _vm.speedNum = false\n    }\n    _vm.e3 = function ($event) {\n      $event.stopPropagation()\n      _vm.ljtkToggle = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.ljtkToggle = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"teacherDetail\" :style=\"{ '--qjbutton-color': qjbutton,'--qjziti-color': qjziti }\" v-if=\"loding\">\r\n\t\t\r\n\t\t<view class=\"lsxq_head\">\r\n\t\t\t<view class=\"lsxq_head_l\">\r\n\t\t\t\t<view class=\"stor_thr_c_n\">\r\n\t\t\t\t\t<view class=\"stor_thr_c_li\" :class=\"jbToggle ? 'stor_thr_c_li_ac' : ''\" @click=\"jbStartTap\">{{jibText == '' ? '级别' : jibText}}<text></text></view>\r\n\t\t\t\t\t<view class=\"stor_thr_c_li\" :class=\"wuzToggle ? 'stor_thr_c_li_ac' : ''\" @click=\"wuzStartTap\">{{wuzText == '' ? '舞种' : wuzText}}<text></text></view>\r\n\t\t\t\t\t<view class=\"stor_thr_c_li\" :class=\"laosToggle ? 'stor_thr_c_li_ac' : ''\" @click=\"laosStartTap\">{{laosText == '' ? '老师' : laosText}}<text></text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"lsxq_head_r\"><text></text><image src=\"/static/images/icon36.png\" @click=\"searchToggle = true\"></image></view>\r\n\t\t\t\r\n\t\t\t<view class=\"les_search\" v-if=\"searchToggle\">\r\n\t\t\t\t<view class=\"les_search_l\"><image src=\"/static/images/search.png\" @click=\"searchTap(keywords)\"></image><input type=\"text\" placeholder-style=\"color:#999999\" placeholder=\"请输入课程名称\" v-model=\"keywords\" confirm-type=\"search\" @confirm=\"searchTap(keywords)\" /></view>\r\n\t\t\t\t<view class=\"les_search_r\" @click=\"searchToggle = false\">清除</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t \r\n\t\t<view class=\"gg_rgba\" v-if=\"jbToggle || wuzToggle || laosToggle\" @click=\"gbTcTap\"></view>\r\n\t\t<!-- 级别 go -->\r\n\t\t<view class=\"teaxzTanc\" v-if=\"jbToggle\">\r\n\t\t\t<view class=\"teaxzTanc_t\">\r\n\t\t\t\t<view v-for=\"(item,index) in jibLists\" :key=\"index\" :class=\"jibIndex == index ? 'teaxzTanc_t_ac' : ''\" @click=\"jibTap(index)\">{{item.name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"teaxzTanc_b\"><view @click=\"jibReact\">重置</view><text @click=\"jibSubTap\">提交</text></view>\r\n\t\t</view>\r\n\t\t<!-- 级别 end -->\r\n\t\t<!-- 舞种 go -->\r\n\t\t<view class=\"teaxzTanc\" v-if=\"wuzToggle\">\r\n\t\t\t<view class=\"teaxzTanc_t\">\r\n\t\t\t\t<view v-for=\"(item,index) in wuzLists\" :key=\"index\" :class=\"wuzIndex == index ? 'teaxzTanc_t_ac' : ''\" @click=\"wuzTap(index)\">{{item.name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"teaxzTanc_b\"><view @click=\"wuzReact\">重置</view><text @click=\"wuzSubTap\">提交</text></view>\r\n\t\t</view>\r\n\t\t<!-- 舞种 end -->\r\n\t\t<!-- 老师 go -->\r\n\t\t<view class=\"teaxzTanc\" v-if=\"laosToggle\">\r\n\t\t\t<view class=\"teaxzTanc_t\">\r\n\t\t\t\t<view v-for=\"(item,index) in laosLists\" :key=\"index\" :class=\"laosIndex == index ? 'teaxzTanc_t_ac' : ''\" @click=\"laosTap(index)\">{{item.name}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"teaxzTanc_b\"><view @click=\"laosReact\">重置</view><text @click=\"laosSubTap\">提交</text></view>\r\n\t\t</view>\r\n\t\t<!-- 老师 end -->\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"tea_ban\">\r\n\t\t\t<view class=\"tea_ban_a\">{{teacherList[uswiperIndex].name}}</view>\r\n\t\t\t<!-- <view class=\"tea_ban_b\">\r\n\t\t\t\t<view class=\"tea_ban_b_t\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"banner\">\r\n\t\t\t\t\t\t<swiper class=\"banner-container\" circular=\"true\" interval=\"3000\" \r\n\t\t\t\t\t\t\tduration=\"1000\"  previous-margin=\"200rpx\" next-margin=\"200rpx\" @change=\"cIndex\" >\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in teachBan\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<swiper-item>{{item.name}}\r\n\t\t\t\t\t\t\t\t\t<image class=\"slide-image\" :class=\"[ currentIndex === index ? 'active':'' ]\" src=\"/static/images/icon23.jpg\" ></image>\r\n\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tea_ban_b_yd\"></view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"tea_ban_uswiper\">\r\n\t\t\t\t<u-swiper :list=\"teacherList\" mode=\"none\" :autoplay=\"false\" :current=\"uswiperIndex\" :effect3d=\"true\" :effect3d-previous-margin=\"200\" @change=\"changeSwiper\"></u-swiper>\r\n\t\t\t</view>\r\n\t\t\t<image src=\"/static/images/icon47.png\" class=\"tea_ban_yd\"></image>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ord_nav tea_one\">\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 0 ? 'ord_nav_li_ac' : ''\" @click=\"navTap(0)\"><view><text>任课详情</text><text></text></view></view>\r\n\t\t\t<view class=\"ord_nav_li\" :class=\"type == 1 ? 'ord_nav_li_ac' : ''\" @click=\"navTap(1)\"><view><text>老师评价</text><text></text></view></view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kcxq_video\" :class=\"speedState ? 'qpvideo' : ''\" v-if=\"type == 0 && teacherList[uswiperIndex].masterpiece != ''\">\r\n\t\t\t<video :src=\"teacherList[uswiperIndex].isoss ? teacherList[uswiperIndex].masterpiece : imgbaseUrl + teacherList[uswiperIndex].masterpiece\" controls id=\"videoId\" @fullscreenchange=\"handleFullScreen\" @controlstoggle=\"handleControlstoggle\">\r\n\t\t\t\t<!-- 倍速按钮 -->\r\n\t\t\t\t<cover-view v-show=\"controlsToggle\" class=\"speed\">\r\n\t\t\t\t\t<!-- <cover-view @click=\"speedNum=true\" class=\"doubleSpeed\">倍速</cover-view> -->\r\n\t\t\t\t\t<cover-view @click=\"speedTap\" class=\"doubleSpeed\">倍速</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t\t<!-- 倍速面板 -->\r\n\t\t\t\t<cover-view class=\"speedNumBox\" v-if=\"speedNum\">\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(0.5)\" :class=\"0.5 == speedRate ? 'activeClass' :'' \">0.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(0.8)\" :class=\"0.8 == speedRate ? 'activeClass' :'' \">0.8倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1)\" :class=\"1 == speedRate ? 'activeClass' :'' \">1倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1.25)\" :class=\"1.25 == speedRate ? 'activeClass' :'' \">1.25倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1.5)\" :class=\"1.5 == speedRate ? 'activeClass' :'' \">1.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(2)\" :class=\"2 == speedRate ? 'activeClass' :'' \">2倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"speedNum = false\">取消</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t</video>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kcxq_one\">\r\n\t\t\t<view class=\"kcxq_one_b\" style=\"margin-top:0;\">\r\n\t\t\t\t<image src=\"/static/images/toux.png\" class=\"kcxq_one_b_l\"></image>\r\n\t\t\t\t<view class=\"kcxq_one_b_r\">\r\n\t\t\t\t\t<view class=\"kcxq_one_b_r_l\"><view>{{teacherList[uswiperIndex].name}}</view><text v-if=\"teacherList[uswiperIndex].work_year*1 > 0\">{{teacherList[uswiperIndex].work_year}}年经验</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"kcxq_one_c\">\r\n\t\t\t\t<view>擅长舞种：{{teacherList[uswiperIndex].skilled_dance}}</view>\r\n\t\t\t\t<view>课程级别：{{teacherList[uswiperIndex].level_name}}</view>\r\n\t\t\t\t<view v-if=\"teacherList[uswiperIndex].section_number*1 > 0\">最近任课：{{teacherList[uswiperIndex].day}}天{{teacherList[uswiperIndex].section_number}}节</view>\r\n\t\t\t\t<view v-else>最近暂无任课</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"lspjCon\" v-if=\"type == 1\">\r\n\t\t\t<view class=\"lspjCon_t\">\r\n\t\t\t\t<view>老师评分<text>{{teacherList[uswiperIndex].score}}</text></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"lspjCon_b\">\r\n\t\t\t\t<text v-for=\"(item,index) in teacherList[uswiperIndex].evaluate\" :key=\"index\">{{item}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"rlxz_con\" v-if=\"type == 0 && array_md_cunc.length > 0\">\r\n\t\t\t<view class=\"rlxz_con_l\">\r\n\t\t\t\t<scroll-view scroll-x=\"true\" :scroll-left=\"scrollLeft\" @scroll=\"scrollJt\">\r\n\t\t\t\t\t<view class=\"rlxz_con_l_li\" :class=\"sjsxIndex == index ? 'rlxz_con_l_li_ac' : ''\" v-for=\"(item,index) in sjsxLists\" :key=\"index\" @click=\"sjsxTap(index,item)\">\r\n\t\t\t\t\t\t<view>{{item.week}}</view><view>{{item.day}}</view><text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rlxz_con_r\">\r\n\t\t\t\t<image src=\"/static/images/icon53.png\"></image>\r\n\t\t\t\t<picker mode=\"date\" :value=\"date_sx\" :start=\"startDate\" :end=\"endDate\" @change=\"bindDateChange_sx\">\r\n\t\t\t\t\t<view class=\"uni-input\">{{date_sx}}</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"md_xz md_xzCon\" v-if=\"type == 0 && array_md_cunc.length > 0\">\r\n\t\t\t<image src=\"/static/images/icon52-3.png\" class=\"md_xz_bj\"></image>\r\n\t\t\t<view class=\"md_xz_title\">{{array_md[index_md]}}</view>\r\n\t\t\t<image src=\"/static/images/icon52-4.png\" class=\"md_xz_xt\"></image>\r\n\t\t\t<picker @change=\"bindPickerChange_md\" :value=\"index_md\" :range=\"array_md\">\r\n\t\t\t\t<view class=\"uni-input\">{{array_md[index_md]}}</view>\r\n\t\t\t</picker>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<!-- <view class=\"teaCon\" v-if=\"type == 0\">\r\n\t\t\t<view class=\"teaCon_li\" v-for=\"(item,index) in 4\" :key=\"index\">\r\n\t\t\t\t<view class=\"teaCon_li_a\">拉丁舞练习</view>\r\n\t\t\t\t<view class=\"teaCon_li_b\">\r\n\t\t\t\t\t<image src=\"/static/images/icon23.jpg\" mode=\"aspectFill\" class=\"teaCon_li_b_l\"></image>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_c\">\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_a\">16:00-17:00</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_b\">上课老师：LINDA</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_c\"><text>入门</text><text>拉丁舞</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" @click=\"navTo('/pages/Schedule/Schedulexq')\">预约</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"teaCon_li_c\">\r\n\t\t\t\t\t<view class=\"teaCon_li_c_l\">\r\n\t\t\t\t\t\t<image src=\"/static/images/toux.png\" v-for=\"(item,index) in 6\" :key=\"index\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_c_r\">已预约：<text>23</text>人;<text>3</text>人在等位</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"teaCon\" v-if=\"type == 0\">\r\n\t\t\t<view class=\"teaCon_li\" v-for=\"(item,index) in storeCourseLists\" :key=\"index\" @click=\"storesxqTap(item)\">\r\n\t\t\t\t<view class=\"teaCon_li_a\">{{item.course.name}}</view>\r\n\t\t\t\t<view class=\"teaCon_li_b\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + item.teacher.image\" mode=\"aspectFill\" class=\"teaCon_li_b_l\"></image>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_c\">\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_a\">{{item.start_time}}-{{item.end_time}}</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_b\">上课老师：{{item.teacher.name}}</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_b\" v-if=\"item.frequency*1 > 0\">次卡消耗：{{item.frequency*1}}次</view>\r\n\t\t\t\t\t\t<view class=\"teaCon_li_b_c_c\"><text v-if=\"item.level_name\">{{item.level_name}}</text><text>{{item.dance_name}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-if=\"item.status == 1\" @click.stop>待开课</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 2\" @click.stop>授课中</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 3\" @click.stop>已完成</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 4\" @click.stop>等位中</view>\r\n\t\t\t\t\t<!-- <view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 6\" @click.stop>未开始预约</view> -->\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r yysj\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 6\" @click.stop><text>{{item.start_reservation}}</text><text>开始预约</text></view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.status == 7\" @click.stop>截止预约</view>\r\n\t\t\t\t\t<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" style=\"background:#BEBEBE\" v-else-if=\"item.equivalent*1 == 0 && (item.appointment_number*1 >= item.maximum_reservation*1)\" @click.stop=\"kqhyts\">预约</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" :style=\"item.member == 0 ? 'background:#BEBEBE' : ''\" v-else-if=\"item.member == 0\" @click.stop=\"ljtkToggle = true\">预约</view>\r\n\t\t\t\t\t<!-- 开启等位 -->\r\n\t\t\t\t\t<view class=\"teaCon_li_b_r\" v-else @click.stop=\"yypdTo(item,'/pages/Schedule/Schedulexq?id' + item.id)\">{{item.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"teaCon_li_c\">\r\n\t\t\t\t\t<view class=\"teaCon_li_c_l\">\r\n\t\t\t\t\t\t<!-- /static/images/toux.png -->\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.avatar\" v-for=\"(item,index) in item.appointment_people\" :key=\"index\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teaCon_li_c_r\">已预约：<text>{{item.appointment_number}}</text>人;<template v-if=\"item.waiting_number*1 > 0\"><text>{{item.waiting_number}}</text>人在等位</template></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"gg_zwsj\" style=\"margin-bottom:60rpx;\" v-if=\"storeCourseLists.length == 0 && type == 0\">\r\n\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<text>暂无课程</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 提示预约弹窗 go -->\r\n\t\t<view class=\"yytnCon\" v-if=\"ljtkToggle\"><view class=\"yytnCon_n\"><image :src=\"imgbaseUrlOss + '/userreport/icon55.png'\"></image><text @click=\"ljktTap\"></text></view><image src=\"/static/images/icon56.png\" @click=\"ljtkToggle = false\"></image></view>\r\n\t\t<!-- 提示预约弹窗 end -->\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tlscxCategoryApi,\r\n\tTeachersIntroductionApi,\r\n\tsearchStoreApi,\r\n\tstoreListsApi,\r\n\tstoreCourseApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\tconst currentDate = this.getDate({\r\n\t\t\tformat: true\r\n\t\t})\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tloding:false,\r\n\t\t\tjibLists:[],\r\n\t\t\tjibIndex:-1,\r\n\t\t\tjibText:'',\r\n\t\t\tjbToggle:false,\r\n\t\t\timgbaseUrlOss:'',\r\n\t\t\twuzLists:[],\r\n\t\t\twuzIndex:-1,\r\n\t\t\twuzText:'',\r\n\t\t\twuzToggle:false,\r\n\t\t\t\r\n\t\t\tlaosLists:[],\r\n\t\t\tlaosIndex:-1,\r\n\t\t\tlaosText:'',\r\n\t\t\tlaosToggle:false,\r\n\t\t\t\r\n\t\t\tkeywords:'',\r\n\t\t\tkeywords_cunc:'',\r\n\t\t\tsearchToggle:false,//搜索是否显示\r\n\t\t\t\r\n\t\t\tcurrentIndex:3,\r\n\t\t\ttype:0,\r\n\t\t\tsjsxLists:[],//时间筛选\r\n\t\t\tsjsxIndex:0,\r\n\t\t\tscrollLeft:0,\r\n\t\t\tdate_sx: currentDate,\r\n\t\t\tarray_md: [],\r\n\t\t\tarray_md_cunc: [],\r\n\t\t\tindex_md: 0,\r\n\t\t\tdateText:'',//时间\r\n\t\t\t\r\n\t\t\tuswiperIndex:0,\r\n\t\t\tteacherList:[],\r\n\t\t\tljtkToggle:false,\r\n\t\t\t\r\n\t\t\tstoreCourseLists:[],\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\t\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tkcxqTeachId:0,//课程详情\r\n\t\t\t\r\n\t\t\tcontrolsToggle:false,//是否显示状态\r\n\t\t\tspeedState:false,//是否进入全屏\r\n\t\t\tspeedNum:false,//是否显示倍速\r\n\t\t\tspeedRate:0,//当前倍数\r\n\t\t\t\r\n\t\t\tqjbutton:'#131315',\r\n\t\t\tqjziti:'#F8F8FA'\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrlOss = this.$baseUrlOss;\r\n\t\tthis.isLogined = uni.getStorageSync('token') ? true : false;\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.qjziti = uni.getStorageSync('storeInfo').written_words\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.dateText = this.getFormattedCurrentDate();\r\n\t\tthis.dateDatasx(this.getFormattedCurrentDate());//获取最近15日\r\n\t\tthis.kcxqTeachId = option.id ? option.id : 0;// option.id 是从课程详情进入\r\n\t\tthis.teachersData();//老师列表>通过老师查找门店>在通过门店和老师共同查找课程\r\n\t\tthis.categoryData();//老师分类\r\n\t},\r\n\tcomputed: {\r\n\t\tstartDate() {\r\n\t\t\treturn this.getDate('start');\r\n\t\t},\r\n\t\tendDate() {\r\n\t\t\treturn this.getDate('end');\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t//点击倍数\r\n\t\tspeedTap(){\r\n\t\t\tthis.speedNum = true;\r\n\t\t},\r\n\t\t//监听进入全屏 和 退出全屏\r\n\t\thandleFullScreen(e){\r\n\t\t\t// console.log('监听进入全屏1',e);\r\n\t\t\t// console.log('监听进入全屏2',e.detail.fullScreen);\r\n\t\t\tthis.speedState = e.detail.fullScreen;\r\n\t\t\tthis.speedNum = false;\r\n\t\t},\r\n\t\t//2.控件（播放/暂停按钮、播放进度、时间）是显示状态\r\n\t\thandleControlstoggle(e){\r\n\t\t\t// console.log(e.detail.show);\r\n\t\t\tthis.controlsToggle = e.detail.show\r\n\t\t},\r\n\t\t//设置倍速速度\r\n\t\thandleSetSpeedRate(rate){\r\n\t\t\t let videoContext = uni.createVideoContext(\"videoId\");\r\n\t\t\t videoContext.playbackRate(rate);\r\n\t\t\t this.speedRate = rate;\r\n\t\t\t this.speedNum = false;\r\n\t\t\t uni.showToast({\r\n\t\t\t \ticon: 'none',\r\n\t\t\t \ttitle: '已切换至' + rate + '倍数',\r\n\t\t\t\tduration:2000\r\n\t\t\t });\r\n\t\t},\r\n\t\t//swiper\r\n\t\tchangeSwiper(index){\r\n\t\t\tconsole.log(index,'index')\r\n\t\t\tthis.uswiperIndex = index;\r\n\t\t\tthis.index_md = 0;\r\n\t\t\tthis.searchStore();//通过老师搜索可选择门店\r\n\t\t},\r\n\t\t//通过老师搜索可选择门店\r\n\t\tsearchStore(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tsearchStoreApi({\r\n\t\t\t// storeListsApi({\r\n\t\t\t\tteacher_id:that.teacherList[that.uswiperIndex].id\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('通过老师搜索可选择门店',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tvar obj = res.data;\r\n\t\t\t\t\tvar array_md = [];\r\n\t\t\t\t\tfor(var i=0;i<obj.length;i++){\r\n\t\t\t\t\t\tarray_md.push(obj[i].name)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.array_md = array_md;\r\n\t\t\t\t\tthat.array_md_cunc = obj;\r\n\t\t\t\t\tif(res.data.length == 0){\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.storeCourseLists = [];//该老师在所有门店下均无课程\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\t\tthat.storeCourseLists = [];//门店课程\r\n\t\t\t\t\t\tthat.storeCourseData();//门店课程\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//门店课程\r\n\t\t/*storeCourseData(){\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tstoreCourseApi({\r\n\t\t\t\tid:that.array_md_cunc.length == 0 ? 0 : that.array_md_cunc[that.index_md].id,\r\n\t\t\t\tlevel_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,\r\n\t\t\t\tdance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,\r\n\t\t\t\tteacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,\r\n\t\t\t\t// date:'2024-10-30',\r\n\t\t\t\tdate:that.dateText,\r\n\t\t\t\tname:that.keywords_cunc,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店课程',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.storeCourseLists = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},*/\r\n\t\t//门店课程\r\n\t\tstoreCourseData(){\r\n\t\t\t\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tstoreCourseApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tid:that.array_md_cunc.length == 0 ? 0 : that.array_md_cunc[that.index_md].id,\r\n\t\t\t\tlevel_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,\r\n\t\t\t\tdance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,\r\n\t\t\t\tteacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,\r\n\t\t\t\t// date:'2024-10-30',\r\n\t\t\t\tdate:that.dateText,\r\n\t\t\t\tname:that.keywords_cunc,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('门店课程',res)\r\n\t\t\t\t/*if (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.storeCourseLists = res.data.data;\r\n\t\t\t\t}*/\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tvar obj = res.data.data;\r\n\t\t\t\t\tthat.storeCourseLists = that.storeCourseLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.storeCourseLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.storeCourseLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t//console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t    this.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\t//详情跳转\r\n\t\tstoresxqTap(item){\r\n\t\t\tconsole.log(this.isLogined,'this.isLogined')\r\n\t\t\tif(!this.isLogined){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 未开启会员并且后端设置了必须开通会员方可查看详情\r\n\t\t\tif(item.course.view_type*1 == 0 && item.member == 0){\r\n\t\t\t\tthis.ljtkToggle = true\r\n\t\t\t}else{\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t// url:'/pages/Schedule/Schedulexq?id=' + item.id\r\n\t\t\t\t\turl:'/pages/mine/myCourse/myCoursexq?id=' + item.id\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t//预约约课/排队\r\n\t\tyypdTo(item){\r\n\t\t\tif(!this.isLogined){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/Schedule/confirmOrder?id=' + item.id  + '&storeid=' + this.array_md_cunc[this.index_md].id\r\n\t\t\t})\r\n\t\t},\r\n\t\t//预约爆满\r\n\t\tkqhyts(){\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '预约课程已满',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1000\r\n\t\t\t})\r\n\t\t},\r\n\t\t//立即开通会员\r\n\t\tljktTap(){\r\n\t\t\tthis.ljtkToggle = false;\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl:'/pages/buy/buy'\r\n\t\t\t})\r\n\t\t},\r\n\t\t//搜索跳转\r\n\t\tsearchTap(keywords){\r\n\t\t\tthis.keywords_cunc = this.keywords;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\t/*uni.navigateTo({\r\n\t\t\t\turl:'/pages/Schedule/searchResults?keywords=' + keywords\r\n\t\t\t})*/\r\n\t\t},\r\n\t\t//选择门店\r\n\t\tbindPickerChange_md: function(e) {\r\n\t\t\tconsole.log('picker发送选择改变，携带值为', e.detail.value)\r\n\t\t\tthis.index_md = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\t//日期转化\r\n\t\tdateDatasx(date){\r\n\t\t\tlet startDate = date;\r\n\t\t\tlet daysToAdd = 15;\r\n\t\t\tthis.sjsxLists = this.getDateArrayWithWeekday(startDate, daysToAdd);\r\n\t\t\tthis.scrollLeft = 1;\r\n\t\t},\r\n\t\t//指定日期往后推迟xx日\r\n\t\tgetDateArrayWithWeekday(startDateStr, daysToAdd) {\r\n\t\t      let dateArray = [];\r\n\t\t      let weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];\r\n\t\t      let startDate = new Date(startDateStr);\r\n\t\t      for (let i = 0; i < daysToAdd; i++) {\r\n\t\t        let newDate = new Date(startDate);\r\n\t\t        newDate.setDate(startDate.getDate() + i);\r\n\t\t        let year = newDate.getFullYear();\r\n\t\t        let month = (newDate.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t        let day = newDate.getDate().toString().padStart(2, '0');\r\n\t\t        let weekdayIndex = newDate.getDay();\r\n\t\t        let weekday = weekdays[weekdayIndex];\r\n\t\t        let formattedDate = `${year}-${month}-${day}（${weekday}）`;\r\n\t\t        dateArray.push({week:weekday,day:`${month}-${day}`,date:`${year}-${month}-${day}`});\r\n\t\t      }\r\n\t\t      return dateArray;\r\n\t\t},\r\n\t\t//获取当前日期\r\n\t\tgetFormattedCurrentDate() {\r\n\t\t  let currentDate = new Date();\r\n\t\t  let year = currentDate.getFullYear();\r\n\t\t  let month = (currentDate.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t  let day = currentDate.getDate().toString().padStart(2, '0');\r\n\t\t  return `${year}-${month}-${day}`;\r\n\t\t},\r\n\t\tcIndex(e) {\r\n\t\t\tthis.currentIndex = e.detail.current;\t\t\t\r\n\t\t},\r\n\t\tscrollJt(e){\r\n\t\t},\r\n\t\tbindDateChange_sx: function(e) {\r\n\t\t\tvar that = this;\r\n\t\t\tthis.date_sx = e.detail.value;\r\n\t\t\tthis.dateDatasx(this.date_sx);\r\n\t\t\tthis.sjsxIndex = 0;\r\n\t\t\tthat.dateText = e.detail.value;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\tsetTimeout(function(){\r\n\t\t\t\tthat.scrollLeft = 0;\r\n\t\t\t},0)\r\n\t\t\t\r\n\t\t},\r\n\t\tgetDate(type) {\r\n\t\t\tconst date = new Date();\r\n\t\t\tlet year = date.getFullYear();\r\n\t\t\tlet month = date.getMonth() + 1;\r\n\t\t\tlet day = date.getDate();\r\n\r\n\t\t\tif (type === 'start') {\r\n\t\t\t\tyear = year;\r\n\t\t\t} else if (type === 'end') {\r\n\t\t\t\tyear = year + 1;\r\n\t\t\t}\r\n\t\t\tmonth = month > 9 ? month : '0' + month;\r\n\t\t\tday = day > 9 ? day : '0' + day;\r\n\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t},\r\n\t\t//时间筛选选择\r\n\t\tsjsxTap(index,item){\r\n\t\t\tthis.sjsxIndex = index;\r\n\t\t\tthis.dateText = item.date;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\tthis.storeCourseData();//门店课程\r\n\t\t},\r\n\t\tnavTap(index){\r\n\t\t\tthis.type = index;\r\n\t\t},\r\n\t\t//老师列表\r\n\t\tteachersData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tTeachersIntroductionApi({\r\n\t\t\t\tpage:1,\r\n\t\t\t\tlimit:99999,\r\n\t\t\t\t// level_id:that.jibIndex == -1 ? '' : that.jibLists[that.jibIndex].id,\r\n\t\t\t\t// dance_id:that.wuzIndex == -1 ? '' : that.wuzLists[that.wuzIndex].id,\r\n\t\t\t\t// teacher_id:that.laosIndex == -1 ? '' : that.laosLists[that.laosIndex].id,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('老师列表',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tfor(var i=0;i<res.data.data.length;i++){\r\n\t\t\t\t\t\tres.data.data[i].image = that.imgbaseUrl + res.data.data[i].image;\r\n\t\t\t\t\t\t// res.data.data[1].masterpiece = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734312077132_ces.mp4'\r\n\t\t\t\t\t\tif(res.data.data[i].masterpiece){\r\n\t\t\t\t\t\t\tres.data.data[i].isoss = res.data.data[i].masterpiece.substring(0,5) == 'https' ? true : false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t//是从课程详情进入找到指定老师\r\n\t\t\t\t\t\tif(that.kcxqTeachId != 0){\r\n\t\t\t\t\t\t\tif(res.data.data[i].id == that.kcxqTeachId){\r\n\t\t\t\t\t\t\t\tthat.uswiperIndex = i\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// console.log(that.uswiperIndex,'that.uswiperIndex')\r\n\t\t\t\t\tthat.teacherList = res.data.data;\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.searchStore();//通过老师搜索可选择门店\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//老师分类\r\n\t\tcategoryData(){\r\n\t\t\tlet that = this;\r\n\t\t\tlscxCategoryApi({}).then(res => {\r\n\t\t\t\tconsole.log('老师分类',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.jibLists = res.data.level;\r\n\t\t\t\t\tthat.wuzLists = res.data.dance;\r\n\t\t\t\t\tthat.laosLists = res.data.teacher;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//关闭所有弹窗\r\n\t\tgbTcTap(){\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t},\r\n\t\t//级别弹窗开启\r\n\t\tjbStartTap(){\r\n\t\t\tthis.jbToggle = !this.jbToggle;\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t},\r\n\t\t//级别选择\r\n\t\tjibTap(index){\r\n\t\t\tthis.jibIndex = index;\r\n\t\t},\r\n\t\t//级别提交\r\n\t\tjibSubTap(){\r\n\t\t\tif(this.jibIndex == -1){\r\n\t\t\t\tthis.jibText = ''\r\n\t\t\t}else{\r\n\t\t\t\tthis.jibText = this.jibLists[this.jibIndex].name\r\n\t\t\t}\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\t// this.storeCourseData();//门店课程\r\n\t\t\t\r\n\t\t\tif(this.array_md_cunc == 0){\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.storeCourseLists = [];//该老师在所有门店下均无课程\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t},500)\r\n\t\t\t}else{\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\t}\r\n\t\t},\r\n\t\t//级别重置\r\n\t\tjibReact(){\r\n\t\t\tthis.jibIndex = -1;\r\n\t\t},\r\n\t\t//舞种弹窗开启\r\n\t\twuzStartTap(){\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.wuzToggle = !this.wuzToggle;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t},\r\n\t\t//舞种选择\r\n\t\twuzTap(index){\r\n\t\t\tthis.wuzIndex = index;\r\n\t\t},\r\n\t\t//舞种提交\r\n\t\twuzSubTap(){\r\n\t\t\tif(this.wuzIndex == -1){\r\n\t\t\t\tthis.wuzText = ''\r\n\t\t\t}else{\r\n\t\t\t\tthis.wuzText = this.wuzLists[this.wuzIndex].name\r\n\t\t\t}\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tif(this.array_md_cunc == 0){\r\n\t\t\t\tthis.storeCourseLists = [];//该老师在所有门店下均无课程\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t},500)\r\n\t\t\t}else{\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\t}\r\n\t\t},\r\n\t\t//舞种重置\r\n\t\twuzReact(){\r\n\t\t\tthis.wuzIndex = -1;\r\n\t\t},\r\n\t\t//老师弹窗开启\r\n\t\tlaosStartTap(){\t\t\t\r\n\t\t\tthis.jbToggle = false;\r\n\t\t\tthis.wuzToggle = false;\r\n\t\t\tthis.laosToggle = !this.laosToggle;\r\n\t\t},\r\n\t\t//老师选择\r\n\t\tlaosTap(index){\r\n\t\t\tthis.laosIndex = index;\r\n\t\t},\r\n\t\t//老师提交\r\n\t\tlaosSubTap(){\r\n\t\t\tconsole.log(this.laosIndex,'this.laosIndex')\r\n\t\t\tif(this.laosIndex == -1){\r\n\t\t\t\tthis.laosText = ''\r\n\t\t\t}else{\r\n\t\t\t\tthis.laosText = this.laosLists[this.laosIndex].name\r\n\t\t\t}\r\n\t\t\tthis.uswiperIndex = this.laosIndex == -1 ? 0 : this.laosIndex;\r\n\t\t\tthis.laosToggle = false;\r\n\t\t\tif(this.array_md_cunc == 0){\r\n\t\t\t\tthis.storeCourseLists = [];//该老师在所有门店下均无课程\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t},500)\r\n\t\t\t}else{\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.storeCourseLists = [];//门店课程\r\n\t\t\t\tthis.storeCourseData();//门店课程\r\n\t\t\t}\r\n\t\t},\r\n\t\t//老师重置\r\n\t\tlaosReact(){\r\n\t\t\tthis.laosIndex = -1;\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.teacherDetail{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./teacherDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752117066870\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}