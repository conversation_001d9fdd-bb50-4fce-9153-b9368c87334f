{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/login/xieYi.vue?abad", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/login/xieYi.vue?6d9f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/login/xieYi.vue?ee1a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/login/xieYi.vue?333d", "uni-app:///pages/login/xieYi.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/login/xieYi.vue?4a29", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/login/xieYi.vue?cb0c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "<PERSON><PERSON>", "onLoad", "uni", "title", "methods", "xieYiFun"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqtB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACSzuB;AAIA;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAEA;IACA;MACA;MACA;QACAC;UACAC;QACA;MACA;MACA;QACAD;UACAC;QACA;MACA;MACA;QACAD;UACAC;QACA;MACA;MACA;QACAD;UACAC;QACA;MACA;MACA;QACAD;UACAC;QACA;MACA;MACA;QACAD;UACAC;QACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACAH;QACAC;MACA;MACA;QACA;UACAJ;QACA;UACAG;UACA;QACA;MACA;QACA;UACAH;QACA;UACAG;UACA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAAshC,CAAgB,29BAAG,EAAC,C;;;;;;;;;;;ACA1iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/xieYi.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/xieYi.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./xieYi.vue?vue&type=template&id=b51bee96&\"\nvar renderjs\nimport script from \"./xieYi.vue?vue&type=script&lang=js&\"\nexport * from \"./xieYi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./xieYi.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/xieYi.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieYi.vue?vue&type=template&id=b51bee96&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieYi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieYi.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"\">\r\n\t\t\t<rich-text :nodes=\"Cont\"></rich-text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tXieYi,\r\n\tcardsApi\r\n} from '@/config/http.achieve.js' \r\nimport util from '@/utils/utils.js';\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttype: '',\r\n\t\t\tCont: \"\"\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t\r\n\t\t// let item = this.$Route.query\r\n\t\tif (options.type) {\r\n\t\t\tthis.type = options.type\r\n\t\t\tif (this.type == 1) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"用户注册购卡协议\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.type == 2) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"隐私政策\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.type == 3) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"用户授权协议\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.type == 4) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"平台服务协议\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.type == 5) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"活动规则\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (this.type == 99) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: \"会员服务协议\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.xieYiFun()\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\txieYiFun() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tif(this.type == 99){\r\n\t\t\t\tcardsApi({\r\n\t\t\t\t\ttype:1\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.Cont = util.formatRichText(res.data.member_service_agreement)\r\n\t\t\t\t})\r\n\t\t\t}else{\r\n\t\t\t\tXieYi({\r\n\t\t\t\t\ttype:this.type\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.Cont = util.formatRichText(res.data)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #fff;\r\n\r\n\t\tborder-top: 2rpx solid #f8f8f8;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 39rpx 32rpx 0;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieYi.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xieYi.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752117063363\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}