{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/add_address.vue?1148", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/add_address.vue?ef13", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/add_address.vue?ef1d", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/add_address.vue?5ce2", "uni-app:///pages/mine/add_address.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/add_address.vue?f9ad", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/add_address.vue?ff6d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "switchVal", "showPopup", "name", "sex", "address", "address_detail", "lat", "lng", "mobile", "Edit_id", "editType", "loding", "qj<PERSON>ton", "onLoad", "uni", "title", "console", "setTimeout", "that", "methods", "choose", "success", "change", "submit", "icon", "duration", "phone", "gender", "area", "detail", "is_default", "addr_id", "<PERSON><PERSON><PERSON><PERSON>", "content", "id", "confirmDel", "addressId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA2tB,CAAgB,0sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC4H/uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IACA;IACAC;IACA;IACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAF;QACAC;MACA;MACAE;QACAH;QACAI;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAN;QACAO;UACAH;UACAA;UACAA;UACAA;UACAF;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAM;MACAN;MACA;IACA;IAEAO;MAAA;MACA;QACA;UAAAR;QAAA;QACA;MACA;MACA;QACA;UAAAA;QAAA;QACA;MACA;MACA;QACA;UAAAA;QAAA;QACA;MACA;MACA;MACA;QACA;UAAAA;QAAA;QACA;MACA;MACA;QACAD;UACAU;UACAT;UACAU;QACA;QACA;MACA;MAEAX;QACAC;MACA;MACA;QACAb;QACAwB;QACAC;QACAC;QACAC;QACA;QACA;QACAC;QACAC;MACA;MACA;QACA;MACA;MACA;QACA;UACAjB;UACA;YAAAC;UAAA;UACAE;YACAH;UACA;QACA;MACA;IACA;IACA;IACAkB;MACA;MAEAlB;QACAC;QACAkB;QACAZ;UAAA;UACA;YAEAP;cACAC;YACA;YACA;cACAmB;YACA;cACA;gBACApB;gBACA;kBACA;oBACAA;kBACA;gBACA;gBACAI;kBAAAH;gBAAA;gBACAE;kBACAH;gBACA;cACA;YACA;UAEA;YACAE;UACA;QACA;MACA;IAEA;IACAmB;MAAA;MACA;QACAC;MACA;QACA;UACA;YAAArB;UAAA;UACAE;YACAH;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChTA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,6vCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/add_address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/add_address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add_address.vue?vue&type=template&id=324373da&\"\nvar renderjs\nimport script from \"./add_address.vue?vue&type=script&lang=js&\"\nexport * from \"./add_address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add_address.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/add_address.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=template&id=324373da&\"", "var components\ntry {\n  components = {\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-switch/u-switch\" */ \"@/components/uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.sex = 1\n    }\n    _vm.e1 = function ($event) {\n      _vm.sex = 2\n    }\n    _vm.e2 = function ($event) {\n      _vm.showPopup = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"loding\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t<view class=\"cont\">\r\n\t\t\t<view class=\"cont_row flex col-top\">\r\n\t\t\t\t<view class=\"cont_row_l\">\r\n\t\t\t\t\t收货人\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cont_row_r flex-1\">\r\n\t\t\t\t\t<input v-model=\"name\" maxlength=\"11\" type=\"text\" placeholder=\"收货人姓名\" />\r\n\t\t\t\t\t<view class=\"check_sex flex\">\r\n\t\t\t\t\t\t<view class=\"check_sex_li flex\" :class=\"sex == 1 ? 'check_sex_li_ac' : ''\" @click=\"sex = 1\">\r\n\t\t\t\t\t\t\t<image v-if=\"sex==1\" src=\"/static/images/dzxz-11.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t<image v-else src=\"/static/images/dzxz.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t男士\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"check_sex_li flex\" :class=\"sex == 2 ? 'check_sex_li_ac' : ''\" @click=\"sex = 2\">\r\n\t\t\t\t\t\t\t<image v-if=\"sex==2\" src=\"/static/images/dzxz-11.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t<image v-else src=\"/static/images/dzxz.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t女士\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"cont_row flex col-top\" @click=\"choose\">\r\n\t\t\t\t<view class=\"cont_row_l\">\r\n\t\t\t\t\t选择地址\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cont_row_r flex-1 flex row-between\">\r\n\t\t\t\t\t<view class=\"select line-1\" :style=\"{color:address?'#333':'#999'}\" style=\"width: 450rpx;\">\r\n\t\t\t\t\t\t{{address?address:'选择详细地址'}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/images/index_help_more.png\" mode=\"scaleToFill\" class=\"select_img\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cont_row flex \">\r\n\t\t\t\t<view class=\"cont_row_l\">\r\n\t\t\t\t\t详细地址\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cont_row_r flex-1 flex row-between\">\r\n\t\t\t\t\t<textarea v-model=\"address_detail\" placeholder=\"请输入省市区县、乡镇\" auto-height placeholder-style=\"color:#999999;\"></textarea>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cont_row flex \">\r\n\t\t\t\t<view class=\"cont_row_l\">\r\n\t\t\t\t\t手机号\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cont_row_r flex-1 flex row-between\">\r\n\t\t\t\t\t<input v-model=\"mobile\" maxlength=\"11\" type=\"number\" placeholder=\"收货人手机号\" placeholder-class=\"color9\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<view class=\"default flex row-between\">\r\n\t\t\t<view class=\"default_t \">\r\n\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t设置为默认地址\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"default_d\">\r\n\t\t\t\t\t启动时将优先定位在默认地址，避免选错\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"\">\r\n\t\t\t\t<u-switch :active-color=\"qjbutton\" size=\"40\" v-model=\"switchVal\" @change=\"change\"></u-switch>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- <view class=\"foot\">\r\n\t\t\t<view v-if=\"!Edit_id\" class=\"add_addr btn\" style=\"margin: 0 auto;\" @click=\"submit()\">\r\n\t\t\t\t添加地址\r\n\t\t\t</view>\r\n\t\t\t<view class=\"edit_use flex row-between\" v-if=\"Edit_id\">\r\n\t\t\t\t<view class=\"btn\" @click=\"delAddress\">\r\n\t\t\t\t\t删除\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"submit\">\r\n\t\t\t\t\t保存\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t<view class=\"add_foo\" v-if=\"!Edit_id\"><view class=\"\" @click=\"submit\">添加地址</view></view>\r\n\t\t<view class=\"add_foo add_fooEdit\" v-if=\"Edit_id\">\r\n\t\t\t<view @click=\"delAddress\">\r\n\t\t\t\t删除\r\n\t\t\t</view>\r\n\t\t\t<view @click=\"submit\">\r\n\t\t\t\t保存\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 弹窗 -->\r\n\t\t<u-popup v-model=\"showPopup\" mode=\"center\" border-radius=\"20\">\r\n\t\t\t<view class=\"prompt\">\r\n\t\t\t\t<view class=\"prompt_t\">\r\n\t\t\t\t\t<view class=\"prompt_t_img\">\r\n\t\t\t\t\t\t<image src=\"/static/images/popup-icon.png\" mode=\"scaleToFill\"></image> \r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"prompt_t_text\">\r\n\t\t\t\t\t\t提示\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"prompt_c\">\r\n\t\t\t\t\t确定退出当前删除？\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"prompt_d\">\r\n\t\t\t\t\t<view class=\"prompt_d_l\" @click=\"showPopup=  false\">\r\n\t\t\t\t\t\t取消\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"prompt_d_r\" @click=\"confirmDel\">\r\n\t\t\t\t\t\t确定\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport{\r\n\t\taddrAdd,\r\n\t\taddrDel,\r\n\t\taddrEdit\r\n\t} from '@/config/http.achieve.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tswitchVal: false,\r\n\t\t\t\tshowPopup:false,\r\n\t\t\t\tname:'',\r\n\t\t\t\tsex:1,\r\n\t\t\t\taddress:'',\r\n\t\t\t\taddress_detail:'',\r\n\t\t\t\tlat:'',\r\n\t\t\t\tlng:'',\r\n\t\t\t\tmobile:'',\r\n\t\t\t\tEdit_id:'',\r\n\t\t\t\teditType:0,\r\n\t\t\t\tloding:false,\r\n\t\t\t\tqjbutton:'#131315',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle:opt.type == 0 ? '添加地址' : '编辑地址'\r\n\t\t\t})\r\n\t\t\tvar that = this;\r\n\t\t\tconsole.log(opt,'opt')\r\n\t\t\tthis.editType = opt.type\r\n\t\t\tif(opt.data){\r\n\t\t\t\tif(opt.data == 'undefined'){\r\n\t\t\t\t\tthis.loding = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tlet item = JSON.parse(opt.data)\r\n\t\t\t\tthis.Edit_id = item.id\r\n\t\t\t\tthis.name = item.name\r\n\t\t\t\tthis.sex =item.gender\r\n\t\t\t\tthis.address = item.area\r\n\t\t\t\tthis.address_detail = item.detail\r\n\t\t\t\tthis.lat = item.lat\r\n\t\t\t\tthis.lng = item.lng\r\n\t\t\t\tthis.mobile = item.phone\r\n\t\t\t\tthis.switchVal= item.is_default ==1?true:false;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle:'加载中'\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t},500)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tchoose() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.address_detail = res.address\r\n\t\t\t\t\t\tthat.address = res.name\r\n\t\t\t\t\t\tthat.lat = res.latitude\r\n\t\t\t\t\t\tthat.lng = res.longitude\r\n\t\t\t\t\t\tconsole.log('位置名称：' + res.name);\r\n\t\t\t\t\t\tconsole.log('详细地址：' + res.address);\r\n\t\t\t\t\t\tconsole.log('纬度：' + res.latitude);\r\n\t\t\t\t\t\tconsole.log('经度：' + res.longitude);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\tconsole.log('change', );\r\n\t\t\t\tthis.switchVal = e\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tsubmit(){\r\n\t\t\t\tif(this.name==''){\r\n\t\t\t\t\tthis.$toast({title:'请输入收货人姓名'})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif(this.address==''){\r\n\t\t\t\t\tthis.$toast({title:'请选择地址'})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif(this.address_detail==''){\r\n\t\t\t\t\tthis.$toast({title:'请输入详细地址'})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tvar reg = /^1[3456789]\\d{9}$/\r\n\t\t\t\tif(this.mobile==''){\r\n\t\t\t\t\tthis.$toast({title:'请输入手机号'})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (!reg.test(this.mobile)) {\r\n\t\t\t\t  uni.showToast({\r\n\t\t\t\t\t  icon:'none',\r\n\t\t\t\t\t  title: '请输入正确的手机号码',\r\n\t\t\t\t\t  duration: 2000\r\n\t\t\t\t  });\r\n\t\t\t\t  return false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle:'加载中'\r\n\t\t\t\t})\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tname:this.name,\r\n\t\t\t\t\tphone:this.mobile,\r\n\t\t\t\t\tgender:this.sex,\r\n\t\t\t\t\tarea:this.address,\r\n\t\t\t\t\tdetail:this.address_detail,\r\n\t\t\t\t\t//lat:this.lat,\r\n\t\t\t\t\t//lng:this.lng,\r\n\t\t\t\t\tis_default:String(this.switchVal==false?'0':'1'),\r\n\t\t\t\t\taddr_id:this.editType == 0 ? 0 : this.Edit_id\r\n\t\t\t\t}\r\n\t\t\t\tif(this.editType == 0){\r\n\t\t\t\t\tdelete data.addr_id\r\n\t\t\t\t}\r\n\t\t\t\taddrAdd(data).then(res=>{\r\n\t\t\t\t\tif(res.code == 1){\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$toast({title:this.editType == 0 ? '添加成功' : '编辑成功'})\r\n\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t},1500)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//删除收货地址\r\n\t\t\tdelAddress(id){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确定要删除该地址吗？',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t\t\t\t}); \r\n\t\t\t\t\t\t\taddrDel({\r\n\t\t\t\t\t\t\t\tid: that.Edit_id\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tif (uni.getStorageSync('diancan')) {\r\n\t\t\t\t\t\t\t\t\t\tif(uni.getStorageSync('diancan').addressId == this.nowId){\r\n\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('diancan')\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tthat.$toast({title:\"删除成功\"})\r\n\t\t\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tconfirmDel(){\r\n\t\t\t\taddrDel({\r\n\t\t\t\t\taddressId:this.Edit_id\r\n\t\t\t\t}).then(res=>{\r\n\t\t\t\t\tif(res.code == 1){\r\n\t\t\t\t\t\tthis.$toast({title:\"删除成功\"})\r\n\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t},500)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.cont {\r\n\t\tmargin: 22rpx auto 0;\r\n\t\twidth: 698rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\t\tpadding: 26rpx 26rpx 0;\r\n\r\n\t\t.cont_row {\r\n\t\t\tpadding-top: 26rpx;\r\n\t\t\tborder-bottom: 2rpx solid rgba(167, 167, 167, 0.2);\r\n\t\t\tpadding-bottom: 26rpx;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\t&:nth-child(1) {\r\n\t\t\t\tpadding-top: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.cont_row_l {\r\n\t\t\t\twidth: 144rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.cont_row_r {\r\n\r\n\t\t\t\tinput {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.color9 {\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttextarea {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.select {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.select_img {\r\n\t\t\t\t\twidth:32rpx;\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.check_sex {\r\n\t\t\t\t\tmargin-top: 26rpx;\r\n\r\n\t\t\t\t\t.check_sex_li {\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\t\tmargin-right: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.default {\r\n\t\tmargin: 30rpx auto 0;\r\n\t\twidth: 698rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 24rpx 24rpx 24rpx 24rpx;\r\n\t\tpadding: 26rpx;\r\n\r\n\t\t.default_t {\r\n\t\t\tview {\r\n\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.default_d {\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tline-height: 30rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// .default_d {\r\n\t\t// \tmargin-top: 8rpx;\r\n\t\t// \tfont-family: SourceHanSansCN;\r\n\t\t// \tfont-size: 24rpx;\r\n\t\t// \tcolor: #A3A3A3;\r\n\t\t// \tline-height: 28rpx;\r\n\t\t// }\r\n\t}\r\n\r\n\t.foot {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbottom: 68rpx;\r\n\r\n\t\t.btn {\r\n\r\n\t\t\twidth: 608rpx;\r\n\t\t\theight: 90rpx;\r\n\t\t\tborder-radius: 94rpx 94rpx 94rpx 94rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t}\r\n\r\n\t\t.edit_use {\r\n\t\t\tpadding: 0 26rpx;\r\n\r\n\t\t\t.btn {\r\n\t\t\t\t&:nth-child(1) {\r\n\t\t\t\t\twidth: 336rpx;\r\n\t\t\t\t\theight: 88rpx;\r\n\t\t\t\t\tborder-radius: 88rpx 88rpx 88rpx 88rpx;\r\n\t\t\t\t\tborder: 2rpx solid #09C867;\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #09C867;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:nth-child(2) {\r\n\t\t\t\t\twidth: 336rpx;\r\n\t\t\t\t\theight: 88rpx;\r\n\t\t\t\t\tborder-radius: 88rpx 88rpx 88rpx 88rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.qu_box{\r\n\t\twidth: 480rpx;\r\n\t\theight: 314rpx;\r\n\t\tpadding: 50rpx;\r\n\t\t.qu_box_title{\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 39rpx;\r\n\t\t}\r\n\t\t.qu_box_cont{\r\n\t\t\tmargin-top: 26rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 39rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t\t.qu_box_use{\r\n\t\t\tpadding: 0 26rpx;\r\n\t\t\tmargin-top: 50rpx;\r\n\t\t\t.qu_box_use_li{\r\n\t\t\t\t&:nth-child(1){\r\n\t\t\t\t\tbackground: #CBF1DE;\r\n\t\t\t\t}\r\n\t\t\t\twidth: 144rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tbackground: #09C867;\r\n\t\t\t\tborder-radius: 88rpx 88rpx 88rpx 88rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n\t\r\n\t.check_sex_li_ac{\r\n\t\tcolor:#333!important;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114330469\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}