{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/address.vue?01c0", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/address.vue?3189", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/address.vue?7924", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/address.vue?bcd9", "uni-app:///pages/mine/address.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/address.vue?53ff", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/address.vue?ae52"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "loding", "lists", "nowId", "showPopup", "shopTip", "upOption", "empty", "icon", "tip", "type", "qj<PERSON>ton", "onLoad", "console", "onShow", "uni", "onUnload", "methods", "addressData", "title", "that", "checkAddr", "goTo", "url", "confirmDel", "addressId", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "content", "success", "id", "duration", "isDefault", "is_default", "onRefresh", "upCallback", "pageNum", "pageSize", "params", "page", "size", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAutB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsG3uB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UACAC;UACAC;QACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACAC;EACA;EACAC;IACA;MACA;MACA;MACAC;;MAEA;MACA;QACA;MAAA;IAEA;MACA;MACAA;IACA;IAEA;EACA;EACAC;IACAD;EACA;EACAE;IACA;IACAC;MACAH;QACAI;MACA;MACA;MACA;QACA;UACAJ;UACAK;UACAA;QACA;MACA;IACA;IACAC;MACAR;MACA;MACA;QACAE;QACAA;QACAF;MACA;QACAE;QACAA;QACAF;MACA;QACAE;QACAA;MACA;IACA;IAGA;IACAO;MACAP;QACAQ;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;QACA;UAEA;YACA;cACAV;YACA;UACA;UAEA;YACAI;UACA;UACA;UACAO;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MAEAZ;QACAI;QACAS;QACAC;UACA;YAEAd;cACAI;YACA;YACA;cACAW;YACA;cACA;gBAEA;kBACA;oBACAf;kBACA;gBACA;gBACAA;gBACAK;gBACAL;kBACAI;kBACAY;gBACA;cACA;YACA;UAEA;YACAlB;UACA;QACA;MACA;IAEA;IAEA;IACAmB;MAAA;MACA;QACAF;QACAG;MACA;QACA;UACA;UACA;YACAd;UACA;QACA;MACA;IAEA;IAEAe;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACAC;gBACAC;gBACAC;kBACAC;kBACAC;gBACA,GACA;gBACA;gBACA;kBACA;oBACA;oBACA;oBACA;oBACA;sBACA;oBACA;oBACA;oBACA;kBAEA;oBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA1B;QACAQ;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxTA;AAAA;AAAA;AAAA;AAA81C,CAAgB,yvCAAG,EAAC,C;;;;;;;;;;;ACAl3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./address.vue?vue&type=template&id=4d173331&\"\nvar renderjs\nimport script from \"./address.vue?vue&type=script&lang=js&\"\nexport * from \"./address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./address.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/address.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=template&id=4d173331&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loding ? _vm.lists.length : null\n  var g1 = _vm.loding ? _vm.lists.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPopup = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"address\" v-if=\"loding\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t<!-- <mescroll-body ref=\"mescrollRef\" @init=\"mescrollInit\" @down=\"downCallback\" @up=\"upCallback\" :up=\"upOption\">\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"li\" v-for=\"(item,index) in lists\" :key=\"index\" @click=\"checkAddr(item)\">\r\n\t\t\t\t\t<view class=\"li_t\">\r\n\t\t\t\t\t\t<view class=\"li_t_info\">\r\n\t\t\t\t\t\t\t{{item.userName}}({{item.userSex==1?'先生':'女士'}})&nbsp;&nbsp; {{item.userPhone}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"li_t_detail\">{{item.addressDetail}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"li_d flex row-between\">\r\n\t\t\t\t\t\t<view class=\"li_d_l flex\" @click.stop=\"isDefault(item)\">\r\n\t\t\t\t\t\t\t<image v-if=\"item.addressDefault == 1\" src=\"/static/images/Mr_x2.png\" mode=\"scaleToFill\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t<image v-else src=\"/static/images/Mr.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t默认地址\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"li_d_use flex\">\r\n\t\t\t\t\t\t\t<view class=\"li_d_li flex\" @click.stop=\"goTo(item)\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/images/addr_edit.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t\t编辑\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"li_d_li flex\" @click.stop=\"nowId = item.addressId,showPopup=true\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/images/addr_del.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t\t删除\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</mescroll-body> -->\r\n\t\t\r\n\t\t<view class=\"list\" style=\"margin-bottom: 128rpx;\" v-if=\"lists.length > 0\">\r\n\t\t\t<view class=\"li\" v-for=\"(item,index) in lists\" :key=\"index\" @click=\"checkAddr(item)\">\r\n\t\t\t\t<view class=\"li_t\">\r\n\t\t\t\t\t<view class=\"li_t_info\">\r\n\t\t\t\t\t\t{{item.name}}({{item.gender==1?'先生':'女士'}})&nbsp;&nbsp; {{item.phone}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"li_t_detail\">{{item.area+item.detail}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"li_d flex row-between\">\r\n\t\t\t\t\t<view class=\"li_d_l flex\" @click.stop=\"isDefault(item)\">\r\n\t\t\t\t\t\t<image v-if=\"item.is_default == 1\" src=\"/static/images/dzxz-11.png\" mode=\"scaleToFill\" class=\"ac\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<image v-else src=\"/static/images/dzxz.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t默认地址\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"li_d_use flex\">\r\n\t\t\t\t\t\t<view class=\"li_d_li flex\" @click.stop=\"goTo(1,item)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/images/addr_edit.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t编辑\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"li_d_li flex\" @click.stop=\"delAddress(item.id)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/images/addr_del.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t\t\t删除\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"gg_zwsj_w\" v-if=\"lists.length == 0\">\r\n\t\t\t<image src=\"/static/images/addr_kong.png\"></image>\r\n\t\t\t<text style=\"display:block;\">收货地址空空如也~</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<view class=\"add_foo\"><view class=\"\" @click=\"goTo(0)\">添加地址</view></view>\r\n\t\t\r\n\t\t<!-- 弹窗 -->\r\n\t\t<u-popup v-model=\"showPopup\" mode=\"center\" border-radius=\"20\">\r\n\t\t\t<view class=\"prompt\">\r\n\t\t\t\t<view class=\"prompt_t\">\r\n\t\t\t\t\t<view class=\"prompt_t_img\">\r\n\t\t\t\t\t\t<image src=\"/static/images/popup-icon.png\" mode=\"scaleToFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"prompt_t_text\">\r\n\t\t\t\t\t\t提示\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"prompt_c\">\r\n\t\t\t\t\t确定退出当前删除？\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"prompt_d\">\r\n\t\t\t\t\t<view class=\"prompt_d_l\" @click=\"showPopup=  false\">\r\n\t\t\t\t\t\t取消\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"prompt_d_r\" @click=\"confirmDel\">\r\n\t\t\t\t\t\t确定\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport MescrollCompMixin from \"@/components/mescroll-uni/mixins/mescroll-comp\";\r\n\timport MescrollMixin from \"@/components/mescroll-uni/mescroll-mixins\";\r\n\timport {\r\n\t\taddrList,\r\n\t\taddrDel,\r\n\t\taddrEdit,\r\n\t\taddrmor\r\n\t} from '@/config/http.achieve.js'\r\n\texport default {\r\n\t\tmixins: [MescrollMixin, MescrollCompMixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloding:false,\r\n\t\t\t\tlists: [],\r\n\t\t\t\tnowId: '',\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tshopTip: false,\r\n\t\t\t\tupOption: {\r\n\t\t\t\t\tempty: {\r\n\t\t\t\t\t\ticon: '/static/images/addr_kong.png',\r\n\t\t\t\t\t\ttip: '还未添加收货地址~'\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\ttype: '',\r\n\t\t\t\tqjbutton:'#131315',\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(opt) {\r\n\t\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\t\tif (opt.type) {\r\n\t\t\t\tthis.type = opt.type\r\n\t\t\t}\r\n\t\t\tconsole.log(opt,'opt哈哈')\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (uni.getStorageSync('pageAddr')) {\r\n\t\t\t\t// 已经有记录，增加进入次数\r\n\t\t\t\tlet count = uni.getStorageSync('pageAddr') + 1;\r\n\t\t\t\tuni.setStorageSync('pageAddr', count);\r\n\r\n\t\t\t\t// 第二次进入\r\n\t\t\t\tif (count != 1) {\r\n\t\t\t\t\t// this.onRefresh()\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 第一次进入，记录次数\r\n\t\t\t\tuni.setStorageSync('pageAddr', 1);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.addressData()//地址列表\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\tuni.removeStorageSync('pageAddr')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//地址列表\r\n\t\t\taddressData(){\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tvar that = this;\r\n\t\t\t\taddrList({}).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tthat.lists = res.data;\r\n\t\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcheckAddr(item) {\r\n\t\t\t\tconsole.log(this.type,'this.type ')\r\n\t\t\t\tif (this.type == '') return\r\n\t\t\t\tif (this.type == 'qu') {\r\n\t\t\t\t\tuni.setStorageSync('qu', item)\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\tconsole.log('qu')\r\n\t\t\t\t} else if (this.type == 'shou') {\r\n\t\t\t\t\tuni.setStorageSync('shou', item)\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\tconsole.log('shou')\r\n\t\t\t\t}else if (this.type == 'diancan') {\r\n\t\t\t\t\tuni.setStorageSync('diancan', item)\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t \r\n\t\t\t// 跳转编辑页面\r\n\t\t\tgoTo(type,item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/mine/add_address?type=${type}&data=${JSON.stringify(item)}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 确认删除\r\n\t\t\tconfirmDel(item) {\r\n\t\t\t\taddrDel({\r\n\t\t\t\t\taddressId: this.nowId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (uni.getStorageSync('diancan')) {\r\n\t\t\t\t\t\t\tif(uni.getStorageSync('diancan').id == this.nowId){\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('diancan')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.$toast({\r\n\t\t\t\t\t\t\ttitle: \"删除成功\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.onRefresh()\r\n\t\t\t\t\t\t}, 500)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//删除收货地址\r\n\t\t\tdelAddress(id){\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确定要删除该地址吗？',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t\t\t\t}); \r\n\t\t\t\t\t\t\taddrDel({\r\n\t\t\t\t\t\t\t\tid: id\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tif (uni.getStorageSync('diancan')) {\r\n\t\t\t\t\t\t\t\t\t\tif(uni.getStorageSync('diancan').id == id){\r\n\t\t\t\t\t\t\t\t\t\t\tuni.removeStorageSync('diancan')\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tthat.addressData();//收货地址\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t},\r\n\r\n\t\t\t// 设置默认\r\n\t\t\tisDefault(item) {\r\n\t\t\t\taddrmor({\r\n\t\t\t\t\tid: item.id,\r\n\t\t\t\t\tis_default: item.is_default == 0 ? 1 : 0\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tthis.addressData()\r\n\t\t\t\t\t\tthis.$toast({\r\n\t\t\t\t\t\t\ttitle: \"设置成功\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\r\n\t\t\tonRefresh() {\r\n\t\t\t\tthis.mescroll.resetUpScroll();\r\n\t\t\t},\r\n\t\t\tasync upCallback(page) {\r\n\t\t\t\t// uni.showLoading()\r\n\t\t\t\t// this.TopListFun()\r\n\t\t\t\tif (page.num == 1) {}\r\n\t\t\t\tlet pageNum = page.num;\r\n\t\t\t\tlet pageSize = page.size;\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tpage: pageNum,\r\n\t\t\t\t\tsize: pageSize,\r\n\t\t\t\t}\r\n\t\t\t\t// this.mescroll.endErr()\r\n\t\t\t\t// this.mescroll.endSuccess(0, 0);\r\n\t\t\t\taddrList(params).then(res => {\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tlet curPageData = res.data;\r\n\t\t\t\t\t\tlet curPageLen = curPageData.length;\r\n\t\t\t\t\t\tlet totalSize = res.total;\r\n\t\t\t\t\t\tif (page.num == 1) {\r\n\t\t\t\t\t\t\tthis.lists = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.lists = this.lists.concat(curPageData);\r\n\t\t\t\t\t\tthis.mescroll.endSuccess(curPageLen, totalSize);\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.mescroll.endErr()\r\n\t\t\t\t\t\t// this.mescroll.endSuccess();\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tnavTo(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.list {\r\n\t\t.li {\r\n\t\t\tmargin: 28rpx auto 0;\r\n\t\t\twidth: 686rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 20rpx;\r\n\t\t\tpadding: 26rpx;\r\n\r\n\t\t\t.li_t {\r\n\t\t\t\t.li_t_info {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.li_t_detail {\r\n\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tpadding-bottom: 34rpx;\r\n\t\t\t\tborder-bottom: 2rpx dashed rgba(167, 167, 167, 0.2);\r\n\t\t\t}\r\n\r\n\t\t\t.li_d {\r\n\t\t\t\tpadding-top: 30rpx;\r\n\r\n\t\t\t\t.li_d_l {\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.li_d_use {\r\n\t\t\t\t\t.li_d_li {\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tip_box {\r\n\t\twidth: 480rpx;\r\n\t\theight: 416rpx;\r\n\t\tpadding: 50rpx 36rpx;\r\n\r\n\t\t.tip_box_title {\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 39rpx;\r\n\t\t}\r\n\r\n\t\t.tip_box_cont {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\twidth: 380rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 54rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #818181;\r\n\t\t\tline-height: 36rpx;\r\n\t\t}\r\n\r\n\t\t.tip_box_btn {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tmargin-top: 54rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #818181;\r\n\t\t\tline-height: 30rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\twidth: 144rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t}\r\n\t}\r\n\r\n\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114330413\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}