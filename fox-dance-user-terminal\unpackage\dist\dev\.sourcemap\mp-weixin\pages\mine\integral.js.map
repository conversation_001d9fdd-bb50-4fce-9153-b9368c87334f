{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/integral.vue?e052", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/integral.vue?928c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/integral.vue?58ff", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/integral.vue?db3e", "uni-app:///pages/mine/integral.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/integral.vue?5a6a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/integral.vue?ee13"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "isH5", "score", "type", "date_sj", "scoreLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "imgbaseUrl", "qj<PERSON>ton", "created", "onLoad", "methods", "tabTap", "bindDateChange_sj", "scoreData", "uni", "title", "size", "date", "console", "that", "onReachBottom", "onPullDownRefresh", "userData", "userDetailApi", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwtB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoD5uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC,6BAEA;EACAC;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAhB;QACAiB;QACAC;QACArB;MACA;QACAsB;QACA;UACA;UACAC;UACAA;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAL;UACAA;QACA;MACA;IAEA;IACAM;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAH;MACA;MACA;MACA;IACA;IACA;IACAI;MACAR;QACAC;MACA;MACA;MACAQ;QACA;UACAL;UACAJ;QACA;MACA;IACA;IACAU;MACAV;QACAW;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9KA;AAAA;AAAA;AAAA;AAA+zC,CAAgB,2tCAAG,EAAC,C;;;;;;;;;;;ACAn1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/integral.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/integral.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./integral.vue?vue&type=template&id=be52af82&\"\nvar renderjs\nimport script from \"./integral.vue?vue&type=script&lang=js&\"\nexport * from \"./integral.vue?vue&type=script&lang=js&\"\nimport style0 from \"./integral.vue?vue&type=style&index=0&lang=less&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/integral.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral.vue?vue&type=template&id=be52af82&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"integral\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t\r\n\t\t<view class=\"int_one\">\r\n\t\t\t<view class=\"int_one_a\">我的积分</view>\r\n\t\t\t<view class=\"int_one_b\">{{score}}</view>\r\n\t\t\t<view class=\"int_one_c\" @click=\"navTo('/pages/mine/order/order')\">积分订单</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"int_two\">\r\n\t\t\t<view class=\"int_two_l\">\r\n\t\t\t\t<view :class=\"type == 0 ? 'int_two_l_ac' : ''\" @click=\"tabTap(0)\">全部</view>\r\n\t\t\t\t<view :class=\"type == 1 ? 'int_two_l_ac' : ''\" @click=\"tabTap(1)\">获得</view>\r\n\t\t\t\t<view :class=\"type == 2 ? 'int_two_l_ac' : ''\" @click=\"tabTap(2)\">消耗</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"int_two_r\">\r\n\t\t\t\t<picker mode=\"date\" :value=\"date_sj\" fields=\"month\" @change=\"bindDateChange_sj\">\r\n\t\t\t\t\t<view class=\"uni-input\">{{date_sj}}<text></text></view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"int_thr\" v-if=\"!zanwsj\">\r\n\t\t\t<view class=\"int_thr_li\" v-for=\"(item,index) in scoreLists\" :key=\"index\">\r\n\t\t\t\t<view class=\"int_thr_li_a\">{{item.type == 1 ? '兑换扣除' : item.type == 2 ? '邀请增加' : item.type == 3 ? '系统增加' : item.type == 4 ? '系统扣除' : ''}}</view>\r\n\t\t\t\t<view class=\"int_thr_li_b\">{{item.create_time}}</view>\r\n\t\t\t\t<view class=\"int_thr_li_c\">{{item.type == 1 ? '-' : item.type == 2 ? '+' : item.type == 3 ? '+' : item.type == 4 ? '-' : '+'}}{{item.score*1}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"gg_loding\">\r\n\t\t\t\t<view class=\"loader-inner ball-clip-rotate\" v-if=\"status == 'loading'\">\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t\t<text>加载中</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"gg_loding_wusj\" v-else>─── 没有更多数据了 ───</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"gg_zwsj\" v-if=\"zanwsj\">\r\n\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<text>暂无数据</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tscoreListApi\r\n} from '@/config/http.achieve.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:false,//是否登录\r\n\t\t\tisH5:false,//是否是h5\r\n\t\t\tscore:0,\r\n\t\t\ttype:0,\r\n\t\t\tdate_sj: '请选择',\r\n\t\t\tscoreLists:[],//积分明细\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\timgbaseUrl:'',//图片地址\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tcreated(){\r\n\t\t\r\n\t},\r\n\tonLoad(options) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\t// this.userData();//个人信息\r\n\t\tthis.scoreData();//积分明细/提现记录\r\n\t},\r\n\tmethods: {\r\n\t\ttabTap(index){\r\n\t\t\tthis.type = index;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.scoreLists = [];\r\n\t\t\tthis.scoreData();\r\n\t\t},\r\n\t\tbindDateChange_sj: function(e) {\r\n\t\t\tthis.date_sj = e.detail.value\r\n\t\t\t//积分明细/提现记录\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.scoreLists = [];\r\n\t\t\tthis.scoreData();\r\n\t\t},\r\n\t\t//积分明细/提现记录\r\n\t\tscoreData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tscoreListApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tsize:10,\r\n\t\t\t\tdate:that.date_sj == '请选择' ? '' : that.date_sj,\r\n\t\t\t\ttype:that.type,//0：全部 1：获得 2：消耗\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('积分明细',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tvar obj = res.data.details.data;\r\n\t\t\t\t\tthat.score = res.data.score\r\n\t\t\t\t\tthat.scoreLists = that.scoreLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.scoreLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.details.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.scoreLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t//console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.scoreData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t\tthis.scoreLists = [];\r\n\t\t\tthis.scoreData();//积分明细/提现记录\r\n\t\t},\r\n\t\t//个人信息\r\n\t\tuserData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tuserDetailApi().then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tconsole.log('个人信息',res);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n\r\n<style lang=\"less\">\r\npage{padding-bottom:0;}\r\n.integral{\r\n\toverflow:hidden;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral.vue?vue&type=style&index=0&lang=less&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./integral.vue?vue&type=style&index=0&lang=less&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752112952958\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}