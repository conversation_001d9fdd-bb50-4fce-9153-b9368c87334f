{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/messageCenter.vue?9eea", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/messageCenter.vue?a90f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/messageCenter.vue?ef1a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/messageCenter.vue?bb14", "uni-app:///pages/mine/messageCenter.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/messageCenter.vue?723a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/messageCenter.vue?faef"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "isLogined", "messageLists", "page", "total_pages", "zanwsj", "status", "loadingText", "loadmoreText", "nomoreText", "imgbaseUrl", "qj<PERSON>ton", "onShow", "onLoad", "methods", "yhbgTap", "uni", "url", "tabTap", "messageData", "title", "size", "console", "that", "onReachBottom", "onPullDownRefresh", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6tB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC4CjvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAEA;MACA;QACAC;UACAC;QACA;MACA;MACA;QACA;QACAD;UACAC;QACA;MACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACAH;QACAI;MACA;MACA;MACA;QACAjB;QACAkB;QACArB;MACA;QACAsB;QACA;UACA;UACAC;UACAA;UACAA;UACA;UACAA;UACA;YACA;cACAA;YACA;cACAA;YACA;UACA;UACA;YACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACAA;UACAP;UACAA;QACA;MACA;IAEA;IACAQ;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAH;MACA;MACA;MACA;IACA;IACAI;MACAV;QACAC;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/JA;AAAA;AAAA;AAAA;AAAo2C,CAAgB,+vCAAG,EAAC,C;;;;;;;;;;;ACAx3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/messageCenter.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/messageCenter.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./messageCenter.vue?vue&type=template&id=cec68a8e&\"\nvar renderjs\nimport script from \"./messageCenter.vue?vue&type=script&lang=js&\"\nexport * from \"./messageCenter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./messageCenter.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/messageCenter.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messageCenter.vue?vue&type=template&id=cec68a8e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messageCenter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messageCenter.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"messageCenter\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t\r\n\t\t<view class=\"mes_one\">\r\n\t\t\t<view :class=\"type == 0 ? 'mes_one_ac' : ''\" @click=\"tabTap(0)\">全部</view>\r\n\t\t\t<view :class=\"type == 1 ? 'mes_one_ac' : ''\" @click=\"tabTap(1)\">消息</view>\r\n\t\t\t<view :class=\"type == 2 ? 'mes_one_ac' : ''\" @click=\"tabTap(2)\">通知</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"mes_two\">\r\n\t\t\t<view class=\"mes_two_li\" v-for=\"(item,index) in messageLists\" :key=\"index\" @click=\"yhbgTap(item)\">\r\n\t\t\t\t<image src=\"/static/images/icon26.png\" class=\"mes_two_li_tz\" v-if=\"item.type == 1 || item.type == 7\"></image>\r\n\t\t\t\t<image :src=\"item.profile == '' ? '/static/images/icon26-1.png' : imgbaseUrl + item.profile\" class=\"mes_two_li_xx\" v-else></image>\r\n\t\t\t\t<view class=\"mes_two_li_c\">\r\n\t\t\t\t\t<div class=\"mes_two_li_c_t\"><view>{{item.type == 1 ? '系统消息' : '通知'}}</view><text>{{item.create_time}}</text></div>\r\n\t\t\t\t\t<div class=\"mes_two_li_c_b\"><view>{{item.content}}</view><text style=\"display:none;\">2</text></div>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"gg_loding\" v-if=\"!zanwsj\">\r\n\t\t\t<view class=\"loader-inner ball-clip-rotate\" v-if=\"status == 'loading'\">\r\n\t\t\t\t<view></view>\r\n\t\t\t\t<text>加载中</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"gg_loding_wusj\" v-else>─── 没有更多数据了 ───</view>\r\n\t\t</view>\r\n\t\t<view class=\"gg_zwsj\" v-if=\"zanwsj\">\r\n\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<text>暂无数据</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"tzxf\" @click=\"navTo('/pages/mine/tzgl')\"><text>通知</text><text>管理</text></view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmessageApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttype:0,\r\n\t\t\tisLogined:true,\r\n\t\t\tmessageLists:[],//消息列表\r\n\t\t\tpage:1,//当前页数\r\n\t\t\ttotal_pages: 1,//总页数\r\n\t\t\tzanwsj:false,//是否有数据\r\n\t\t\tstatus: 'loading',//底部loding是否显示\r\n\t\t\tloadingText: '努力加载中',\r\n\t\t\tloadmoreText: '轻轻上拉',\r\n\t\t\tnomoreText: '实在没有了',\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.page = 1;\r\n\t\tthis.messageLists = [];\r\n\t\tthis.messageData()//消息列表\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t},\r\n\tmethods: {\r\n\t\t//用户报告跳转\r\n\t\tyhbgTap(item){\r\n\t\t\t\r\n\t\t\tvar type = item.type\r\n\t\t\tif(type == 7){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:'/pages/prizedraw/dengji'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif(type == 3 || type == 4 || type == 5){\r\n\t\t\t\t// 3=周报,4=月报,5=年报\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:type == 3 ? '/pages/mine/userReport/weeksUserReport?id=' + item.id : type == 4 ? '/pages/mine/userReport/monthUserReport?id=' + item.id : type == 5 ? '/pages/mine/userReport/yearsUserReport?id=' + item.id : ''\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\ttabTap(index){\r\n\t\t\tthis.type = index;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.messageLists = [];\r\n\t\t\tthis.messageData();//消息列表\r\n\t\t},\r\n\t\t//消息列表\r\n\t\tmessageData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmessageApi({\r\n\t\t\t\tpage:that.page,\r\n\t\t\t\tsize:10,\r\n\t\t\t\ttype:that.type,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('消息列表',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tvar obj = res.data.data;\r\n\t\t\t\t\tthat.messageLists = that.messageLists.concat(obj);\r\n\t\t\t\t\tthat.zanwsj = that.messageLists.length ? true : false;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t// that.total_pages = Math.ceil(res.total/20);\r\n\t\t\t\t\tthat.total_pages = res.data.last_page;\r\n\t\t\t\t\tif (that.page != 1) {\r\n\t\t\t\t\t\tif (that.total_pages >= that.page) {\r\n\t\t\t\t\t\t\tthat.status = 'loading'\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(that.messageLists.length == 0){\r\n\t\t\t\t\t\tthat.zanwsj = true;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.zanwsj = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(res.data.total*1<=10){\r\n\t\t\t\t\t\tthat.status = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t//console.log('到底了');\r\n\t\t\tif (this.page != 1) {\r\n\t\t\t\tif (this.total_pages >= this.page) {\r\n\t\t\t\t\tthis.messageData();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t    console.log('我被下拉了');\r\n\t\t    this.page = 1;\r\n\t\t\tthis.messageLists = [];\r\n\t\t\tthis.messageData();//消息列表\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage{padding-bottom: 0;background: #FFFFFF;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messageCenter.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./messageCenter.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752135320200\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}