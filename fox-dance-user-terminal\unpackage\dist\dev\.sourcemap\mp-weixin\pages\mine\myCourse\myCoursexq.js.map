{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/myCourse/myCoursexq.vue?c527", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/myCourse/myCoursexq.vue?42cf", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/myCourse/myCoursexq.vue?a330", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/myCourse/myCoursexq.vue?80c4", "uni-app:///pages/mine/myCourse/myCoursexq.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/myCourse/myCoursexq.vue?cd26", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/myCourse/myCoursexq.vue?5ce2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "navLists", "courseDetail", "id", "kcId", "imgbaseUrl", "yyToggle", "controlsToggle", "speedState", "speedNum", "speedRate", "controlsToggle_tc", "speedState_tc", "speedNum_tc", "speedRate_tc", "ljtkToggle", "qj<PERSON>ton", "type", "imgbaseUrlOss", "appointmentrecordid", "onShow", "onLoad", "uni", "methods", "bfTap", "console", "speedTap", "handleFullScreen", "handleControlstoggle", "handleSetSpeedRate", "videoContext", "icon", "title", "duration", "speedTap_tc", "handleFullScreen_tc", "handleControlstoggle_tc", "handleSetSpeedRate_tc", "videoContext_tc", "qxyySubTap", "that", "courseData", "AppointmentRecordId", "res", "homeTap", "url", "dhTap", "name", "latitude", "longitude", "success", "yypdTo", "setTimeout", "kqhyts", "ljktTap", "navTo", "downloadFile", "fail", "complete", "saveVideo", "filePath"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2I7vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;EACA;EACAC;IACAC;MACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACA;MACAR;QACAS;QACAC;QACAC;MACA;IAEA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;MACA;MACA;MACAhB;QACAS;QACAC;QACAC;MACA;MACA;IACA;IACA;IACAM;MACA;MACAjB;QACAU;MACA;MACA;QACA7B;MACA;QACAsB;QACA;UACAH;UACAkB;UACAlB;YACAS;YACAC;YACAC;UACA;UACAO;QACA;UACAA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAnB;QACAU;MACA;MACA;QACA7B;QACAc;QACAyB;MACA;QACAjB;QACA;UACAH;UACA;UACA;UACA;UACA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA;YACAqB;UACA;UACA;YACAA;YACAA;UACA;UACAH;QACA;MACA;IACA;IACAI;MACAtB;QACAuB;MACA;IACA;IACA;IACAC;MACA;MACAxB;QACAyB;QACAC;QACAC;QACAC;UACAzB;QACA;MACA;IACA;IACA;IACA0B;MACA;QACA7B;UACAS;UACAC;QACA;QACAoB;UACA9B;YACAuB;UACA;QACA;QACA;MACA;MACApB;MACA;MACA;MACA;QACA;QACA;MACA;MACAH;QACAuB;MACA;IACA;IACA;IACAQ;MACA/B;QACAU;QACAD;QACAE;MACA;IACA;IACA;IACAqB;MACA;MACAhC;QACAuB;MACA;IACA;IACAU;MACAjC;QACAuB;MACA;IACA;IACA;IACAW;MACA;QACAlC;UACAU;UACAD;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MAEAT;QAAAU;MAAA;MACA;MACA;MACAV;QACAuB;QACAK;UACA;YACA;YACAV;UACA;QACA;QACAiB;UACAnC;YAAAU;YAAAD;UAAA;QACA;QACA2B;UACApC;QACA;MACA;IACA;IACA;IACAqC;MACArC;QACAsC;QACAV;UACA5B;YAAAU;UAAA;QACA;QACAyB;UACA;QAAA;MAEA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACtaA;AAAA;AAAA;AAAA;AAA43C,CAAgB,4vCAAG,EAAC,C;;;;;;;;;;;ACAh5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/myCourse/myCoursexq.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/myCourse/myCoursexq.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myCoursexq.vue?vue&type=template&id=1f46fa05&\"\nvar renderjs\nimport script from \"./myCoursexq.vue?vue&type=script&lang=js&\"\nexport * from \"./myCoursexq.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myCoursexq.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/myCourse/myCoursexq.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myCoursexq.vue?vue&type=template&id=1f46fa05&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.courseDetail.id ? _vm.courseDetail.videoFileList.length : null\n  var g1 = _vm.courseDetail.id\n    ? _vm.courseDetail.music_link == \"\" &&\n      _vm.courseDetail.reviewVideoFileList.length == 0\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.yyToggle = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.yyToggle = true\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      _vm.ljtkToggle = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.yyToggle = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.ljtkToggle = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myCoursexq.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myCoursexq.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"myCoursexq\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"courseDetail.id\">\r\n\t\t<view class=\"kcxq_video\" :class=\"speedState ? 'qpvideo' : ''\" v-if=\"courseDetail.videoFileList.length > 0\">\r\n\t\t\t<video v-for=\"(item,index) in courseDetail.videoFileList\" :key=\"index\" :src=\"item.url\" controls :id=\"'videoId_'+index\" @fullscreenchange=\"handleFullScreen\" @controlstoggle=\"handleControlstoggle\">\r\n\t\t\t\t<!-- 倍速按钮 -->\r\n\t\t\t\t<cover-view v-show=\"controlsToggle\" class=\"speed\">\r\n\t\t\t\t\t<!-- <cover-view @click=\"speedNum=true\" class=\"doubleSpeed\">倍速</cover-view> -->\r\n\t\t\t\t\t<cover-view @click=\"downloadFile(item.url)\" class=\"doubleSpeed\">下载视频</cover-view>\r\n\t\t\t\t\t<cover-view @click=\"speedTap(index)\" class=\"doubleSpeed\">倍速</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t\t<!-- 倍速面板 -->\r\n\t\t\t\t<cover-view class=\"speedNumBox\" v-if=\"item.toggle\">\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(0.5,index)\" :class=\"0.5 == speedRate ? 'activeClass' :'' \">0.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(0.8,index)\" :class=\"0.8 == speedRate ? 'activeClass' :'' \">0.8倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1,index)\" :class=\"1 == speedRate ? 'activeClass' :'' \">1倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1.25,index)\" :class=\"1.25 == speedRate ? 'activeClass' :'' \">1.25倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(1.5,index)\" :class=\"1.5 == speedRate ? 'activeClass' :'' \">1.5倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate(2,index)\" :class=\"2 == speedRate ? 'activeClass' :'' \">2倍速</cover-view>\r\n\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"speedTap(index)\">取消</cover-view>\r\n\t\t\t\t</cover-view>\r\n\t\t\t</video>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kcxq_one\">\r\n\t\t\t<view class=\"kcxq_one_a\">{{courseDetail.course.name}}<text v-if=\"courseDetail.level_name != ''\">{{courseDetail.level_name}}</text></view>\r\n\t\t\t<view class=\"kcxq_one_bz\" v-if=\"courseDetail.notes != ''\"><image src=\"/static/images/icon59.png\"></image>{{courseDetail.notes}}</view>\r\n\t\t\t<view class=\"kcxq_one_b\" v-if=\"courseDetail.teacher\">\r\n\t\t\t\t<image :src=\"imgbaseUrl + courseDetail.teacher.image\" mode=\"aspectFit\" class=\"kcxq_one_b_l\"></image>\r\n\t\t\t\t<view class=\"kcxq_one_b_r\">\r\n\t\t\t\t\t<view class=\"kcxq_one_b_r_l\"><view>{{courseDetail.teacher.name}}</view><text v-if=\"courseDetail.teacher.work_year*1 > 0\">{{courseDetail.teacher.work_year}}年经验</text></view>\r\n\t\t\t\t\t<view class=\"kcxq_one_b_r_r\" @click=\"navTo('/pages/index/teacherDetail?id=' + courseDetail.teacher.id)\">老师详情<image src=\"/static/images/introduce_more.png\"></image></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"kcxq_one_c\" :style=\"courseDetail.teacher ? '' : 'margin-left:0'\">\r\n\t\t\t\t<view>上课时间：{{courseDetail.start_time}}</view>\r\n\t\t\t\t<view>课程时长：{{courseDetail.duration}}分钟</view>\r\n\t\t\t\t<view @click=\"dhTap\">上课地址：{{courseDetail.store.address}}<image src=\"/static/images/icon18.png\"></image></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kcxq_two\" v-if=\"courseDetail.appointment_number*1 > 0\">\r\n\t\t\t<view class=\"kcxq_two_xf\">已预约{{courseDetail.appointment_number*1}}人<template v-if=\"courseDetail.waiting_number*1 > 0\">、前方{{courseDetail.waiting_number*1}}人在等位</template></view>\r\n\t\t\t<view class=\"kcxq_two_t\"><view class=\"kcxq_two_t_n\"><text>学生</text><text></text></view></view>\r\n\t\t\t<view class=\"kcxq_two_b\">\r\n\t\t\t\t<view v-for=\"(item,index) in courseDetail.appointment_people\" :key=\"index\"><image :src=\"imgbaseUrl + item.avatar\"></image><text>{{item.nickname}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"kcxq_two kcxq_thr\" :class=\"speedState_tc ? 'qpvideo' : ''\">\r\n\t\t\t<view class=\"kcxq_two_t\"><view class=\"kcxq_two_t_n\"><text>本节回顾</text><text></text></view></view>\r\n\t\t\t<view class=\"kcxq_thr_b\">\r\n\t\t\t\t<view class=\"kcxq_thr_b_t\" v-if=\"courseDetail.music_link != ''\">\r\n\t\t\t\t\t<view><image src=\"/static/images/icon37.png\"></image><text>{{courseDetail.music_link}}</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"videoLi\" v-for=\"(item,index) in courseDetail.reviewVideoFileList\" :key=\"index\" >\r\n\t\t\t\t\t<image src=\"/static/images/bf.png\" class=\"videoLi_bf\" v-if=\"!item.toggleVideo\"></image>\r\n\t\t\t\t\t<image :src=\"item.image_url\" class=\"videoLi_bj\" @click=\"bfTap(index,item.url)\" v-if=\"!item.toggleVideo\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<!-- <video v-for=\"(item,index) in courseDetail.reviewVideoFileList\" :key=\"index\" :src=\"item.url\" controls :id=\"'videoId_tc_'+index\"  @fullscreenchange=\"handleFullScreen_tc\" @controlstoggle=\"handleControlstoggle_tc\" style=\"display: block;width: 100%;position: relative;\" v-if=\"courseDetail.review_video != ''\"> -->\r\n\t\t\t\t\t<video :src=\"item.url\" controls :id=\"'videoId_tc_'+index\"  @fullscreenchange=\"handleFullScreen_tc\" @controlstoggle=\"handleControlstoggle_tc\" style=\"display: block;width: 100%;position: relative;\" v-if=\"courseDetail.review_video != '' && item.toggleVideo\">\r\n\t\t\t\t\t\t<!-- 倍速按钮 -->\r\n\t\t\t\t\t\t<cover-view v-show=\"controlsToggle_tc\" class=\"speed\">\r\n\t\t\t\t\t\t\t<!-- <cover-view @click=\"speedNum=true\" class=\"doubleSpeed\">倍速</cover-view> -->\r\n\t\t\t\t\t\t\t<cover-view @click=\"downloadFile(item.url)\" class=\"doubleSpeed\">下载视频</cover-view>\r\n\t\t\t\t\t\t\t<cover-view @click=\"speedTap_tc(index)\" class=\"doubleSpeed\">倍速</cover-view>\r\n\t\t\t\t\t\t</cover-view>\r\n\t\t\t\t\t\t<!-- 倍速面板 -->\r\n\t\t\t\t\t\t<cover-view class=\"speedNumBox\" v-if=\"item.toggle\">\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(0.5,index)\" :class=\"0.5 == speedRate_tc ? 'activeClass' :'' \">0.5倍速</cover-view>\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(0.8,index)\" :class=\"0.8 == speedRate_tc ? 'activeClass' :'' \">0.8倍速</cover-view>\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(1,index)\" :class=\"1 == speedRate_tc ? 'activeClass' :'' \">1倍速</cover-view>\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(1.25,index)\" :class=\"1.25 == speedRate_tc ? 'activeClass' :'' \">1.25倍速</cover-view>\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(1.5,index)\" :class=\"1.5 == speedRate_tc ? 'activeClass' :'' \">1.5倍速</cover-view>\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"handleSetSpeedRate_tc(2,index)\" :class=\"2 == speedRate_tc ? 'activeClass' :'' \">2倍速</cover-view>\r\n\t\t\t\t\t\t\t<cover-view class=\"number\" @click.stop=\"speedTap_tc(index)\">取消</cover-view>\r\n\t\t\t\t\t\t</cover-view>\r\n\t\t\t\t\t</video>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"courseDetail.music_link == '' && courseDetail.reviewVideoFileList.length == 0\" style=\"width:100%;text-align:center;font-size:26rpx;\">暂无回顾内容</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<!-- 0=未报名,1=待开课,2=授课中,3=已完成,4=等位中,5=已取消 -->\r\n\t\t<view class=\"kcxq_foo\">\r\n\t\t\t<view class=\"kcxq_foo_l\">\r\n\t\t\t\t<view @click=\"homeTap\"><image src=\"/static/tabbar/tab_home.png\"></image>首页</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"kcxq_foo_r\">\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"back_999\" v-if=\"courseDetail.status == 1\" @click=\"yyToggle = true\">取消预约</view>\r\n\t\t\t\t<view class=\"back_999\" v-else-if=\"courseDetail.status == 2\">授课中</view>\r\n\t\t\t\t<view class=\"back_999\" v-else-if=\"courseDetail.status == 3\">已完成</view>\r\n\t\t\t\t<view class=\"back_999\" v-else-if=\"courseDetail.status == 4\" @click=\"yyToggle = true\">取消等位</view>\r\n\t\t\t\t<view class=\"back_999\" v-else-if=\"courseDetail.status == 5\">已取消</view>\r\n\t\t\t\t<!-- 从列表进入才展示 未开始预约和截止预约 -->\r\n\t\t\t\t<view class=\"back_999\" v-else-if=\"courseDetail.status == 6\">未开始预约</view>\r\n\t\t\t\t<view class=\"back_999\" v-else-if=\"courseDetail.status == 7\">截止预约</view>\r\n\t\t\t\t<!-- 未开启等位并且课程预约爆满 (课程预约满员时开启等位:0=关闭,1=开启)-->\r\n\t\t\t\t<view class=\"back\" style=\"background:#BEBEBE\"\r\n\t\t\t\t\tv-else-if=\"courseDetail.equivalent*1 == 0 && (courseDetail.appointment_number*1 >= courseDetail.maximum_reservation*1)\"\r\n\t\t\t\t\************=\"kqhyts\">预约</view>\r\n\t\t\t\t<view class=\"back\" :style=\"courseDetail.member == 0 ? 'background:#BEBEBE' : ''\"\r\n\t\t\t\t\tv-else-if=\"courseDetail.member == 0\" @click.stop=\"ljtkToggle = true\">预约</view>\r\n\t\t\t\t<!-- 开启等位 -->\r\n\t\t\t\t<view class=\"back\" v-else\r\n\t\t\t\t\************=\"yypdTo(courseDetail,'/pages/Schedule/Schedulexq?id' + courseDetail.id)\">\r\n\t\t\t\t\t{{courseDetail.waiting_number*1 > 0 ? '去排队' : '预约'}}</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 取消预约弹窗 go -->\r\n\t\t<view class=\"xjTanc\" v-if=\"yyToggle\">\r\n\t\t\t<view class=\"xjTanc_n\">\r\n\t\t\t\t<view class=\"xjTanc_a\">温馨提示</view>\r\n\t\t\t\t<view class=\"xjTanc_b\">是否取消预约？</view>\r\n\t\t\t\t<view class=\"xjTanc_c\">\r\n\t\t\t\t\t<view @click=\"yyToggle = false\">取消</view>\r\n\t\t\t\t\t<view class=\"bak\" @click=\"qxyySubTap\">确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 取消预约弹窗 end -->\r\n\t\t\r\n\t\t\r\n\t\t<!-- 提示预约弹窗 go -->\r\n\t\t<view class=\"yytnCon\" v-if=\"ljtkToggle\"><view class=\"yytnCon_n\"><image :src=\"imgbaseUrlOss + '/userreport/icon55.png'\"></image><text @click=\"ljktTap\"></text></view><image src=\"/static/images/icon56.png\" @click=\"ljtkToggle = false\"></image></view>\r\n\t\t<!-- 提示预约弹窗 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmyCourseXqApi,\r\n\tcancelCourseApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tnavLists:['全部','等位中','待开课','授课中','已完成'],\r\n\t\t\tcourseDetail:{id:0},\r\n\t\t\tkcId:0,\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tyyToggle:false,\r\n\t\t\t\r\n\t\t\tcontrolsToggle:false,//是否显示状态\r\n\t\t\tspeedState:false,//是否进入全屏\r\n\t\t\tspeedNum:false,//是否显示倍速\r\n\t\t\tspeedRate:0,//当前倍数\r\n\t\t\t\r\n\t\t\tcontrolsToggle_tc:false,//弹窗课包目录>是否显示状态\r\n\t\t\tspeedState_tc:false,//弹窗课包目录>是否进入全屏\r\n\t\t\tspeedNum_tc:false,//弹窗课包目录>是否显示倍速\r\n\t\t\tspeedRate_tc:0,//弹窗课包目录>当前倍数\r\n\t\t\tljtkToggle:false,\r\n\t\t\tqjbutton:'#131315',\r\n\t\t\ttype:0,\r\n\t\t\timgbaseUrlOss:\"\",\r\n\t\t\tappointmentrecordid:0\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.imgbaseUrlOss = this.$baseUrlOss;\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.appointmentrecordid = option.appointmentrecordid ? option.appointmentrecordid : 0\r\n\t\tthis.type = option.type ? option.type : 0\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.kcId = option.id;\r\n\t\tthis.courseData();//课程详情\r\n\t\tuni.setStorageSync('schedulesx',1);\r\n\t},\r\n\tmethods: {\r\n\t\tbfTap(index){\r\n\t\t\tconsole.log(index)\r\n\t\t\tthis.courseDetail.reviewVideoFileList[index].toggleVideo = true\r\n\t\t},\r\n\t\t//点击倍数\r\n\t\tspeedTap(index){\r\n\t\t\t// this.speedNum = true;\r\n\t\t\tthis.courseDetail.videoFileList[index].toggle = !this.courseDetail.videoFileList[index].toggle;\r\n\t\t},\r\n\t\t//监听进入全屏 和 退出全屏\r\n\t\thandleFullScreen(e){\r\n\t\t\t// console.log('监听进入全屏1',e);\r\n\t\t\t// console.log('监听进入全屏2',e.detail.fullScreen);\r\n\t\t\tthis.speedState = e.detail.fullScreen;\r\n\t\t\tthis.speedNum = false;\r\n\t\t\t// this.courseDetail.course.video_arr[index].toggle = false;\r\n\t\t},\r\n\t\t//2.控件（播放/暂停按钮、播放进度、时间）是显示状态\r\n\t\thandleControlstoggle(e){\r\n\t\t\t// console.log(e.detail.show);\r\n\t\t\tthis.controlsToggle = e.detail.show\r\n\t\t},\r\n\t\t//设置倍速速度\r\n\t\thandleSetSpeedRate(rate,index){\r\n\t\t\t let videoContext = uni.createVideoContext(\"videoId_\"+index);\r\n\t\t\t videoContext.playbackRate(rate);\r\n\t\t\t this.speedRate = rate;\r\n\t\t\t this.speedNum = false;\r\n\t\t\t this.courseDetail.videoFileList[index].toggle = false;\r\n\t\t\t uni.showToast({\r\n\t\t\t \ticon: 'none',\r\n\t\t\t \ttitle: '已切换至' + rate + '倍数',\r\n\t\t\t\tduration:2000\r\n\t\t\t });\r\n\t\t\t \r\n\t\t},\r\n\t\t//弹窗课包目录>点击倍数\r\n\t\tspeedTap_tc(index){\r\n\t\t\tthis.speedNum_tc = true;\r\n\t\t\tthis.courseDetail.reviewVideoFileList[index].toggle = !this.courseDetail.reviewVideoFileList[index].toggle\r\n\t\t},\r\n\t\t//弹窗课包目录>监听进入全屏 和 退出全屏\r\n\t\thandleFullScreen_tc(e){\r\n\t\t\t// console.log('监听进入全屏1',e);\r\n\t\t\t// console.log('监听进入全屏2',e.detail.fullScreen);\r\n\t\t\tthis.speedState_tc = e.detail.fullScreen;\r\n\t\t\tthis.speedNum_tc = false;\r\n\t\t},\r\n\t\t//弹窗课包目录>2.控件（播放/暂停按钮、播放进度、时间）是显示状态\r\n\t\thandleControlstoggle_tc(e){\r\n\t\t\t// console.log(e.detail.show);\r\n\t\t\tthis.controlsToggle_tc = e.detail.show\r\n\t\t},\r\n\t\t//设置倍速速度\r\n\t\thandleSetSpeedRate_tc(rate,index){\r\n\t\t\t let videoContext_tc = uni.createVideoContext(\"videoId_tc_\"+index);\r\n\t\t\t videoContext_tc.playbackRate(rate);\r\n\t\t\t this.speedRate_tc = rate;\r\n\t\t\t this.speedNum_tc = false;\r\n\t\t\t uni.showToast({\r\n\t\t\t \ticon: 'none',\r\n\t\t\t \ttitle: '已切换至' + rate + '倍数',\r\n\t\t\t\tduration:2000\r\n\t\t\t });\r\n\t\t\t this.courseDetail.reviewVideoFileList[index].toggle = false;\r\n\t\t},\r\n\t\t//取消预约提交\r\n\t\tqxyySubTap(){\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tcancelCourseApi({\r\n\t\t\t\tid:that.courseDetail.appointment_record_id,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('取消预约提交',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.yyToggle = false;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon:'success',\r\n\t\t\t\t\t\ttitle: '取消成功',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.courseData();//课程详情\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.yyToggle = false;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//课程详情\r\n\t\tcourseData(){\r\n\t\t\tlet that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tmyCourseXqApi({\r\n\t\t\t\tid:that.kcId,\r\n\t\t\t\ttype:that.type,\r\n\t\t\t\tAppointmentRecordId:that.appointmentrecordid\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('课程详情1',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// res.data.status = 1;\r\n\t\t\t\t\t// res.data.music_link = 'www.hsdf.com'\r\n\t\t\t\t\t// res.data.course.video = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4'\r\n\t\t\t\t\t// res.data.review_video = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4'\r\n\t\t\t\t\t/*res.data.videoFileList = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\turl:'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4',\r\n\t\t\t\t\t\t\tid:0,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\t\t\t\t\tres.data.reviewVideoFileList = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\turl:'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/videos/1734320048014_ces.mp4',\r\n\t\t\t\t\t\t\tid:0,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]*/\r\n\t\t\t\t\tfor(var i=0;i<res.data.videoFileList.length;i++){\r\n\t\t\t\t\t\tres.data.videoFileList[i].toggle = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tfor(var j=0;j<res.data.reviewVideoFileList.length;j++){\r\n\t\t\t\t\t\tres.data.reviewVideoFileList[j].toggle = false;\r\n\t\t\t\t\t\tres.data.reviewVideoFileList[j].toggleVideo = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.courseDetail = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\thomeTap(){\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl:'/pages/index/index'\r\n\t\t\t})\r\n\t\t},\r\n\t\t//导航\r\n\t\tdhTap(){\r\n\t\t\tvar that = this;\r\n\t\t\tuni.openLocation({\r\n\t\t\t\tname:that.courseDetail.store.address,\r\n\t\t\t\tlatitude: that.courseDetail.store.latitude*1,\r\n\t\t\t\tlongitude: that.courseDetail.store.longitude*1,\r\n\t\t\t\tsuccess: function () {\r\n\t\t\t\t\tconsole.log('success');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t//预约约课/排队\r\n\t\tyypdTo(item){\r\n\t\t\tif(!this.isLogined){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '请先登录'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t},1000)\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tconsole.log(item,'item');\r\n\t\t\t// return false;\r\n\t\t\t// 未开启会员\r\n\t\t\tif(item.member == 0){\r\n\t\t\t\tthis.ljtkToggle = true\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:'/pages/Schedule/confirmOrder?id=' + item.id + '&storeid=' + item.store.id\r\n\t\t\t})\r\n\t\t},\r\n\t\t//预约爆满\r\n\t\tkqhyts(){\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '预约课程已满',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1000\r\n\t\t\t})\r\n\t\t},\r\n\t\t//立即开通会员\r\n\t\tljktTap(){\r\n\t\t\tthis.ljtkToggle = false;\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl:'/pages/buy/buy'\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 下载并处理附件\r\n\t\tdownloadFile(url) {\r\n\t\t  if(url == ''){\r\n\t\t\t  uni.showToast({\r\n\t\t\t  \ttitle: '暂无可下载的附件',\r\n\t\t\t  \ticon: 'none'\r\n\t\t\t  });\r\n\t\t\t  return false;\r\n\t\t  }\r\n\t\t  // 检查文件类型\r\n\t\t  const ext = url.split('.').pop().toLowerCase();\r\n\t\t  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext);\r\n\t\t  const isVideo = ['mp4', 'mov', 'avi', 'm4v'].includes(ext);\r\n\t\t\r\n\t\t  uni.showLoading({ title: '下载中...' });\r\n\t\t  var that = this;\r\n\t\t  // 下载文件到临时路径\r\n\t\t  uni.downloadFile({\r\n\t\t    url: url,\r\n\t\t    success: (res) => {\r\n\t\t      if (res.statusCode === 200) {\r\n\t\t        const tempPath = res.tempFilePath;\r\n\t\t\t\tthat.saveVideo(tempPath);\r\n\t\t      }\r\n\t\t    },\r\n\t\t    fail: (err) => {\r\n\t\t      uni.showToast({ title: '下载失败', icon: 'none' });\r\n\t\t    },\r\n\t\t    complete: () => {\r\n\t\t      uni.hideLoading();\r\n\t\t    }\r\n\t\t  });\r\n\t\t},\r\n\t\t// 保存视频到相册\r\n\t\tsaveVideo(tempPath) {\r\n\t\t  uni.saveVideoToPhotosAlbum({\r\n\t\t    filePath: tempPath,\r\n\t\t    success: () => {\r\n\t\t      uni.showToast({ title: '视频已保存到相册' });\r\n\t\t    },\r\n\t\t    fail: (err) => {\r\n\t\t      // 处理权限问题（同图片保存）\r\n\t\t    }\r\n\t\t  });\r\n\t\t},\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myCoursexq.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myCoursexq.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752135320282\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}