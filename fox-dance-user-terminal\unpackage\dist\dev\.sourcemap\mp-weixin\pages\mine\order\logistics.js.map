{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/logistics.vue?745e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/logistics.vue?e33f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/logistics.vue?d4f4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/logistics.vue?7352", "uni-app:///pages/mine/order/logistics.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/order/logistics.vue?ebfc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "name", "images", "imgbaseUrl", "logisticsLists", "loding", "logisticsData", "onShow", "onLoad", "console", "methods", "wlData", "uni", "title", "id", "type", "that", "icon", "duration", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwuB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqC5vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,iBACA,IACA,IACA,IACA,IACA,GACA;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;IACA;IACA;IACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACAC;QACAC;MACA;MACA;QACAC;QACAC;MACA;QACAN;QACA;UACAO;UACAA;UACAJ;QACA;UACAA;UACAA;YACAK;YACAJ;YAAA;YACAK;UACA;UACAC;YACAP;UACA;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAm5C,CAAgB,mxCAAG,EAAC,C", "file": "pages/mine/order/logistics.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/order/logistics.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./logistics.vue?vue&type=template&id=36a97fcf&scoped=true&\"\nvar renderjs\nimport script from \"./logistics.vue?vue&type=script&lang=js&\"\nexport * from \"./logistics.vue?vue&type=script&lang=js&\"\nimport style0 from \"./logistics.vue?vue&type=style&index=0&id=36a97fcf&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"36a97fcf\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/order/logistics.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./logistics.vue?vue&type=template&id=36a97fcf&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./logistics.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./logistics.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"logistics\" v-if=\"loding\">\r\n\t\t\r\n\t\t<view class=\"log_con\">\r\n\t\t\t<view class=\"log_con_t\">\r\n\t\t\t\t<view class=\"ord_con_li_b_li\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + images\" mode=\"scaleToFill\" class=\"ord_con_li_b_li_l\"></image>\r\n\t\t\t\t\t<view class=\"ord_con_li_b_li_r\">\r\n\t\t\t\t\t\t<view class=\"ord_con_li_b_li_r_a\" style=\"font-size: 32rpx;\">{{logisticsData.status == 1 ? '待发货' : logisticsData.status == 2 ? '运输中' : logisticsData.status == 3 ? '已完成' : ''}}</view>\r\n\t\t\t\t\t\t<!-- <view class=\"ord_con_li_b_li_r_b\"><text>￥12.99</text>/份</view> -->\r\n\t\t\t\t\t\t<view class=\"ord_con_li_b_li_r_c\">{{name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> \r\n\t\t\t<view class=\"log_con_c\">{{logisticsData.expressName}} {{logisticsData.number}}</view>\r\n\t\t\t<view class=\"log_con_b\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"wl_tanc_b\">\r\n\t\t\t\t\t<view class=\"orde_thr_f_li\" v-for=\"(item,index) in logisticsData.logisticsList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"orde_thr_f_li_l\">\r\n\t\t\t\t\t\t\t<text></text> \r\n\t\t\t\t\t\t\t<view></view> \r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"orde_thr_f_li_r\">\r\n\t\t\t\t\t\t\t<view class=\"orde_thr_f_li_r_a\">{{item.context}}</view>\r\n\t\t\t\t\t\t\t<view class=\"orde_thr_f_li_r_b\">{{item.time}}</view>\r\n\t\t\t\t\t\t</view> \r\n\t\t\t\t\t</view>           \r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\texpressApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tname:'',\r\n\t\t\timages:'',\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tlogisticsLists:[\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t\t{},\r\n\t\t\t],\r\n\t\t\tloding:false,\r\n\t\t\tlogisticsData:{}\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tonLoad(option) {\r\n\t\tconsole.log(option,'option')\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.name = option.name;\r\n\t\tthis.images = option.images;\r\n\t\tthis.wlData(option.id,option.type);//物流信息\r\n\t},\r\n\tmethods: {\r\n\t\t//物流信息\r\n\t\twlData(id,type) {\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\texpressApi({\r\n\t\t\t\tid: id,\r\n\t\t\t\ttype:type ? 2 : 1\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('物流信息', res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\tthat.logisticsData = res.data;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '暂无物流信息', //保存路径\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tuni.navigateBack({})\r\n\t\t\t\t\t},2000)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.logistics{overflow:hidden;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./logistics.vue?vue&type=style&index=0&id=36a97fcf&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./logistics.vue?vue&type=style&index=0&id=36a97fcf&scoped=true&lang=scss&\""], "sourceRoot": ""}