{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/ranking.vue?edc5", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/ranking.vue?61c9", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/ranking.vue?9cb9", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/ranking.vue?649f", "uni-app:///pages/mine/ranking.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/ranking.vue?1460", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/ranking.vue?a119"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "type", "rankingA", "rankingB", "imgbaseUrl", "qj<PERSON>ton", "onShow", "onLoad", "methods", "tabTap", "myRankData", "uni", "title", "console", "that", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAutB,CAAgB,ssBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmE3uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACA;MACA;IACA;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAX;MACA;QACAY;QACA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACA;UACAC;UACAA;UACA;UACAH;QACA;MACA;IACA;IACAI;MACAJ;QACAK;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAA81C,CAAgB,yvCAAG,EAAC,C;;;;;;;;;;;ACAl3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/ranking.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/ranking.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ranking.vue?vue&type=template&id=11567273&\"\nvar renderjs\nimport script from \"./ranking.vue?vue&type=script&lang=js&\"\nexport * from \"./ranking.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ranking.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/ranking.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=template&id=11567273&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.rankingA.length\n  var g1 = g0 != 0 ? _vm.rankingA.length : null\n  var g2 = g0 != 0 ? _vm.rankingA.length : null\n  var g3 = g0 != 0 ? _vm.rankingA.length : null\n  var g4 = _vm.rankingA.length\n  var g5 = _vm.rankingA.length\n  var g6 = _vm.rankingB.length == 0 && _vm.rankingA.length != 0\n  var g7 = _vm.rankingA.length\n  var g8 = _vm.rankingA.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ranking\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t\r\n\t\t<view class=\"ran_one\">\r\n\t\t\t<view :class=\"type == 0 ? 'ran_one_ac' : ''\" @click=\"tabTap(0)\">本月上课次数榜</view>\r\n\t\t\t<view :class=\"type == 1 ? 'ran_one_ac' : ''\" @click=\"tabTap(1)\">上月刷课王</view>\r\n\t\t</view>\r\n\t\t\t\r\n\t\t<view class=\"ran_two\" v-if=\"rankingA.length != 0\">\r\n\t\t\t<view class=\"ran_two_li\">\r\n\t\t\t\t<view class=\"ran_two_li_a\" v-if=\"rankingA.length >= 2\">\r\n\t\t\t\t\t<image src=\"/static/images/icon24.png\"></image>\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + rankingA[1].user.avatar\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view>{{rankingA[1].user.nickname}}</view>\r\n\t\t\t\t\t<text>上课{{rankingA[1].count}}次</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ran_two_li_b\">TOP2</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ran_two_li\">\r\n\t\t\t\t<view class=\"ran_two_li_a\" v-if=\"rankingA.length >= 1\">\r\n\t\t\t\t\t<image src=\"/static/images/icon23.png\"></image>\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + rankingA[0].user.avatar\" mode=\"aspectFit\" style=\"border-color:#FEC41F;\"></image>\r\n\t\t\t\t\t<view>{{rankingA[0].user.nickname}}</view>\r\n\t\t\t\t\t<text>上课{{rankingA[0].count}}次</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ran_two_li_b\">TOP1</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ran_two_li\">\r\n\t\t\t\t<view class=\"ran_two_li_a\" v-if=\"rankingA.length >= 3\">\r\n\t\t\t\t\t<image src=\"/static/images/icon25.png\"></image>\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + rankingA[2].user.avatar\" mode=\"aspectFit\" style=\"border-color:#E4B98F;\"></image>\r\n\t\t\t\t\t<view>{{rankingA[2].user.nickname}}</view>\r\n\t\t\t\t\t<text>上课{{rankingA[2].count}}次</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ran_two_li_b\">TOP3</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"ran_thr\">\r\n\t\t\t<view class=\"ran_thr_t\" v-if=\"rankingA.length != 0\"><view>排名</view><view>用户名</view><view>上课次数</view></view>\r\n\t\t\t<view class=\"ran_thr_b\" v-if=\"rankingA.length != 0\">\r\n\t\t\t\t<view class=\"ran_thr_b_li\" v-for=\"(item,index) in rankingB\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"ran_thr_b_li_a\">{{index+4}}</view>\r\n\t\t\t\t\t<view class=\"ran_thr_b_li_b\"><image :src=\"imgbaseUrl + item.user.avatar\" mode=\"aspectFit\"></image><text>{{item.user.nickname}}</text></view>\r\n\t\t\t\t\t<view class=\"ran_thr_b_li_c\">已上课{{item.count}}次</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"font-size: 26rpx;color: #333;text-align:center;margin:40rpx 0;\" v-if=\"rankingB.length == 0 && rankingA.length != 0\">暂无更多排名</view>\r\n\t\t\t<view class=\"ran_thr_c\"  v-if=\"rankingA.length != 0\">仅展示前100名</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"gg_zwsj\" v-if=\"rankingA.length == 0\" style=\"margin-bottom:60rpx;\">\r\n\t\t\t<view class=\"gg_zwsj_w\">\r\n\t\t\t\t<image src=\"/static/images/wusj.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<text style=\"color:#fff;\">暂无排名</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tmyRankApi,\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\ttype:0,\r\n\t\t\trankingA:[],\r\n\t\t\trankingB:[],\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.myRankData();//排行榜\r\n\t},\r\n\tmethods: {\r\n\t\ttabTap(type){\r\n\t\t\tthis.type = type;\r\n\t\t\tthis.myRankData();//排行榜\r\n\t\t},\r\n\t\t//排行榜\r\n\t\tmyRankData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tmyRankApi({\r\n\t\t\t\ttype:that.type\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('排行榜',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t/*res.data = [{\r\n\t\t\t\t\t\t\"count\": 2,\r\n\t\t\t\t\t\t\t\"user\": {\r\n\t\t\t\t\t\t\t\t\"nickname\": \"昵称昵称1\",\r\n\t\t\t\t\t\t\t\t\"avatar\": \"/static/images/avatar.png\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\"count\": 2,\r\n\t\t\t\t\t\t\t\t\"user\": {\r\n\t\t\t\t\t\t\t\t\t\"nickname\": \"昵称昵称2\",\r\n\t\t\t\t\t\t\t\t\t\"avatar\": \"/static/images/avatar.png\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"count\": 2,\r\n\t\t\t\t\t\t\t\t\t\"user\": {\r\n\t\t\t\t\t\t\t\t\t\t\"nickname\": \"昵称昵称\",\r\n\t\t\t\t\t\t\t\t\t\t\"avatar\": \"/static/images/avatar.png\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t]*/\r\n\t\t\t\t\t// that.myRankData = res.data;\r\n\t\t\t\t\tthat.rankingA = res.data.slice(0,3)\r\n\t\t\t\t\tthat.rankingB = res.data.slice(3,99)\r\n\t\t\t\t\t// console.log(that.rankingA,'that.rankingA')\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ranking{overflow:hidden;}\r\npage{padding-bottom: 0;background: #000;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ranking.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752112957643\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}