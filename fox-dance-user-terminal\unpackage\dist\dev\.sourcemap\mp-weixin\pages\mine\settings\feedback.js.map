{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/settings/feedback.vue?6d5c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/settings/feedback.vue?0e35", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/settings/feedback.vue?6f47", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/settings/feedback.vue?05f8", "uni-app:///pages/mine/settings/feedback.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/settings/feedback.vue?ed8a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/settings/feedback.vue?7df7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "feedLists", "uploadimgs", "type", "contents", "contact_mode", "imgbaseUrl", "qj<PERSON>ton", "onShow", "onLoad", "methods", "fkSubTap", "uni", "icon", "title", "duration", "problem", "image", "console", "setTimeout", "feedbackCateData", "that", "uploadImgTap", "count", "success", "i", "uploadImgDel", "tabTap", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC6L;AAC7L,gBAAgB,8LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuuB,CAAgB,usBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2C3vB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACAH;QACAE;MACA;MACA;MACA;QACAX;QACAa;QACAC;QACAZ;MACA;QACAa;QACA;UACAN;UACAA;YACAE;YACAC;UACA;UACAI;YACAP;UACA;QACA;MACA;IACA;IACA;IACAQ;MACAR;QACAE;MACA;MACA;MACA;QACAI;QACA;UACAG;UACAT;QACA;MACA;IACA;IACA;IACAU;MACA;MACA;MAEAV;QACAW;QAAA;QACAC;UACAZ;YACAE;UACA;UACA;UACAI;UACA;UAAA,2BACAO;YACA;cACA;gBACA;kBAEAJ;oBACAP;oBACAD;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEAQ;cACA;YACA;UAAA;UAjBA;YAAA;UAmBA;QACA;MAEA;IAEA;IACAK;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAhB;QACAiB;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAA03C,CAAgB,0vCAAG,EAAC,C;;;;;;;;;;;ACA94C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/settings/feedback.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/settings/feedback.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./feedback.vue?vue&type=template&id=0f828e6e&\"\nvar renderjs\nimport script from \"./feedback.vue?vue&type=script&lang=js&\"\nexport * from \"./feedback.vue?vue&type=script&lang=js&\"\nimport style0 from \"./feedback.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/settings/feedback.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedback.vue?vue&type=template&id=0f828e6e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedback.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedback.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"feedback\" :style=\"{ '--qjbutton-color': qjbutton }\">\r\n\t\t\r\n\t\t<view class=\"fee_one fee_one_yi\">\r\n\t\t\t<view class=\"fee_one_t\">反馈类型</view>\r\n\t\t\t<view class=\"fee_one_b\">\r\n\t\t\t\t<view class=\"fee_one_b_li\" :class=\"type == index ? 'fee_one_b_li_ac' : ''\" @click=\"tabTap(index)\" v-for=\"(item,index) in feedLists\" :key=\"index\">\r\n\t\t\t\t\t{{item}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"fee_one fee_two\">\r\n\t\t\t<view class=\"fee_one_t\">反馈内容</view>\r\n\t\t\t<textarea placeholder=\"问题描述的越详细，有助于我们更快的解决问题\" placeholder-style=\"color: #999999;\" v-model=\"contents\"></textarea>\r\n\t\t\t<view class=\"fee_two_tp\">\r\n\t\t\t\t<view class=\"fee_two_tp_li\" v-for=\"(item,index) in uploadimgs\" :key=\"index\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrl + item\" mode=\"aspectFill\" class=\"fee_two_tp_li_tp\"></image>\r\n\t\t\t\t\t<image src=\"/static/images/icon49-1.png\" class=\"fee_two_tp_li_gb\" @click=\"uploadImgDel(index)\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fee_two_tp_li\" @click=\"uploadImgTap\">\r\n\t\t\t\t\t<image src=\"/static/images/icon50.png\" class=\"fee_two_tp_li_tp\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<view class=\"fee_one fee_thr\">\r\n\t\t\t<view class=\"fee_one_t\">请留下您的联系方式</view>\r\n\t\t\t<input type=\"text\" placeholder=\"手机号/邮箱/QQ号\" placeholder-style=\"color: #999999;\" v-model=\"contact_mode\" />\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"lea_two_sub\" @click=\"fkSubTap\">提交</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tfeedbackCateApi,\r\n\tfeedbackSubApi,\r\n\tupImg\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tfeedLists:[],\r\n\t\t\tuploadimgs:[],\r\n\t\t\ttype:-1,\r\n\t\t\tcontents:'',\r\n\t\t\tcontact_mode:'',//联系方式\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.feedbackCateData();\r\n\t},\r\n\tmethods: {\r\n\t\t//用户反馈\r\n\t\tfkSubTap(){\r\n\t\t\tif (this.type == -1) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t\ttitle: '请选择反馈类型',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (this.contents.split(\" \").join(\"\").length == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t\ttitle: '请输入反馈内容',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (this.contact_mode.split(\" \").join(\"\").length == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t\ttitle: '请输入联系方式',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tfeedbackSubApi({\r\n\t\t\t\ttype:that.feedLists[that.type],\r\n\t\t\t\tproblem:that.contents,\r\n\t\t\t\timage:that.uploadimgs.join(','),\r\n\t\t\t\tcontact_mode:that.contact_mode\r\n\t\t\t}).then(res => {\r\n\t\t\t\tconsole.log('分类',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'提交成功',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tuni.navigateBack({})\r\n\t\t\t\t\t},1000);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//分类\r\n\t\tfeedbackCateData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tfeedbackCateApi({}).then(res => {\r\n\t\t\t\tconsole.log('分类',res)\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.feedLists = res.data;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//上传图片\r\n\t\tuploadImgTap() {\r\n\t\t\tlet that = this\r\n\t\t\tlet num = 5 - that.uploadimgs.length\r\n\t\t\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: num, //默认9\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '上传中'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconst tempFilePaths = res.tempFilePaths\r\n\t\t\t\t\tconsole.log(tempFilePaths);\r\n\t\t\t\t\t// const tempFilePaths = res.tempFiles\r\n\t\t\t\t\tfor (let i = 0; i < tempFilePaths.length; i++) {\r\n\t\t\t\t\t\tupImg(tempFilePaths[i], 'file', ).then(ress => {\r\n\t\t\t\t\t\t\tif (ress.code == 1) {\r\n\t\t\t\t\t\t\t\tif (i == tempFilePaths.length - 1) {\r\n\t\t\r\n\t\t\t\t\t\t\t\t\tthat.$toast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '上传完毕',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// that.$toast({\r\n\t\t\t\t\t\t\t\t// \ttitle: '上传成功',\r\n\t\t\t\t\t\t\t\t// \ticon: 'success'\r\n\t\t\t\t\t\t\t\t// })\r\n\t\t\r\n\t\t\t\t\t\t\t\tthat.uploadimgs.push(ress.data.file.url)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t})\r\n\t\t\r\n\t\t},\r\n\t\tuploadImgDel(index, type) {\r\n\t\t\tthis.uploadimgs.splice(index, 1)\r\n\t\t},\r\n\t\ttabTap(index){\r\n\t\t\tthis.type = index;\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.feedback{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedback.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./feedback.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752116123707\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}