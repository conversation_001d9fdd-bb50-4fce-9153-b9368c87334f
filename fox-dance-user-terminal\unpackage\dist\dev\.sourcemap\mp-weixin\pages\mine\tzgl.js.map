{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzgl.vue?e0c9", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzgl.vue?c115", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzgl.vue?a13b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzgl.vue?7c1b", "uni-app:///pages/mine/tzgl.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzgl.vue?b726", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzgl.vue?41d4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "ewm<PERSON><PERSON><PERSON>", "succrss<PERSON>s<PERSON><PERSON>gle", "errTs<PERSON>oggle", "glInfo", "bind_status", "qrcode", "onShow", "methods", "wemTap", "uni", "title", "console", "that", "icon", "ewmEndTap", "noticeManageData", "xxtsTap", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAotB,CAAgB,msBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACsExuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;QAAAC;MAAA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;MACA;QACA;QACAC;UACAC;QACA;QACA;QACA;UACAC;UACA;YACAF;YACAG;YACAH;cACAI;cACAH;YACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;IACA;IACA;IACAC;MACAN;QACAC;MACA;MACA;MACA;QACAC;QACAF;QACA;UACAG;QACA;MACA;IACA;IACA;IACAI;MAEAP;QACAC;MACA;MACA;MACA;QACAC;QACAF;QACA;UACAG;UACAA;QACA;UACAA;UACAA;QACA;MACA;IAEA;IACAK;MACAR;QACAS;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/JA;AAAA;AAAA;AAAA;AAA21C,CAAgB,svCAAG,EAAC,C;;;;;;;;;;;ACA/2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/tzgl.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/tzgl.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tzgl.vue?vue&type=template&id=3ef73b04&\"\nvar renderjs\nimport script from \"./tzgl.vue?vue&type=script&lang=js&\"\nexport * from \"./tzgl.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tzgl.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/tzgl.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzgl.vue?vue&type=template&id=3ef73b04&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.succrssTsToggle = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.errTsToggle = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzgl.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzgl.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tzgl\">\r\n\t\t<view class=\"tzglOne\">\r\n\t\t\t<view class=\"tzglOne_t\" @click=\"navTo('/pages/mine/tzglxq')\">\r\n\t\t\t\t<view class=\"tzglOne_t_l\">小程序消息管理</view>\r\n\t\t\t\t<view class=\"tzglOne_t_r\">详细设置<image src=\"/static/images/right_more.png\"></image></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t \r\n\t\t<view class=\"tzglTwo\">\r\n\t\t\t<view class=\"tzglOne_t\">\r\n\t\t\t\t<view class=\"tzglOne_t_l\">公众号消息设置</view>\r\n\t\t\t\t<view class=\"tzglOne_t_r\" v-if=\"glInfo.bind_status == 0\"><image src=\"/static/images/icon88.png\"></image>未绑定</view>\r\n\t\t\t\t<view class=\"tzglOne_t_r\" style=\"color:#131315\"  v-if=\"glInfo.bind_status == 1\"><image src=\"/static/images/icon88-1.png\"></image>已绑定</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tzglTwo_b\">\r\n\t\t\t\t<view @click=\"wemTap\"><image src=\"/static/images/icon89.png\"></image>{{glInfo.bind_status == 0 ? '绑定微信公众号' : glInfo.bind_status == 1 ? '解绑微信公众号' : ''}}</view>\r\n\t\t\t\t<view @click=\"xxtsTap\"><image src=\"/static/images/icon90.png\"></image>消息推送测试</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 绑定二维码 go -->\r\n\t\t<view class=\"bdewmCon\" v-if=\"ewmToggle\">\r\n\t\t\t<view class=\"bdewmCon_t\">\r\n\t\t\t\t<view class=\"bdewmCon_t_a\">绑定二维码</view>\r\n\t\t\t\t<image :src=\"glInfo.qrcode\" mode=\"aspectFill\" class=\"bdewmCon_t_b\" :show-menu-by-longpress=\"true\"></image>\r\n\t\t\t\t<view class=\"bdewmCon_t_c\">使用说明</view>\r\n\t\t\t\t<view class=\"bdewmCon_t_d\"><text></text>长按二维码围片识别<text></text></view>\r\n\t\t\t</view>\r\n\t\t\t<image src=\"/static/images/popup_close1.png\" class=\"bdewmCon_b\" @click=\"ewmEndTap\"></image>\r\n\t\t</view>\r\n\t\t<!-- 绑定二维码 end -->\r\n\t\t\r\n\t\t\r\n\t\t<!-- 提示 go -->\r\n\t\t<view class=\"tstsTanc\" v-if=\"succrssTsToggle\">\r\n\t\t\t<view class=\"tstsTanc_n\">\r\n\t\t\t\t<image src=\"/static/images/icon91.png\" class=\"tstsTanc_bj\"></image>\r\n\t\t\t\t<view class=\"tstsTanc_n_n\">\r\n\t\t\t\t\t<view class=\"tstsTanc_a\">提示</view>\r\n\t\t\t\t\t<view class=\"tstsTanc_b\">通知推送完毕，请查收，如您未收到消息，请联系商家。</view>\r\n\t\t\t\t\t<view class=\"tstsTanc_c\" @click=\"succrssTsToggle = false\">我知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 提示 end -->\r\n\t\t\r\n\t\t<!-- 提示 go -->\r\n\t\t<view class=\"tstsTanc\" v-if=\"errTsToggle\">\r\n\t\t\t<view class=\"tstsTanc_n\">\r\n\t\t\t\t<image src=\"/static/images/icon91-1.png\" class=\"tstsTanc_bj\"></image>\r\n\t\t\t\t<view class=\"tstsTanc_n_n\">\r\n\t\t\t\t\t<view class=\"tstsTanc_a\">提示</view>\r\n\t\t\t\t\t<view class=\"tstsTanc_b\">通知消息推送不成功，请检查以下可能</view>\r\n\t\t\t\t\t<view class=\"tstsTanc_b tstsTanc_b_cha\"><text></text>您的账号暂未绑定公众号，请先关注</view>\r\n\t\t\t\t\t<view class=\"tstsTanc_c\" @click=\"errTsToggle = false\">我知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 提示 end -->\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tjcBindApi,\r\n\tnoticeManageApi,\r\n\tpushTestingApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tewmToggle:false,\r\n\t\t\tsuccrssTsToggle:false,\r\n\t\t\terrTsToggle:false,\r\n\t\t\tglInfo:{bind_status:-1,qrcode:''}\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.noticeManageData(); //通知管理二维码\r\n\t\tthis.ewmToggle = false;\r\n\t},\r\n\tmethods: {\r\n\t\t//公众号绑定/解绑\r\n\t\twemTap(){\r\n\t\t\tif(this.glInfo.bind_status == 0){\r\n\t\t\t\tthis.ewmToggle = true;\r\n\t\t\t}else{\r\n\t\t\t\t//解绑二维码\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tjcBindApi({}).then(res => {\r\n\t\t\t\t\tconsole.log('解绑二维码',res);\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.noticeManageData();//通知管理二维码\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '解绑成功'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t//关闭二维码弹窗\r\n\t\tewmEndTap(){\r\n\t\t\tthis.noticeManageData(); //通知管理二维码\r\n\t\t\tthis.ewmToggle = false;\r\n\t\t},\r\n\t\t//通知管理二维码\r\n\t\tnoticeManageData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tnoticeManageApi({}).then(res => {\r\n\t\t\t\tconsole.log('通知管理二维码',res);\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.glInfo = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//消息推送\r\n\t\txxtsTap(){\r\n\t\t\t\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '推送中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tpushTestingApi({}).then(res => {\r\n\t\t\t\tconsole.log('消息推送测试',res);\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthat.succrssTsToggle = true;\r\n\t\t\t\t\tthat.errTsToggle = false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthat.succrssTsToggle = false;\r\n\t\t\t\t\tthat.errTsToggle = true;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.tzgl{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzgl.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzgl.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752114330524\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}