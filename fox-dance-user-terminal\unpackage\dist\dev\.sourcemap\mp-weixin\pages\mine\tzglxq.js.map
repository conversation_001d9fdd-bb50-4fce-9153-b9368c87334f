{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzglxq.vue?5fd3", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzglxq.vue?9c78", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzglxq.vue?36aa", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzglxq.vue?970c", "uni-app:///pages/mine/tzglxq.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzglxq.vue?58b9", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/tzglxq.vue?0b49"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "lists", "xzIndex", "onShow", "onLoad", "methods", "detailSettingData", "uni", "title", "console", "res", "that", "xzTap", "switch1Change", "type", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAstB,CAAgB,qsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2B1uB;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAC;IACA;MACAC;MACAC;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAPA,CAQA;MACAC;IAEA;EACA;EACAC,2BAEA;EACAC;IACA;EACA;;EACAC;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACAC;QACAF;QACA;UACA;YACAG;UACA;UACAC;QACA;MACA;IACA;IACAC;MACAH;MACA;IACA;IACAI;MACA;AACA;AACA;;MAGAN;QACAC;MACA;MACA;MACA;QAAAM;MAAA;QACAL;QACA;UACAF;UACAI;QACA;MACA;IAEA;IAEAI;MACAR;QACAS;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAA61C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACAj3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/tzglxq.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/tzglxq.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tzglxq.vue?vue&type=template&id=4ed5eb77&\"\nvar renderjs\nimport script from \"./tzglxq.vue?vue&type=script&lang=js&\"\nexport * from \"./tzglxq.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tzglxq.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/tzglxq.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzglxq.vue?vue&type=template&id=4ed5eb77&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzglxq.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzglxq.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tzglxq\">\r\n\t\t\r\n\t\t<!-- <view class=\"tzxq_one\">您当前已设置消息，接收通知0条，未能接收12条我们非常在意您的体验，不会肆意骚扰，请放心接受以下提醒</view> -->\r\n\t\t\r\n\t\t<view class=\"tzxq_two\">\r\n\t\t\t<view class=\"tzxq_two_t\"><text></text>以下通知未生效，点击可恢复接收:</view>\r\n\t\t\t<view class=\"tzxq_two_b\">\r\n\t\t\t\t <!-- @click=\"xzTap(index)\" -->\r\n\t\t\t\t<view class=\"tzxq_two_b_li\" v-for=\"(item,index) in lists\" :key=\"index\">\r\n\t\t\t\t\t{{item.name}}<switch :checked=\"item.select\" @change=\"switch1Change(index,item)\" color=\"#131315\" style=\"transform:scale(0.7);position: relative;left:14rpx;\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tdetailSettingApi,\r\n\tsetStatusApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tlists:[\r\n\t\t\t\t/*{name:'账号绑定通知',id:1,select:true},\r\n\t\t\t\t{name:'开课提醒',id:2,select:false},\r\n\t\t\t\t{name:'会员卡过期提醒',id:3,select:false},\r\n\t\t\t\t{name:'报名结果通知',id:4,select:false},\r\n\t\t\t\t{name:'会员卡状态提醒',id:5,select:false},\r\n\t\t\t\t{name:'会员预约课程提醒',id:6,select:false},\r\n\t\t\t\t{name:'服务人员变更提醒',id:7,select:false},\r\n\t\t\t\t{name:'签到成功通知',id:8,select:false}*/\r\n\t\t\t],\r\n\t\t\txzIndex:-1,\r\n\t\t\t\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.detailSettingData();//详细设置\r\n\t},\r\n\tmethods: {\r\n\t\t//详细设置\r\n\t\tdetailSettingData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tdetailSettingApi({}).then(res => {\r\n\t\t\t\tconsole.log('详细设置',res);\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tfor(var i=0;i<res.data.length;i++){\r\n\t\t\t\t\t\tres.data[i].select = res.data[i].status == 0 ? false : true\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.lists = res.data;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\txzTap(index){\r\n\t\t\tconsole.log(index,'index');\r\n\t\t\tthis.xzIndex = index;\r\n\t\t},\r\n\t\tswitch1Change(e,item){\r\n\t\t\t/*console.log('itemm',item)\r\n\t\t\tthis.lists[e].select = !this.lists[e].select;\r\n\t\t\tconsole.log(this.lists,'this.lists')*/\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tsetStatusApi({type:item.type}).then(res => {\r\n\t\t\t\tconsole.log('设置通知状态',res);\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.lists[e].select = !that.lists[e].select\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\t\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.tzglxq{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzglxq.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tzglxq.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752112954027\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}