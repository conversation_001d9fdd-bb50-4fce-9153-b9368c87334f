{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/confirmOrder.vue?46f7", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/confirmOrder.vue?f6b9", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/confirmOrder.vue?5ffb", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/confirmOrder.vue?4fbe", "uni-app:///pages/prizedraw/confirmOrder.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/confirmOrder.vue?99ba", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/confirmOrder.vue?ea45"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLogined", "productxq", "name", "image", "jpid", "imgbaseUrl", "remark", "s<PERSON><PERSON><PERSON><PERSON>", "area", "qj<PERSON>ton", "onShow", "onLoad", "methods", "dhSubTap", "uni", "icon", "title", "duration", "id", "addr_id", "console", "url", "goToAddr", "addressData", "page", "size", "arrdArr", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4tB,CAAgB,2sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC+ChvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;QAAAF;MAAA;IACA;EACA;EACAG;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;MACAH;QACAE;MACA;MACA;QACA;UACAE;UACAC;UACAb;QACA;UACAc;UACA;YACAN;YACAA;cACAO;YACA;UACA;QACA;MACA;MACA;QACA;UACAH;UACAC;UACAb;QACA;UACAc;UACA;YACAN;YACAA;cACAO;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAR;QACAO;MACA;IACA;IACA;IACAE;MAAA;MACA;QAAAC;QAAAC;MAAA;QACA;UACAL;UACA;UACA;YACA;cACAM;YACA;UACA;UACA;YACA;cAAAlB;YAAA;UACA;YACA;YACAM;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAa;MACAb;QACAO;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACnKA;AAAA;AAAA;AAAA;AAAm2C,CAAgB,8vCAAG,EAAC,C;;;;;;;;;;;ACAv3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/prizedraw/confirmOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/prizedraw/confirmOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./confirmOrder.vue?vue&type=template&id=413081c0&\"\nvar renderjs\nimport script from \"./confirmOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./confirmOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./confirmOrder.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prizedraw/confirmOrder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=template&id=413081c0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"confirmOrder jpdh\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"productxq.jpid\">\r\n\t\t\r\n\t\t<view class=\"qrdd_a\" @click=\"goToAddr('diancan')\" v-if=\"shouAddr.area == ''\"><image src=\"/static/images/icon33.png\"></image>添加收货地址</view>\r\n\t\t<view class=\"qrdd_b\" @click=\"goToAddr('diancan')\" v-else>\r\n\t\t\t<view class=\"qrdd_b_a\"><image src=\"/static/images/icon38.png\"></image>{{shouAddr.name}} {{shouAddr.phone}}</view>\r\n\t\t\t<view class=\"qrdd_b_b\">{{shouAddr.area+shouAddr.detail}}</view>\r\n\t\t\t<image src=\"/static/images/index_shop_more.png\" class=\"qrdd_b_jt\"></image>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"qrdd_c\">\r\n\t\t\t<view class=\"qrdd_c_li\">\r\n\t\t\t\t<image :src=\"imgbaseUrl + productxq.image\" mode=\"aspectFill\" class=\"qrdd_c_li_l\"></image>\r\n\t\t\t\t<!-- <image src=\"/static/images/icon23.jpg\" mode=\"aspectFill\" class=\"qrdd_c_li_l\"></image> -->\r\n\t\t\t\t<view class=\"qrdd_c_li_r\">\r\n\t\t\t\t\t<view class=\"qrdd_c_li_r_a\">{{productxq.name}}</view>\r\n\t\t\t\t\t<!-- <view class=\"qrdd_c_li_r_b\">已选：420g</view> -->\r\n\t\t\t\t\t<view class=\"qrdd_c_li_r_c\"><view></view><text>x1</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"qrdd_d\">\r\n\t\t\t<view>运费</view>\r\n\t\t\t<input type=\"text\" :disabled=\"true\" value=\"包邮\"  placeholder-style=\"color: #999999;\" />\r\n\t\t</view>\r\n\t\t<view class=\"qrdd_d qrdd_bz\">\r\n\t\t\t<view>备注</view>\r\n\t\t\t<input type=\"text\" placeholder=\"如有特殊要求，请填写\" v-model=\"remark\" placeholder-style=\"color: #999999;\" />\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<view class=\"peodex_foo\">\r\n\t\t\t<view class=\"peodex_foo_l\">共1件<text></text></view>\r\n\t\t\t<view class=\"peodex_foo_r\" @click=\"dhSubTap\">兑换</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\texchangeGoodsApi,\r\n\taddrList,\r\n\texchangeGoodsLevelApi\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLogined:true,\r\n\t\t\tproductxq:{\r\n\t\t\t\tname:'',\r\n\t\t\t\timage:'',\r\n\t\t\t\tjpid:0\r\n\t\t\t},\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tremark:'',\r\n\t\t\tshouAddr:{area:''},\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tif (uni.getStorageSync('diancan')) {\r\n\t\t\tthis.shouAddr = uni.getStorageSync('diancan')\r\n\t\t}else{\r\n\t\t\tthis.shouAddr = {area:''}\r\n\t\t}\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\t// this.productxq = JSON.parse(option.productxq);\r\n\t\tthis.addressData();\r\n\t\tthis.productxq = uni.getStorageSync('dhspGoods')\r\n\t},\r\n\tmethods: {\r\n\t\t//提交兑换\r\n\t\tdhSubTap(){\r\n\t\t\tif(this.shouAddr.area == ''){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t\ttitle:'请选择收货地址~',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tvar that = this;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tif(uni.getStorageSync('dy_type') == 'cj'){\r\n\t\t\t\texchangeGoodsApi({\r\n\t\t\t\t\tid:uni.getStorageSync('dhspGoods').jpid,\r\n\t\t\t\t\taddr_id:that.shouAddr.id,\r\n\t\t\t\t\tremark:that.remark\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log('抽奖-提交兑换',res)\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\turl:'/pages/prizedraw/success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif(uni.getStorageSync('dy_type') == 'dj'){\r\n\t\t\t\texchangeGoodsLevelApi({\r\n\t\t\t\t\tid:uni.getStorageSync('dhspGoods').jpid,\r\n\t\t\t\t\taddr_id:that.shouAddr.id,\r\n\t\t\t\t\tremark:that.remark\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tconsole.log('等级-提交兑换',res)\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\turl:'/pages/prizedraw/success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t//收货地址\r\n\t\tgoToAddr(type) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/mine/address?type=${type}`\r\n\t\t\t})\r\n\t\t},\r\n\t\t//收货地址\r\n\t\taddressData(){\r\n\t\t\taddrList({page: 1,size: 999,}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tconsole.log(res,'地址列表')\r\n\t\t\t\t\tvar arrdArr = [];\r\n\t\t\t\t\tfor(var i=0;i<res.data.length;i++){\r\n\t\t\t\t\t\tif(res.data[i].is_default == 1){\r\n\t\t\t\t\t\t\tarrdArr.push(res.data[i])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(arrdArr.length == 0){\r\n\t\t\t\t\t\tthis.shouAddr = {area:''}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.shouAddr = arrdArr[0]\r\n\t\t\t\t\t\tuni.setStorageSync('diancan',arrdArr[0])\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.mescroll.endErr()\r\n\t\t\t\t\t// this.mescroll.endSuccess();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.confirmOrder{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./confirmOrder.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752117066392\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}