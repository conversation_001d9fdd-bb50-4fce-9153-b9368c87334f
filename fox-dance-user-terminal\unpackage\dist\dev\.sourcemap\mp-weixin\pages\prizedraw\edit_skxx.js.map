{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/edit_skxx.vue?bde4", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/edit_skxx.vue?a9ab", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/edit_skxx.vue?a41b", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/edit_skxx.vue?b6f3", "uni-app:///pages/prizedraw/edit_skxx.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/edit_skxx.vue?279f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgbaseUrl", "isLogined", "productxq", "id", "onShow", "onLoad", "methods", "userData", "uni", "title", "console", "that", "bcSubTap", "icon", "duration", "payment_code", "setTimeout", "UploadImg", "count", "sizeType", "sourceType", "success", "driver", "change", "navTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAytB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkC7uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;IAAA,qDACA,sDACA,2DACA,qDACA;EAEA;EACAC,2BAEA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;MACA;MACA;QACA;UACAC;UACAC;UACAH;QACA;MACA;IACA;IACA;IACAI;MAAA;MACA;QACAJ;UACAC;UACAI;UACAC;QACA;QACA;MACA;MACAN;QACAC;MACA;MACA;MACA;QACAM;MACA;QACA;UACAP;UACAA;UACAA;YACAC;YACAI;YACAC;UACA;UACAE;YACAR;UACA;QACA;MACA;IACA;IACA;IACAS;MACA;MACAT;QACAU;QACAC;QACAC;QACAC;UACA;UACAb;YACAC;UACA;UACA;YAAAa;UAAA;YACAZ;YACA;cACAF;cACAG;cACAD;YACA;UACA;QAEA;MACA;IAEA;IACAa;MACAb;MACA;IACA;IACAc;MACAhB;QACAiB;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC9IA;AAAA;AAAA;AAAA;AAAg2C,CAAgB,2vCAAG,EAAC,C", "file": "pages/prizedraw/edit_skxx.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/prizedraw/edit_skxx.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit_skxx.vue?vue&type=template&id=e3de8dea&\"\nvar renderjs\nimport script from \"./edit_skxx.vue?vue&type=script&lang=js&\"\nexport * from \"./edit_skxx.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit_skxx.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prizedraw/edit_skxx.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_skxx.vue?vue&type=template&id=e3de8dea&\"", "var components\ntry {\n  components = {\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-switch/u-switch\" */ \"@/components/uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_skxx.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_skxx.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"confirmOrder edit_skxx\" :style=\"{ '--qjbutton-color': qjbutton }\" v-if=\"productxq.id\">\r\n\t\t \r\n\t\t<view class=\"hbdhCon_a\">\r\n\t\t\t<image src='/static/images/icon62.png' class=\"hbdhCon_a_bj\"></image>\r\n\t\t\t<view class=\"hbdhCon_a_n\"  @click=\"UploadImg\">\r\n\t\t\t\t<image :src=\"payment_code == null || payment_code == '' ? '/static/images/icon70.png' : imgbaseUrl + payment_code\" class=\"hbdhCon_a_l\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"hbdhCon_a_r\">{{payment_code == null || payment_code == '' ? '点击添加微信收款码' : '点击更换微信收款码'}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"hbdhCon_b\">\r\n\t\t\t<view class=\"hbdhCon_b_t\">\r\n\t\t\t\t<view>设为默认收款账户</view>\r\n\t\t\t\t<text>每次兑换会默认使用该账户</text>\r\n\t\t\t</view>\r\n\t\t\t<u-switch :active-color=\"qjbutton\" size=\"40\" v-model=\"switchVal\" @change=\"change\"></u-switch>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\t\t\r\n\t\t<view class=\"peodex_foo\">\r\n\t\t\t<view class=\"peodex_foo_r\" @click=\"bcSubTap\">保存</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n\tupImg,\r\n\tuserInfoApi,\r\n\tpaymentcodeApi,\r\n} from '@/config/http.achieve.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tisLogined:true,\r\n\t\t\tproductxq:{id:1},\r\n\t\t\timgbaseUrl:'',\r\n\t\t\tswitchVal: true,\r\n\t\t\tpayment_code:'',\r\n\t\t\tqjbutton:'#131315',\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\t\r\n\t},\r\n\tonLoad(option) {\r\n\t\tthis.qjbutton = uni.getStorageSync('storeInfo').button\r\n\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\tthis.userData();//获取收款信息\r\n\t\tthis.switchVal = uni.getStorageSync('skxxMr') == 1 ? true : false\r\n\t},\r\n\tmethods: {\r\n\t\t//获取收款信息\r\n\t\tuserData(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tuserInfoApi({}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tconsole.log('个人信息',res);\r\n\t\t\t\t\tthat.payment_code = res.data.payment_code;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t//提交收款信息\r\n\t\tbcSubTap(){\r\n\t\t\tif(this.payment_code == null || this.payment_code == ''){\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请传收款信息',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t})\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中'\r\n\t\t\t});\r\n\t\t\tlet that = this;\r\n\t\t\tpaymentcodeApi({\r\n\t\t\t\tpayment_code:that.payment_code\r\n\t\t\t}).then(res => {\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tuni.setStorageSync('skxxMr',this.switchVal ? 1 : 2)\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tuni.navigateBack({})\r\n\t\t\t\t\t},1500)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 更换头像\r\n\t\tUploadImg() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: 1,\r\n\t\t\t\tsizeType: ['original', 'compressed'],\r\n\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconst tempFilePaths = res.tempFilePaths[0]\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle:'加载中'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tupImg(tempFilePaths, 'file',{driver:'cos'} ).then(ress => {\r\n\t\t\t\t\t\tconsole.log('上传图片',ress)\r\n\t\t\t\t\t\tif (ress.code == 1) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tthat.payment_code = ress.data.file.url\r\n\t\t\t\t\t\t\tconsole.log(that.payment_code,'that.payment_code')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\r\n\t\t},\r\n\t\tchange(e) {\r\n\t\t\tconsole.log('change', );\r\n\t\t\tthis.switchVal = e\r\n\t\t},\r\n\t\tnavTo(url){\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl:url\r\n\t\t\t})\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.confirmOrder{overflow: hidden;}\r\npage{padding-bottom: 0;}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_skxx.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit_skxx.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}