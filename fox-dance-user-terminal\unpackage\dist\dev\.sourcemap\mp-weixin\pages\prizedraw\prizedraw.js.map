{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/prizedraw.vue?2a0f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/prizedraw.vue?478a", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/prizedraw.vue?6c04", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/prizedraw.vue?39fa", "uni-app:///pages/prizedraw/prizedraw.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/prizedraw.vue?f935", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/prizedraw/prizedraw.vue?462a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgbaseUrlOss", "imgbaseUrl", "loding", "height", "noticeText", "box_skins", "navBg", "navHeight", "titleStyle", "fontWeight", "fontSize", "color", "titleStyle_on", "dc<PERSON><PERSON><PERSON>", "slcToggle", "istgdhToggle", "cjzLoding", "cjType", "jpLists", "cjNum", "zjjgLists", "cjId", "countdown", "timer", "cj_moreNum", "cjdh_loding", "cj<PERSON><PERSON>", "jinzLd", "onLoad", "onUnload", "console", "clearInterval", "methods", "goodsSpTo", "uni", "url", "prizedrawData", "title", "that", "prize", "noticeArr", "res", "cjStartTap", "icon", "duration", "type", "setTimeout", "tgdhTap", "onPageScroll", "scrollTop", "e", "frontColor", "backgroundColor", "navbarHeight", "startOne", "<PERSON>h", "navTo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AAC0L;AAC1L,gBAAgB,8LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAytB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0K7uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;QACAC;QACAC;MACA;MACAE;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAlBA,CAmBA;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;;EACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;QACAG;MACA;MAEA;MACA;QACAP;QACA;UACAQ;UACA;YACA;YACA;YACA;cACA;AACA;AACA;AACA;cACA,+GACAC,0EACAA,0EACAA;cACAC,iFACAzC;YACA;YACAuC;YACA;UACA;;UACA;YACA;cACAG;YACA;cACAA;YACA;UACA;UAEAH;UACAA;UACAJ;QACA;MACA;IACA;IACA;IACAQ;MAAA;MACA;MACA;QACAR;UACAS;UACAC;UACAP;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;;MAEA;MACAH;QACAG;MACA;MACA;QACAQ;MACA;QACA;QACA;UACAX;UACAI;UACAA;UACAA;;UAEA;UACA;UACAQ;YACAR;UACA;QAEA;MACA;IACA;IACA;IACAS;MACA;MACA;MACA;QACA;;QAEA;QACAhB;QAEA;QACA;QACA;QACA;MAEA;QACA;UACAG;YACAS;YACAN;YACAO;UACA;UACA;QACA;QACAN;;QAEA;QACA;QACAP;QACA;QACA;QACA;UACAO;UACA;YACA;YACA;YACAP;YACA;YACA;YACA;YACA;UACA;QACA;QACA;AACA;AACA;AACA;QACA;UACA;QACA;QACAO;QACAA;QACAQ;UACAR;QACA;QACAQ;UACAR;QACA;MACA;IAGA;IACAU;MACA;MACA,IACAC,YACAC,EADAD;MAEA;MACA;MAEAf;QACAiB;QACAC;MACA;IAEA;IACA;IACAC;MAKA;MACA;MACA;MACA;MACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACAvB;UACAO;UACAR;UAEA;YACA;YACAQ;YACAA;UACA;UAEA;YACA;YACAA;YACA;cACA;cACAA;cACAP;cACAO;cACAA;cACAA;cACA;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;IACAiB;MACA;AACA;AACA;AACA;AACA;IAJA,CAKA;IACAC;MACAtB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5dA;AAAA;AAAA;AAAA;AAA0hC,CAAgB,+9BAAG,EAAC,C;;;;;;;;;;;ACA9iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/prizedraw/prizedraw.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/prizedraw/prizedraw.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./prizedraw.vue?vue&type=template&id=bcee8718&\"\nvar renderjs\nimport script from \"./prizedraw.vue?vue&type=script&lang=js&\"\nexport * from \"./prizedraw.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prizedraw.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prizedraw/prizedraw.vue\"\nexport default component.exports", "export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prizedraw.vue?vue&type=template&id=bcee8718&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-navbar/u-navbar\" */ \"@/components/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uniNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar\" */ \"@/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue\"\n      )\n    },\n    openOneBox: function () {\n      return import(\n        /* webpackChunkName: \"components/open-one-box/open-one-box\" */ \"@/components/open-one-box/open-one-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.dcToggle = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.slcToggle = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.slcToggle = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prizedraw.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prizedraw.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"prizedraw\" v-if=\"loding\">\r\n\r\n\t\t<u-navbar title=\"抽奖\" :border-bottom=\"false\" :back-icon-color=\"navBg == 1 ? '#fff' : '#fff'\"\r\n\t\t\t:title-color=\"navBg == 1 ? '#fff' : '#fff'\" :background=\"navBg == 1 ? { background: '#060505' } : ''\">\r\n\t\t</u-navbar>\r\n\t\t \r\n\t\t<view class=\"pri_one\" :style=\"'margin-top:-'+navHeight+'px'\">\r\n\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon63.png'\" class=\"pri_one_bj\">\r\n\t\t\t\t<view class=\"pri_one_a\" :style=\"'margin-top:'+(navHeight+10)+'px'\" v-if=\"noticeText != ''\">\r\n\t\t\t\t\t<uni-notice-bar scrollable single :text=\"noticeText\" background-color=\"transparent\" color=\"#fff\"\r\n\t\t\t\t\t\t:single=\"true\" speed=\"50\"> </uni-notice-bar>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view :style=\"'margin-top:'+(navHeight+10)+'px;width: 100%;overflow: hidden;height: 54rpx;'\"\r\n\t\t\t\t\tv-if=\"noticeText == ''\"></view>\r\n\t\t\t\t<view class=\"pri_one_b\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon64.png'\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"pri_one_c\" @click=\"navTo('/pages/prizedraw/winningrecord')\">\r\n\t\t\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon66.png'\" />\r\n\t\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"pri_two\">\r\n\t\t\t<view class=\"pri_two_t\">\r\n\t\t\t\t<image src=\"/static/images/icon67.png\"></image>奖池预览<image src=\"/static/images/icon67.png\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pri_two_b\">\r\n\t\t\t\t<view class=\"pri_two_b_li\" v-for=\"(item,index) in jpLists\" :key=\"index\" v-if=\"item.type*1 != 4\">\r\n\t\t\t\t\t<image src=\"/static/images/icon71.png\" class=\"pri_two_b_li_bj\"></image>\r\n\t\t\t\t\t<view class=\"pri_two_b_li_t\">\r\n\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"pri_two_b_li_b\">{{item.namezs}}</view>\r\n\t\t\t\t\t<view class=\"pri_two_b_li_num\">{{item.remaining_num + '/' + item.frequency}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- ios底部安全距离 go -->\r\n\t\t<view class=\"aqjlViw\"></view>\r\n\t\t<!-- ios底部安全距离 end -->\r\n\r\n\t\t<view class=\"pri_foo\">\r\n\t\t\t<image :src=\"imgbaseUrlOss + '/userreport/icon65.png'\" class=\"pri_foo_bj\" />\r\n\t\t\t<view class=\"pri_foo_t\"><text></text>\r\n\t\t\t\t<view>剩余抽奖次数：{{cjNum}}次</view><text></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pri_foo_b\">\r\n\t\t\t\t<view @click=\"cjStartTap(1)\"></view>\r\n\t\t\t\t<view @click=\"cjStartTap(10)\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\r\n\r\n\r\n\t\t<!-- 抽奖中弹窗 go -->\r\n\t\t<view class=\"cjtcTc cjloding\" v-if=\"cjzLoding\">\r\n\t\t\t<view class=\"cjtcTc_n\" @click.stop>\r\n\t\t\t\t<image src=\"/static/images/icon68.png\" class=\"cjtcTc_sltitle\"></image>\r\n\t\t\t\t<view class=\"cjtcslTc_a\">\r\n\t\t\t\t\t<!-- <open-one-box ref=\"oneBox\" :list=\"box_skins\" :result=\"cjId\" @finsh=\"finsh\"> -->\r\n\t\t\t\t\t<open-one-box ref=\"oneBox\" :list=\"jpLists\" :result=\"cjId\" @finsh=\"finsh\">\r\n\t\t\t\t\t\t<!-- 可以使用插槽自定内容 -->\r\n\t\t\t\t\t\t<!-- <template v-slot=\"{ item }\">\r\n\t\t\t\t\t\t\t<image :src=\"item.img\" style=\"width: 100rpx;height: 100rpx;\"></image>\r\n\t\t\t\t\t\t</template> -->\r\n\t\t\t\t\t</open-one-box>\r\n\t\t\t\t\t<view class=\"cjloding_xian\"></view>\r\n\t\t\t\t\t<view class=\"boxshadow_l\"></view>\r\n\t\t\t\t\t<view class=\"boxshadow_r\"></view>\r\n\t\t\t\t\t<!-- <view style=\"display: flex;align-items: center;justify-content: center;margin-top: 30rpx;\">\r\n\t\t\t\t\t\t<button @click=\"startOne\" style=\"width: 200rpx;background: #278891;\">开始--{{cjId}}</button>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cjtcslTc_f\">\r\n\t\t\t\t\t<view @click=\"tgdhTap\">\r\n\t\t\t\t\t\t<image :src=\"istgdhToggle ? '/static/images/cjxz-1.png' : '/static/images/cjxz.png'\"></image>\r\n\t\t\t\t\t\t跳过动画\r\n\t\t\t\t\t\t<text v-if=\"cjType == 10\" style=\"font-size:26rpx;\">（第{{cj_moreNum+1}}轮）</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 抽奖中弹窗 end -->\r\n\r\n\r\n\t\t<!-- 单抽奖品弹窗 go -->\r\n\t\t<view class=\"cjtcTc\" v-if=\"dcToggle\" @click=\"dcToggle = false\">\r\n\t\t\t<view class=\"cjtcTc_n\" @click.stop>\r\n\t\t\t\t<image :src=\"zjjgLists[0].type*1 == 4 ? '/static/images/icon86.png' : '/static/images/icon69.png'\"\r\n\t\t\t\t\tclass=\"cjtcTc_title\" :style=\"zjjgLists[0].type*1 == 4 ? 'width:398rpx;height:162rpx;' : ''\"></image>\r\n\t\t\t\t<view class=\"cjtcTc_a\" style=\"height:476rpx;\">\r\n\t\t\t\t\t<view class=\"pri_two_b_li animate__animated animate__bounceIn\" style=\"float:initial;margin: auto;\">\r\n\t\t\t\t\t\t<image src=\"/static/images/icon71.png\" class=\"pri_two_b_li_bj\"></image>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<template v-if=\"zjjgLists[0].type*1 == 4\">\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_cards\" style=\"background:none;\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/images/icon87.png\" class=\"pri_two_b_li_hb_bj\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"width: 100%;height:100%;\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_b_hb\">谢谢参与</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_t\">\r\n\t\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + zjjgLists[0].image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_b\">{{zjjgLists[0].name}}</view>\r\n\t\t\t\t\t\t</template>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cjtcTc_f\" @click=\"cjStartTap(1)\">再抽一次</view>\r\n\t\t\t\t<!-- <view class=\"cjtcslTc_f\">\r\n\t\t\t\t\t<view @click=\"tgdhTap\">\r\n\t\t\t\t\t\t<image :src=\"istgdhToggle ? '/static/images/cjxz-1.png' : '/static/images/cjxz.png'\"></image>\r\n\t\t\t\t\t\t跳过动画\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 单抽奖品弹窗 end -->\r\n\r\n\t\t<!-- 10连抽奖品弹窗 go -->\r\n\t\t<view class=\"cjtcTc\" v-if=\"slcToggle\" @click=\"slcToggle = false\">\r\n\t\t\t<view class=\"cjtcTc_n\" @click=\"slcToggle = false\">\r\n\t\t\t\t<image src=\"/static/images/icon69.png\" class=\"cjtcTc_title\"></image>\r\n\t\t\t\t<view class=\"cjtcTc_b\" @click.stop>\r\n\t\t\t\t\t<view class=\"pri_two_b_li  animate__animated animate__bounceIn\" v-for=\"(item,index) in zjjgLists\"\r\n\t\t\t\t\t\t:key=\"index\">\r\n\t\t\t\t\t\t<image src=\"/static/images/icon71.png\" class=\"pri_two_b_li_bj\"></image>\r\n\r\n\t\t\t\t\t\t<template v-if=\"item.type*1 == 4\">\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_cards\" style=\"background:none;\">\r\n\t\t\t\t\t\t\t\t<image src=\"/static/images/icon87.png\" class=\"pri_two_b_li_hb_bj\"\r\n\t\t\t\t\t\t\t\t\tstyle=\"width: 100%;height:100%;\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_b_hb\">谢谢参与</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_t\">\r\n\t\t\t\t\t\t\t\t<image :src=\"imgbaseUrl + item.image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"pri_two_b_li_b\">{{item.name}}</view>\r\n\t\t\t\t\t\t</template>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cjtcTc_f\" @click.stop=\"cjStartTap(10)\">再抽一次</view>\r\n\t\t\t\t<!-- <view class=\"cjtcslTc_f\">\r\n\t\t\t\t\t<view @click.stop=\"tgdhTap\">\r\n\t\t\t\t\t\t<image :src=\"istgdhToggle ? '/static/images/cjxz-1.png' : '/static/images/cjxz.png'\"></image>\r\n\t\t\t\t\t\t跳过动画\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 10连抽奖品弹窗 end -->\r\n\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tprizedrawApi,\r\n\t\tdrawSubApi\r\n\t} from '@/config/http.achieve.js'\r\n\t// 获取系统状态栏的高度\r\n\tlet systemInfo = uni.getSystemInfoSync();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timgbaseUrlOss: '',\r\n\t\t\t\timgbaseUrl: '', //图片地址\r\n\t\t\t\tloding: false,\r\n\t\t\t\theight: 0,\r\n\t\t\t\tnoticeText: '',\r\n\t\t\t\tbox_skins: [],\r\n\t\t\t\tnavBg: '',\r\n\t\t\t\tnavHeight: 0,\r\n\t\t\t\ttitleStyle: {\r\n\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\tfontSize: '34rpx',\r\n\t\t\t\t\tcolor: '#fff',\r\n\t\t\t\t},\r\n\t\t\t\ttitleStyle_on: {\r\n\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\tfontSize: '34rpx',\r\n\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t},\r\n\t\t\t\tdcToggle: false, //单抽抽奖结果弹窗是否显示\r\n\t\t\t\tslcToggle: false, //10连抽奖结果弹窗是否显示\r\n\t\t\t\tistgdhToggle: false, //是否跳过动画\r\n\t\t\t\tcjzLoding: false, //抽奖动画是否开启\r\n\t\t\t\tcjType: 1, //抽奖类型 1/10\r\n\t\t\t\tjpLists: [], //奖池预览\r\n\t\t\t\tcjNum: 0, //抽奖次数\r\n\t\t\t\tzjjgLists: [\r\n\t\t\t\t\t/*{\r\n\t\t\t\t\t\t\"id\": 2,\r\n\t\t\t\t\t\t\"type\": \"2\",\r\n\t\t\t\t\t\t\"price\": \"2.00\",\r\n\t\t\t\t\t\t\"goods\": null,\r\n\t\t\t\t\t\t\"memberCard\": null\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\"id\":3,\"type\":\"3\",\"price\":\"0.00\",\"goods\":null,\"memberCard\":{\"name\":\"日常卡-次卡\",\"type\":0}},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"id\": 1,\r\n\t\t\t\t\t\t\"type\": \"1\",\r\n\t\t\t\t\t\t\"price\": \"0.00\",\r\n\t\t\t\t\t\t\"goods\": {\r\n\t\t\t\t\t\t\t\"name\": \"印尼进口营多捞面速食泡面夜宵食品网红拉面拌面方便面整箱\",\r\n\t\t\t\t\t\t\t\"image\": \"/storage/default/20241023/O1CN01v2BipQ1Pk522c5aa07389ab876c7f9c9d4fd52c8fc26d9a76.png\"\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"memberCard\": null\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{type:4},*/\r\n\t\t\t\t], //中奖结果列表\r\n\t\t\t\tcjId: 0, //中奖结果id\r\n\t\t\t\tcountdown: 9,\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tcj_moreNum: 0, //多轮抽奖，当前是第几轮\r\n\t\t\t\tcjdh_loding: true,\r\n\t\t\t\tcjKey: {},\r\n\t\t\t\tjinzLd:true,//抽奖禁止连点\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.navHeight = (uni.getSystemInfoSync().statusBarHeight + 44);\r\n\t\t\tthis.imgbaseUrlOss = this.$baseUrlOss;\r\n\t\t\tthis.imgbaseUrl = this.$baseUrl;\r\n\t\t\tthis.prizedrawData(); //奖池列表\r\n\t\t},\r\n\t\tonUnload: function() {\r\n\t\t\tconsole.log('onHide', 'onHide');\r\n\t\t\tclearInterval(this.timer);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//商品跳转详情\r\n\t\t\tgoodsSpTo(item) {\r\n\t\t\t\tif (item.type * 1 == 1) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/prizedraw/winningrecordxq?id=' + item.goods_id\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//奖池列表\r\n\t\t\tprizedrawData() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tprizedrawApi({}).then(res => {\r\n\t\t\t\t\tconsole.log('奖池列表', res)\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tthat.loding = true;\r\n\t\t\t\t\t\tif (res.data.prize.length > 0) {\r\n\t\t\t\t\t\t\t// noticeText: '· 182****3311抽中了POP MART泡泡玛特1　　　· 182****3313抽中了POP MART泡泡玛特2　　　· 182****3314抽中了POP MART泡泡玛特3　　',\r\n\t\t\t\t\t\t\tvar noticeArr = []; //类型:1=商品,2=红包,3=会员卡\r\n\t\t\t\t\t\t\tfor (var i = 0; i < res.data.prize.length; i++) {\r\n\t\t\t\t\t\t\t\t/*var name = res.data.prize[i].type * 1 == 1 ? (res.data.prize[i].goods ? res.data\r\n\t\t\t\t\t\t\t\t\t\t.prize[i].goods.name : 'null') : res.data.prize[i].type * 1 == 2 ? res.data\r\n\t\t\t\t\t\t\t\t\t.prize[i].price * 1 + '元现金红包' : res.data.prize[i].type * 1 == 3 ? res.data\r\n\t\t\t\t\t\t\t\t\t.prize[i].memberCard.name : ''*/\r\n\t\t\t\t\t\t\t\tvar name = res.data.prize[i].name == '' ? (res.data.prize[i].type * 1 == 1 ? (res.data.prize[i].goods ? res.data\r\n\t\t\t\t\t\t\t\t\t\t.prize[i].goods.name : 'null') : res.data.prize[i].type * 1 == 2 ? res.data\r\n\t\t\t\t\t\t\t\t\t.prize[i].price * 1 + '元现金红包' : res.data.prize[i].type * 1 == 3 ? res.data\r\n\t\t\t\t\t\t\t\t\t.prize[i].memberCard.name : '') : res.data.prize[i].name\r\n\t\t\t\t\t\t\t\tnoticeArr.push('　· ' + res.data.prize[i].user.mobile + ' 抽中了' + name + ' ' + res\r\n\t\t\t\t\t\t\t\t\t.data.prize[i].create_time + '　　　')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.noticeText = noticeArr.join(' ');\r\n\t\t\t\t\t\t\t// console.log(noticeArr,'noticeArr')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tfor(var j=0;j<res.data.jackpot.length;j++){\r\n\t\t\t\t\t\t\tif(res.data.jackpot[j].name.length > 11){\r\n\t\t\t\t\t\t\t\tres.data.jackpot[j].namezs = res.data.jackpot[j].name ? res.data.jackpot[j].name.substring(0,11) + '...' : ''\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tres.data.jackpot[j].namezs = res.data.jackpot[j].name\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthat.jpLists = res.data.jackpot;\r\n\t\t\t\t\t\tthat.cjNum = res.data.luck_draw_frequency * 1\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//开始抽奖\r\n\t\t\tcjStartTap(type) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (this.cjNum * 1 <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\ttitle: \"抽奖次数不足\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.dcToggle = false; //关闭单次抽奖结果弹窗\r\n\t\t\t\tthis.slcToggle = false; //关闭10连抽抽奖结果弹窗\r\n\t\t\t\t// this.istgdhToggle = false; //跳过动画\r\n\t\t\t\tthis.cjType = type;\r\n\r\n\t\t\t\t//请求接口拿到抽奖结果 go\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tdrawSubApi({\r\n\t\t\t\t\ttype: type == 1 ? 1 : 10\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\t// console.log('抽奖结果',res)\r\n\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.prizedrawData();\r\n\t\t\t\t\t\tthat.cjId = res.data[0].id;\r\n\t\t\t\t\t\tthat.zjjgLists = res.data;\r\n\r\n\t\t\t\t\t\t//执行抽奖动画\r\n\t\t\t\t\t\tthis.cjzLoding = true; //开启抽奖弹窗动画\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tthat.startOne(); //开始抽奖\r\n\t\t\t\t\t\t}, 500);\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//跳过动画\r\n\t\t\ttgdhTap() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t/**/\r\n\t\t\t\tif (this.cjType == 1) {\r\n\t\t\t\t\t//单次抽奖跳过\r\n\r\n\t\t\t\t\tthis.countdown = 9;\r\n\t\t\t\t\tclearInterval(this.timer);\r\n\r\n\t\t\t\t\tthis.cjzLoding = false; //关闭抽奖弹窗动画\r\n\t\t\t\t\t// this.istgdhToggle = true;\r\n\t\t\t\t\tthis.cjType == 1 ? this.dcToggle = true : this.slcToggle = true;\r\n\t\t\t\t\t// this.istgdhToggle = !this.istgdhToggle;\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif(!that.jinzLd){\r\n\t\t\t\t\t  uni.showToast({\r\n\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\ttitle:'您点击的太快了~',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t  });\r\n\t\t\t\t\t  return false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.jinzLd = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\t//10连抽奖\r\n\t\t\t\t\tthis.countdown = 9;\r\n\t\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\t\tthis.cjzLoding = false;\r\n\t\t\t\t\tthis.cj_moreNum++\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\tthat.cjzLoding = true;\r\n\t\t\t\t\t\tif (this.cj_moreNum == that.zjjgLists.length) {\r\n\t\t\t\t\t\t\t//轮数到了截止抽奖\r\n\t\t\t\t\t\t\tthis.countdown = 9;\r\n\t\t\t\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\t\t\t\tthis.cjzLoding = false; //关闭抽奖弹窗动画\r\n\t\t\t\t\t\t\tthis.slcToggle = true;\r\n\t\t\t\t\t\t\tthis.cj_moreNum = 0;\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t/*this.cjdh_loding = false;\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\tthat.cjdh_loding = true;\r\n\t\t\t\t\t})*/\r\n\t\t\t\t\tif (this.cj_moreNum == that.zjjgLists.length) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.cjId = that.zjjgLists[that.cj_moreNum * 1].id;\r\n\t\t\t\t\tthat.cjKey = that.zjjgLists[that.cj_moreNum * 1];\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.startOne(); //开始抽奖\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\t\tthat.jinzLd = true\r\n\t\t\t\t\t},1500)\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\tonPageScroll(e) {\r\n\t\t\t\tconst top = uni.upx2px(100)\r\n\t\t\t\tconst {\r\n\t\t\t\t\tscrollTop\r\n\t\t\t\t} = e\r\n\t\t\t\tlet percent = scrollTop / top > 1 ? 1 : scrollTop / top\r\n\t\t\t\tthis.navBg = percent;\r\n\r\n\t\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\t\tfrontColor: this.navBg == 1 ? '#000000' : '#ffffff',\r\n\t\t\t\t\tbackgroundColor: this.navBg == 1 ? '#ffffff' : '#000000' // 可根据需要修改背景颜色\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\t// 转换字符数值为真正的数值\r\n\t\t\tnavbarHeight() {\r\n\t\t\t\t// #ifdef  H5\r\n\t\t\t\treturn this.height ? this.height : 44;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS || MP\r\n\t\t\t\t// 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)\r\n\t\t\t\t// 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式\r\n\t\t\t\t// return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度\r\n\t\t\t\tlet height = systemInfo.platform == 'ios' || systemInfo.platform == 'devtools' ? 44 : 48;\r\n\t\t\t\treturn this.height ? this.height : height;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tstartOne() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\t// 每秒减少 1 秒\r\n\t\t\t\t\tthis.countdown--;\r\n\t\t\t\t\t// 当倒计时结束时\r\n\t\t\t\t\t// console.log('this.countdown',this.countdown)\r\n\t\t\t\t\tif (this.countdown === 0) {\r\n\t\t\t\t\t\t// 清除定时器\r\n\t\t\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\t\t\tthat.countdown = 9;\r\n\t\t\t\t\t\tconsole.log('倒计时结束');\r\n\r\n\t\t\t\t\t\tif (that.cjType == 1) {\r\n\t\t\t\t\t\t\t//单抽\r\n\t\t\t\t\t\t\tthat.cjzLoding = false; //关闭抽奖弹窗动画\r\n\t\t\t\t\t\t\tthat.dcToggle = true\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (that.cjType == 10) {\r\n\t\t\t\t\t\t\t//10连抽\r\n\t\t\t\t\t\t\tthat.cjzLoding = false; //关闭抽奖弹窗动画\r\n\t\t\t\t\t\t\tif (that.cj_moreNum == that.zjjgLists.length) {\r\n\t\t\t\t\t\t\t\t//轮数到了截止抽奖\r\n\t\t\t\t\t\t\t\tthat.countdown = 9;\r\n\t\t\t\t\t\t\t\tclearInterval(that.timer);\r\n\t\t\t\t\t\t\t\tthat.cjzLoding = false; //关闭抽奖弹窗动画\r\n\t\t\t\t\t\t\t\tthat.slcToggle = true;\r\n\t\t\t\t\t\t\t\tthat.cj_moreNum = 1;\r\n\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.tgdhTap(); //继续抽奖\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tthis.$refs.oneBox.start()\r\n\t\t\t},\r\n\t\t\tfinsh(e) {\r\n\t\t\t\t/*var that = this;\r\n\t\t\t\tsetTimeout(function(){\r\n\t\t\t\t\tthat.cjzLoding = false;//关闭抽奖弹窗动画\r\n\t\t\t\t\tthat.cjType == 1 ? that.dcToggle = true : that.slcToggle = true;\r\n\t\t\t\t},600)*/\r\n\t\t\t},\r\n\t\t\tnavTo(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground: #060505;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prizedraw.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./prizedraw.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752030102237\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}