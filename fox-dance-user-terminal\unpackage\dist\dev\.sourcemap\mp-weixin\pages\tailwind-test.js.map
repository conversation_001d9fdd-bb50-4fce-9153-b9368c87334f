{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/tailwind-test.vue?ee1c", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/tailwind-test.vue?4ff9", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/tailwind-test.vue?85f1", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/tailwind-test.vue?f1c9", "uni-app:///pages/tailwind-test.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/tailwind-test.vue?73b8", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/pages/tailwind-test.vue?db82"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "onLoad", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACuL;AACvL,gBAAgB,8LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8sB,CAAgB,4sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwHluB;EACAC;EACAC;IACA,QAEA;EACA;EACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAAiiC,CAAgB,2/BAAG,EAAC,C;;;;;;;;;;;ACArjC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tailwind-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tailwind-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tailwind-test.vue?vue&type=template&id=4be4f1be&scoped=true&\"\nvar renderjs\nimport script from \"./tailwind-test.vue?vue&type=script&lang=js&\"\nexport * from \"./tailwind-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tailwind-test.vue?vue&type=style&index=0&id=4be4f1be&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4be4f1be\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tailwind-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tailwind-test.vue?vue&type=template&id=4be4f1be&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tailwind-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tailwind-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- TailwindCSS 测试页面 -->\n    <view class=\"header\">\n      <text class=\"title\">TailwindCSS 测试页面</text>\n    </view>\n    \n    <!-- 基础样式测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">基础样式测试</text>\n      \n      <!-- 颜色和背景 -->\n      <view class=\"test-item bg-red-500 text-white p-4 m-2 rounded-lg\">\n        红色背景，白色文字，内边距，外边距，圆角\n      </view>\n      \n      <view class=\"test-item bg-blue-500 text-white p-4 m-2 rounded-lg\">\n        蓝色背景，白色文字\n      </view>\n      \n      <view class=\"test-item bg-green-500 text-white p-4 m-2 rounded-lg\">\n        绿色背景，白色文字\n      </view>\n    </view>\n    \n    <!-- 布局测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">布局测试</text>\n      \n      <!-- Flex布局 -->\n      <view class=\"flex justify-between items-center bg-gray-200 p-4 m-2 rounded-lg\">\n        <text class=\"text-sm\">左侧文字</text>\n        <text class=\"text-lg font-bold\">中间粗体</text>\n        <text class=\"text-xs\">右侧小字</text>\n      </view>\n      \n      <!-- Grid布局 -->\n      <view class=\"grid grid-cols-2 gap-4 p-4 m-2\">\n        <view class=\"bg-purple-500 text-white p-4 rounded text-center\">网格1</view>\n        <view class=\"bg-pink-500 text-white p-4 rounded text-center\">网格2</view>\n        <view class=\"bg-indigo-500 text-white p-4 rounded text-center\">网格3</view>\n        <view class=\"bg-yellow-500 text-black p-4 rounded text-center\">网格4</view>\n      </view>\n    </view>\n    \n    <!-- 响应式测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">响应式测试</text>\n      \n      <view class=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/4 bg-teal-500 text-white p-4 m-2 rounded-lg\">\n        响应式宽度测试\n      </view>\n    </view>\n    \n    <!-- 动画测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">动画测试</text>\n      \n      <view class=\"transform hover:scale-105 transition-transform duration-300 bg-orange-500 text-white p-4 m-2 rounded-lg\">\n        悬停缩放动画\n      </view>\n    </view>\n    \n    <!-- 自定义rpx单位测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">rpx单位转换测试</text>\n      \n      <view class=\"test-rpx\">\n        <text class=\"text-base\">这应该是32rpx字体大小（1rem = 32rpx）</text>\n      </view>\n      \n      <view class=\"w-32 h-32 bg-cyan-500 rounded-lg m-4\">\n        128rpx x 128rpx 的正方形\n      </view>\n    </view>\n    \n    <!-- 按钮测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">按钮样式测试</text>\n      \n      <view class=\"flex flex-col space-y-4 p-4\">\n        <button class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\n          主要按钮\n        </button>\n        \n        <button class=\"bg-transparent hover:bg-blue-500 text-blue-700 font-semibold hover:text-white py-2 px-4 border border-blue-500 hover:border-transparent rounded\">\n          次要按钮\n        </button>\n        \n        <button class=\"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-full\">\n          圆角按钮\n        </button>\n      </view>\n    </view>\n    \n    <!-- 卡片测试 -->\n    <view class=\"section\">\n      <text class=\"section-title\">卡片样式测试</text>\n      \n      <view class=\"max-w-sm rounded overflow-hidden shadow-lg bg-white m-4\">\n        <view class=\"px-6 py-4\">\n          <view class=\"font-bold text-xl mb-2\">卡片标题</view>\n          <text class=\"text-gray-700 text-base\">\n            这是一个使用TailwindCSS样式的卡片组件，包含标题、内容和标签。\n          </text>\n        </view>\n        <view class=\"px-6 pt-4 pb-2\">\n          <view class=\"inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2\">\n            #标签1\n          </view>\n          <view class=\"inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2\">\n            #标签2\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'TailwindTest',\n  data() {\n    return {\n      \n    }\n  },\n  onLoad() {\n    console.log('TailwindCSS 测试页面加载完成')\n  }\n}\n</script>\n\n<style scoped>\n/* 这里可以添加一些自定义样式来补充TailwindCSS */\n.container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.header {\n  @apply text-center py-8;\n}\n\n.title {\n  @apply text-3xl font-bold text-white;\n}\n\n.section {\n  @apply bg-white mx-4 my-4 rounded-lg p-4;\n}\n\n.section-title {\n  @apply text-xl font-semibold text-gray-800 mb-4 block;\n}\n\n.test-item {\n  @apply text-center;\n}\n\n.test-rpx {\n  @apply p-4 bg-gray-100 rounded;\n}\n\n/* 测试自定义CSS与TailwindCSS的结合 */\n.custom-gradient {\n  background: linear-gradient(45deg, #ff6b87, #ff8e53);\n}\n</style>\n", "import mod from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tailwind-test.vue?vue&type=style&index=0&id=4be4f1be&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tailwind-test.vue?vue&type=style&index=0&id=4be4f1be&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752113440218\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}