{"version": 3, "sources": ["webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue?354f", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue?0622", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue?4472", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue?796a", "uni-app:///uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue?f26e", "webpack:///D:/Project/fox/用户端/fox-dance-user-terminal/uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue?c7df"], "names": ["t", "name", "data", "indicatorStyle", "visible", "fixNvueBug", "dateShow", "timeShow", "title", "time", "year", "month", "day", "hour", "minute", "second", "startYear", "startMonth", "startDay", "startHour", "startMinute", "startSecond", "endYear", "endMonth", "endDay", "endHour", "endMinute", "endSecond", "options", "virtualHost", "props", "type", "default", "value", "modelValue", "start", "end", "returnType", "disabled", "border", "hideSecond", "watch", "handler", "immediate", "months", "days", "hours", "minutes", "seconds", "computed", "years", "ymd", "hms", "currentDateIsStart", "currentDateIsEnd", "minYear", "maxYear", "minMonth", "max<PERSON><PERSON><PERSON>", "minDay", "maxDay", "minHour", "maxHour", "minMinute", "maxMinute", "minSecond", "maxSecond", "selectTimeText", "okText", "clearText", "cancelText", "mounted", "methods", "lessThanTen", "parseTimeType", "initPickerValue", "defaultValue", "compareValueWithStartAndEnd", "winner", "superTimeStamp", "dateBase", "parseValue", "defaultDate", "parseDatetimeRange", "pointType", "point", "getCurrentRange", "range", "capitalize", "checkValue", "daysInMonth", "createTimeStamp", "createDomSting", "hhmmss", "initTime", "bindDateChange", "bindTimeChange", "initTimePicker", "tiggerTimePicker", "clearTime", "setTime"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACgM;AAChM,gBAAgB,8LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,0sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmF7wB;AAGA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA,mBAEA;EADAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAaA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAKAC;EAEA;EACAC;IACAC;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IAEAR;MACAS;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;IAgBAZ;MACAW;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACAC;IACA;IACAR;MACAO;QACA;MACA;MACAC;IACA;IACAP;MACAM;QACA;MACA;MACAC;IACA;IAEA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEAN;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAG;MACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EAEAC,6BAQA;EAEAC;IACA;AACA;AACA;AACA;IAEAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACAC;MACA;QACAA;QACAA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA5C;MACAE;MACAC;MAEA;QACA;UACA0C;QACA;UACAA;QACA;UACAA;QACA;MACA;QACAA;MACA;QACAA;MACA;QACAA;MACA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;QACAC;MACA;MACA;QACA/C;QACA+C;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;QACA;QACAC;QACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;UACAC;UACA;QACA;QACA;UACAC;QACA;QACA;QACA;QACA,kGACAA;UACAA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;QACAlF;QACA;UACAA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAmF;MACA,yBACA,MACA,+BACA,MACA;MAEA,2CACA,MACA;MAEA;QACAC;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3wBA;AAAA;AAAA;AAAA;AAAw5C,CAAgB,6vCAAG,EAAC,C;;;;;;;;;;;ACA56C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./time-picker.vue?vue&type=template&id=60a1244c&\"\nvar renderjs\nimport script from \"./time-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./time-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./time-picker.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-datetime-picker/components/uni-datetime-picker/time-picker.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./time-picker.vue?vue&type=template&id=60a1244c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.visible && _vm.dateShow\n      ? _vm.__map(_vm.years, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.lessThanTen(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    _vm.visible && _vm.dateShow\n      ? _vm.__map(_vm.months, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.lessThanTen(item)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var l2 =\n    _vm.visible && _vm.dateShow\n      ? _vm.__map(_vm.days, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.lessThanTen(item)\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var l3 =\n    _vm.visible && _vm.timeShow\n      ? _vm.__map(_vm.hours, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.lessThanTen(item)\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var l4 =\n    _vm.visible && _vm.timeShow\n      ? _vm.__map(_vm.minutes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = _vm.lessThanTen(item)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var l5 =\n    _vm.visible && _vm.timeShow && !_vm.hideSecond\n      ? _vm.__map(_vm.seconds, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m5 = _vm.lessThanTen(item)\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./time-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./time-picker.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-datetime-picker\">\r\n\t\t<view @click=\"initTimePicker\">\r\n\t\t\t<slot>\r\n\t\t\t\t<view class=\"uni-datetime-picker-timebox-pointer\"\r\n\t\t\t\t\t:class=\"{'uni-datetime-picker-disabled': disabled, 'uni-datetime-picker-timebox': border}\">\r\n\t\t\t\t\t<text class=\"uni-datetime-picker-text\">{{time}}</text>\r\n\t\t\t\t\t<view v-if=\"!time\" class=\"uni-datetime-picker-time\">\r\n\t\t\t\t\t\t<text class=\"uni-datetime-picker-text\">{{selectTimeText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t\t<view v-if=\"visible\" id=\"mask\" class=\"uni-datetime-picker-mask\" @click=\"tiggerTimePicker\"></view>\r\n\t\t<view v-if=\"visible\" class=\"uni-datetime-picker-popup\" :class=\"[dateShow && timeShow ? '' : 'fix-nvue-height']\"\r\n\t\t\t:style=\"fixNvueBug\">\r\n\t\t\t<view class=\"uni-title\">\r\n\t\t\t\t<text class=\"uni-datetime-picker-text\">{{selectTimeText}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"dateShow\" class=\"uni-datetime-picker__container-box\">\r\n\t\t\t\t<picker-view class=\"uni-datetime-picker-view\" :indicator-style=\"indicatorStyle\" :value=\"ymd\"\r\n\t\t\t\t\t@change=\"bindDateChange\">\r\n\t\t\t\t\t<picker-view-column>\r\n\t\t\t\t\t\t<view class=\"uni-datetime-picker-item\" v-for=\"(item,index) in years\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"uni-datetime-picker-item\">{{lessThanTen(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column>\r\n\t\t\t\t\t\t<view class=\"uni-datetime-picker-item\" v-for=\"(item,index) in months\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"uni-datetime-picker-item\">{{lessThanTen(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column>\r\n\t\t\t\t\t\t<view class=\"uni-datetime-picker-item\" v-for=\"(item,index) in days\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"uni-datetime-picker-item\">{{lessThanTen(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t\t<!-- 兼容 nvue 不支持伪类 -->\r\n\t\t\t\t<text class=\"uni-datetime-picker-sign sign-left\">-</text>\r\n\t\t\t\t<text class=\"uni-datetime-picker-sign sign-right\">-</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"timeShow\" class=\"uni-datetime-picker__container-box\">\r\n\t\t\t\t<picker-view class=\"uni-datetime-picker-view\" :class=\"[hideSecond ? 'time-hide-second' : '']\"\r\n\t\t\t\t\t:indicator-style=\"indicatorStyle\" :value=\"hms\" @change=\"bindTimeChange\">\r\n\t\t\t\t\t<picker-view-column>\r\n\t\t\t\t\t\t<view class=\"uni-datetime-picker-item\" v-for=\"(item,index) in hours\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"uni-datetime-picker-item\">{{lessThanTen(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column>\r\n\t\t\t\t\t\t<view class=\"uni-datetime-picker-item\" v-for=\"(item,index) in minutes\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"uni-datetime-picker-item\">{{lessThanTen(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!hideSecond\">\r\n\t\t\t\t\t\t<view class=\"uni-datetime-picker-item\" v-for=\"(item,index) in seconds\" :key=\"index\">\r\n\t\t\t\t\t\t\t<text class=\"uni-datetime-picker-item\">{{lessThanTen(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t\t<!-- 兼容 nvue 不支持伪类 -->\r\n\t\t\t\t<text class=\"uni-datetime-picker-sign\" :class=\"[hideSecond ? 'sign-center' : 'sign-left']\">:</text>\r\n\t\t\t\t<text v-if=\"!hideSecond\" class=\"uni-datetime-picker-sign sign-right\">:</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"uni-datetime-picker-btn\">\r\n\t\t\t\t<view @click=\"clearTime\">\r\n\t\t\t\t\t<text class=\"uni-datetime-picker-btn-text\">{{clearText}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-datetime-picker-btn-group\">\r\n\t\t\t\t\t<view class=\"uni-datetime-picker-cancel\" @click=\"tiggerTimePicker\">\r\n\t\t\t\t\t\t<text class=\"uni-datetime-picker-btn-text\">{{cancelText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click=\"setTime\">\r\n\t\t\t\t\t\t<text class=\"uni-datetime-picker-btn-text\">{{okText}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tinitVueI18n\r\n\t} from '@dcloudio/uni-i18n'\r\n\timport i18nMessages from './i18n/index.js'\r\n\tconst {\r\n\t\tt\r\n\t} = initVueI18n(i18nMessages)\r\n\timport {\r\n\t\tfixIosDateFormat\r\n\t} from './util'\r\n\r\n\t/**\r\n\t * DatetimePicker 时间选择器\r\n\t * @description 可以同时选择日期和时间的选择器\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=xxx\r\n\t * @property {String} type = [datetime | date | time] 显示模式\r\n\t * @property {Boolean} multiple = [true|false] 是否多选\r\n\t * @property {String|Number} value 默认值\r\n\t * @property {String|Number} start 起始日期或时间\r\n\t * @property {String|Number} end 起始日期或时间\r\n\t * @property {String} return-type = [timestamp | string]\r\n\t * @event {Function} change  选中发生变化触发\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'UniDatetimePicker',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tindicatorStyle: `height: 50px;`,\r\n\t\t\t\tvisible: false,\r\n\t\t\t\tfixNvueBug: {},\r\n\t\t\t\tdateShow: true,\r\n\t\t\t\ttimeShow: true,\r\n\t\t\t\ttitle: '日期和时间',\r\n\t\t\t\t// 输入框当前时间\r\n\t\t\t\ttime: '',\r\n\t\t\t\t// 当前的年月日时分秒\r\n\t\t\t\tyear: 1920,\r\n\t\t\t\tmonth: 0,\r\n\t\t\t\tday: 0,\r\n\t\t\t\thour: 0,\r\n\t\t\t\tminute: 0,\r\n\t\t\t\tsecond: 0,\r\n\t\t\t\t// 起始时间\r\n\t\t\t\tstartYear: 1920,\r\n\t\t\t\tstartMonth: 1,\r\n\t\t\t\tstartDay: 1,\r\n\t\t\t\tstartHour: 0,\r\n\t\t\t\tstartMinute: 0,\r\n\t\t\t\tstartSecond: 0,\r\n\t\t\t\t// 结束时间\r\n\t\t\t\tendYear: 2120,\r\n\t\t\t\tendMonth: 12,\r\n\t\t\t\tendDay: 31,\r\n\t\t\t\tendHour: 23,\r\n\t\t\t\tendMinute: 59,\r\n\t\t\t\tendSecond: 59,\r\n\t\t\t}\r\n\t\t},\r\n\t\toptions: {\r\n\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\tvirtualHost: false,\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP-TOUTIAO\r\n\t\t\tvirtualHost: true\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'datetime'\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tstart: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tend: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\treturnType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'string'\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\thideSecond: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// #ifndef VUE3\r\n\t\t\tvalue: {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tthis.parseValue(fixIosDateFormat(newVal))\r\n\t\t\t\t\t\tthis.initTime(false)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.time = ''\r\n\t\t\t\t\t\tthis.parseValue(Date.now())\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tmodelValue: {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tif (newVal) {\r\n\t\t\t\t\t\tthis.parseValue(fixIosDateFormat(newVal))\r\n\t\t\t\t\t\tthis.initTime(false)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.time = ''\r\n\t\t\t\t\t\tthis.parseValue(Date.now())\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\ttype: {\r\n\t\t\t\thandler(newValue) {\r\n\t\t\t\t\tif (newValue === 'date') {\r\n\t\t\t\t\t\tthis.dateShow = true\r\n\t\t\t\t\t\tthis.timeShow = false\r\n\t\t\t\t\t\tthis.title = '日期'\r\n\t\t\t\t\t} else if (newValue === 'time') {\r\n\t\t\t\t\t\tthis.dateShow = false\r\n\t\t\t\t\t\tthis.timeShow = true\r\n\t\t\t\t\t\tthis.title = '时间'\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.dateShow = true\r\n\t\t\t\t\t\tthis.timeShow = true\r\n\t\t\t\t\t\tthis.title = '日期和时间'\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tstart: {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.parseDatetimeRange(fixIosDateFormat(newVal), 'start')\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tend: {\r\n\t\t\t\thandler(newVal) {\r\n\t\t\t\t\tthis.parseDatetimeRange(fixIosDateFormat(newVal), 'end')\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\r\n\t\t\t// 月、日、时、分、秒可选范围变化后，检查当前值是否在范围内，不在则当前值重置为可选范围第一项\r\n\t\t\tmonths(newVal) {\r\n\t\t\t\tthis.checkValue('month', this.month, newVal)\r\n\t\t\t},\r\n\t\t\tdays(newVal) {\r\n\t\t\t\tthis.checkValue('day', this.day, newVal)\r\n\t\t\t},\r\n\t\t\thours(newVal) {\r\n\t\t\t\tthis.checkValue('hour', this.hour, newVal)\r\n\t\t\t},\r\n\t\t\tminutes(newVal) {\r\n\t\t\t\tthis.checkValue('minute', this.minute, newVal)\r\n\t\t\t},\r\n\t\t\tseconds(newVal) {\r\n\t\t\t\tthis.checkValue('second', this.second, newVal)\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 当前年、月、日、时、分、秒选择范围\r\n\t\t\tyears() {\r\n\t\t\t\treturn this.getCurrentRange('year')\r\n\t\t\t},\r\n\r\n\t\t\tmonths() {\r\n\t\t\t\treturn this.getCurrentRange('month')\r\n\t\t\t},\r\n\r\n\t\t\tdays() {\r\n\t\t\t\treturn this.getCurrentRange('day')\r\n\t\t\t},\r\n\r\n\t\t\thours() {\r\n\t\t\t\treturn this.getCurrentRange('hour')\r\n\t\t\t},\r\n\r\n\t\t\tminutes() {\r\n\t\t\t\treturn this.getCurrentRange('minute')\r\n\t\t\t},\r\n\r\n\t\t\tseconds() {\r\n\t\t\t\treturn this.getCurrentRange('second')\r\n\t\t\t},\r\n\r\n\t\t\t// picker 当前值数组\r\n\t\t\tymd() {\r\n\t\t\t\treturn [this.year - this.minYear, this.month - this.minMonth, this.day - this.minDay]\r\n\t\t\t},\r\n\t\t\thms() {\r\n\t\t\t\treturn [this.hour - this.minHour, this.minute - this.minMinute, this.second - this.minSecond]\r\n\t\t\t},\r\n\r\n\t\t\t// 当前 date 是 start\r\n\t\t\tcurrentDateIsStart() {\r\n\t\t\t\treturn this.year === this.startYear && this.month === this.startMonth && this.day === this.startDay\r\n\t\t\t},\r\n\r\n\t\t\t// 当前 date 是 end\r\n\t\t\tcurrentDateIsEnd() {\r\n\t\t\t\treturn this.year === this.endYear && this.month === this.endMonth && this.day === this.endDay\r\n\t\t\t},\r\n\r\n\t\t\t// 当前年、月、日、时、分、秒的最小值和最大值\r\n\t\t\tminYear() {\r\n\t\t\t\treturn this.startYear\r\n\t\t\t},\r\n\t\t\tmaxYear() {\r\n\t\t\t\treturn this.endYear\r\n\t\t\t},\r\n\t\t\tminMonth() {\r\n\t\t\t\tif (this.year === this.startYear) {\r\n\t\t\t\t\treturn this.startMonth\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmaxMonth() {\r\n\t\t\t\tif (this.year === this.endYear) {\r\n\t\t\t\t\treturn this.endMonth\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn 12\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tminDay() {\r\n\t\t\t\tif (this.year === this.startYear && this.month === this.startMonth) {\r\n\t\t\t\t\treturn this.startDay\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmaxDay() {\r\n\t\t\t\tif (this.year === this.endYear && this.month === this.endMonth) {\r\n\t\t\t\t\treturn this.endDay\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn this.daysInMonth(this.year, this.month)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tminHour() {\r\n\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\tif (this.currentDateIsStart) {\r\n\t\t\t\t\t\treturn this.startHour\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\treturn this.startHour\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmaxHour() {\r\n\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\tif (this.currentDateIsEnd) {\r\n\t\t\t\t\t\treturn this.endHour\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 23\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\treturn this.endHour\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tminMinute() {\r\n\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\tif (this.currentDateIsStart && this.hour === this.startHour) {\r\n\t\t\t\t\t\treturn this.startMinute\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\tif (this.hour === this.startHour) {\r\n\t\t\t\t\t\treturn this.startMinute\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmaxMinute() {\r\n\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\tif (this.currentDateIsEnd && this.hour === this.endHour) {\r\n\t\t\t\t\t\treturn this.endMinute\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 59\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\tif (this.hour === this.endHour) {\r\n\t\t\t\t\t\treturn this.endMinute\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 59\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tminSecond() {\r\n\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\tif (this.currentDateIsStart && this.hour === this.startHour && this.minute === this.startMinute) {\r\n\t\t\t\t\t\treturn this.startSecond\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\tif (this.hour === this.startHour && this.minute === this.startMinute) {\r\n\t\t\t\t\t\treturn this.startSecond\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 0\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmaxSecond() {\r\n\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\tif (this.currentDateIsEnd && this.hour === this.endHour && this.minute === this.endMinute) {\r\n\t\t\t\t\t\treturn this.endSecond\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 59\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\tif (this.hour === this.endHour && this.minute === this.endMinute) {\r\n\t\t\t\t\t\treturn this.endSecond\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 59\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * for i18n\r\n\t\t\t */\r\n\t\t\tselectTimeText() {\r\n\t\t\t\treturn t(\"uni-datetime-picker.selectTime\")\r\n\t\t\t},\r\n\t\t\tokText() {\r\n\t\t\t\treturn t(\"uni-datetime-picker.ok\")\r\n\t\t\t},\r\n\t\t\tclearText() {\r\n\t\t\t\treturn t(\"uni-datetime-picker.clear\")\r\n\t\t\t},\r\n\t\t\tcancelText() {\r\n\t\t\t\treturn t(\"uni-datetime-picker.cancel\")\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmounted() {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tconst res = uni.getSystemInfoSync();\r\n\t\t\tthis.fixNvueBug = {\r\n\t\t\t\ttop: res.windowHeight / 2,\r\n\t\t\t\tleft: res.windowWidth / 2\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * @param {Object} item\r\n\t\t\t * 小于 10 在前面加个 0\r\n\t\t\t */\r\n\r\n\t\t\tlessThanTen(item) {\r\n\t\t\t\treturn item < 10 ? '0' + item : item\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 解析时分秒字符串，例如：00:00:00\r\n\t\t\t * @param {String} timeString\r\n\t\t\t */\r\n\t\t\tparseTimeType(timeString) {\r\n\t\t\t\tif (timeString) {\r\n\t\t\t\t\tlet timeArr = timeString.split(':')\r\n\t\t\t\t\tthis.hour = Number(timeArr[0])\r\n\t\t\t\t\tthis.minute = Number(timeArr[1])\r\n\t\t\t\t\tthis.second = Number(timeArr[2])\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 解析选择器初始值，类型可以是字符串、时间戳，例如：2000-10-02、'08:30:00'、 1610695109000\r\n\t\t\t * @param {String | Number} datetime\r\n\t\t\t */\r\n\t\t\tinitPickerValue(datetime) {\r\n\t\t\t\tlet defaultValue = null\r\n\t\t\t\tif (datetime) {\r\n\t\t\t\t\tdefaultValue = this.compareValueWithStartAndEnd(datetime, this.start, this.end)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdefaultValue = Date.now()\r\n\t\t\t\t\tdefaultValue = this.compareValueWithStartAndEnd(defaultValue, this.start, this.end)\r\n\t\t\t\t}\r\n\t\t\t\tthis.parseValue(defaultValue)\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 初始值规则：\r\n\t\t\t * - 用户设置初始值 value\r\n\t\t\t * \t- 设置了起始时间 start、终止时间 end，并 start < value < end，初始值为 value， 否则初始值为 start\r\n\t\t\t * \t- 只设置了起始时间 start，并 start < value，初始值为 value，否则初始值为 start\r\n\t\t\t * \t- 只设置了终止时间 end，并 value < end，初始值为 value，否则初始值为 end\r\n\t\t\t * \t- 无起始终止时间，则初始值为 value\r\n\t\t\t * - 无初始值 value，则初始值为当前本地时间 Date.now()\r\n\t\t\t * @param {Object} value\r\n\t\t\t * @param {Object} dateBase\r\n\t\t\t */\r\n\t\t\tcompareValueWithStartAndEnd(value, start, end) {\r\n\t\t\t\tlet winner = null\r\n\t\t\t\tvalue = this.superTimeStamp(value)\r\n\t\t\t\tstart = this.superTimeStamp(start)\r\n\t\t\t\tend = this.superTimeStamp(end)\r\n\r\n\t\t\t\tif (start && end) {\r\n\t\t\t\t\tif (value < start) {\r\n\t\t\t\t\t\twinner = new Date(start)\r\n\t\t\t\t\t} else if (value > end) {\r\n\t\t\t\t\t\twinner = new Date(end)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\twinner = new Date(value)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (start && !end) {\r\n\t\t\t\t\twinner = start <= value ? new Date(value) : new Date(start)\r\n\t\t\t\t} else if (!start && end) {\r\n\t\t\t\t\twinner = value <= end ? new Date(value) : new Date(end)\r\n\t\t\t\t} else {\r\n\t\t\t\t\twinner = new Date(value)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn winner\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 转换为可比较的时间戳，接受日期、时分秒、时间戳\r\n\t\t\t * @param {Object} value\r\n\t\t\t */\r\n\t\t\tsuperTimeStamp(value) {\r\n\t\t\t\tlet dateBase = ''\r\n\t\t\t\tif (this.type === 'time' && value && typeof value === 'string') {\r\n\t\t\t\t\tconst now = new Date()\r\n\t\t\t\t\tconst year = now.getFullYear()\r\n\t\t\t\t\tconst month = now.getMonth() + 1\r\n\t\t\t\t\tconst day = now.getDate()\r\n\t\t\t\t\tdateBase = year + '/' + month + '/' + day + ' '\r\n\t\t\t\t}\r\n\t\t\t\tif (Number(value)) {\r\n\t\t\t\t\tvalue = parseInt(value)\r\n\t\t\t\t\tdateBase = 0\r\n\t\t\t\t}\r\n\t\t\t\treturn this.createTimeStamp(dateBase + value)\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 解析默认值 value，字符串、时间戳\r\n\t\t\t * @param {Object} defaultTime\r\n\t\t\t */\r\n\t\t\tparseValue(value) {\r\n\t\t\t\tif (!value) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time' && typeof value === \"string\") {\r\n\t\t\t\t\tthis.parseTimeType(value)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet defaultDate = null\r\n\t\t\t\t\tdefaultDate = new Date(value)\r\n\t\t\t\t\tif (this.type !== 'time') {\r\n\t\t\t\t\t\tthis.year = defaultDate.getFullYear()\r\n\t\t\t\t\t\tthis.month = defaultDate.getMonth() + 1\r\n\t\t\t\t\t\tthis.day = defaultDate.getDate()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.type !== 'date') {\r\n\t\t\t\t\t\tthis.hour = defaultDate.getHours()\r\n\t\t\t\t\t\tthis.minute = defaultDate.getMinutes()\r\n\t\t\t\t\t\tthis.second = defaultDate.getSeconds()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.hideSecond) {\r\n\t\t\t\t\tthis.second = 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 解析可选择时间范围 start、end，年月日字符串、时间戳\r\n\t\t\t * @param {Object} defaultTime\r\n\t\t\t */\r\n\t\t\tparseDatetimeRange(point, pointType) {\r\n\t\t\t\t// 时间为空，则重置为初始值\r\n\t\t\t\tif (!point) {\r\n\t\t\t\t\tif (pointType === 'start') {\r\n\t\t\t\t\t\tthis.startYear = 1920\r\n\t\t\t\t\t\tthis.startMonth = 1\r\n\t\t\t\t\t\tthis.startDay = 1\r\n\t\t\t\t\t\tthis.startHour = 0\r\n\t\t\t\t\t\tthis.startMinute = 0\r\n\t\t\t\t\t\tthis.startSecond = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (pointType === 'end') {\r\n\t\t\t\t\t\tthis.endYear = 2120\r\n\t\t\t\t\t\tthis.endMonth = 12\r\n\t\t\t\t\t\tthis.endDay = 31\r\n\t\t\t\t\t\tthis.endHour = 23\r\n\t\t\t\t\t\tthis.endMinute = 59\r\n\t\t\t\t\t\tthis.endSecond = 59\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type === 'time') {\r\n\t\t\t\t\tconst pointArr = point.split(':')\r\n\t\t\t\t\tthis[pointType + 'Hour'] = Number(pointArr[0])\r\n\t\t\t\t\tthis[pointType + 'Minute'] = Number(pointArr[1])\r\n\t\t\t\t\tthis[pointType + 'Second'] = Number(pointArr[2])\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!point) {\r\n\t\t\t\t\t\tpointType === 'start' ? this.startYear = this.year - 60 : this.endYear = this.year + 60\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (Number(point)) {\r\n\t\t\t\t\t\tpoint = parseInt(point)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// datetime 的 end 没有时分秒, 则不限制\r\n\t\t\t\t\tconst hasTime = /[0-9]:[0-9]/\r\n\t\t\t\t\tif (this.type === 'datetime' && pointType === 'end' && typeof point === 'string' && !hasTime.test(\r\n\t\t\t\t\t\t\tpoint)) {\r\n\t\t\t\t\t\tpoint = point + ' 23:59:59'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst pointDate = new Date(point)\r\n\t\t\t\t\tthis[pointType + 'Year'] = pointDate.getFullYear()\r\n\t\t\t\t\tthis[pointType + 'Month'] = pointDate.getMonth() + 1\r\n\t\t\t\t\tthis[pointType + 'Day'] = pointDate.getDate()\r\n\t\t\t\t\tif (this.type === 'datetime') {\r\n\t\t\t\t\t\tthis[pointType + 'Hour'] = pointDate.getHours()\r\n\t\t\t\t\t\tthis[pointType + 'Minute'] = pointDate.getMinutes()\r\n\t\t\t\t\t\tthis[pointType + 'Second'] = pointDate.getSeconds()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取 年、月、日、时、分、秒 当前可选范围\r\n\t\t\tgetCurrentRange(value) {\r\n\t\t\t\tconst range = []\r\n\t\t\t\tfor (let i = this['min' + this.capitalize(value)]; i <= this['max' + this.capitalize(value)]; i++) {\r\n\t\t\t\t\trange.push(i)\r\n\t\t\t\t}\r\n\t\t\t\treturn range\r\n\t\t\t},\r\n\r\n\t\t\t// 字符串首字母大写\r\n\t\t\tcapitalize(str) {\r\n\t\t\t\treturn str.charAt(0).toUpperCase() + str.slice(1)\r\n\t\t\t},\r\n\r\n\t\t\t// 检查当前值是否在范围内，不在则当前值重置为可选范围第一项\r\n\t\t\tcheckValue(name, value, values) {\r\n\t\t\t\tif (values.indexOf(value) === -1) {\r\n\t\t\t\t\tthis[name] = values[0]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 每个月的实际天数\r\n\t\t\tdaysInMonth(year, month) { // Use 1 for January, 2 for February, etc.\r\n\t\t\t\treturn new Date(year, month, 0).getDate();\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 生成时间戳\r\n\t\t\t * @param {Object} time\r\n\t\t\t */\r\n\t\t\tcreateTimeStamp(time) {\r\n\t\t\t\tif (!time) return\r\n\t\t\t\tif (typeof time === \"number\") {\r\n\t\t\t\t\treturn time\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttime = time.replace(/-/g, '/')\r\n\t\t\t\t\tif (this.type === 'date') {\r\n\t\t\t\t\t\ttime = time + ' ' + '00:00:00'\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn Date.parse(time)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 生成日期或时间的字符串\r\n\t\t\t */\r\n\t\t\tcreateDomSting() {\r\n\t\t\t\tconst yymmdd = this.year +\r\n\t\t\t\t\t'-' +\r\n\t\t\t\t\tthis.lessThanTen(this.month) +\r\n\t\t\t\t\t'-' +\r\n\t\t\t\t\tthis.lessThanTen(this.day)\r\n\r\n\t\t\t\tlet hhmmss = this.lessThanTen(this.hour) +\r\n\t\t\t\t\t':' +\r\n\t\t\t\t\tthis.lessThanTen(this.minute)\r\n\r\n\t\t\t\tif (!this.hideSecond) {\r\n\t\t\t\t\thhmmss = hhmmss + ':' + this.lessThanTen(this.second)\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.type === 'date') {\r\n\t\t\t\t\treturn yymmdd\r\n\t\t\t\t} else if (this.type === 'time') {\r\n\t\t\t\t\treturn hhmmss\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn yymmdd + ' ' + hhmmss\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 初始化返回值，并抛出 change 事件\r\n\t\t\t */\r\n\t\t\tinitTime(emit = true) {\r\n\t\t\t\tthis.time = this.createDomSting()\r\n\t\t\t\tif (!emit) return\r\n\t\t\t\tif (this.returnType === 'timestamp' && this.type !== 'time') {\r\n\t\t\t\t\tthis.$emit('change', this.createTimeStamp(this.time))\r\n\t\t\t\t\tthis.$emit('input', this.createTimeStamp(this.time))\r\n\t\t\t\t\tthis.$emit('update:modelValue', this.createTimeStamp(this.time))\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('change', this.time)\r\n\t\t\t\t\tthis.$emit('input', this.time)\r\n\t\t\t\t\tthis.$emit('update:modelValue', this.time)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 用户选择日期或时间更新 data\r\n\t\t\t * @param {Object} e\r\n\t\t\t */\r\n\t\t\tbindDateChange(e) {\r\n\t\t\t\tconst val = e.detail.value\r\n\t\t\t\tthis.year = this.years[val[0]]\r\n\t\t\t\tthis.month = this.months[val[1]]\r\n\t\t\t\tthis.day = this.days[val[2]]\r\n\t\t\t},\r\n\t\t\tbindTimeChange(e) {\r\n\t\t\t\tconst val = e.detail.value\r\n\t\t\t\tthis.hour = this.hours[val[0]]\r\n\t\t\t\tthis.minute = this.minutes[val[1]]\r\n\t\t\t\tthis.second = this.seconds[val[2]]\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 初始化弹出层\r\n\t\t\t */\r\n\t\t\tinitTimePicker() {\r\n\t\t\t\tif (this.disabled) return\r\n\t\t\t\tconst value = fixIosDateFormat(this.time)\r\n\t\t\t\tthis.initPickerValue(value)\r\n\t\t\t\tthis.visible = !this.visible\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 触发或关闭弹框\r\n\t\t\t */\r\n\t\t\ttiggerTimePicker(e) {\r\n\t\t\t\tthis.visible = !this.visible\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 用户点击“清空”按钮，清空当前值\r\n\t\t\t */\r\n\t\t\tclearTime() {\r\n\t\t\t\tthis.time = ''\r\n\t\t\t\tthis.$emit('change', this.time)\r\n\t\t\t\tthis.$emit('input', this.time)\r\n\t\t\t\tthis.$emit('update:modelValue', this.time)\r\n\t\t\t\tthis.tiggerTimePicker()\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 用户点击“确定”按钮\r\n\t\t\t */\r\n\t\t\tsetTime() {\r\n\t\t\t\tthis.initTime()\r\n\t\t\t\tthis.tiggerTimePicker()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$uni-primary: #007aff !default;\r\n\r\n\t.uni-datetime-picker {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\t/* width: 100%; */\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-datetime-picker-view {\r\n\t\theight: 130px;\r\n\t\twidth: 270px;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-datetime-picker-item {\r\n\t\theight: 50px;\r\n\t\tline-height: 50px;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.uni-datetime-picker-btn {\r\n\t\tmargin-top: 60px;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.uni-datetime-picker-btn-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: $uni-primary;\r\n\t}\r\n\r\n\t.uni-datetime-picker-btn-group {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-datetime-picker-cancel {\r\n\t\tmargin-right: 30px;\r\n\t}\r\n\r\n\t.uni-datetime-picker-mask {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0px;\r\n\t\ttop: 0px;\r\n\t\tleft: 0px;\r\n\t\tright: 0px;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\t\ttransition-duration: 0.3s;\r\n\t\tz-index: 998;\r\n\t}\r\n\r\n\t.uni-datetime-picker-popup {\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 30px;\r\n\t\twidth: 270px;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\theight: 500px;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\twidth: 330px;\r\n\t\t/* #endif */\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\ttransition-duration: 0.3s;\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.fix-nvue-height {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\theight: 330px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-datetime-picker-time {\r\n\t\tcolor: grey;\r\n\t}\r\n\r\n\t.uni-datetime-picker-column {\r\n\t\theight: 50px;\r\n\t}\r\n\r\n\t.uni-datetime-picker-timebox {\r\n\r\n\t\tborder: 1px solid #E5E5E5;\r\n\t\tborder-radius: 5px;\r\n\t\tpadding: 7px 10px;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-datetime-picker-timebox-pointer {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\r\n\t.uni-datetime-picker-disabled {\r\n\t\topacity: 0.4;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: not-allowed !important;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-datetime-picker-text {\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 50px\r\n\t}\r\n\r\n\t.uni-datetime-picker-sign {\r\n\t\tposition: absolute;\r\n\t\ttop: 53px;\r\n\t\t/* 减掉 10px 的元素高度，兼容nvue */\r\n\t\tcolor: #999;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tfont-size: 16px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.sign-left {\r\n\t\tleft: 86px;\r\n\t}\r\n\r\n\t.sign-right {\r\n\t\tright: 86px;\r\n\t}\r\n\r\n\t.sign-center {\r\n\t\tleft: 135px;\r\n\t}\r\n\r\n\t.uni-datetime-picker__container-box {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-top: 40px;\r\n\t}\r\n\r\n\t.time-hide-second {\r\n\t\twidth: 180px;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./time-picker.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./time-picker.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752030104778\n      var cssReload = require(\"D:/DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}