@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
@font-face{
    font-family: 'pht';
	src: url('https://file.foxdance.com.cn/ttf/pht.otf') format('opentype');
}
@font-face{
    font-family: 'fzlt';
	src: url('https://file.foxdance.com.cn/ttf/fzlts.TTF') format('opentype');
}
.logsetTc{
	width:100%;
	height: 100%;
	position: fixed;
	bottom:0;left:0;
	z-index: 9;
	background:rgba(0,0,0,.6);
}
.logsetTc_n{
	width: 100%;
	height:auto;
	overflow:hidden;
	border-radius:20rpx 20rpx 0 0;
	position: absolute;
	bottom:0;left:0;
	z-index:10;
	background:#fff;
	padding:30rpx;
	box-sizing: border-box;
	padding-bottom:calc(24rpx + env(safe-area-inset-bottom));
}
.logsetTc_t view{
	font-size: 32rpx;
	color:#333;
	font-weight:bold;
}
.logsetTc_t text{
	margin-top: 14rpx;
	display:block;
	font-size:26rpx;
	color:#999;
}
.logsetTc_f{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content:center;
	margin-top:40rpx;
}
.logsetTc_f view{
	width:190rpx;
	height:84rpx;
	text-align:center;
	line-height:84rpx;
	font-size:30rpx;
	color:#333;
	margin:0 18rpx;
	border-radius:10rpx;
}
.logsetTc_f view:nth-child(1){
	background:#f2f2f2;
}
.logsetTc_f view:nth-child(2){
	background:#131315;
	color:#fff;
}
.edma_one_a{
	width: 90rpx!important;
	height: 90rpx!important;
	border-radius: 50%;
}
.qyrz_con_li.tx{height:auto!important;}
.qyrz_con_li.tx .qyrz_con_li_r{height:auto!important;}
.qyrz_con_li.tx button{
	position:absolute;
	width: 100%;
	height: 100%;
	opacity: 0;
	top: 0;left:0;
	z-index: 9;
}
.qyrz_con_li{
	padding:26rpx 0;
	height:76rpx;
	overflow:hidden;
	border-bottom:1px solid #F5F5F5;
	overflow:hidden;
	display: flex;
	align-items:center;
	display: flex;
	align-items:center;
	justify-content: space-between;
	position:relative;
}
.qyrz_con_li_l{
	 color: #333333;
	 font-size: 26rpx;
}
.qyrz_con_li_r{
	width:calc(100% - 180rpx);
	height:76rpx;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: flex-end;
	position: relative;
}
.qyrz_con_li_r button{
	display: block;
	width: 100%;
	height: 100%;
	position:absolute;
	left:0;top:0;
	z-index:9;
}
.qyrz_con_li_r input{
	color: #333;
	height: 76rpx;
	font-size: 26rpx;
	width: 100%;
	display:block;
	text-align:right;
}
.qyrz_con_li_r switch{
	zoom:.9;
}
.qyrz_con_li_zp_b_li{
	width: 268rpx;
	height: 268rpx;
	border:1px solid #999999;
	border-radius: 10rpx;
	background: #F8F8F8;
	overflow:hidden;
	position:relative;
	float:left;
	margin-left:20rpx;
	margin-top:20rpx;
}
.qyrz_con_li_zp_b_li_tp{
	display:block;
	width: 100%;
	height:100%;
}
.qyrz_con_li_zp_b_li_gb{
	position:absolute;
	top:12rpx;
	right:12rpx;
	width:40rpx;
	height:40rpx;
	border-radius:50%;
	background-color:#fff;
	z-index: 2;
}
.gbimg{
	display:block;
	width:40rpx;
	height:40rpx;
	border-radius:50%;
	background-color:#fff;
}
.qyrz_con_li_zp_b .qyrz_con_li_zp_b_li:last-child{
	border:1px dashed #999999!important;
}
.qyrz_con_li_zp_b{
	margin:0 6rpx 26rpx 6rpx;
	height:auto;
	overflow:hidden;
}
.qyrz_con_li_zp_t{
	margin:46rpx 26rpx 0 26rpx;
	font-size: 26rpx;
	color:#333;
}
.qyrz_con_li_r image{
	width:14rpx;
	height:20rpx;
	margin-left:10rpx;
}
.qyrz_con_li_r picker{
	display: block;
	width: 100%;
	height:100%;
}
.qyrz_con_li_r .uni-input{
	width: 100%;
	height:76rpx;
	font-size: 26rpx;
	color:#333;
	display: flex;
	align-items:center;
	justify-content: flex-end;
}
.ind_one{margin:-26rpx 32rpx 0 32rpx;height:280rpx;-overflow:hidden;border-radius:16rpx;background:#FFF;position:relative;z-index: 3;}
.ind_one_tx{width:146rpx;height:146rpx;border-radius:50%;overflow:hidden;position:absolute;right:32rpx;top:-46rpx;border:4rpx solid #FFF;background:#fff}
.ind_one_tx image{display:block;width:100%;height:100%;border-radius:50%}
.ind_one_a{margin:20rpx 200rpx 0 52rpx;height:auto;overflow:hidden}
.ind_one_a view{font-size:32rpx;color:#333333;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.ind_one_a text{color:#999999;font-size:20rpx}
.ind_one_b{width:100%;height:auto;overflow:hidden;margin-top:32rpx}
.ind_one_b_li{width:33.33%;height:auto;overflow:hidden;position:relative;float:left}
.ind_one_b_li_a{color:#999999;font-size:20rpx;text-align:center}
.ind_one_b_li_b{text-align:center;color:#333;font-size:32rpx;margin-top:8rpx;font-weight:bold}
.ind_one_b_li .xian{width:1px;height:60rpx;background:#F0F0F0;position:absolute;right:0;top:50%;margin-top:-30rpx}
.ind_one_c{margin:28rpx 52rpx 0 52rpx;height:auto;overflow:hidden;display:flex;align-items:center;position:relative}
.ind_one_c_r{width:380rpx;height:8rpx;border-radius:50rpx;background:#999999;overflow:hidden;position:absolute;right:0}
.ind_one_c_r view{height:100%;background:#131315}
.ind_one_c_l{font-size:20rpx;color:#333333;display:flex;align-items:center}
.ind_one_c_l image{width:28rpx;height:28rpx}
.ind_one_c_c{font-size:20rpx;color:#333333;margin-left:10rpx}
.ind_one_c_c text{font-size:28rpx;color:#333333;font-weight:bold}
.ind_two{margin:26rpx 32rpx 0 32rpx;border-radius:16rpx;background:#FFF;overflow:hidden;padding:20rpx 0}
.ind_two_li{width:33.33%;height:auto;overflow:hidden;float:left;position:relative}
.ind_two_li image{width:80rpx;height:80rpx;margin:auto}
.ind_two_li_a{font-size:26rpx;color:#131315;text-align:center;font-weight:bold;margin-top:4rpx}
.ind_two_li_b{display:block;text-align:center;font-size:20rpx;color:#999999;zoom:.6;margin-top:6rpx}
.ind_two_li text{display:block;width:1px;height:60rpx;background:#F0F0F0;right:0;position:absolute;top:50%;margin-top:-30rpx}
.ind_thr{margin:26rpx 32rpx 0 32rpx;border-radius:16rpx;height:100rpx;background:#FFF;overflow:hidden}
.ind_thr_l{width:30%;height:100%;overflow:hidden;float:left}
.ind_thr_c{width:35%;height:100%;overflow:hidden;float:left;align-items:center;font-size:32rpx;color:#fe73fe;display:flex;align-items:center;justify-content:center;position:relative;font-weight:bold}
.ind_thr_r{width:35%;height:100%;overflow:hidden;float:left;display:flex;align-items:center;justify-content:center;flex-direction:column}
.ind_thr_r_a{display:flex;align-items:center;justify-content:center;font-size:28rpx;color:#131315;font-weight:bold}
.ind_thr_r_a image{width:24rpx;height:24rpx;margin:0 10rpx}
.ind_thr_r_a image:last-child{-webkit-transform:rotate(180deg);transform:rotate(180deg)}
.ind_thr_r_b{display:block;text-align:center;font-size:20rpx;color:#999999;zoom:.6;margin-top:6rpx}
.ind_thr_l{display:flex;align-items:center;justify-content:center;flex-direction:column;position:relative}
.ind_thr_l view{color:#131315;font-size:28rpx;font-weight:bold}
.ind_thr_l text{display:block;text-align:center;font-size:20rpx;color:#999999;zoom:.6;margin-top:6rpx}
.ind_thr_l .xian{display:block;width:1px;height:40rpx;background:#F0F0F0;right:0;position:absolute;top:50%;margin-top:-20rpx;zoom:1}
.ind_thr_c .xian{display:block;width:1px;height:40rpx;background:#F0F0F0;right:0;position:absolute;top:50%;margin-top:-20rpx}
.ind_thr_c image{width:29rpx;height:39rpx;margin-right:16rpx}
.ind_thr_c view{max-width:calc(100% - 66rpx);font-size:32rpx;color:#fe73fe;-text-align:center}
.ind_fou{margin:26rpx 32rpx 0 32rpx;height:auto;overflow:hidden;border-radius:16rpx;background:#fff;padding:0 32rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.ind_fou_l{width:28rpx;height:28rpx;overflow:hidden}
.ind_fou_r{width:calc(100% - 50rpx)}
.ind_fou_r .uni-noticebar{padding:0!important;margin:0!important}
.ind_fou_r .uni-noticebar text{font-size:20rpx!important;color:#999!important;line-height:52rpx!important;height:52rpx!important}
.ind_fou_r .uni-noticebar__content-wrapper{font-size:20rpx!important;height:52rpx!important}
.ind_fiv{margin:26rpx 32rpx 0 32rpx;height:auto;overflow:hidden;border-radius:16rpx;background:#fff}
.ind_fiv_t{width:100%;height:70rpx;overflow:hidden;padding:0 52rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.ind_fiv_t view{font-size:26rpx;color:#131315;font-weight:bold}
.ind_fiv_t text{font-size:20rpx;color:#999999}
.ind_fiv_b{width:100%;height:630rpx;overflow:hidden;position:relative}
.ind_fiv_b swiper{width:100%;height:630rpx;overflow:hidden}
.ind_fiv_b swiper image{display:block;width:100%;height:630rpx;overflow:hidden}
.ind_fiv_b_yd{position:absolute;left:0;bottom:16rpx;width:100%;display:flex;justify-content:center}
.ind_fiv_b_yd text{display:block;width:6rpx;height:6rpx;border-radius:50%;background:#fff;margin:0 6rpx}
.ind_fiv_b_yd_ac{width:36rpx!important;border-radius:20rpx!important}
.ind_ban{width:100%;height:446rpx;height:646rpx;overflow:hidden;position:relative;}
.ind_swiper_bj{
	width: 100%;
	height:446rpx;
	height:646rpx;
	overflow:hidden;
	position:absolute;
	top: 0;left:0;
	z-index: 1;
}
.ind_swiper_bj image{
	display:block;
	width: 100%;
	height: 100%;
	transition: 1.2s;
	position: absolute;top: 0;
	left: 0;
}
.ind_ban_box{width:100%;height:36rpx;position:absolute;bottom:0;left:0;transition:0.8s;-background:linear-gradient(to top,#FADAFF 0%,transparent 100%);z-index:3;}
.ind_ban swiper{width:100%;height:446rpx;height:646rpx;overflow:hidden;position:relative;z-index: 2;}
.ind_ban swiper swiper-item{position:relative;}
/*.ind_ban swiper view{display:block;max-width:70%;max-height:70%;margin:auto;position: absolute;top: 0;left:0;bottom:0;right: 0;margin: auto;display: flex;
align-items:center;
justify-content: center;}
.ind_ban swiper image{display:block;max-width:100%;max-height:100%;position:relative;-top:-20rpx;margin: auto;}*/
.ind_ban swiper view{width: 100%;height: 100%;}
.ind_ban swiper view image{width: 100%;height: 100%;}
.min_one{margin:26rpx 26rpx 42rpx 26rpx;height:auto;overflow:hidden;display:flex;align-items:center;position:relative}
.min_one_l{width:108rpx;height:108rpx;border-radius:50%}
.min_one_c{width:440rpx;height:auto;overflow:hidden;display:flex;flex-direction:column;justify-content:center;margin-left:22rpx}
.min_one_c_a{font-size:32rpx;color:#333333;display:flex;align-items:center;font-weight:bold}
.min_one_c_a image{width:32rpx;height:32rpx;margin-left:16rpx}
.min_one_c_b{margin-top:10rpx;font-size:24rpx;color:#000000}
.min_one_c_b text{color:#131315}
.min_one_r{display:flex;align-items:center;position:absolute;right:0}
.min_one_r image:nth-child(1){width:48rpx;height:48rpx}
.min_one_r image:nth-child(2){width:12rpx;height:24rpx;margin-left:40rpx}
.min_two{margin:0 26rpx;height:auto;overflow:hidden}
.min_two_li{width:33.33%;height:auto;overflow:hidden;position:relative;float:left}
.min_two_li view{font-weight:500;font-size:26rpx;color:#333333;text-align:center}
.min_two_li view:nth-child(1){font-weight:bold}
.min_two_li view:nth-child(2){margin-top:26rpx}
.min_two_li text{width:1px;height:60rpx;background:#D9D1D0;position:absolute;right:0;top:50%;margin-top:-30rpx}
.min_thr{margin:42rpx 26rpx 26rpx 26rpx;height:auto;overflow:hidden;display:flex;justify-content:space-between}
.min_thr_l{width:calc(50% - 10rpx);height:290rpx;background:#FFFFFF;border-radius:20rpx;overflow:hidden;display:flex;flex-direction:column;justify-content:center;align-items:center}
.min_thr_l_t{width:100%;display:flex;justify-content:center;align-items:center}
.min_thr_l_t view{float:left}
.min_thr_l_t view text:nth-child(1){font-weight:bold;font-size:32rpx;color:#333333;position:relative;z-index:2}
.min_thr_l_t text:nth-child(2){width:100%;height:10rpx;background:linear-gradient( 90deg,#131315 28%,rgba(255,255,255,0) 100%);display:block;position:relative;top:-16rpx;z-index:1}
.min_thr_l_t image{width:32rpx;height:32rpx;margin-left:10rpx;position:relative;top:-4rpx}
.min_thr_l_b{width:120rpx;height:120rpx;margin-top:30rpx}
.min_thr_r{width:calc(50% - 10rpx);height:290rpx;overflow:hidden;display:flex;flex-direction:column;justify-content:space-between}
.min_thr_r_t{width:100%;height:calc(50% - 12rpx);background:#FFFFFF;border-radius:20rpx;display:flex;align-items:center;justify-content:space-between;padding:0 26rpx;box-sizing:border-box}
.min_thr_r_t .min_thr_l_b{width:100rpx;height:100rpx;margin-top:0}
.min_thr_r .min_thr_l_t{width:auto}
.min_five{margin:26rpx;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box;overflow:hidden}
.min_five_t{font-size:32rpx;color:#333333;font-weight:bold}
.min_five_b{margin-top:6rpx}
.min_five_b view{width:25%;height:auto;float:left;overflow:hidden;margin-top:26rpx;position:relative}
.min_five_b view button{display:block;width:100%;height:100%;position:absolute;top:0;left:0;opacity:0}
.min_five_b view image{width:48rpx;height:48rpx;display:block;margin:auto}
.min_five_b view text{font-size:26rpx;color:#333333;display:block;text-align:center;margin-top:16rpx}
.min_fou{margin:0 26rpx;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box;display:flex;justify-content:space-between}
.min_fou_l{width:calc(50% - 10rpx);height:auto;overflow:hidden}
.min_fou_l_t{display:flex;align-items:center}
.min_fou_l_t view{font-size:32rpx;color:#333333;font-weight:bold}
.min_fou_l_t text{width:72rpx;height:28rpx;background:linear-gradient( 270deg,#FF8686 0%,#C62C2C 100%);border-radius:20rpx 42rpx 42rpx 0rpx;font-weight:400;font-size:20rpx;color:#FFFFFF;line-height:28rpx;margin-left:12rpx;text-align:center}
.min_fou_l_b{height:188rpx;background:#FFFBF0;border-radius:10rpx;margin-top:46rpx;position:relative;display:flex;flex-direction:column;justify-content:center}
.min_fou_l_b_a{margin-left:26rpx;font-size:26rpx;color:#333333}
.min_fou_l_b_b{font-weight:bold;font-size:40rpx;color:#000000;margin:20rpx 26rpx 0 26rpx}
.min_fou_l_b_c{width:100rpx;height:40rpx;background:linear-gradient( 270deg,#FF1208 0%,#FF7901 100%);border-radius:82rpx;font-size:20rpx;color:#F8F8FA;text-align:center;line-height:40rpx;position:absolute;right:20rpx;bottom:20rpx}
.min_fou_r_b{height:188rpx;background:#FDEEE3;border-radius:10rpx;margin-top:46rpx;position:relative;display:flex;flex-direction:column}
.min_fou_r_b image{width:80rpx;height:99rpx;position:absolute;right:26rpx;top:50%;margin-top:-50rpx}
.min_fou_r_b_a{font-size:26rpx;color:#333333;font-weight:bold;margin:22rpx 26rpx 0 26rpx}
.min_fou_r_b_b{font-size:20rpx;color:#F11919;font-weight:bold;margin:20rpx 26rpx 0 26rpx}
.min_fou_r_b_c{width:100rpx;height:40rpx;background:linear-gradient( 270deg,#FF1208 0%,#FF7901 100%);border-radius:82rpx;font-size:20rpx;color:#F8F8FA;text-align:center;line-height:40rpx;position:absolute;left:20rpx;bottom:20rpx}
.gg_rgba{width:100%;height:100%;position:fixed;top:0;left:0;background:rgba(0,0,0,.6);z-index:99999}
.min_ewm{width:546rpx;height:666rpx;background:#FFFFFF;border-radius:20rpx;position:fixed;top:0;left:0;right:0;bottom:0;margin:auto;background:#fff;z-index:999999}
.min_ewm_c{width:144rpx;height:60rpx;background:#131315;border-radius:82rpx;text-align:center;line-height:60rpx;font-size:26rpx;color:#F8F8FA;margin:auto}
.min_ewm_b{width:386rpx;height:386rpx;display:block;margin:60rpx auto}
.min_ewm_t{font-size:26rpx;color:#333333;font-weight:bold;text-align:center;position:relative;margin-top:26rpx}
.min_ewm_t image{width:40rpx;height:40rpx;position:absolute;right:26rpx;top:-4rpx}
.mucars_one{width:100%;height:96rpx;background:#FFFFFF;overflow:hidden;position:fixed;top:0;left:0;z-index:9}
.mucars_one_li{width:50%;height:auto;overflow:hidden;float:left}
.mucars_one_li picker{display:block;width:100%;height:96rpx}
.mucars_one_li .uni-input{display:flex;align-items:center;justify-content:center;width:100%;height:96rpx;font-size:32rpx;color:#333333}
.mucars_one_li .uni-input text{display:block;width:0;height:0;border-left:10rpx solid transparent;border-right:10rpx solid transparent;border-top:14rpx solid #D6D6D6;margin-left:10rpx;position:relative;top:4rpx}
.mycards_two{margin:96rpx 26rpx 0 26rpx;height:auto;overflow:hidden}
.mycards_two_li{margin-top:26rpx;padding:26rpx;box-sizing:border-box;width:100%;height:auto;background:linear-gradient( 270deg,#383838 0%,#615B5B 50%,#383838 100%);border-radius:20rpx;overflow:hidden;position:relative}
.mycards_two_li_t_b{float:right;width:128rpx;height:56rpx;background:linear-gradient( to right,#F5C386 0%,#F9E3C8 100%);border-radius:50rpx;font-size:26rpx;color:#3D210A;text-align:center;line-height:56rpx;margin-top:34rpx}
.mycards_two_li_t_zt{position:absolute;right:0;top:0;background:#FF2D55;border-radius:0 20rpx 0 20rpx;padding:0 10rpx;font-size:26rpx;color:#FFFFFF;text-align:center;line-height:56rpx}
.mycards_two_li_t{display:flex;align-items:center}
.mycards_two_li_t_l{width:92rpx;height:92rpx;border-radius:50%}
.mycards_two_li_t_r{width:calc(100% - 200rpx);margin-left:20rpx}
.mycards_two_li_t_r view{font-size:32rpx;color:#F6C891}
.mycards_two_li_t_r text{display:block;font-size:26rpx;color:#F6C891;margin-top:10rpx}
.mycards_thr{
	margin: 96rpx 26rpx 0 26rpx;
	height: auto;
	overflow: hidden;
}
.mycards_thr_li{
	margin-top: 26rpx;
	padding:22rpx 20rpx 22rpx 26rpx;
	box-sizing: border-box;
	width: 100%;
	height: 393rpx;
	background:#935ae3;
	border-radius: 20rpx;
	overflow: hidden;
	position: relative;
}
.mycards_thr_li_bj{
	width: 100%;
	height: 100%;
	position:absolute;
	top:0;left:0;
	z-index:1;
}
.mycards_thr_li_c{
	margin-top:88rpx;
	display: flex;
	align-items:center;
	justify-content: space-between;
	position:relative;
	z-index:2;
}
.mycards_thr_li_c_l{
	width: 172rpx;
	height: 172rpx;
	border-radius:50%;
	overflow:hidden;
	position:relative;
	z-index:2;
}
.mycards_thr_li_c_l image{
	display:block;
	width: 100%;
	height: 100%;
	border-radius:50%;
}
.mycards_thr_li_c_r{
	width:calc(100% - 200rpx);
	display: flex;
	align-items:flex-end;
	justify-content: center;
	flex-direction: column;
}
.mycards_thr_li_c_r_a{
	font-size: 70rpx;
	font-size: 50rpx;
	font-size: 46rpx;
	color:#ffffff;
	-text-shadow:
	    1px 0 0 #54209e,
	    -1px 0 0 #54209e,
	    0 1px 0 #54209e,
	    0 -1px 0 #54209e;
	text-align: right;
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp:2;
	overflow: hidden;
	-font-weight: bold;
	font-family: 'fzlt';
}
.mycards_thr_li_c_r_b{
	font-size: 36rpx;
	font-size: 32rpx;
	color:#fff;
	text-align:right;
	width: 100%;
	-font-weight: bold;
	margin-top: 10rpx;
	font-family: 'fzlt';
}
.mycards_thr_li_c_r_b text{
	font-size: 60rpx;
	font-size: 52rpx;
	color:#ffffff;
	margin:0 10rpx;
}
.mycards_thr_li_c_r_f{
	width: 100%;
	height:auto;
	padding:0 18rpx 20rpx 26rpx;
	box-sizing: border-box;
	overflow:hidden;
	position:absolute;
	left:0;
	bottom:0;
	display: flex;
	align-items:center;
	justify-content: space-between;
	z-index: 2;
}
.mycards_thr_li_c_r_f_l{
	font-size: 24rpx;
	font-family: 'pht';
	color:#ffffff;
}
.mycards_thr_li_c_r_f_r{
	width: 120rpx;
	height:52rpx;
	overflow:hidden;
	border-radius:50rpx;
	text-align:center;
	line-height:52rpx;
	font-size: 36rpx;
	font-family: 'pht';
	color:#242222;
	background:#ffffff;
	box-shadow: 0 0 10rpx #7545bb;
	-font-weight: bold;
	font-family: 'fzlt';
}
.mycards_thr_li_zt{
	width: 150rpx;
	height: 28rpx;
	text-align:center;
	line-height: 26rpx;
	font-size: 22rpx;
	color:#010101;
	position:absolute;
	right:22rpx;
	top:62rpx;
	background:#ffffff;
	border-radius:50rpx;
	font-weight: bold;
	border:1px solid #54209e;
	box-shadow:0 0 10rpx #7545bb;
	z-index:2;
}
.mymemxqCon{margin:26rpx;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box}
.mymemxqCon_t{font-weight:bold;font-size:26rpx;color:#333333;margin-bottom:26rpx}
.mymemxqCon_b{margin-top:20rpx;display:flex;align-items:center;justify-content:space-between}
.mymemxqCon_b view{font-size:26rpx;color:#999999}
.mymemxqCon_b text{font-size:26rpx;color:#333333}
.mymemxqCon_c{margin-top:10rpx;background:#F8F8FA;border-radius:20rpx;padding:26rpx;box-sizing:border-box}
.mymemxqCon_c view{font-size:26rpx;color:#333333;margin-top:10rpx}
.mymemxqCon_c view:nth-child(1){margin-top:0}
.mymemxqCon_jh{margin-top:20rpx;display:flex;align-items:center;justify-content:space-between}
.mymemxqCon_jh_l{font-size:26rpx;color:#999999}
.mymemxqCon_jh_r{font-size:26rpx;color:#ED860B;display:flex;align-items:center}
.mymemxqCon_jh_r image{width:22rpx;height:22rpx;margin-left:10rpx}
.ord_nav{width:100%;height:92rpx;background:#FFFFFF;position:fixed;top:0;left:0;z-index:9}
.ord_nav_li{width:33.33%;height:92rpx;float:left;overflow:hidden;display:flex;align-items:center;justify-content:center}
.ord_nav_li view text:nth-child(1){font-size:26rpx;color:#999999;position:relative;z-index:2}
.ord_nav_li view text:nth-child(2){display:block;height:12rpx;background:linear-gradient( 90deg,#131315 0%,#FFFFFF 100%);border-radius:42rpx;opacity:0;position:relative;top:-14rpx;z-index:1}
.ord_nav_li_ac view text:nth-child(1){font-weight:bold;color:#333333!important;font-family:Maoken Glitch Sans}
.ord_nav_li_ac view text:nth-child(2){opacity:1!important}
.ord_con{width:100%;height:auto;overflow:hidden;margin-top:92rpx}
.ord_con_li{margin:20rpx 30rpx 0 30rpx;height:auto;overflow:hidden;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box}
.ord_con_li_a{width:100%;height:auto;overflow:hidden;display:flex;justify-content:space-between;align-items:center;-border-bottom:1px solid #EDEDED;-padding-bottom:14rpx}
.ord_con_li_a view{font-size:26rpx;color:#999999}
.ord_con_li_a text{font-size:26rpx;color:#131315}
.ord_con_li_b{width:100%;height:auto;overflow:hidden}
.ord_con_li_b_li{width:100%;height:auto;overflow:hidden;padding:26rpx 0;border-bottom:1px solid #EBEBEB;display:flex;align-items:center;justify-content: space-between;}
.ord_con_li_b_li_l{width:158rpx;height:158rpx;border:1px solid #131315;border-radius: 20rpx;}
.ord_con_li_b_li_r{width:430rpx;width:calc(100% - 180rpx);height:158rpx;overflow:hidden;display:flex;flex-direction:column;}
.ord_con_li_b_li_r_a{font-size:26rpx;color:#333333;font-weight:bold;
display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp:1;
overflow: hidden;
margin-top:4rpx;
}
.ord_con_li_b_li_r_bb{font-size: 22rpx;color: #999999;margin-top:10rpx;}
.ord_con_li_b_li_r_cc{
	display: flex;
	align-items:center;
	justify-content: space-between;
	margin-top:40rpx;
}
.ord_con_li_b_li_r_cc view{font-size: 28rpx;color:#5C5C5C;}
.ord_con_li_b_li_r_cc text{font-size: 26rpx;color:#999;}
.ord_con_li_b_li_r_b{font-size:26rpx;color:#333;margin-top:10rpx}
.ord_con_li_b_li_r_b text{color:#EB3527;font-weight:bold}
.ord_con_li_b_li_r_c{font-size:26rpx;color:#999999;margin-top:10rpx}
.ord_con_li_c{padding:20rpx 0;-border-bottom:1px solid #EDEDED;display:flex;align-items:baseline;justify-content:flex-end}
.ord_con_li_c text{font-size:22rpx;color:#999999}
.ord_con_li_c view{font-size:26rpx;color:#333;margin-left:20rpx}
.ord_con_li_d{padding-top:20rpx;display:flex;align-items:baseline;justify-content:flex-end}
.ord_con_li_d view{border:1px solid #131315;height:56rpx;border-radius:84rpx;display:flex;line-height:54rpx;justify-content:center;font-size:26rpx;color:#131315;margin-left:40rpx;padding:0 20rpx}
.ord_con_li_d .back{background:#131315!important;color:#FFFFFF!important}
.log_con{margin:32rpx;height:auto;overflow:hidden;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box}
.log_con_t{width:100%;height:auto;overflow:hidden}
.log_con_t .ord_con_li_b_li_l{margin-left:0}
.log_con_t .ord_con_li_b_li{padding-top:0}
.log_con_c{font-size:26rpx;color:#999999;margin-top:20rpx}
.wl_tanc_b{margin-top:30rpx;overflow:auto}
.orde_thr_f_li{width:100%;height:auto;overflow:hidden;position:relative;-padding-top:60rpx;padding-bottom:40rpx}
.orde_thr_f .orde_thr_f_li:last-child{margin-bottom:60rpx}
.orde_thr_f_li_l{width:24rpx;height:100%;overflow:hidden;position:absolute;top:0;left:0}
.orde_thr_f_li_l text{display:block;width:16rpx;height:16rpx;background:#D9D9D9;border-radius:50%;box-sizing:border-box;display:block;margin:auto;position:relative;z-index:2;top:10rpx}
.orde_thr_f_li_l view{width:1px;height:100%;background:#D9D9D9;position:absolute;left:50%;-margin-left:-1rpx;top:0;z-index:1}
.wl_tanc_b .orde_thr_f_li:nth-child(1) .orde_thr_f_li_l view{top:10rpx}
.wl_tanc_b .orde_thr_f_li:nth-child(1) .orde_thr_f_li_l text{border:4rpx solid #FFCA9A;box-sizing:content-box;background:#D02926!important}
.wl_tanc_b .orde_thr_f_li:nth-child(1) .orde_thr_f_li_r .orde_thr_f_li_r_a{color:#D02926!important;font-weight:bold}
.wl_tanc_b .orde_thr_f_li:nth-child(1) .orde_thr_f_li_r .orde_thr_f_li_r_b{color:#D02926!important;font-weight:bold}
.wl_tanc_b .orde_thr_f_li:last-child{padding-bottom:0rpx}
.orde_thr_f_li_r{margin-left:46rpx;height:auto;overflow:hidden}
.orde_thr_f_li_r_a{font-size:24rpx;font-weight:400;color:#999;line-height:40rpx}
.orde_thr_f_li_r_b{font-size:24rpx;font-weight:500;color:#999;margin-top:12rpx}
.orde_thr_f_li_r_c{border-radius:10rpx;background:#F8FBFF;padding:28rpx;box-sizing:border-box;margin-top:18rpx}
.orde_thr_f_li_r_c_b{width:100%;height:auto;overflow:hidden}
.myCourse .ord_nav_li{width:20%}
.myCourse .ord_nav_li view text:nth-child(2){top:24rpx;height:6rpx;background:#131315}
.myCourse .ord_nav_li.ord_nav_li_ac view text:nth-child(1){color:#131315!important}
.myCourse_con{width:100%;height:auto;overflow:hidden;margin-top:92rpx}
.myCourse_con_li{margin:26rpx 26rpx 0 26rpx;height:auto;overflow:hidden;padding:26rpx;box-sizing:border-box;position:relative;border-radius:20rpx;background:#fff}
.myCourse_con_li_zt{position:absolute;right:0;top:0;height:40rpx;line-height:40rpx;background:#131315;border-radius:0rpx 20rpx 0rpx 20rpx;padding:0 20rpx;font-size:20rpx;color:#FFFFFF}
.myCourse_con_li_a{margin-right:100rpx;font-size:32rpx;color:#333333;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.myCourse_con_li_a text{display:inline-block;font-weight:400;height:34rpx;line-height:34rpx;font-size:22rpx;color:#FFFFFF;background:#131315;border-radius:10rpx;padding:0 10rpx;margin-right:12rpx;position:relative;top:-6rpx}
.myCourse_con_li_b{margin-top:14rpx;font-size:26rpx;color:#999999}
.myCourse_con_li_b image{display:inline-block;width:32rpx;height:32rpx;margin-right:6rpx;position:relative;top:8rpx}
.myCourse_con_li_c{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between;margin-top:20rpx}
.myCourse_con_li_c_l{display:flex;align-items:center;font-size:26rpx;color:#333333}
.myCourse_con_li_c_l image{width:42rpx;height:42rpx;border-radius:50%;margin-right:12rpx}
.myCourse_con_li_c_r{width:144rpx;height:60rpx;background:#131315;border-radius:82rpx;font-size:26rpx;color:#F8F8FA;text-align:center;line-height:60rpx}
.aqjlViw{width:100%;height:calc(20rpx + env(safe-area-inset-bottom));overflow:hidden}
.kcxq_video{width:100%;height:auto;overflow:hidden;position:relative}
.kcxq_video video{display:block;width:100%;height:432rpx;position:relative}
.kcxq_video .swiper{display:block;width:100%;height:432rpx}
.kcxq_video .swiper image{display:block;width:100%;height:432rpx}
.speed{position:absolute;right:20rpx;top:16rpx}
.doubleSpeed{color:#fff;font-size:26rpx;padding:4rpx 6rpx;float:left;margin-left:8rpx;}
.speedModal{background-color:rgba(0,0,0,0.7)}
.speedNumBox{display:flex;flex-direction:column;justify-content:space-around;align-items:center;background-color:rgba(0,0,0,0.6);width:120rpx;height:100%;position:absolute;right:0rpx;top:0}
.speedNumBox .number{width:120rpx;font-size:20rpx;height:14.28%;display:flex;justify-content:center;align-items:center;text-align:center;color:#fff}
.qpvideo .doubleSpeed{font-size:14rpx!important}
.qpvideo .speedNumBox .number{font-size:14rpx!important}
.activeClass{font-weight:bold!important}
.speedNumBox .active{color:red}
.speedNumBox .noActive{color:#fff}
.speedNumBox .number:nth-child(1){margin-top:20rpx}
.qpvideo .speedNumBox .number:nth-child(1){margin-top:0rpx!important}
.kcxq_foo{width:100%;height:calc(110rpx + env(safe-area-inset-bottom));padding:0 26rpx;box-sizing:border-box;background:#fff;overflow:hidden;position:fixed;bottom:0;left:0;z-index:9;box-shadow:0px 2px 4px 0px rgba(0,0,0,0.89);display:flex;justify-content:space-between;align-items:baseline;padding-top:16rpx}
.kcxq_foo_l{display:flex}
.kcxq_foo_l view{display:flex;flex-direction:column;justify-content:center;font-size:26rpx;color:#333333}
.kcxq_foo_l view image{width:48rpx;height:48rpx}
.kcxq_foo_r{display:flex;justify-content:center;align-items:center;position:relative;top:2rpx}
.kcxq_foo_r view{min-width:78rpx;padding:0 36rpx;height:60rpx;border-radius:82rpx;border:1px solid #131315;font-size:26rpx;color:#131315;text-align:center;line-height:58rpx;margin-left:20rpx}
.kcxq_foo_r .back{background:#131315!important;color:#fff!important}
.kcxq_foo_r .back_999{color:#999!important;border-color:#999!important}
.kcxq_one{width:100%;height:auto;overflow:hidden;padding:26rpx;box-sizing:border-box;background:#fff}
.kcxq_one_a{font-weight:bold;font-size:32rpx;color:#000000}
.kcxq_one_a text{font-weight:400;font-size:22rpx;color:#FFFFFF;height:34rpx;line-height:34rpx;background:#131315;border-radius:10rpx;padding:0 10rpx;display:inline-block;margin-left:20rpx;position:relative;top:-6rpx}
.kcxq_one_a_bq{margin-top:16rpx}
.kcxq_one_a_bq text:nth-child(1){margin-left:0!important}
.kcxq_one_c{margin-left:100rpx;height:auto;overflow:hidden}
.kcxq_one_c view{font-size:26rpx;color:#999999;margin-top:10rpx}
.kcxq_one_c view image{display:inline-block;width:28rpx;height:28rpx;margin-left:20rpx;position:relative;top:6rpx}
.kcxq_one_b{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between;margin-top:50rpx}
.kcxq_one_b_l{width:80rpx;height:80rpx;border-radius:50%}
.kcxq_one_b_r{width:calc(100% - 100rpx);height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between}
.kcxq_one_b_r_l{-display:flex;flex-direction:column}
.kcxq_one_b_r_l view{font-size:26rpx;color:#333333;font-weight:bold}
.kcxq_one_b_r_l text{font-weight:400;font-size:22rpx;color:#FFFFFF;height:34rpx;line-height:34rpx;background:#131315;border-radius:10rpx;padding:0 10rpx;display:inline-block;margin-top:8rpx}
.kcxq_one_b_r_r{font-size:26rpx;color:#999999;display:flex;align-items:center}
.kcxq_one_b_r_r image{width:12rpx;height:20rpx;margin-left:20rpx;position:relative;top:2rpx}
.kcxq_one_bz{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;font-size:26rpx;color:#999999;margin-top:16rpx}
.kcxq_one_bz image{width:28rpx;height:28rpx;margin-right:10rpx}
.kcxq_two{width:100%;height:auto;overflow:hidden;background:#fff;margin-top:20rpx;padding:26rpx 0;box-sizing:border-box;position:relative}
.kcxq_two_t{margin:0 26rpx;height:auto;overflow:hidden}
.kcxq_two_t_n{float:left}
.kcxq_two_t_n text:nth-child(1){position:relative;z-index:2;font-size:32rpx;color:#333333;font-weight:bold;font-family:'pht';letter-spacing: 2px;}
.kcxq_two_t_n text:nth-child(2){display:block;position:relative;z-index:1;height:10rpx;background:linear-gradient( 90deg,#131315 28%,rgba(255,255,255,0) 100%);top:-16rpx}
.kcxq_two_xf{position:absolute;right:0;top:0;height:48rpx;background:#131315;border-radius:20rpx 0rpx 0rpx 20rpx;font-size:20rpx;color:#FFFFFF;padding:0 14rpx;line-height:48rpx}
.kcxq_two_b view{width:20%;float:left;height:auto;overflow:hidden;margin-top:26rpx}
.kcxq_two_b view image{display:block;width:80rpx;height:80rpx;border-radius:50%;margin:auto}
.kcxq_two_b view text{display:block;margin-top:20rpx;font-size:26rpx;color:#333333;text-align:center;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.kcxq_thr{margin-bottom:120rpx}
.kcxq_thr_b{width:100%;height:auto;overflow:hidden}
.kcxq_thr_b_t{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:center;margin-bottom:24rpx}
.kcxq_thr_b_t view{height:auto;background:#F8F8F8;border-radius:112rpx;font-size:26rpx;color:#333333;display:flex;align-items:center;justify-content:center;padding:26rpx 22rpx;max-width:90%;margin:auto}
.kcxq_thr_b_t view image{width:23rpx;height:30rpx;margin-right:10rpx}
.kcxq_thr_b_t view text{width:calc(100% - 40rpx);font-size:26rpx;color:#333333;white-space:normal;word-break:break-all;margin-left:6rpx}
.inv_one{width:100%;height:882rpx;overflow:hidden;position:relative}
.inv_one image{display:block;width:100%;height:100%}
.videoLi{
	width: 100%;
	height:480rpx;
	overflow:hidden;
	position: relative;
}
.videoLi_bf{
	width:80rpx;
	height: 80rpx;
	position:absolute;
	top:0;left:0;
	right: 0;bottom:0;
	margin: auto;
	z-index: 9;
	opacity: .8;
}
.videoLi video{
	width: 100%;
	height:480rpx;
	overflow:hidden;
}
.videoLi_bj{
	width: 100%;
	height: 100%;
	position:absolute;
	top:0;left:0;
	z-index:8;
}
/* .inv_one view{font-size:32rpx;color:#4388F8;text-align:center;width:100%;position:absolute;top:190rpx;text-align:center} */
.inv_one text{display:block;width:144rpx;height:60rpx;background:rgba(255,255,255,0.8);border-radius:100rpx 0rpx 0rpx 100rpx;font-size:26rpx;color:#747474;text-align:center;line-height:60rpx;position:absolute;right:0;top:216rpx}
.inv_one_wz{
	width: 100%;
	height:auto;
	overflow:hidden;
	top:0;
	margin-top:126rpx;
	position:absolute;
	z-index:2;
}
.inv_one_wz view{
	text-align:center;
	color:#3354BD;
	font-size: 28rpx;
	font-weight: bold;
	line-height: 40rpx;
}
.inv_two{width:100%;height:auto;overflow:hidden;margin:62rpx 0}
.inv_two_li{width:33.33%;width:50%;position:relative;float:left}
.inv_two_li button{display:block;width:100%!important;height:100%!important;position:absolute;top:0;left:0;z-index:9;opacity:0}
.inv_two_li image{width:80rpx;height:80rpx;display:block;margin:auto}
.inv_two_li view{font-size:26rpx;color:#333333;text-align:center;margin-top:50rpx}
.inv_two_li text{width:1px;height:140rpx;background:#EBEBEB;position:absolute;right:0;top:50%;margin-top:-70rpx}
.inv_thr{display:flex;align-items:center;justify-content:center}
.inv_thr .inv_thr_a{width:16rpx;height:16rpx;background:#E8A051;border-radius:50%;margin:0 14rpx}
.inv_thr .inv_thr_b{width:24rpx;height:24rpx;background:#3EA2FB;border-radius:50%;margin:0 14rpx}
.inv_thr view{font-weight:bold;font-size:26rpx;color:#333333;margin:0 14rpx}
.inv_four{margin:38rpx 28rpx;height:auto;overflow:hidden;padding:26rpx 0;background:#AECFFF;border-radius:20rpx}
.inv_four_li{width:50%;float:left;position:relative}
.inv_four_li_xian{width:1px;height:60rpx;background:#fff;position:absolute;right:0;top:50%;margin-top:-30rpx}
.inv_four_li_a{font-size:26rpx;color:#3885E4;text-align:center}
.inv_four_li_a text{font-size:46rpx;font-family: 'pht';}
.inv_four_li_b{font-size:26rpx;color:#3885E4;text-align:center;margin-top:26rpx;font-family: 'pht';}
.edi_ban{width:100%;height:auto;overflow:hidden}
.edi_ban image{display:block;width:100%;height:548rpx}
.edi_one{width:100%;height:auto;overflow:hidden;text-align:center;position:relative;margin-top:-80rpx}
.edi_one image{display:block;width:158rpx;height:158rpx;margin:auto;margin-bottom:26rpx;border-radius:50%;background-color:#fff}
.edi_one text{font-size:26rpx;color:#999999}
.edi_one button{display:block;width: 100%;height: 100%;position:absolute;top:0;left:0;z-index:9;opacity: 0;}
.edma_two{width:452rpx;height:auto;overflow:hidden;margin:auto;margin-top:58rpx}
.edma_two_li{width:100%;height:80rpx;overflow:hidden;border-bottom:1px solid #EBEBEB;display:flex;align-items:center;justify-content:space-between}
.edma_two_li view{font-weight:500;font-size:26rpx;color:#333333}
.edma_two_li input{width:calc(100% - 96rpx);font-weight:400;font-size:26rpx;color:#333333}
.edma_thr{width:500rpx;height:auto;overflow:hidden;margin:400rpx auto 40rpx auto}
.edma_thr view{width:100%;height:90rpx;overflow:hidden;background:#EEEEEE;border-radius:50rpx;font-size:32rpx;color:#333333;line-height:90rpx;text-align:center;margin-bottom:30rpx}
.edma_thr view:nth-child(1){color:#fff!important;background:#131315!important}
.int_one{margin:20rpx 32rpx;height:286rpx;background:#131315;border-radius:20rpx;overflow:hidden;padding:0 74rpx;box-sizing:border-box;display:flex;justify-content:center;flex-direction:column;position:relative}
.int_one_a{font-size:26rpx;color:#FFFFFF}
.int_one_b{font-size:60rpx;color:#FFFFFF;font-weight:bold;margin-top:22rpx}
.int_one_c{width:164rpx;height:68rpx;background:#FFFFFF;border-radius:40rpx 0rpx 0rpx 40rpx;position:absolute;right:0;top:50%;margin-top:-34rpx;font-size:26rpx;color:#131315;text-align:center;line-height:68rpx}
.int_two{margin:28rpx 32rpx 0 32rpx;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between}
.int_two_l{width:332rpx;height:84rpx;overflow:hidden;background:#FFFFFF;border-radius:10rpx;overflow:hidden}
.int_two_l view{width:33.33%;height:84rpx;overflow:hidden;text-align:center;line-height:84rpx;font-size:32rpx;color:#333333;float:left}
.int_two_l_ac{color:#fff!important;background:#131315!important;border-radius:10rpx}
.int_two_r{width:240rpx;height:84rpx;background:#FFFFFF;border-radius:10rpx}
.int_two_r .uni-input{width:100%;height:84rpx;background:#FFFFFF;border-radius:10rpx;display:flex;align-items:center;justify-content:center;font-size:32rpx;color:#333333;position:static}
.int_two_r .uni-input text{display:block;width:0;height:0;border-left:10rpx solid transparent;border-right:10rpx solid transparent;border-top:14rpx solid #333333;margin-left:14rpx;position:relative;top:2rpx}
.int_thr{margin:32rpx;height:auto;overflow:hidden;background:#fff;border-radius:20rpx;padding:0 26rpx;box-sizing:border-box}
.int_thr_li{padding:26rpx 0;border-bottom:1px solid #E5E5E5;position:relative}
.int_thr .int_thr_li:last-child{border-bottom:none}
.int_thr_li_a{font-weight:bold;font-size:26rpx;color:#333333}
.int_thr_li_b{font-size:26rpx;color:#999999;margin-top:14rpx}
.int_thr_li_c{font-size:32rpx;color:#FD7C49;width:200rpx;position:absolute;right:0;top:50%;margin-top:-24rpx;text-align:right}
.inv_news{
	width: 100%;
	height:auto;
	overflow:hidden;
}
.inv_news_a{
	height:auto;
	overflow:hidden;
	position: relative;
}
.inv_news_a_bj{
	display: block;
	width:100%;
	height:100%;
	position:absolute;
	top:0;left:0;
}
.inv_news_a_t{
	margin:0 40rpx;
	height:54rpx;
	border-radius:50rpx;
	background:#ad1f31;
	text-align:center;
	line-height:54rpx;
	font-size: 36rpx;
	color:#fff;
	font-weight: bold;
}
.inv_news_a_b{
	width: 100%;
	height:auto;
	overflow:hidden;
}
.inv_news_a_b_li{
	width: 322rpx;
	height: 228rpx;
	height:auto;
	padding:32rpx 0rpx;
	overflow: hidden;
	position:relative;
	margin-top: 34rpx;
	border-radius:14rpx;
	background:#fff;
	box-shadow:0 0 10rpx #e1e0e0;
}
.inv_news_a_b .inv_news_a_b_li:nth-child(odd){
	float:left;
	margin-left:40rpx;
}
.inv_news_a_b .inv_news_a_b_li:nth-child(even){
	float:right;
	margin-right:40rpx;
}
.inv_news_a_b_li image{
	display: block;
	width: 92rpx;
	height: 92rpx;
	border-radius:50%;
	margin:auto;
}
.inv_news_a_b_li text{
	color: #333333;
	font-size: 32rpx;
	display: block;
	text-align:center;
	margin-top:16rpx;
	font-weight: bold;
	font-family: 'pht';
}
.inv_news_a_b_li view{
	color: #acacac;
	font-size:20rpx;
	display: block;
	text-align:center;
	margin-top:16rpx;
	font-family: 'pht';
}
.inv_news_a_b_li button{
	display:block;
	width: 100%;
	height: 100%;
	position:absolute;
	top:0;left:0;
	opacity:0;
}
.inv_news_a_b .inv_four_li{
	width:33.33%;
}
.inv_news_a_b .inv_four_li .inv_four_li_xian{
	background:#131315;
	height:50rpx;
	margin-top:-25rpx;
}
.inv_news_a_b .inv_four_li_a{
	color:#131315;
}
.inv_news_a_b .inv_four_li_b {
	color:#131315;
	margin-top:20rpx;
}
.yqmx .inv_news_a_b{
	width:auto;
	margin:30rpx 40rpx;
	border-radius: 14rpx;
	background: #fff;
	box-shadow: 0 0 10rpx #e1e0e0;
	padding: 50rpx 0;
}
.yqmx{
	margin-top:36rpx;
}
.yqmx .inv_four_li_b{
	color: #acacac;
	font-size: 20rpx;
	display: block;
	text-align: center;
	margin-top:20rpx;
}
.cou_con{width:100%;height:auto;overflow:hidden;margin-top:92rpx}
.cou_con_li{margin:32rpx 32rpx 0 32rpx;height:172rpx;border-radius:20rpx;overflow:hidden;background:#FFFFFF}
.cou_con_li_l{width:140rpx;height:172rpx;overflow:hidden;position:relative;display:flex;align-items:center;justify-content:center;flex-direction:column;background:#FFF2F2;float:left}
.cou_con_li_l_a{width:100rpx;height:26rpx;background:linear-gradient( 270deg,#FA7D07 0%,#FFBB25 100%);border-radius:20rpx 0rpx 20rpx 0rpx;font-size:16rpx;color:#FFFFFF;text-align:center;line-height:26rpx;position:absolute;left:0;top:0}
.cou_con_li_l_b{font-size:32rpx;color:#FF1E02}
.cou_con_li_l_b text{font-size:60rpx}
.cou_con_li_l_c{font-size:26rpx;color:#FF1E02}
.cou_con_li_r{width:calc(100% - 140rpx);height:172rpx;overflow:hidden;position:relative;display:flex;justify-content:center;flex-direction:column;float:left;padding:0 16rpx;box-sizing:border-box}
.cou_con_li_r_d{width:144rpx;height:56rpx;background:#131315;border-radius:88rpx;font-size:26rpx;text-align:center;line-height:56rpx;color:#FFFFFF;position:absolute;right:16rpx;top:50%;margin-top:-28rpx}
.cou_con_li_r_a{font-size:32rpx;color:#333333;font-weight:bold}
.cou_con_li_r_b{font-size:24rpx;color:#999999;margin:10rpx 0}
.cou_con_li_r_c{font-size:24rpx;color:#999999}
.les_con{width:100%;height:auto;overflow:hidden;margin-top:94rpx}
.les_con_li{margin:26rpx 26rpx 0 26rpx;height:auto;padding:26rpx;overflow:hidden;box-sizing:border-box;border-radius:10rpx;background:#fff;display:flex;justify-content:space-between}
.les_con_li_l{width:214rpx;height:126rpx;border-radius:10rpx}
.les_con_li_r{width:calc(100% - 234rpx);height:126rpx;overflow:hidden}
.les_con_li_r_a{font-weight:bold;font-size:30rpx;color:#333333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.les_con_li_r_b{font-size:26rpx;color:#BCBCBC;margin:10rpx 0}
.les_con_li_r_c{font-size:26rpx;color:#BCBCBC}
.les_search{width:100%;height:94rpx;background:#FFFFFF;position:fixed;top:0;left:0;padding:0 26rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between;z-index:9}
.les_search_l{width:612rpx;height:70rpx;background:#F6F6F6;border-radius:96rpx;overflow:hidden;padding:0 26rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.les_search_l image{width:28rpx;height:28rpx}
.les_search_l input{width:calc(100% - 40rpx);font-size:26rpx;color:#333}
.les_search_r{font-size:26rpx;color:#131315}
.kcxq_tab{position:relative}
.kcxq_tab .ord_nav_li{width:50%}
.kcxq_tab .ord_nav_li view text:nth-child(1){font-size:32rpx!important;color:#333!important}
.kbxq_one{width:100%;height:auto;overflow:hidden;margin-top:26rpx}
.kbxq_one_b{height:auto;overflow:hidden;margin:0 26rpx;font-size:26rpx;color:#333333}
.kbxq_one_c{width:100%;height:auto;overflow:hidden;margin-top:28rpx}
.kbxq_one_c image{display:block;width:100%;height:auto}
.kbxq_one .kcxq_one_b{margin:50rpx 26rpx;width:auto}
.kbxq_ml{margin:0 26rpx 26rpx 26rpx}
.kbxq_ml_li{width:100%;height:auto;overflow:hidden;margin-bottom:26rpx}
.kbxq_ml_li_t{width:100%;height:86rpx;background:#F6F6F6;border-radius:10rpx;display:flex;align-items:center;justify-content:space-between;padding:0 26rpx;box-sizing:border-box}
.kbxq_ml_li_t view{width:calc(100% - 80rpx);font-weight:bold;font-size:32rpx;color:#333333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.kbxq_ml_li_t text{display:block;width:0;height:0;border-left:10rpx solid transparent;border-right:10rpx solid transparent;border-top:14rpx solid #999}
.kbxq_ml_li_b{}
.kbxq_ml_li_b_li{width:100%;height:auto;overflow:hidden;border-bottom:1px solid #EBEBEB;padding:46rpx 26rpx 26rpx 26rpx;box-sizing:border-box;display:flex;justify-content:space-between}
.kbxq_ml_li_b_li view{font-size:26rpx;color:#333333;width:calc(100% - 100rpx)}
.kbxq_ml_li_b_li text{font-size:26rpx;color:#131315}
.video_tanc{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.8);display:flex;align-items:center;justify-content:center;flex-direction:column}
.video_tanc video{display:block;width:100%;height:50vh;position:relative}
.video_tanc image{display:block;width:56rpx;height:56rpx;margin-top:30rpx}
.ran_one{width:380rpx;height:70rpx;background:#FFFFFF;border-radius:58rpx;overflow:hidden;margin:22rpx auto;display:flex;align-items:center;justify-content:space-between}
.ran_one view{height:70rpx;text-align:center;line-height:70rpx;font-size:26rpx;color:#6A6A6A}
.ran_one view:nth-child(1){width:56%}
.ran_one view:nth-child(2){width:44%}
.ran_one_ac{background:#131315!important;color:#fff!important;border-radius:58rpx}
.ran_thr{width:100%;height:auto;overflow:hidden;border-radius:20rpx 20rpx 0rpx 0rpx;background:#fff}
.ran_thr_t{width:100%;height:68rpx;overflow:hidden;margin-top:16rpx;padding:0 26rpx;box-sizing:border-box}
.ran_thr_t view{font-size:26rpx;color:#999999;line-height:68rpx;float:left}
.ran_thr_t view:nth-child(1){text-align:left;width:10%}
.ran_thr_t view:nth-child(2){width:50%;padding-left:40rpx;box-sizing:border-box}
.ran_thr_t view:nth-child(3){text-align:right;width:40%}
.ran_thr_b_li{width:100%;height:76rpx;overflow:hidden;padding:0 26rpx;box-sizing:border-box}
.ran_thr_b_li_a{text-align:left;width:10%;font-size:26rpx;color:#333333;font-weight:bold;float:left;line-height:76rpx}
.ran_thr_b_li_b{width:50%;height:76rpx;float:left;display:flex;align-items:center;justify-content:space-between}
.ran_thr_b_li_b image{width:52rpx;height:52rpx;border-radius:50%}
.ran_thr_b_li_b text{font-size:26rpx;color:#333333;width:calc(100% - 67rpx)}
.ran_thr_b_li_c{width:40%;font-weight:400;font-size:26rpx;float:left;text-align:right;line-height:76rpx}
.ran_thr_c{font-size:26rpx;color:#999999;margin:40rpx;text-align:center}
.ran_two{margin:85rpx 48rpx 0 48rpx;height:auto;height:424rpx;overflow:hidden}
.ran_two_li{float:left;height:100%;position:relative}
.ran_two_li_a{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between;flex-direction:column}
.ran_two .ran_two_li:nth-child(1) .ran_two_li_a{position:relative;top:76rpx}
.ran_two .ran_two_li:nth-child(2) .ran_two_li_a{position:relative;top:18rpx}
.ran_two .ran_two_li:nth-child(3) .ran_two_li_a{position:relative;top:76rpx}
.ran_two_li_a image:nth-child(1){width:76rpx;height:76rpx;position:relative;z-index:2}
.ran_two_li_a image:nth-child(2){width:130rpx;height:130rpx;border-radius:50%;border:4rpx solid #C3CBD2;margin-top:-36rpx}
.ran_two_li_a view{font-size:32rpx;color:#FFFFFF;margin-top:12rpx;margin-bottom:2rpx;line-height:32rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.ran_two_li_a text{font-size:26rpx;color:#999999}
.ran_two .ran_two_li .ran_two_li_b{position:absolute;bottom:0;left:0;width:100%;font-size:40rpx;color:#FFFFFF;display:flex;align-items:center;justify-content:center;font-weight:bold}
.ran_two .ran_two_li:nth-child(1){width:209rpx}
.ran_two .ran_two_li:nth-child(1) .ran_two_li_b{background:#B9C4C9;height:90rpx;border-top-left-radius:20rpx}
.ran_two .ran_two_li:nth-child(2){width:calc(100% - 383rpx)}
.ran_two .ran_two_li:nth-child(2) .ran_two_li_b{background:#FEC41F;height:150rpx;border-radius:20rpx 20rpx 0 0}
.ran_two .ran_two_li:nth-child(3){width:174rpx}
.ran_two .ran_two_li:nth-child(3) .ran_two_li_b{background:#DFAD7B;height:90rpx;border-top-right-radius:20rpx}
.ran_two_li{}
.mes_one{width:100%;height:auto;overflow:hidden;position:fixed;top:0;left:0;z-index:9;background:#fff;display:flex;align-items:center;justify-content:center}
.mes_one view{width:163rpx;height:68rpx;background:#F6F6F6;border-radius:140rpx;font-weight:500;font-size:26rpx;color:#333333;margin:20rpx 23rpx;text-align:center;line-height:68rpx}
.mes_one_ac{background:#131315!important;color:#FFFFFF!important}
.mes_two{margin:100rpx 26rpx 0 26rpx;height:auto;overflow:hidden}
.mes_two_li{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid #F4F4F4;overflow:hidden;padding:20rpx 0}
.mes_two_li_tz{width:96rpx;height:96rpx;border-radius:50%}
.mes_two_li_xx{width:96rpx;height:96rpx;border-radius:50%;border:1px solid #131315;box-sizing:border-box;background:#131315;}
.mes_two_li_c{width:calc(100% - 120rpx);height:auto;overflow:hidden}
.mes_two_li_c_t{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between}
.mes_two_li_c_t view{width:calc(100% - 240rpx);font-size:32rpx;color:#333333;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.mes_two_li_c_t text{font-size:24rpx;color:#777777}
.mes_two_li_c_b{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between;margin-top:8rpx}
.mes_two_li_c_b view{width:100%;font-weight:400;font-size:24rpx;color:#333333}
.lea_one{width:100%;height:122rpx;overflow:hidden;padding:0 34rpx;background:#131315;line-height:40rpx;text-align:right;box-sizing:border-box;line-height:102rpx}
.lea_one text{font-size:26rpx;color:#FFFFFF}
.lea_two{padding:38rpx 32rpx;background:#F6F6F6;border-radius:20rpx;box-sizing:border-box;background:#F6F6F6;margin-top:-20rpx;position:relative}
.lea_two_a{background:#FFFFFF;border-radius:20rpx;overflow:hidden;margin-bottom:26rpx}
.lea_two_a_li{height:88rpx;background:#fff;overflow:hidden;padding:0 26rpx;box-sizing:border-box;border-bottom:1px solid #EBEBEB;display:flex;align-items:center;justify-content:space-between;position:relative}
.lea_two_a .lea_two_a_li:last-child{border-bottom:none}
.lea_two_a_li_l{font-size:26rpx;color:#131315}
.lea_two_a_li_l text{color:#131315}
.lea_two_a_li_r{width:calc(100% - 200rpx)}
.lea_two_a_li_r image{width:12rpx;height:21rpx}
.lea_two_a_li_r>.uni-input{height:88rpx;display:flex;align-items:center;justify-content:flex-end;font-size:26rpx;color:#333333}
.lea_two_a_li_r>.uni-input text{font-size:26rpx;color:#333333}
.lea_two_a_li_r>.uni-input image{margin-left:20rpx}
.lea_two_b{overflow:hidden;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box}
.lea_two_b_a{overflow:hidden}
.lea_two_b textarea{display:block;height:210rpx;font-size:26rpx;color:#333;width:100%;margin-top:26rpx}
.lea_two_sub{width:502rpx;height:90rpx;background:#131315;text-align:center;line-height:90rpx;font-size:26rpx;color:#FFFFFF;margin:50rpx auto;border-radius:50rpx}
.lea_con{width:100%;height:auto;overflow:hidden;margin-top:96rpx}
.lea_con_li{padding:26rpx;box-sizing:border-box;background:#fff;background:#FFFFFF;border-radius:20rpx;margin:18rpx 32rpx 0 32rpx}
.lea_con_li_a{display:flex;align-items:center;justify-content:space-between}
.lea_con_li_a_l{font-size:26rpx;color:#333333;display:flex;align-items:center}
.lea_con_li_a_l text{display:inline-block;width:1px;height:29rpx;background:#EBEBEB;margin:0 12rpx}
.lea_con_li_a_r{font-size:26rpx;color:#3B6C2A}
.lea_con_li_b{font-weight:400;font-size:26rpx;color:#999999;margin-top:12rpx}
.lea_con_li_c{width:100%;height:auto;overflow:hidden;border-top:1px solid #EBEBEB;padding-top:20rpx;margin-top:20rpx}
.lea_con_li_c view{width:144rpx;height:60rpx;background:#131315;border-radius:82rpx;font-size:26rpx;color:#F8F8FA;text-align:center;line-height:60rpx;float:right}
.lea_nav{width:100%;height:96rpx;background:#FFFFFF;overflow:hidden;display:flex;align-items:center;justify-content:space-between;position:fixed;top:0;left:0;z-index:9}
.lea_nav_l{width:536rpx}
.lea_nav_l_li{width:33.33%;float:left;line-height:96rpx;font-size:26rpx;color:#333333;text-align:center}
.lea_nav_l_li_ac{color:#131315!important;font-weight:bold}
.lea_nav_r{width:calc(100% - 536rpx);display:flex;align-items:center;justify-content:center;height:36rpx;border-left:1px solid #EBEBEB;position:relative;overflow:hidden}
.lea_nav_r_sj{position:absolute;top:0;left:0;width:100%;height:100%}
.lea_nav_r_sj .uni-date-editor{opacity:0}
.lea_nav_r text{font-size:26rpx;color:#333333}
.lea_nav_r image{width:28rpx;height:28rpx;margin-left:20rpx}
.set_con{margin:26rpx 32rpx;height:auto;overflow:hidden;background:#fff;border-radius:20rpx}
.set_con_li{width:100%;height:88rpx;overflow:hidden;border-bottom:1px solid #EBEBEB;box-sizing:border-box;padding:0 26rpx;display:flex;align-items:center;justify-content:space-between}
.set_con_li image{width:14rpx;height:22rpx}
.set_con .set_con_li:last-child{border-bottom:none}
.fee_one{padding:20rpx 32rpx;box-sizing:border-box;background:#fff;overflow:hidden;margin-top:20rpx}
.fee_one_t{font-size:32rpx;color:#333333;font-weight:bold}
.fee_one_b{height:auto;overflow:hidden;margin-left:32rpx}
.fee_one_b .fee_one_b_li{height:60rpx;border-radius:10rpx;border:1px solid #EBEBEB;min-width:170rpx;min-width:206rpx;padding:0 18rpx;font-size:26rpx;color:#999999;line-height:60rpx;float:left;margin-right:32rpx;text-align:center;overflow:hidden;margin-top:24rpx}
.fee_one_b_li_ac{background:#131315!important;color:#fff!important;border-color:#131315!important}
.fee_one_yi{padding:20rpx 0rpx}
.fee_one_yi .fee_one_t{margin:0 32rpx}
.fee_two textarea{width:100%;font-size:26rpx;color:#333;display:block;margin-top:26rpx;height:100rpx}
.fee_thr input{width:100%;font-size:26rpx;color:#333;display:block;margin-top:26rpx}
.fee_two_tp{width:100%;height:auto;overflow:hidden}
.fee_two_tp_li{width:168rpx;height:168rpx;border-radius:10rpx;overflow:hidden;float:left;margin-right:20rpx;margin-top:20rpx;position:relative;border:1px solid #F8F8F8}
.fee_two_tp_li_tp{display:block;width:100%;height:100%}
.fee_two_tp_li_gb{width:35rpx;height:35rpx;position:absolute;right:10rpx;top:10rpx;border-radius:50%;background-color:#fff}
.buy_one{width:100%;height:96rpx;background:#FFFFFF;position:relative;display:flex;align-items:center;padding:0 26rpx;box-sizing:border-box}
.buy_one view{font-size:26rpx;color:#333333;margin-right:48rpx}
.buy_one_ac{font-weight:bold!important;font-size:32rpx!important;color:#131315!important}
.memk_one{width:100%;height:76rpx;background:#FFFFFF;display:flex}
.memk_one view{display:flex;justify-content:center;align-items:center;flex-direction:column;font-size:26rpx;color:#333333;margin-left:48rpx}
.memk_one text{width:40rpx;height:6rpx;background:#131315;border-radius:74rpx;overflow:hidden;opacity:0;position:relative;top:14rpx}
.memk_one_ac{font-weight:bold;color:#131315!important}
.memk_one_ac text{opacity:1!important}
.memk_ban{margin:20rpx 28rpx 0 28rpx;height:392rpx;border-radius:20rpx;overflow:hidden;position:relative}
.memk_ban_bj{display:block;width:100%;height:100%}
.memk_ban_n{width:100%;height:auto;overflow:hidden;position:absolute;bottom:62rpx;left:0;padding:0 70rpx;box-sizing:border-box}
.memk_ban_n_v{max-width:100%;float:left;display:flex;align-items:center}
.memk_ban_n_v view{font-size:26rpx;color:#FFFFFF;margin-right:18rpx;font-weight:bold}
.memk_ban_n_v text{display:block;width:0;height:0;border-left:10rpx solid transparent;border-right:10rpx solid transparent;border-top:14rpx solid #fff;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}
.memk_ban .swiper{display:block;width:100%;height:392rpx;overflow:hidden}
.memk_ban .swiper image{display:block;width:100%;height:100%}
.memk_two{margin:52rpx 28rpx 0 28rpx;height:auto;overflow:hidden}
.memk_two_b{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between;flex-wrap:wrap}
.memk_two_b_li{width:334rpx;height:auto;min-height:68rpx;background:#FFFFFF;border-radius:50rpx 10rpx 10rpx 50rpx;margin-top:20rpx;display:flex;align-items:center;justify-content:space-between}
.memk_two_b_li_l{width:68rpx;height:68rpx;background:#131315;border-radius:108rpx;font-size:26rpx;color:#FFFFFF;text-align:center;line-height:68rpx}
.memk_two_b_li_r{width:calc(100% - 100rpx);background:#FFFFFF;border-radius:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;margin-right:20rpx}
.memk_two_b_li_r view{font-size:26rpx;color:#333333;width:100%;text-align:center}
.memk_thr{width:100%;height:300rpx;overflow:auto;white-space:nowrap;margin-top:66rpx;padding-top:10rpx}
.memk_thr_li{width:216rpx;height:272rpx;background:#FFFFFF;border-radius:20rpx;border:2px solid #fff;position:relative;display:flex;display:inline-block;align-items:center;justify-content:center;flex-direction:column;margin:0 14rpx;transition:0.5s}
.memk_thr .memk_thr_li:last-child{margin-right:28rpx}
.memk_thr .memk_thr_li:nth-child(1){margin-left:28rpx}
.memk_thr_li_a{color:#333333;font-size:26rpx;text-align:center;margin-top:30rpx}
.memk_thr_li_a text{color:#333333;font-size:60rpx;font-weight:bold;font-family:Maoken Glitch Sans;font-family:'pht'}
.memk_thr_li_b{font-size:26rpx;color:#333333;text-align:center;margin:24rpx 0}
.memk_thr_li_c{text-align:center}
.memk_thr_li_c text{display:inline-block;height:50rpx;background:#EEF0F2;border-radius:84rpx;padding:0 18rpx;font-size:26rpx;color:#333333;line-height:50rpx;font-weight:bold}
.memk_thr_li_ac{transition:0.5s;border:2px solid #131315!important;position:relative;top:-8rpx}
.memk_thr_li_ac .memk_thr_li_xz{opacity:1!important}
.memk_thr_li_xz{width:38rpx;height:38rpx;position:absolute;left:50%;margin-left:-19rpx;bottom:-19rpx;opacity:0;border-radius:50%;background:#131315;}
.memk_fou{width:502rpx;height:90rpx;border-radius:80rpx;background:#131315;font-size:26rpx;color:#FFFFFF;text-align:center;line-height:90rpx;margin:56rpx auto 0 auto}
.memk_fiv{margin:30rpx;font-size:22rpx;color:#333333;text-align:center}
.memk_fiv text{font-size:22rpx;color:#333333}
.memk_six{margin:28rpx 28rpx 270rpx 28rpx;height:auto;overflow:hidden;padding:26rpx;box-sizing:border-box;border:1px dashed #DEDEDE;border-radius:20rpx}
.memk_six_a{font-size:32rpx;color:#333333;display:flex;align-items:center;font-weight:bold}
.memk_six_a image{width:32rpx;height:32rpx;margin-right:10rpx}
.memk_six_b{font-size:28rpx;color:#999999;margin-top:20rpx}
.coursePackage .les_search{position:-webkit-sticky;position:sticky}
.cour_one{width:100%;height:96rpx;background:#FFFFFF;-overflow:hidden;position:relative}
.cour_one_n{width:100%;height:96rpx;background:#FFFFFF;overflow:hidden;position:relative;z-index:999999}
.cour_one_n view{width:33.33%;height:96rpx;font-size:32rpx;line-height:96rpx;color:#333333;text-align:center;line-height:96rpx;float:left}
.cour_one_ac{font-weight:bold;color:#131315!important}
.cour_two{width:100%;height:auto;overflow:hidden;margin-bottom:240rpx}
.cour_two_li{margin:20rpx 26rpx 0 26rpx;padding:26rpx;box-sizing:border-box;border-radius:20rpx;background:#fff;display:flex;align-items:center;justify-content:space-between}
.cour_two_li_l{width:214rpx;height:236rpx;border-radius:10rpx;overflow:hidden}
.cour_two_li_l image{display:block;width:100%;height:100%}
.cour_two_li_r{width:calc(100% - 234rpx);min-height:236rpx;overflow:hidden;display:flex;flex-direction:column;justify-content:center}
.cour_two_li_r_a{font-weight:bold;font-size:30rpx;color:#333333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.cour_two_li_r_b{font-size:26rpx;color:#131315;margin-top:10rpx}
.cour_two_li_r_c{font-size:26rpx;color:#BCBCBC;margin-top:10rpx}
.cour_two_li_r_d{font-size:26rpx;color:#BCBCBC;margin-top:10rpx}
.cour_two_li_r_e{display:flex;align-items:center;justify-content:space-between;margin-top:18rpx}
.cour_two_li_r_e_l{font-size:20rpx;color:#A5A5A5;display:flex;align-items:center}
.cour_two_li_r_e_l text{font-size:28rpx;color:#FF0000;font-weight:bold;margin-left:20rpx}
.cour_two_li_r_e_r{width:120rpx;height:50rpx;background:#131315;border-radius:98rpx;font-size:26rpx;color:#FFFFFF;text-align:center;line-height:50rpx}
.stor_thr_c_fixed .cour_one_n{border-radius:20rpx;border-radius:20rpx 20rpx 0 0}
.poi_one{width:100%;height:auto;overflow:hidden;background:#fff;display:flex;-margin-top:20rpx;border-top:20rpx solid #F6F6F6;border-bottom:20rpx solid #F6F6F6}
.myMemberCard{overflow:hidden}
.poi_one_l{width:30%;height:152rpx;overflow:hidden;display:flex;align-items:center;justify-content:center;flex-direction:column}
@font-face{font-family:'MaokenGlitchSans';src:url('https://danceadmin.xinzhiyukeji.cn/ttf/Maoken_Glitch_Sans.subset.OTF') format('opentype')}
.poi_one_l view{font-size:60rpx;color:#333333;font-weight:bold;font-family:'pht'}
.poi_one_l text{display:block;font-size:26rpx;color:#333333;margin-top:10rpx}
.poi_one_r{width:35%;height:152rpx;overflow:hidden;display:flex;align-items:center;justify-content:center;flex-direction:column;position:relative;left:30rpx}
.poi_one_r image{display:block;width:48rpx;height:48rpx}
.poi_one_r text{display:block;font-size:26rpx;color:#333333;margin-top:26rpx}
.poi_two{width:100%;height:104rpx;background:#FFFFFF;-margin-top:20rpx;display:flex}
.poi_two_l{font-weight:bold;line-height:104rpx;width:206rpx;font-size:32rpx;color:#333333;text-align:right;font-family:Maoken Glitch Sans;font-family:'pht';letter-spacing: 2px;}
.poi_two_r{width:calc(100% - 206rpx);height:104rpx;overflow:hidden;display:flex;align-items:center;justify-content:center}
.poi_two_r .les_search_l{width:364rpx!important}
.poi_thr{width:100%;height:auto;overflow:hidden;-margin-bottom:220rpx}
.poi_thr .gg_loding{margin-bottom:220rpx}
.poi_thr_l{width:206rpx;height:auto;height:100%;overflow:hidden;float:left;background:#F6F6F6;position:absolute;left:0}
.poi_thr_l_li{width:100%;min-height:90rpx;display:flex;align-items:center;justify-content:space-between;border-radius:20rpx 0 0 20rpx;position:relative}
.poi_thr_l_li .yj1{width:14rpx;height:12rpx;position:absolute;right:0;top:-12rpx}
.poi_thr_l_li .yj2{width:14rpx;height:12rpx;position:absolute;right:0;bottom:-12rpx}
.poi_thr_l_li text{width:6rpx;height:28rpx;background:#FB6304;border-radius:38rpx;margin-left:26rpx;opacity:0}
.poi_thr_l_li view{width:calc(100% - 52rpx);text-align:center;font-size:26rpx;color:#333333;margin-right:16rpx}
.poi_thr_l_li_ac{background:#fff!important}
.poi_thr_l_li_ac text{opacity:1!important}
.poi_thr_l_li_ac view{color:#FB6304!important;font-weight:bold}
.poi_thr_l .poi_thr_l_li_ac:nth-child(1){border-top-left-radius:0!important}
.poi_thr_r{width:calc(100% - 206rpx);min-height:500rpx;min-height:calc(100vh - 344rpx);min-height:100vh;background:#fff;overflow:hidden;float:right;padding-bottom:20rpx}
.poi_thr_r_li{width:100%;height:auto;overflow:hidden;padding:0 20rpx;display:flex;align-items:center;justify-content:space-between;margin-top:20rpx}
.poi_thr_r_li_l{width:180rpx;height:180rpx;display:block;border-radius:20rpx}
.poi_thr_r_li_r{width:calc(100% - 200rpx);height:180rpx;overflow:hidden;display:flex;justify-content:center;flex-direction:column}
.poi_thr_r_li_r_a{font-weight:bold;font-size:26rpx;color:#333333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.poi_thr_r_li_r_b{display:flex;align-items:center;justify-content:space-between;margin-top:20rpx}
.poi_thr_r_li_r_b text{font-size:26rpx;color:#F94F4B}
.poi_thr_r_li_r_b view{width:128rpx;height:60rpx;background:#131315;border-radius:82rpx;text-align:center;line-height:60rpx;font-size:26rpx;color:#F8F8FA}
.poi_thr_fixed .poi_thr_l{height:100%;position:fixed;top:0;left:0;z-index:9;overflow:auto}
.poi_thr_fixed .poi_thr_l .poi_thr_l_li:last-child{margin-bottom:200rpx}
.sear_one{margin:120rpx 52rpx 52rpx 52rpx;height:auto;overflow:hidden}
.sear_one_t{display:flex;align-items:center;justify-content:space-between;font-size:32rpx;color:#333333;font-weight:bold}
.sear_one_t image{width:42rpx;height:42rpx}
.sear_one_b{width:100%;height:auto;overflow:hidden;margin-top:10rpx}
.sear_one_b text{height:44rpx;line-height:44rpx;background:#FFFFFF;border-radius:50rpx;padding:0 20rpx;font-size:22rpx;color:#717171;float:left;margin-right:56rpx;margin-top:18rpx}
.search .les_search{background:none;height:auto;padding:26rpx}
.search .les_search_l{background:#FFFFFF}
.seajg_con{margin:100rpx 26rpx 26rpx 26rpx;height:auto;overflow:hidden}
.seajg_con_li{width:calc(50% - 12rpx);height:auto;background:#FFFFFF;border-radius:20rpx;margin-top:20rpx;float:left;overflow:hidden}
.seajg_con .seajg_con_li:nth-child(even){float:right}
.seajg_con_li image{display:block;width:100%;height:336rpx}
.seajg_con_li_a{margin:20rpx 12rpx 0 12rpx;font-size:26rpx;color:#333333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.seajg_con_li_b{margin:26rpx 12rpx 30rpx 12rpx;display:flex;align-items:center;justify-content:space-between}
.seajg_con_li_b view{font-size:32rpx;color:#F94F4B}
.seajg_con_li_b text{display:block;width:144rpx;height:60rpx;background:#131315;border-radius:82rpx;font-size:26rpx;color:#F8F8FA;text-align:center;line-height:60rpx}
.peode_foo{width:100%;height:auto;overflow:hidden;padding:24rpx 0;padding-bottom:calc(24rpx + env(safe-area-inset-bottom));box-sizing:border-box;position:fixed;bottom:0;left:0;z-index:9;background:#fff}
.peode_foo view{width:502rpx;height:90rpx;background:#131315;text-align:center;line-height:90rpx;border-radius:50rpx;font-weight:500;font-size:26rpx;color:#FFFFFF;margin:auto}
.pro_ban{width:100%;height:750rpx;overflow:hidden;position:relative}
.pro_ban .swiper{display:block;width:100%;height:750rpx;overflow:hidden}
.pro_ban .swiper image{display:block;width:100%;height:750rpx}
.pro_ban_xf{width:100%;position:absolute;text-align:right;bottom:18rpx;left:0}
.pro_ban_xf text{padding:0 20rpx;height:40rpx;background:#F1F1F1;border-radius:80rpx;float:right;margin-right:26rpx;font-size:26rpx;color:#999999;line-height:40rpx}
.pro_one{margin:20rpx 26rpx 0 26rpx;height:auto;overflow:hidden;background:#131315;border-radius:20rpx}
.pro_one_a{font-size:48rpx;color:#FFFFFF;margin:26rpx}
.pro_one_b{width:100%;height:auto;overflow:hidden;background:#fff;border-radius:20rpx 20rpx 0 0}
.pro_one_b_t{font-weight:bold;font-size:26rpx;color:#333333;margin:26rpx 26rpx 0 26rpx;margin:26rpx 26rpx 26rpx 26rpx}
.pro_one_b_b{width:100%;height:auto;overflow:hidden;margin-bottom:26rpx}
.pro_one_b_b_li{width:25%;height:100rpx;border-right:1px solid #F2F2F2;margin-top:20rpx;float:left;display:flex;align-items:center;flex-direction:column;justify-content:center}
.pro_one_b_b .pro_one_b_b_li:nth-child(4n+4){border-right:none}
.pro_one_b_b_li view{font-size:26rpx;color:#333333;text-align:center}
.pro_one_b_b_li text{display:block;text-align:center;font-size:26rpx;color:#333333;margin-top:10rpx}
.pro_two{margin:20rpx 26rpx 0 26rpx;height:auto;overflow:hidden;background:#fff;padding:26rpx;box-sizing:border-box;border-radius:20rpx;font-size:26rpx;color:#999}
.pro_two text{font-size:26rpx;color:#333333;margin-left:10rpx}
.pro_two image{display:inline-block;width:32rpx;height:32rpx;margin-left:10rpx;position:relative;top:6rpx}
.pro_fou{width:100%;height:auto;overflow:hidden;margin-bottom:158rpx}
.pro_thr{margin:20rpx 26rpx 158rpx 26rpx;height:auto;overflow:hidden;background:#fff;padding:26rpx;box-sizing:border-box;border-radius:20rpx}
.pro_thr_t{font-weight:bold;font-size:26rpx;color:#333333}
.pro_thr_b{margin-top:26rpx;height:auto;overflow:hidden}
.pro_thr_c{width:100%;height:auto;overflow:hidden}
.pro_thr_c_li{width:100%;height:auto;overflow:hidden;padding:36rpx 0 16rpx 0;border-bottom:1px solid #E9E9E9;display:flex;justify-content:space-between}
.pro_thr_c .pro_thr_c_li:last-child{border-bottom:none}
.pro_thr_c_li view{width:150rpx;font-size:26rpx;color:#333333}
.pro_thr_c_li text{width:calc(100% - 160rpx);font-size:26rpx;color:#333333}
.qrdd_a{margin:20rpx 26rpx;height:100rpx;background:#FFFFFF;border-radius:10rpx;display:flex;align-items:center;justify-content:center;font-weight:400;font-size:26rpx}
.qrdd_a image{width:32rpx;height:32rpx;margin-right:12rpx}
.qrdd_b{margin:20rpx 26rpx;height:auto;background:#FFFFFF;border-radius:10rpx;padding:26rpx;box-sizing:border-box}
.qrdd_b_a{font-size:26rpx;color:#333333}
.qrdd_b_b{font-size:26rpx;color:#333333;margin-top:10rpx}
.qrdd_d{margin:20rpx 26rpx;height:auto;background:#FFFFFF;border-radius:10rpx;padding:26rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.qrdd_d view{font-size:26rpx;color:#333333}
.qrdd_d input{width:calc(100% - 100rpx);font-size:26rpx;color:#333;text-align:right}
.qrdd_c{margin:20rpx 26rpx;height:auto;background:#FFFFFF;border-radius:10rpx;padding:26rpx;box-sizing:border-box}
.qrdd_c_li{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between}
.qrdd_c_li_l{width:158rpx;height:158rpx;border-radius:10rpx}
.qrdd_c .pri_two_b_li_hb_bj {width:153rpx;height:158rpx;}
.qrdd_c .jlcon_li_hb{width:158rpx;height:158rpx;border-radius:10rpx}
.qrdd_c .pri_two_b_li_hb {height:158rpx;}
.qrdd_c .pri_two_b_li_hb_n {font-size: 36rpx;margin-top: 38rpx;}
.qrdd_c_li_r{width:calc(100% - 178rpx);height:158rpx;display:flex;justify-content:center;flex-direction:column;overflow:hidden}
.qrdd_c_li_r_a{font-size:26rpx;color:#333333;font-weight:bold}
.qrdd_c_li_r_b{font-size:26rpx;color:#999999;margin:10rpx 0}
.qrdd_c_li_r_c{display:flex;align-items:center;justify-content:space-between;margin-top:10rpx}
.qrdd_c_li_r_c view{font-size:32rpx;color:#EB3527}
.qrdd_c_li_r_r text{font-size:26rpx;color:#999999}
.peodex_foo{width:100%;height:auto;overflow:hidden;padding:24rpx;padding-bottom:calc(24rpx + env(safe-area-inset-bottom));box-sizing:border-box;position:fixed;bottom:0;left:0;z-index:9;background:#fff;display:flex;align-items:center;justify-content:space-between}
.peodex_foo_l{font-size:26rpx;color:#333333}
.peodex_foo_l text{color:#EB3527}
.peodex_foo_r{width:321rpx;height:76rpx;background:#131315;border-radius:72rpx;font-size:26rpx;color:#FFFFFF;text-align:center;line-height:76rpx}
.qrdd_bz{margin-bottom:144rpx}
.kcxq_ten{background:#fff;margin-bottom:120rpx;padding-bottom:26rpx}
.kcxq_ten .kbxq_ml{margin-top:26rpx}
.kcxq_foo_r .kcxq_foo_r_sj{background:none;font-size:26rpx;color:#333333;padding:0;border:none}
.kcxq_foo_r .kcxq_foo_r_sj text{color:#FF6257;font-size:32rpx;font-weight:bold}
.lsxq_ban{width:100%;height:708rpx;overflow:hidden;margin-top:-200rpx}
.lsxq_ban image{display:block;width:100%;height:708rpx;overflow:hidden}
.lsxq_title{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:center;-margin-top:-30rpx;position:relative;top:-50rpx}
.lsxq_title text{padding:0 30rpx;height:86rpx;background:#131315;border-radius:82rpx;font-size:32rpx;color:#FFFFFF;font-weight:bold;line-height:86rpx}
.lsxq_two{width:100%;height:auto;background:#F6F6F6;border-radius:20rpx;margin-top:-106rpx}
.lsxq_two .cour_two{margin-top:80rpx;margin-bottom:30rpx}
.kc_foo .peodex_foo_r{width:144rpx;height:60rpx;line-height:60rpx}
.kc_foo .peodex_foo_l text{font-size:32rpx;font-weight:bold;color:#FF6257}
.xsk_one{margin:28rpx;background:#FFFFFF;border-radius:20rpx;overflow:hidden}
.xsk_one_li{width:100%;height:90rpx;border-bottom:1px solid #EBEBEB;overflow:hidden;box-sizing:border-box;padding:0 26rpx;display:flex;align-items:center;justify-content:space-between}
.xsk_one_li_l{font-size:26rpx;color:#333333}
.xsk_one_li_r{display:flex;align-items:center;font-size:22rpx;color:#999999;width: calc(100% - 150rpx);justify-content: flex-end;text-align: right;}
.xsk_one_li_r image{width:12rpx;height:22rpx;margin-left:10rpx}
.xsk_one .xsk_one_li:last-child{border-bottom:none}
.xsk_one_title{font-size:30rpx;color:#333333;font-weight:bold;margin:26rpx 26rpx 0 26rpx}
.xsk_xy{margin:-250rpx 52rpx 150rpx 52rpx;height:auto;overflow:hidden;display:flex;align-items:center;font-size:20rpx;color:#333333}
.xsk_xy image{width:30rpx;height:30rpx;margin-right:10rpx}
.xsk_xy text{color:#131315}
.thq_tanc{width:100%;height:auto;overflow:hidden;position:fixed;bottom:0;left:0;background:#F8F8FA;border-radius:20rpx 20rpx 0rpx 0rpx;z-index:999999;padding:32rpx;box-sizing:border-box}
.thq_tanc_t{height:68rpx;display:flex;align-items:center;justify-content:space-between}
.thq_tanc_t text{font-weight:bold;font-size:32rpx;color:#333333}
.thq_tanc_t image{width:34rpx;height:34rpx}
.thq_tanc_b{width:100%;max-height:1000rpx;overflow:auto}
.thq_tanc .cou_con_li{margin-left:0;margin-right:0}
.teacherDetail .teaxzTanc{position:fixed!important}
.ordzf_one{font-size:60rpx;color:#FF2B1F;margin-top:80rpx;text-align:center}
.ordzf_one text{font-size:80rpx;font-weight:bold}
.ordzf_two{font-size:26rpx;color:#999999;text-align:center}
.ordzf_thr{margin:38rpx 36rpx;height:auto;overflow:hidden}
.ordzf_thr_li{width:686rpx;height:132rpx;background:#FFFFFF;border-radius:20rpx;padding:0 26rpx;box-sizing:border-box;position:relative;display:flex;align-items:center}
.ordzf_thr_li_l{width:48rpx;height:48rpx}
.ordzf_thr_li_c{margin-left:26rpx}
.ordzf_thr_li_c view{font-weight:bold;font-size:26rpx;color:#333333}
.ordzf_thr_li_c text{display:block;font-size:26rpx;color:#999999;margin-top:8rpx}
.ordzf_thr_li_r{width:39rpx;height:39rpx;position:absolute;right:26rpx}
.ordzf_foo{width:502rpx;height:90rpx;background:#131315;text-align:center;line-height:90rpx;font-size:26rpx;color:#FFFFFF;margin:100rpx auto;border-radius:50rpx}
.mdqh_con{margin-top:206rpx}
.mdqh_con_li{margin:20rpx 26rpx 0 26rpx;height:auto;overflow:hidden;background:#FFFFFF;border-radius:20rpx;padding:26rpx;box-sizing:border-box;display:flex;justify-content:space-between}
.mdqh_con_li_l{width:210rpx;height:210rpx;border-radius:10rpx}
.mdqh_con_li_r{width:calc(100% - 236rpx);height:210rpx;overflow:hidden;display:flex;flex-direction:column;justify-content:center}
.mdqh_con_li_r_a{font-size:32rpx;color:#333;font-weight:bold}
.mdqh_con_li_r_b{font-size:20rpx;color:#999999;margin-top:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
.mdqh_con_li_r_c{font-size:20rpx;color:#999999;display:flex;align-items:center;margin-top:10rpx}
.mdqh_con_li_r_c image:nth-child(1){width:26rpx;height:26rpx;margin-right:10rpx}
.mdqh_con_li_r_c view{width:calc(100% - 92rpx);font-size:20rpx;color:#999999;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.mdqh_con_li_r_c image:nth-child(3){width:48rpx;height:48rpx;margin-left:10rpx}
.mdqh_con_li_r_d{display:flex;align-items:center;justify-content:space-between;margin-top:20rpx}
.mdqh_con_li_r_d text{height:38rpx;background:none;border-radius:4rpx;border:1px solid #131315;padding:0 10rpx;font-size:20rpx;color:#131315;line-height:38rpx}
.mdqh_con_li_r_d image{width:40rpx;height:40rpx;border-radius:50%}
.mdqh_con_li_r_d view{width:136rpx;height:50rpx;background:#131315;border-radius:82rpx;font-size:24rpx;color:#F8F8FA;text-align:center;line-height:50rpx}
.mdqh_head{width:100%;height:auto;overflow:hidden;background:#fff;position:fixed;top:0;left:0;z-index:9}
.mdqh_head_t{padding:12rpx 26rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.mdqh_head_t_l{width:170rpx;font-size:26rpx;color:#333333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.mdqh_head_t_l image{display:inline-block;width:32rpx;height:32rpx;margin-right:6rpx;position:relative;top:6rpx}
.mdqh_head_t_r{width:510rpx;height:64rpx;background:#F3F3F3;border-radius:96rpx;padding:0 20rpx;box-sizing:border-box;display:flex;align-items:center}
.mdqh_head_t_r image{width:32rpx;height:32rpx}
.mdqh_head_t_r input{width:calc(100% - 50rpx);font-size:26rpx;color:#333;margin-left:10rpx}
.mdqh_head_b{width:100%;height:auto;overflow:hidden}
.mdqh_head_b_li{width:33.33%;height:96rpx;overflow:hidden;float:left;font-size:32rpx;color:#333333;display:flex;align-items:center;justify-content:center}
.mdqh_head_b_li image{width:23rpx;height:30rpx;margin-left:8rpx}
.mdqh_head_b_li_ac{font-weight:bold;color:#131315!important}
.switchStores.black .peode_foo view{background:#131315}
.switchStores.black .mdqh_con_li_r_d text{color:#131315;font-size:20rpx;border:1px solid #131315;background:none}
.stor_one{margin:22rpx 26rpx 0 26rpx;height:auto;overflow:hidden;background:#FFFFFF;border-radius:20rpx}
.stor_one_t{padding:26rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.stor_one_t_l{width:120rpx;height:120rpx;border-radius:10rpx}
.stor_one_t_r{width:calc(100% - 146rpx);height:auto;overflow:hidden}
.stor_one_t_r_a{font-weight:bold;font-size:32rpx;color:#333}
.stor_one_t_r_b{font-size:26rpx;color:#999999;margin:10rpx 0}
.stor_one_t_r_c{font-size:26rpx;color:#999999;display:flex;align-items:center}
.stor_one_t_r_c image{width:32rpx;height:32rpx;margin-right:6rpx}
.stor_one_b{border-top:1px solid #EBEBEB;display:flex;align-items:center;justify-content:space-between;padding:26rpx;box-sizing:border-box}
.stor_one_b view{font-size:26rpx;color:#999999;width:calc(100% - 164rpx)}
.stor_one_b text{width:144rpx;height:60rpx;background:#131315;border-radius:82rpx;text-align:center;line-height:60rpx;font-size:26rpx;color:#F8F8FA}
.stor_two{margin:40rpx 26rpx 0 26rpx;height:auto;overflow:hidden}
.stor_two_b{height:auto;overflow:hidden}
.stor_two_b video{width:100%;height:390rpx;display:block;border-radius:20rpx;margin-top:20rpx}
.stor_two_b image{display:block;width:100%;border-radius:20rpx;margin-top:22rpx}
.stor_thr{width:100%;height:auto;overflow:hidden;margin-top:30rpx}
.teaCon_li{margin:20rpx 26rpx 0 26rpx;height:auto;background:#FFFFFF;overflow:hidden;padding:26rpx;box-sizing:border-box;border-radius:20rpx}
.teaCon_li_a{font-size:32rpx;color:#000000;font-weight:bold;padding-bottom:26rpx}
.teaCon_li_b{display:flex;align-items:center;position:relative;padding:32rpx 0;border-bottom:1px solid #EADCDA;border-top:1px solid #EADCDA;justify-content: space-between;}
.teaCon_li_b_l{width:118rpx;height:118rpx;border-radius:10rpx;}
.teaCon_li_b_l{width:160rpx;height:160rpx;border-radius:14rpx;}
.teaCon_li_b_button{width: 100%;height: auto;overflow:hidden;margin-top: 10rpx;}
.teaCon_li_b_button .teaCon_li_b_r{
	position:static;
	float:right;
}
.teaCon_li_b_c{margin-left:38rpx;width:320rpx;height:auto;overflow:hidden}
.teaCon_li_b_c{margin-left:0rpx;width:calc(100% - 182rpx);height:auto;overflow:hidden}
.teaCon_li_b_c_a{font-size:26rpx;color:#131315}
.teaCon_li_b_c_b{font-size:26rpx;color:#333333;margin:6rpx 0}
.teaCon_li_b_c_c{font-size:26rpx;color:#333333}
.teaCon_li_b_c_c text{padding:0 6rpx;height:36rpx;line-height:36rpx;background:#131315;border-radius:10rpx;font-size:22rpx;color:#FFFFFF;margin-right:8rpx;display:inline-block}
.teaCon_li_b_c_yg{font-size: 24rpx;color: #131315;
display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp:99;
overflow: hidden;margin-top: 10rpx;}
.teaCon_li_b_r{position:absolute;right:0;width:144rpx;height:60rpx;background:#131315;border-radius:82rpx;font-size:24rpx;color:#F8F8FA;text-align:center;line-height:60rpx}
.teaCon_li_b_r.yysj{
	    background: #BEBEBE;
	    font-size: 20rpx;
		display: flex;
		align-items:center;
		justify-content:center;
		flex-direction: column;
}
.teaCon_li_b_r.yysj text{
	font-size: 20rpx;
	line-height: 1;
}
.teaCon_li_b_r.yysj text:nth-child(2){
	margin-top:6rpx;
}
.teaCon_li_c{display:flex;align-items:center;justify-content:space-between;margin-top:26rpx}
.teaCon_li_c_l{display:flex;align-items:center}
.teaCon_li_c_l image{width:40rpx;height:40rpx;border-radius:50%;border:1px solid #131315;margin-left:-12rpx}
.teaCon_li_c_l image:nth-child(1){margin-left:0}
.teaCon_li_c_r{font-size:26rpx;color:#999999}
.teaCon_li_c_r text{color:#131315}
.stor_thr_c{margin:28rpx 26rpx 0 26rpx;height:96rpx;position:relative}
.stor_thr_c_n{width:100%;height:auto;overflow:hidden;background:#FFFFFF;border-radius:20rpx;z-index:999999999;position:relative}
.stor_thr_c_li{width:33.33%;height:96rpx;float:left;overflow:hidden;font-size:32rpx;color:#333333;display:flex;align-items:center;justify-content:center;text-align: center;}
.stor_thr_c_li text{display:block;width:0;height:0;border-left:10rpx solid transparent;border-right:10rpx solid transparent;border-top:14rpx solid #D9D9D9;margin-left:8rpx;font-weight:bold}
.teaxzTanc{width:100%;height:auto;position:absolute;top:96rpx;left:0;background:#fff;z-index:99999999;-box-shadow:0 0 10rpx #e6e6e6;border-radius:20rpx;border-radius:0 0 20rpx 20rpx}
.teaxzTanc_t{width:100%;max-height:180rpx;overflow:auto;margin-top:26rpx}
.teaxzTanc_t view{width:152rpx;height:72rpx;background:#F8F8FA;border-radius:10rpx;font-size:28rpx;color:#333333;display:flex;align-items:center;justify-content:center;float:left;margin-left:16rpx;margin-top:26rpx;text-align:center;line-height: 1;}
.teaxzTanc_t view:nth-child(4n+1){margin-left:26rpx}
.teaxzTanc_t view:nth-child(1){margin-top:0rpx}
.teaxzTanc_t view:nth-child(2){margin-top:0rpx}
.teaxzTanc_t view:nth-child(3){margin-top:0rpx}
.teaxzTanc_t view:nth-child(4){margin-top:0rpx}
.teaxzTanc_t_ac{background:#131315!important;color:#fff!important}
.teaxzTanc_b{margin:26rpx;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between}
.teaxzTanc_b view{width:calc(50% - 13rpx);height:76rpx;border:1px solid #131315;border-radius:50rpx;display:flex;align-items:center;justify-content:center;font-size:26rpx;color:#131315}
.teaxzTanc_b text{width:calc(50% - 13rpx);height:76rpx;border:1px solid #131315;background:#131315;font-size:26rpx;color:#fff;display:flex;align-items:center;justify-content:center;border-radius:50rpx}
.stor_thr_c_li_ac{color:#131315!important;font-weight:bold}
.stor_thr_c_li_ac text{border-top:none!important;border-bottom:14rpx solid #131315!important}
.lsxq_head{width:100%;height:100rpx;background:#FFFFFF;position:fixed;top:0;left:0;z-index:99999999999}
.lsxq_head_l{width:calc(100% - 165rpx);height:100rpx;overflow:hidden;float:left}
.lsxq_head_r{width:165rpx;height:100rpx;overflow:hidden;position:relative;display:flex;align-items:center;justify-content:flex-end}
.lsxq_head_r image{float:right;width:48rpx;height:48rpx;margin-right:26rpx}
.lsxq_head_r text{width:1px;height:30rpx;background:#333333;position:absolute;left:0;top:50%;margin-top:-15rpx}
.teacherDetail .teaxzTanc{border-top-left-radius:0;border-top-right-radius:0}
.lsxq_head .les_search{z-index:9999999999}
.tea_ban{width:100%;height:auto;overflow:hidden;margin-top:156rpx}
.tea_ban_uswiper{width:100%;height:278rpx;height:340rpx;overflow:hidden;margin-top:54rpx}
.tea_ban_uswiper swiper{height:278rpx!important}
.tea_ban_uswiper .u-swiper-wrap{height:340rpx}
.tea_ban_uswiper .u-swiper-image{display:block;width:278rpx!important;height:278rpx!important;margin:auto;border-radius:16rpx}
.tea_ban_yd{display:block;width:314rpx;height:43rpx;margin:-10rpx auto 80rpx auto}
.tea_ban_a{font-weight:bold;font-size:32rpx;color:#000000;text-align:center}
.tea_ban_b{width:100%;height:auto;overflow:hidden;margin-top:40rpx}
.tea_ban_b_t{width:100%;height:360rpx;overflow:hidden;display:flex;align-items:center;justify-content:center;position:relative}
.banner{width:100%;display:flex;flex-direction:row;position:absolute;z-index:999}
.banner-container{width:100%;height:350rpx}
.slide-image{position:absolute;height:260rpx;width:98%;border-radius:10rpx;width:214rpx;height:214rpx;z-index:5;opacity:0.7;top:15%;left:50%;margin-left:-107rpx}
.active{opacity:1;z-index:10;height:350rpx;width:98%;width:278rpx;height:278rpx;border-radius:10rpx;top:20rpx;transition:all 0.2s ease-in 0s;margin-left:-137rpx!important}
.active{}
.tea_ban_b_yd{width:100%;height:auto;overflow:hidden;margin-top:48rpx}
.tea_ban_b_yd text{display:block;width:20rpx;height:20rpx;background:#333333;border-radius:50%;margin:0 20rpx}
.tea_one{position:static}
.tea_one .ord_nav_li{width:50%;position:relative;top:4rpx}
.tea_one .ord_nav_li view text{font-size:32rpx}
.rlxz_con{width:100%;height:118rpx;-overflow:hidden;background:#fff;display:flex}
.rlxz_con_l{width:calc(100% - 82rpx);height:118rpx;-overflow:auto;white-space:nowrap}
.rlxz_con_l scroll-view{display:block;width:100%;height:148rpx}
.rlxz_con_l_li{display:inline-block;width:118rpx;height:118rpx;position:relative}
.rlxz_con_l_li view{font-size:26rpx;color:#333333;text-align:center}
.rlxz_con_l_li view:nth-child(1){margin-top:25rpx}
.rlxz_con_l_li view:nth-child(2){margin-top:8rpx}
.rlxz_con_l_li text{display:block;width:0;height:0;border-left:10rpx solid transparent;border-right:10rpx solid transparent;border-top:14rpx solid #131315;position:absolute;left:50%;margin-left:-8rpx;bottom:-14rpx;opacity:0}
.rlxz_con_l_li_ac{background:#131315!important}
.rlxz_con_l_li_ac view{color:#FFFFFF!important}
.rlxz_con_l_li_ac text{opacity:1!important}
.rlxz_con_r{width:82rpx;height:118rpx;overflow:hidden;position:relative;background:#131315;display:flex;align-items:center;justify-content:center}
.rlxz_con_r image{width:48rpx;height:53rpx}
.rlxz_con_r picker{display:block;width:100%;height:118rpx;opacity:0;position:absolute;top:0;left:0}
.rlxz_con_r picker .uni-input{display:block;width:100%;height:118rpx}
.md_xz{margin:30rpx 26rpx 0 26rpx;height:76rpx;overflow:hidden;position:relative}
.md_xzCon .uni-input{width:100%;height:76rpx;position:absolute;top:0;left:0;z-index:98;opacity:0}
.md_xz .uni-input{width:100%;height:76rpx;position:absolute;top:0;left:0;z-index:98;opacity:0}
.md_xz_bj{display:block;width:100%;height:100%;position:absolute;top:0;left:0}
.md_xz_title{float:left;min-width:200rpx;line-height:76rpx;background:#131315;font-size:26rpx;color:#FFFFFF;margin-left:74rpx;position:relative;z-index:2;padding-right:10rpx}
.md_xz_xt{width:72rpx;height:76rpx;float:left;position:relative;z-index:2}
.lspjCon{padding:26rpx;box-sizing:border-box;background:#fff;box-sizing:border-box;margin-top:26rpx}
.lspjCon_t{width:100%;height:auto;overflow:hidden}
.lspjCon_t view{width:33.33%;float:left;font-size:26rpx;color:#333333}
.lspjCon_t view text{color:#E3D68C;font-size:26rpx;margin-left:10rpx}
.lspjCon_b{width:100%;height:auto;overflow:hidden}
.lspjCon_b text{height:40rpx;border-radius:10rpx;border:1px solid #E8C501;float:left;margin-top:26rpx;margin-right:26rpx;font-size:20rpx;color:#E8C501;line-height:40rpx;padding:0 14rpx}
.my_head{width:100%;height:auto;overflow:hidden;background:#fff;position:fixed;top:0;left:0;z-index:9999999999999}
.my_head_n{margin:0 20rpx 0rpx 20rpx;margin-top:calc(20rpx + env(safe-area-inset-bottom));overflow:hidden}
.my_head_t{width:100%;height:auto;overflow:hidden;box-sizing:border-box;padding:0 20rpx;position:relative;display:flex;align-items:center}
.my_head_b{width:100%;height:auto;overflow:hidden;margin-top:20rpx}
.my_head_t_l{width:310rpx;-height:108rpx;height:40rpx;position:absolute;left:0;-top:0;display:flex;align-items:center;justify-content:space-between}
.my_head_t_l image{width:32rpx;height:32rpx}
.my_head_t_l text{font-size:26rpx;color:#333333;width:calc(100% - 44rpx)}
.my_head_t_title{font-size:32rpx;color:#333333;width:100%;-line-height:108rpx;text-align:center}
.schedule .teaxzTanc{position:fixed;border-top-left-radius:0!important;border-top-right-radius:0!important}
.searchResults .teaCon{margin-top:124rpx}
.yycgCon{margin:150rpx 130rpx 0 130rpx;height:auto;overflow:hidden}
.yycgCon image{display:block;width:491rpx;height:426rpx;margin:auto}
.yycgCon_a{text-align:center;font-size:32rpx;color:#333333;font-weight:bold;margin-top:180rpx}
.yycgCon_b{width:452rpx;font-size:26rpx;color:#999999;text-align:center;margin:20rpx auto;line-height:44rpx}
.yycgCon_b text{font-size:26rpx;color:#131315}
.yytnCon{width:100%;height:100%;position:fixed;top:0;left:0;background:rgba(0,0,0,.6);z-index:999999999999;display:flex;align-items:center;justify-content:center;flex-direction:column}
/* .yytnCon_n{width:468rpx;height:668rpx;overflow:hidden;position:relative} */
.yytnCon_n{width:549rpx;height:790rpx;overflow:hidden;position:relative}
.yytnCon_n image{display:block;width:100%;height:100%}
.yytnCon_n text{display:block;width:400rpx;height:90rpx;position:absolute;left:50%;margin-left:-200rpx;bottom:0}
.yytnCon>image{display:block;width:56rpx;height:56rpx;margin-top:50rpx}
.serkf_one{width:100%;height:auto;overflow:hidden;position:fixed;top:0;left:0;z-index:1}
.serkf_one_n{margin:26rpx;height:auto;overflow:hidden;height:228rpx;background:#FFFFFF;border-radius:30rpx;padding:26rpx}
.serkf_one_t{width:100%;height:auto;height:144rpx;overflow:hidden;display:flex;align-items:center;position:relative}
.serkf_one_t_l{width:80rpx;height:80rpx;border-radius:50%}
.serkf_one_t_c{width:390rpx;height:auto;overflow:hidden;margin-left:20rpx}
.serkf_one_t_c_a{font-size:32rpx;color:#333333;font-weight:bold}
.serkf_one_t_c_b{display:flex;font-size:26rpx;color:#333333;margin-top:10rpx}
.serkf_one_t_c_b image{width:32rpx;height:32rpx;margin-left:10rpx}
.serkf_one_t_r{width:144rpx;height:144rpx;position:absolute;right:0;top:0}
.serkf_one_b{display:flex;align-items:center;font-size:24rpx;color:#333333;margin-top:10rpx}
.serkf_one_b image{width:33rpx;height:33rpx;margin-right:10rpx}
.serv_foo{width:100%;height:calc(104rpx + env(safe-area-inset-bottom));padding:0 20rpx;box-sizing:border-box;background:#fff;overflow:hidden;position:fixed;bottom:0;left:0;z-index:9;display:flex;justify-content:space-between;align-items:baseline;padding-top:20rpx}
.serv_foo input{height:64rpx;width:calc(100% - 140rpx);border-radius:50rpx;background:#F3F3F3;box-sizing:border-box;padding:0 24rpx}
.serv_foo view{width:120rpx;height:64rpx;background:#131315;border-radius:80rpx;font-size:26rpx;color:#FFFFFF;text-align:center;line-height:64rpx}
.serv_con{margin:308rpx 26rpx 104rpx 26rpx;height:auto;overflow:hidden}
.serv_con_li{width:100%;height:auto;overflow:hidden;margin-bottom:36rpx}
.serv_con_li_date{text-align:center;font-size:26rpx;color:#999;margin-bottom:36rpx}
.serv_con_li_l{width:82rpx;height:82rpx;border-radius:50%;float:left}
.serv_con_li_r{width:calc(100% - 98rpx);height:auto;overflow:hidden;float:right}
.serv_con_li_r view{max-width:100%;padding:20rpx;box-sizing:border-box;float:left;background:#FFFFFF;border-radius:0rpx 80rpx 80rpx 50rpx;font-size:26rpx;color:#333333}
.serv_con_li_r view image{width:300rpx;height:300rpx;margin-right:40rpx}
.serv_con_li_br .serv_con_li_r{float:left}
.serv_con_li_br .serv_con_li_l{float:right}
.serv_con_li_br  .serv_con_li_r view{background:#131315!important;color:#fff!important;border-radius:80rpx 0rpx 50rpx 80rpx!important;float:right!important;text-align:right!important}
.serv_con_hhend{text-align:center;font-size:26rpx;color:#999;margin-bottom:20rpx}
.serv_con_hhend text{color:#131315!important;font-weight:bold}
.add_foo{width:100%;height:auto;overflow:hidden;position:fixed;bottom:0;left:0;z-index:9;background:#fff;box-shadow:0px 2px 4rpx 0px rgba(0,0,0,0.89)}
.add_foo view{margin:20rpx;height:88rpx;background:#131315;border-radius:80rpx;overflow:hidden;font-size:26rpx;color:#F8F8F8;text-align:center;line-height:88rpx;margin-bottom:calc(20rpx + env(safe-area-inset-bottom))}
.add_fooEdit{padding:0 20rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.add_fooEdit view{width:calc(50% - 13rpx);border:1px solid #131315;margin:20rpx 0}
.add_fooEdit view:nth-child(1){background:none!important;color:#131315!important}
.xjTanc{width:100%;height:100%;position:fixed;top:0;left:0;z-index:9;background:rgba(0,0,0,0.8);display:flex;align-items:center;justify-content:center}
.xjTanc_n{width:480rpx;height:auto;overflow:hidden;border-radius:20rpx;background:#fff;padding:50rpx 0;display:flex;align-items:center;justify-content:center;flex-direction:column}
.xjTanc_n{font-weight:bold;font-size:32rpx;color:#333333}
.xjTanc_a{font-size:32rpx;color:#333333;font-weight:bold}
.xjTanc_b{font-size:26rpx;color:#333333;margin:20rpx 0 50rpx 0}
.xjTanc_c{display:flex;align-items:center;justify-content:center}
.xjTanc_c view{width:144rpx;height:60rpx;text-align:center;line-height:58rpx;font-size:26rpx;color:#131315;border:1px solid #131315;margin:0 18rpx;border-radius:50rpx}
.xjTanc_c .bak{color:#fff!important;background:#131315!important}
.userReport{width:100%;height:100vh;overflow:hidden}
.userReport .yueb{width:100%;height:100vh;overflow:hidden}
.userReport .swiper{width:100%;height:100vh}
.userReport .swiper .swiper-item{width:100%;height:100vh;position:relative;overflow:hidden}
.userReport .swiper .swiper-item image{display:block;width:100%;height:100%}
.yueb_one_l{width:518rpx;height:728rpx;position:absolute;top:252rpx;left:0rpx;top:20%}
.yueb_one_r{width:228rpx;height:841rpx;position:absolute;top:60rpx;right:50rpx;top:5%}
.yueb_one_f{width:581rpx;height:179rpx;position:absolute;left:50%;margin-left:-290rpx;bottom:102rpx;bottom:10%}
.yueb_one_f view{width:154rpx;height:154rpx;display:flex;align-items:center;justify-content:center;flex-direction:column;position:absolute;top:10rpx;left:50%;margin-left:-88rpx}
.yueb_one_f view text{font-weight:600;font-size:32rpx;color:#FFFFFF;display:block}
.yueb_one_f view text{font-weight:600;font-size:32rpx;color:#FFFFFF;display:block;letter-spacing:2px}
.yueb_two_a{width:296rpx!important;position:absolute;top:20rpx;right:0}
.yueb_two_f{width:55%!important;position:absolute;bottom:0;left:50%;margin-left:-27.5%}
.yueb_two_b{margin:20% 52rpx 0 52rpx;height:auto;overflow:hidden}
.yueb_two_b_li{margin-top:72rpx;font-weight:bold}
.yueb_two_b .yueb_two_b_li:nth-child(1){margin-top:0!important}
.yueb_two_b_li view{font-size:32rpx;color:#333333;margin-top:32rpx}
.yueb_two_b_li view:nth-child(1){margin-top:0!important}
.yueb_two_b_li view text{color:#E93044}
.yueb_fou{width:100%;height:100%}
.yueb_fou_a{font-weight:bold;font-size:32rpx;color:#333333;text-align:center;margin-top:10%}
.yueb_fou_b{font-size:90rpx;color:#E93044;font-family:Maoken Glitch Sans;margin-top:30rpx;position:relative;z-index:2;text-align:center}
.yueb_fou_f{width:627rpx!important;margin:auto;margin-top:-70rpx;position:relative;z-index:1}
.yueb_five_b{margin:32rpx 50rpx 0 50rpx;text-align:center}
.yueb_five .yueb_fou_f{margin-top:10rpx!important}
.yueb_six_f{width:350rpx!important;position:absolute;right:20rpx;bottom:30rpx}
.yueb_six_a{margin:10% 98rpx 0 98rpx;height:auto;overflow:hidden}
.yueb_six_a view{font-weight:600;font-size:32rpx;color:#333333;margin-top:32rpx}
.yueb_six_a view:nth-child(1){margin-top:0}
.yueb_six_b{margin:132rpx 96rpx 0 20rpx;height:auto;overflow:hidden}
.yueb_six_c{margin:122rpx 52rpx 0 52rpx;height:auto;overflow:hidden}
.yueb_six_c_a{font-weight:600;font-size:32rpx;color:#333333}
.yueb_six_c_a text{color:#E93044!important}
.yueb_six_c .yueb_six_c_a:nth-child(2){margin-top:32rpx!important}
.yueb_six_b_a{font-weight:600;font-size:32rpx;color:#333333;text-align:left;margin-left:314rpx}
.yueb_six_b .yueb_six_b_a:nth-child(2){margin-top:32rpx}
.yueb_six_b_a text{color:#E93044;font-size:60rpx}
.years_yueb{position:fixed;top:0;left:0}
.years_one{width:100%;height:100vh}
.years_one_bj{width:100%;height:100vh;position:absolute;top:0;left:0;z-index:-1}
.years_one_a{font-size:160rpx;color:#FFBA71;font-family:AaHouDiHei;position:relative;margin:70rpx 34rpx 0 34rpx}
.years_one_b{font-weight:400;font-size:100rpx;color:#FFFFFF;font-family:AaHouDiHei;margin:0rpx 34rpx 0 34rpx}
.years_one_c{width:326rpx;height:84rpx;border-radius:60rpx 60rpx 60rpx 60rpx;border:2px solid #FFFFFF;display:flex;align-items:center;justify-content:center;font-size:32rpx;color:#FFFFFF;margin:36rpx 42rpx 0 34rpx;line-height:0}
.years_two_a{margin-top:46rpx;text-align:center;font-size:80rpx;color:#FFFFFF}
.years_two_b{font-size:32rpx;color:#FFFFFF;text-align:center}
.years_two_b text{color:#FFBA71;font-size:80rpx}
.years_two_d{font-size:60rpx;color:#FFFFFF;margin-top:82rpx;text-align:center}
.years_two_e{margin-top:24rpx;font-size:50rpx;color:#FFBA71;text-align:center}
.years_two_f{font-size:32rpx;color:#FFFFFF;width:480rpx;margin:24rpx auto;text-align:center}
.years_two_f text{color:#FFBA71}
.years_thr_a{margin:20rpx 42rpx}
.years_thr_a text{font-size:60rpx;color:#FFFFFF;display:block}
.years_thr_b{width:100%;height:310rpx;padding:0 42rpx;box-sizing:border-box;position:absolute;bottom:82rpx;left:0}
.years_thr_b_a{font-size:50rpx;color:#FFFFFF;margin-top:10rpx}
.years_thr_b_b{font-size:50rpx;color:#FFBA71;margin-top:10rpx}
.years_thr_b_c{font-size:50rpx;color:#FFFFFF;margin-top:10rpx}
.years_thr_b_d{font-size:50rpx;color:#FFFFFF;margin-top:10rpx}
.years_thr_b_d text{color:#FFBA71}
.years_thr_b_e{font-size:50rpx;color:#fff;text-align:justify}
.years_thr_b_e text{color:#FFBA71}
.years_thr_f{width:100%;padding:0 42rpx;box-sizing:border-box;position:absolute;bottom:94rpx;left:0}
.years_fou_a{margin:14rpx 72rpx 0 72rpx;font-size:32rpx;color:#FFFFFF}
.years_fou_a text{color:#DCB649}
.weeks_yueb{position:fixed;top:0;left:0}
.weeks_one_a{margin:20rpx 50rpx 0 50rpx}
.weeks_one_a text{display:block;font-size:100rpx;color:#FFFFFF}
.weeks_one_b{width:1px;height:260rpx;background:#fff;margin:128rpx 0 0 50rpx}
.weeks_one_c{margin:40rpx 32rpx 0 32rpx}
.weeks_one_c text{display:block;font-size:32rpx;color:#FFFFFF}
.weeks_one_d{width:396rpx;height:84rpx;background:#FFFFFF;border-radius:124rpx;text-align:center;line-height:84rpx;font-weight:bold;font-size:32rpx;color:#333333;position:absolute;bottom:114rpx;left:50%;margin-left:-198rpx}
.weeks_two_n{width:100%;height:514rpx;-overflow:hidden;padding:0 62rpx;box-sizing:border-box;position:absolute;bottom:110rpx;left:0}
.weeks_two_a{font-size:32rpx;color:#FFFFFF;margin-top:60rpx}
.weeks_two_a text{color:#131315;font-size:60rpx}
.weeks_two_n .weeks_two_a:nth-child(1){margin-top:0!important}
.weeks_thr_a{margin:0 36rpx}
.weeks_thr_a view{font-size:32rpx;color:#333333;margin-bottom:20rpx}
.weeks_thr_a view text{color:#FF0000;font-size:60rpx}
.weeks_thr_a{margin:0 36rpx 28rpx 36rpx;font-size:32rpx;color:#333333}
.weeks_thr_a text{color:#FF0000;font-size:60rpx}
.weeks_thr_b{width:2px;height:261rpx;background:#333;position:absolute;left:46rpx;bottom:182rpx}
.weeks_thr_c{width:100%;height:auto;overflow:hidden;padding:0 30rpx;box-sizing:border-box;position:absolute;font-size:32rpx;color:#333333;left:0rpx;bottom:76rpx}
.weeks_fou_a{margin:14rpx 38rpx 0 38rpx;font-size:32rpx;color:#333333}
.weeks_fou_b{margin:8rpx 38rpx 0 38rpx;margin:8rpx 0rpx 0 38rpx;font-size:80rpx;color:#333333;font-weight:bold}
.weeks_fou_b text{color:#FF0000;font-size:84rpx;font-weight:bold}
.weeks_fou_c{margin-left:38rpx;width:408rpx;height:2px;background:#333333;overflow:hidden;margin-top:10rpx}
.weeks_fou_d{margin:28rpx 38rpx 0 38rpx;font-size:28rpx;color:#000000}
.weeks_five_a{margin:56rpx 60rpx 0 60rpx;font-size:32rpx;color:#333333;text-align:center}
.weeks_five_a text{color:#FF0000}
.weeks_five_b{width:2px;height:150rpx;background:#333333;margin:38rpx 0 0 86rpx}
.xjTanc.hszt .xjTanc_c view{color:#131315;border-color:#131315}
.xjTanc.hszt .bak{background:#131315!important}
.winningrecord .ord_nav_li{width:25%}
.winningrecord .ord_nav_li view text:nth-child(1){font-size:28rpx}
.winningrecord.myCourse .ord_nav_li view text:nth-child(2){background:#131315!important;top:10rpx!important}
.winningrecord.myCourse .ord_nav_li.ord_nav_li_ac view text:nth-child(1){color:#131315!important}
.winningrecord.myCourse .ord_nav{height:62rpx!important}
.winningrecord.myCourse .ord_nav_li{height:62rpx!important}
.jlcon{margin:62rpx 26rpx 0 26rpx;height:auto;overflow:hidden}
.jlcon_li{height:auto;overflow:hidden;padding:32rpx;box-sizing:border-box;background:#fff;border-radius:16rpx;overflow:hidden;margin-top:26rpx;display:flex;align-items:center;justify-content:space-between;position:relative}
.jlcon_li_zt{float:left;position:absolute;top:0;left:0;z-index:3;padding:0 16rpx;height:30rpx;line-height:30rpx;background:#C1C0C5;font-size:16rpx;color:#555555;border-radius:16rpx 0}
.jlcon_li_zt.ddh{background:#EE4225!important;color:#fff!important}
.jlcon_li_zt.ydh{background:#BDE0C2!important;color:#50A05A!important}
.jlcon_li_l{width:242rpx;height:242rpx;overflow:hidden;border-radius:16rpx;border:1px solid #F5F5F5;box-sizing:border-box}
.jlcon_li_l image{display:block;width:100%;height:100%}
.jlcon_li_hb{width:242rpx;height:242rpx;overflow:hidden;border-radius:16rpx;border:1px solid #F5F5F5;box-sizing:border-box}
.jlcon_li_hb .pri_two_b_li_hb{margin-top:14rpx;}
.jlcon_li_cards{width:242rpx;height:242rpx;overflow:hidden;border-radius:16rpx;border:1px solid #F5F5F5;box-sizing:border-box;display: flex;
align-items:center;
justify-content: center;}
.jlcon_li_cards image{width: 216rpx;height: 128rpx;}
.jlcon_li_r{width:calc(100% - 270rpx);height:auto;overflow:hidden}
.jlcon_li_r_a{font-weight:bold;font-size:28rpx;color:#131315;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden}
.jlcon_li_r_b{font-size:26rpx;color:#999999;margin-top:6rpx}
.jlcon_li_r_c{font-size:28rpx;color:#999999;text-align:right;margin-top:6rpx}
.jlcon_li_r_d{width:128rpx;height:64rpx;overflow:hidden;text-align:center;line-height:64rpx;border-radius:50rpx;background:#131315;float:right;margin-top:20rpx;color:#ffffff;font-size:28rpx}
.jlcon_li_r_ckwl{width:170rpx;height:64rpx;overflow:hidden;text-align:center;line-height:62rpx;border-radius:50rpx;border:1px solid #131315;float:right;margin-top:20rpx;color:#131315;font-size:28rpx}
.winningrecordxq .peode_foo{background:#F6F6F6}
.winningrecordxq .peode_foo view{background:#333333}
.winningrecordxq .pro_thr{margin:20rpx 32rpx 0rpx 32rpx}
.winningrecordxq .pro_fou{width:auto;margin:20rpx 32rpx 138rpx 32rpx;height:auto;overflow:hidden;background:#fff;border-radius:20rpx}
.winningrecordxq .pro_ban_xf{bottom:60rpx}
.winningrecordxq .pro_one{background:none;position:relative;margin-top:-40rpx;z-index:5}
.winningrecordxq .pro_one_b_t{font-size:32rpx}
.winningrecordxq .pro_thr_c_li{border-bottom:none;padding:26rpx 0 0 0;justify-content:left}
.winningrecordxq .pro_thr_c_li:nth-child(1){padding-top:0}
.winningrecordxq .pro_thr_c_li text{width:auto}
.winningrecordxq .pro_thr_c_li view{width:auto;margin-right:60rpx}
.jpdh .qrdd_c_li_r{justify-content:space-between}
.jpdh .qrdd_c_li_r_a{font-size:28rpx;margin-top:6rpx}
.jpdh .qrdd_c_li_r_c text{font-size:28rpx;color:#999999}
.jpdh .qrdd_c_li_r_c{margin-bottom:6rpx}
.jpdh .peodex_foo .peodex_foo_r{background:#131315;height:90rpx;line-height:90rpx}
.jpdh .qrdd_b_a image{width:32rpx;height:32rpx;margin-right:10rpx}
.jpdh .qrdd_b_a{display:flex;align-items:center}
.jpdh .qrdd_b_b{margin-left:42rpx;margin-right:30rpx;color:#999999;font-size:26rpx}
.jpdh .qrdd_b{position:relative}
.qrdd_b_jt{width:14rpx;height:24rpx;position:absolute;right:26rpx;top:50%;margin-top:-12rpx}
.jpdh .qrdd_bz{margin-bottom:164rpx}
.jpdh .yycgCon_b text{color:#131315}
.hbdhCon_a{margin:20rpx 26rpx 0 26rpx;height:388rpx;overflow:hidden;position:relative;-display:flex;align-items:center;justify-content:center}
.hbdhCon_a_bj{display:block;width:100%;height:100%;position:absolute;top:0;left:0;z-index:1}
.hbdhCon_a_n{margin:118rpx 130rpx 0 130rpx;height:146rpx;overflow:hidden;position:relative;z-index:2;display:flex;align-items:center;justify-content:space-between}
.hbdhCon_a_l{width:147rpx;height:147rpx}
.hbdhCon_a_r{width:calc(100% - 180rpx);color:#131315;font-size:32rpx;margin-left:34rpx}
.hbdhCon_b{margin:20rpx 26rpx 0 26rpx;height:auto;background:#FFFFFF;border-radius:10rpx;padding:26rpx;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between}
.hbdhCon_b_t view{color:#333333;font-size:26rpx}
.hbdhCon_b_t text{display:block;color:#999;font-size:26rpx;margin-top:10rpx}
.edit_skxx .hbdhCon_a_r{width:auto;margin-left:0}
.edit_skxx .peodex_foo .peodex_foo_r{width:502rpx;height:90rpx;background:#333333;margin:auto;line-height:90rpx}
.edit_skxx .peodex_foo{background:#F8F8FA}
.pri_one{width:100%;height:641rpx;overflow:hidden;position:relative}
.pri_one_bj{display:block;width:100%;height:100%;position:absolute;top:0;left:0;z-index:1}
.pri_one_a{position:relative;z-index:2;margin:0 96rpx;height:54rpx;overflow:hidden;background:rgba(0,0,0,.7)}
.pri_one_b{width:320rpx;height:198rpx;overflow:hidden;margin:136rpx auto 0 auto;position:relative;z-index:2;left:24rpx}
.pri_one_b image{display:block;width:100%;height:100%}
.pri_one_c{width:118rpx;height:118rpx;position:absolute;bottom:23rpx;left:23rpx;z-index:2}
.pri_one_c image{display:block;width:100%;height:100%}
.pri_foo{width:100%;height:196rpx;overflow:hidden;position:fixed;bottom:0;left:0;z-index:9}
.pri_foo image{display:block;width:100%;height:100%;position:absolute;top:0;left:0}
.pri_foo_t{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:center;color:#ffffff;font-size:24rpx;position:relative;z-index:2;margin-top:26rpx}
.pri_foo_t text{display:block;width:80rpx;height:5rpx;background:linear-gradient( to left,#615386 0%,#493F62 100%);margin:0 20rpx}
.pri_foo_t text:last-child{background:linear-gradient( to right,#615386 0%,#493F62 100%)}
.pri_foo_b{position:relative;z-index:2;margin:22rpx 72rpx 0 72rpx;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:space-between}
.pri_foo_b view{width:282rpx;height:84rpx;overflow:hidden}
.pri_two{width:100%;height:auto;overflow:hidden;margin:20rpx 0 230rpx 0rpx}
.pri_two_t{width:100%;height:auto;overflow:hidden;display:flex;align-items:center;justify-content:center;font-size:34rpx;color:#fff}
.pri_two_t image{width:116rpx;height:24rpx;margin:0 20rpx}
.pri_two_t image:nth-child(1){-webkit-transform:rotate(180deg);transform:rotate(180deg)}
.pri_two_b{width:100%;height:auto;overflow:hidden}
.pri_two_b_li{width:220rpx;height:300rpx;border-radius:16rpx;overflow:hidden;float:left;margin:20rpx 0 0 22rpx;box-sizing:border-box;position:relative;background:#fff;}
.pri_two_b_li_bj{width:100%;height:100%;position:absolute;top:0;left:0;z-index:4}
.pri_two_b_li_t{position:relative;z-index:2;width:100%;height:212rpx;overflow:hidden}
.pri_two_b_li_t image{display:block;width:100%;height:100%}
.pri_two_b_li_b{overflow:hidden;font-size: 22rpx;color: #551112;display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp:2;
overflow: hidden;
margin:14rpx 16rpx;
line-height:30rpx;
}
.pri_two_b_li_cards{
	position: relative;
	z-index: 2;
	width: 100%;
	height: 212rpx;
	overflow: hidden;
	background: linear-gradient(270deg, #473F3F 0%, #514343 48.5%, #211C1B 100%);
}
.pri_two_b_li_hb{
	position: relative;
	z-index: 2;
	width: 100%;
	height: 212rpx;
	overflow: hidden;
}
.pri_two_b_li_hb_bj{
	display:block;
	width:210rpx;
	height:214rpx;
	position:absolute;
	top: 0;left:0;
	right: 0;bottom:0;
	margin: auto;
	z-index: 1;
}
.pri_two_b_li_hb_n{
	position:relative;
	z-index: 2;
	width: 100%;
	-height:100rpx;
	margin-top:52rpx;
	-background:rgba(0,0,0,.6);
	display: flex;
	align-items:baseline;
	justify-content:center;
	color:#835333;
	font-size: 46rpx;
	-webkit-transform: rotate(-5deg);
	        transform: rotate(-5deg);
}
.pri_two_b_li_hb_n image{
	width: 20rpx;
	height: 20rpx;
}
.pri_two_b_li_b_hb{
	font-size: 22rpx;
	color: #551112;
	overflow: hidden;
	margin: 14rpx 16rpx;
	line-height: 60rpx;
	height:60rpx;
	text-align:center;
}
/*.bak{
	width:63rpx;
	height:63rpx;
	border-radius:50%;
	position: fixed;
	top:0;left:30rpx;
	z-index:9;
	overflow:hidden;
}
.bak image{
	display:block;
	width: 100%;
	height:100%;
}*/
.pri_one_a .uni-noticebar__content-text{font-size:20rpx!important;line-height:54rpx!important}
.pri_one_a .uni-noticebar{padding:0!important;margin:0!important}
.cjtcTc{width:100%;height:100%;position:fixed;top:0;left:0;z-index:9;background:rgba(0,0,0,.8);display:flex;align-items:center;justify-content:center}
.cjtcTc_title{display:block;width:364rpx;height:58rpx;margin:auto}
.cjtcTc_f{width:282rpx;height:84rpx;background:linear-gradient(180deg,#E9E5F2 0%,#D0D2D7 100%);box-shadow:0 5rpx 13rpx 0 #d8dae061;border-radius:50rpx;color:#131315;font-size:32rpx;text-align:center;line-height:84rpx;margin:auto;margin-top:80rpx;margin-bottom:80rpx;font-weight:bold}
.cjtcTc_a{width:100%;height:auto;overflow:hidden;margin-top:140rpx}
.cjtcTc_a .pri_two_b_li{width:344rpx;height:476rpx;border-radius:26rpx;margin:0}
.cjtcTc_a .pri_two_b_li .pri_two_b_li_t{height:335rpx}
.cjtcTc_a .pri_two_b_li .pri_two_b_li_hb{height:335rpx}
.cjtcTc_a .pri_two_b_li .pri_two_b_li_hb_bj {
	width:324rpx;height:330rpx;
}
.cjtcTc_a .pri_two_b_li_hb_n{
	font-size: 70rpx;
	margin-top: 90rpx;
}
.cjtcTc_a .pri_two_b_li_hb_n image {
	width: 30rpx;
	height: 30rpx;
}
.cjtcTc_a .pri_two_b_li_b_hb{
	font-size: 32rpx;
	height: 120rpx;
	line-height: 120rpx;
}
.cjtcTc_a .pri_two_b_li_cards {
	height:335rpx;
}
.cjtcTc_a .pri_two_b_li_b{font-size:32rpx;line-height:42rpx;margin:28rpx 24rpx}
.cjtcTc_b{width:100%;height:auto;overflow:hidden;display:flex;flex-wrap:wrap;justify-content:center;margin-top:74rpx}
.cjtcTc_b .pri_two_b_li{width:154rpx;height:210rpx;border-radius:10rpx;margin:16rpx 0 0 16rpx;position:relative;}
.pri_two_b_li_num{position:absolute;font-size: 22rpx;color: #551112;right:16rpx;bottom: 14rpx;right: 0;bottom: 0;background:rgba(0,0,0,.7);color:#fff;padding:6rpx 14rpx 6rpx 10rpx;border-radius:16rpx 0 0 0;}
.cjtcTc_b .pri_two_b_li_t{height:150rpx}
.cjtcTc_b .pri_two_b_li_b{font-size:18rpx;line-height:22rpx;margin:10rpx 10rpx}
.cjtcTc_sltitle{display:block;width:218rpx;height:58rpx}
.cjtcslTc_f{}
.cjtcslTc_f view{color:#ffffff;font-size:33rpx;display:flex;align-items:center}
.cjtcslTc_f view image{width:38rpx;height:38rpx;margin-right:16rpx}
.cjtcslTc_a{width:100%;height:auto;overflow:hidden;margin:200rpx 0}
.cjtcTc .cjtcTc_n{display:flex;align-items:center;justify-content:center;flex-direction:column}
.cjloding{display:block;}
.cjloding .cjtcTc_n{height: 100%;}
.cjloding .cjtcslTc_a{position:relative;}
.cjloding_xian{width:6rpx;height:300rpx;background:#615386;position:absolute;bottom:0;left: 50%;margin-left:-3rpx;z-index:9;}
.boxshadow_l{width: 210rpx;height:300rpx;background:#615386;position:absolute;left:0;bottom:0;z-index:9;background: linear-gradient(to right, #000 0%, transparent 100%);}
.boxshadow_r{width: 210rpx;height:300rpx;background:#615386;position:absolute;right:0;bottom:0;z-index:9;background: linear-gradient(to left, #000 0%, transparent 100%);}
.cjtcTc_b .pri_two_b_li_cards{
	height: 150rpx;
}
.cjtcTc_b .pri_two_b_li_b_hb {
    font-size: 18rpx;
    line-height:44rpx;
    margin: 10rpx 10rpx;
    height: 44rpx;
}
.cjtcTc_b .pri_two_b_li_hb{
	height:150rpx;
}
.cjtcTc_b .pri_two_b_li_hb_bj{
	width: 140rpx;
	height:142rpx;
}
.cjtcTc_b .pri_two_b_li_hb_n {
	font-size: 28rpx;
	margin-top: 40rpx;
}
.cjtcTc_b .pri_two_b_li_hb_n image{
	width: 16rpx;
	height: 16rpx;
}
.tab-bar .uicon-bag{
	font-weight: bold!important;
}
.tab-bar .uicon-bag-fill{
	font-weight: bold!important;
}
.teach_li_r_t.flex{
	flex-wrap: wrap;
}
.userReport .ord_nav_li{
	width:25%;
}
.userbg_t{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: space-between;
}
.userbg_t_l{
	color: #131315;
	font-size: 28rpx;
	font-weight: bold;
}
.userbg_t_r{
	display: flex;
	align-items:center;
	font-size: 28rpx;
	color: #999999;
}
.userbg_t_r image{
	width:12rpx;
	height:20rpx;
	margin-left:12rpx;	
	position: relative;
	top: 2rpx;
}
.userbg_b{
	width: 100%;
	height:auto;
	overflow:hidden;
	margin-top:16rpx;
	font-size: 28rpx;
	color: #999999;
}
.lxkcCon{
	width:100%;
	height: 100%;
	position: fixed;
	bottom:0;left:0;
	background:rgba(0,0,0,.6);
	z-index: 9;
}
.lxkcCon_n{
	width: 100%;
	height:auto;
	background: #FFFFFF;
	border-radius: 20rpx 20rpx 0rpx 0rpx;
	position:absolute;
	bottom:0;left:0;
	padding-bottom:calc(24rpx + env(safe-area-inset-bottom));
}
.lxkcCon_t{
	font-weight: 500;
	font-size: 26rpx;
	color: #333333;
	line-height: 30rpx;
	margin:26rpx;
}
.lxkcCon_f{
	width:502rpx;
	height:90rpx;
	font-size: 26rpx;
	color: #F8F8FA;
	margin:auto;
	border-radius:50rpx;
	background:#131315;
	text-align:center;
	line-height:90rpx;
	margin-bottom:20rpx;
}
.lxkcCon_c{
	width: 100%;
	height:auto;
	overflow:hidden;
	max-height: 1000rpx;
	overflow:auto;
}
.lxkcCon .teaCon_li{
	margin:0;
}
/*全局控制颜色 go*/
.lxkcCon_f{
	background: var(--qjbutton-color);
}
.edg{
	color: var(--qjbutton-color);
}
.memk_fou{
	background: var(--qjbutton-color);
}
.ordzf_foo{
	background: var(--qjbutton-color);
}
.teaxzTanc_b text {
	border-color:var(--qjbutton-color)!important;
	background: var(--qjbutton-color);
}
.teaxzTanc_t_ac{
	background:var(--qjbutton-color)!important;
}
.rlxz_con_l_li_ac {
    background:var(--qjbutton-color)!important;
}
.rlxz_con_l_li text {
	border-top: 14rpx solid var(--qjbutton-color);
}
.rlxz_con_r{
	background:var(--qjbutton-color);
}
.md_xz .md_xz_bj{
	background-color:var(--qjbutton-color);
}
.md_xz .md_xz_xt{
	background-color:var(--qjbutton-color);
}
.md_xz .md_xz_title{
	background-color:var(--qjbutton-color);
}
.stor_thr_c_li_ac{
	color:var(--qjbutton-color)!important;
}
.stor_thr_c_li_ac text {
	border-bottom: 14rpx solid var(--qjbutton-color)!important;
}
.stor_one_b text{
	background-color:var(--qjbutton-color);
}
.store_list .store_li_d .btn{
	background-color:var(--qjbutton-color);
}
.kcxq_one_b_r_l text {
	background-color:var(--qjbutton-color);
}
.teaCon_li_b_r{
	background:var(--qjbutton-color);	
	color:var(--qjziti-color);
}
.teaCon_li_b_c_c text{
	background:var(--qjbutton-color);
	-color:var(--qjziti-color);
}
.teach .teach_list .teach_li .teach_li_r .teach_li_r_t .teach_li_r_tag {
	background:var(--qjbutton-color)!important;
}
.cour_two_li_r_e_r{
	background:var(--qjbutton-color);
}
.poi_thr_r_li_r_b view {
	background:var(--qjbutton-color);
}
/* .poi_thr_l_li text {
	background:var(--qjbutton-color);
}
.poi_thr_l_li_ac view {
	color:var(--qjbutton-color)!important;
} */
.int_one{
	background:var(--qjbutton-color);
}
.int_two_l_ac {
	background:var(--qjbutton-color)!important;
}
.ord_con_li_d .back {
	border-color:var(--qjbutton-color)!important;
	background:var(--qjbutton-color)!important;
}
.kcxq_one_a text {
	background:var(--qjbutton-color)
}
.kcxq_foo_r .back {
    background:var(--qjbutton-color)!important;
	border: 1px solid var(--qjbutton-color)!important;
}
.kcxq_one_b_r_l text{
	background:var(--qjbutton-color);
}
.lsxq_title text{
	background:var(--qjbutton-color);
}
.peodex_foo_r{
	background:var(--qjbutton-color);
}
.cou_con_li_r_d{
	background:var(--qjbutton-color);
}
.lea_two_sub{
	background:var(--qjbutton-color);
}
.lea_one{
	background:var(--qjbutton-color);
}
.lea_con_li_c view{
	background:var(--qjbutton-color);
}
.fee_one_b_li_ac {
	background:var(--qjbutton-color)!important;
	border: 1px solid var(--qjbutton-color)!important;
}
.mes_one_ac{
	background:var(--qjbutton-color)!important;
}
.mes_two_li_xx {
	background:var(--qjbutton-color)!important;
	border: 1px solid var(--qjbutton-color)!important;
}
.min_ewm_c{
	background:var(--qjbutton-color)
}
.edma_thr view:nth-child(1) {
	background:var(--qjbutton-color)!important;
}
.myCourse_con_li_c_r{
	background:var(--qjbutton-color);
}
.myCourse_con_li_zt{
	background:var(--qjbutton-color);
}
.kcxq_one_b_r_l text{
	background:var(--qjbutton-color);
}
.kcxq_one_a text{
	background:var(--qjbutton-color);
}
.kcxq_two_xf{
	background:var(--qjbutton-color);
}
.kcxq_foo_r .back{background:var(--qjbutton-color)!important;}
.memk_two_b_li_l {background:var(--qjbutton-color);}
.memk_thr_li_ac{
	border: 2px solid var(--qjbutton-color)!important;
}
.memk_thr_li_xz{background:var(--qjbutton-color);}
.pro_one{
	background:var(--qjbutton-color);
}
.peode_foo view{
	background:var(--qjbutton-color);
}
.add_foo view{
	background:var(--qjbutton-color);
}
.li_d_l .ac{
	border-radius:50%;
	background:var(--qjbutton-color);
}
.check_sex_li_ac image{
	border-radius:50%;
	background:var(--qjbutton-color);
}
.jlcon_li_r_d{
	background:var(--qjbutton-color);
}
.winningrecordxq .peode_foo view {
    background:var(--qjbutton-color);
}
.jpdh .peodex_foo .peodex_foo_r {
	background:var(--qjbutton-color);
}
.edit_skxx .peodex_foo .peodex_foo_r {
	background:var(--qjbutton-color);
}
.ran_one_ac {
	background:var(--qjbutton-color)!important;
}
.seajg_con_li_b text {
	background:var(--qjbutton-color);
}
.xytcCon .ty {
	background:var(--qjbutton-color)!important;
}
.myMemberCard  .ty {
	background:var(--qjbutton-color)!important;
}
.messageCenter  .tzxf {
	background:var(--qjbutton-color)!important;
}
/*全局控制颜色 end*/
/*tzgl go*/
.tzxf{
	width: 122rpx;
	height: 122rpx;
	background: #131315;
	box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0,0,0,0.25);
	border-radius:50%;
	font-size: 26rpx;
	color: #FFFFFF;
	text-align:center;
	position:absolute;
	right:32rpx;
	bottom:20%;
	display: flex;
	align-items:center;
	justify-content:center;
	flex-direction:column;
}
.tzxf text{
	line-height: 40rpx;
	display: block;
	font-size: 26rpx;
	color: #FFFFFF;
	letter-spacing: 2px;
}
.tzglOne{
	margin:20rpx 26rpx 0 26rpx;
	padding:30rpx 26rpx;
	height:auto;
	overflow:hidden;
	background: #FFFFFF;
	border-radius: 20rpx;
	box-sizing: border-box;
}
.tzglOne_t{
	display: flex;
	align-items:center;
	justify-content: space-between;
}
.tzglOne_t_l{
	font-size: 28rpx;
	color: #131315;
	font-weight: bold;
}
.tzglOne_t_r{
	font-size: 24rpx;
	color: #999999;
	display: flex;
	align-items:center;
}
.tzglOne_t_r image{
	width: 14rpx;
	height: 22rpx;
	margin-left:10rpx;
}
.tzglTwo{
	margin:20rpx 26rpx 0 26rpx;
	padding:30rpx 26rpx;
	height:auto;
	overflow:hidden;
	background: #FFFFFF;
	border-radius: 20rpx;
	box-sizing: border-box;
}
.tzglTwo .tzglOne_t_r image{
	width: 40rpx;
	height: 40rpx;
}
.tzglTwo_b{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: space-between;
}
.tzglTwo_b view{
	width: 307rpx;
	height: 88rpx;
	background: #EFEFEF;
	border-radius: 8rpx;
	display: flex;
	align-items:center;
	justify-content: center;
	font-size: 24rpx;
	color: #131315;
	margin-top:26rpx;
}
.tzglTwo_b view image{
	width:48rpx;
	height:48rpx;
	margin-right: 8rpx;
}
.tzxq_one{
	font-size: 28rpx;
	color: #131315;
	margin:20rpx 26rpx 0 26rpx;
}
.tzxq_two{
	margin:20rpx 26rpx;
	padding:0 26rpx;
	box-sizing: border-box;
	background: #FFFFFF;
	border-radius: 20rpx;
	overflow:hidden;
}
.tzxq_two_t{
	display: flex;
	align-items:center;
	font-size: 32rpx;
	color: #131315;
	margin-top:36rpx;
	font-weight: bold;
}
.tzxq_two_t text{
	width: 6rpx;
	height: 30rpx;
	background: #131315;
	border-radius: 40rpx;
	margin-right: 10rpx;
}
.tzxq_two_b{
	width: 100%;
	height:auto;
	overflow:hidden;
}
.tzxq_two_b_li{
	display: flex;
	align-items:center;
	justify-content: space-between;
	font-size: 28rpx;
	color: #333333;
	height: 110rpx;
	border-bottom:1px solid #EBEBEB;
}
.tzxq_two .tzxq_two_b_li:last-child{
	border-bottom:none!important;
}
.tstsTanc{
	width: 100%;
	height: 100%;
	position: fixed;
	top:0;left:0;
	z-index:9;
	background:rgba(0,0,0,.7);
	display: flex;
	align-items:center;
	justify-content: center;
}
.tstsTanc_n{
	width: 626rpx;
	height:678rpx;
	overflow:hidden;
	position:relative;
	top:-80rpx;
}
.tstsTanc_bj{
	width:100%;
	height: 100%;
	position:absolute;
	top:0;left:0;
	z-index: 0;
}
.tstsTanc_n_n{
	position:relative;
	z-index: 2;
	overflow:hidden;
}
.tstsTanc_a{
	font-weight: bold;
	font-size: 32rpx;
	color: #000000;
	text-align:center;
	margin-top: 330rpx;
}
.tstsTanc_b{
	font-size: 28rpx;
	color: #999999;
	text-align:center;
	margin:20rpx 70rpx 0 70rpx;
}
.tstsTanc_b text{
	width: 14rpx;
	height: 14rpx;
	background: #999999;
	border-radius:50%;
	display:inline-block;
	margin-right: 10rpx;
}
.tstsTanc_c{
	width: 422rpx;
	height: 80rpx;
	background: #131315;
	border-radius: 88rpx;
	font-size: 32rpx;
	color: #FFFFFF;
	text-align:center;
	line-height:80rpx;
	margin:64rpx auto 0 auto;
}
.tstsTanc_b_cha{
	font-size:24rpx;
	display: flex;
	align-items:center;
	justify-content:center;
}
.bdewmCon{width:100%;height:100%;position:fixed;top:0;left:0;background:rgba(0,0,0,.6);
z-index:99999;
display: flex;
align-items:center;
justify-content:center;
flex-direction:column;
}
.bdewmCon_t{
	width: 566rpx;
	height: auto;
	padding: 52rpx 0;
	background: #FFFFFF;
	border-radius: 20rpx;
}
.bdewmCon_t_a{
	font-size: 32rpx;
	color: #131315;
	text-align:center;
	font-weight: bold;
}
.bdewmCon_t_b{
	width:414rpx;
	height:414rpx;
	display:block;
	margin: auto;
	margin-top:20rpx;
}
.bdewmCon_t_c{
	text-align:center;
	font-size: 28rpx;
	color: #131315;
	margin-top:20rpx;
}
.bdewmCon_b{
	width:48rpx;
	height:48rpx;
	margin-top: 52rpx;
}
.bdewmCon_t_d{
	display:flex;
	align-items:center;
	justify-content:center;
	font-size: 20rpx;
	color: #131315;
	margin-top:16rpx;
}
.bdewmCon_t_d text{
	display:block;
	width:73rpx;
	height: 0px;
	border-bottom:1px dashed #131315;
	margin:0 16rpx;
}
/*tzgl end*/
/*specification go*/
.specification{
	width:100%;
	height: 100%;
	background:rgba(0,0,0,.7);
	position: fixed;
	bottom:0;left:0;
	z-index:9999;
}
.spe_n{
	width: 100%;
	height:auto;
	overflow:hidden;
	background: #FFFFFF;
	border-radius: 20rpx 20rpx 0 0;
	position: fixed;
	bottom:0;left:0;
	padding:26rpx;
	box-sizing: border-box;
}
.spe_n_b{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: space-between;
	margin-top: 30rpx;
}
.spe_n_b_l{
	font-size: 28rpx;
	color: #131315;
	font-weight: bold;
}
.spe_n_b_r{
	display: flex;
	align-items:center;
}
.spe_n_b_r view{
	width: 54rpx;
	height: 54rpx;
	background: #F7F8FC;
	border-radius: 4rpx;
	text-align:center;
	line-height:54rpx;
	font-size:40rpx;
}
.spe_n_b_r input{
	width: 70rpx;
	height: 54rpx;
	font-size: 28rpx;
	color: #131315;
	text-align:center;
}
.spe_n_a{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: space-between;
}
.spe_n_a_l{
	width: 160rpx;
	height: 160rpx;
	border-radius: 20rpx;
	border:1px solid #131315;
	overflow:hidden;
}
.spe_n_a_l image{
	display:block;
	width: 100%;
	height:100%;
}
.spe_n_a_r{
	width:calc(100% - 180rpx);
	height:160rpx;
	overflow:hidden;
	position:relative;
}
.spe_n_a_r_a{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: space-between;
}
.spe_n_a_r_a view{
	width:calc(100% - 70rpx);
	font-size: 28rpx;
	font-weight: bold;
	color: #131315;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp:1;
	overflow: hidden;
}
.spe_n_a_r_a image{
	width: 44rpx;
	height: 44rpx;
}
.spe_n_a_r_b{
	font-size: 22rpx;
	color: #999999;
	margin-top:8rpx;
}
.spe_n_a_r_c{
	width: 100%;
	height:auto;
	overflow:hidden;
	position: absolute;
	bottom:0;left:0;
	display: flex;
	align-items:center;
	justify-content: space-between;
}
.spe_n_a_r_c view{
	font-weight: 400;
	font-size: 28rpx;
	color:#5C5C5C;
}
.spe_n_a_r_c text{
	font-size: 26rpx;
	color: #999999;
}
.spe_n_c{
	width: 100%;
	height:auto;
	overflow:hidden;
}
.spe_n_c_li{
	width: 100%;
	height:auto;
	overflow:hidden;
	margin-top: 32rpx;
}
.spe_n_c_t{
	font-weight: bold;
	font-size: 28rpx;
	color: #131315;
}
.spe_n_c_t_b{
	width: 100%;
	height:auto;
	overflow:hidden;
}
.spe_n_c_t_b view{
	width: 132rpx;
	height: 70rpx;
	background: #F7F8FC;
	border-radius: 4rpx;
	text-align:center;
	display: flex;
	align-items:center;
	justify-content:center;
	font-size: 24rpx;
	color: #131315;
	padding: 0 10rpx;
	margin:20rpx 0rpx 0 20rpx;
	float:left;
	border: 1px solid #F7F8FC;
}
.spe_n_c_t_b view:nth-child(4n+1){
	margin-left: 0;
}
.sho_con_b_a_b_ac{
	border: 1px solid #131315!important;
}
.spe_n_d{
	width: 608rpx;
	height: 96rpx;
	background: #131315;
	border-radius: 88rpx;
	text-align:center;
	line-height: 96rpx;
	font-size: 32rpx;
	color: #FFFFFF;
	margin:60rpx auto 40rpx auto;
}
.spe_n_f{
	width: 100%;
	height:auto;
	overflow:hidden;
	display: flex;
	align-items:center;
	justify-content: space-between;
	flex-direction:column;
}
/*specification end*/
/*signing go*/
.sig_one{
	margin:30rpx 26rpx 0 26rpx;
	/* height:calc(100vh - 650rpx);
	overflow:auto; 
	background:#ccc;*/
	font-size: 28rpx;
	color: #131315;
}
.sig_two{
	margin:0 66rpx;
	height:auto;
	overflow:hidden;
}
.sig_two.sig_two_fixed{
	width: 100%;
	margin:0!important;
	padding:24rpx 66rpx 0 66rpx;
	box-sizing: border-box;
	position: fixed;
	bottom:0;left:0;
	background:#fff;
}
.sig_two_t{
	font-size: 32rpx;
	color: #131315;
	text-align:center;
	font-weight:bold;
}
.sig_two_b{
	height: 310rpx;
	background: #F6F6F6;
	border-radius: 20rpx;
	margin-top:40rpx;
}
.sig_two_b text{
	display:block;
	width: 100%;
	text-align:center;
	line-height:310rpx;
	font-size: 28rpx;
	color: #999999;
}
.sig_two_b image{
	display:block;
	max-width: 100%;
	max-height: 100%;
	margin: auto;
}
.sig_two_f{
	width:100%;
	height: 96rpx;
	background: #131315;
	border-radius: 88rpx;
	font-size: 32rpx;
	color: #FFFFFF;
	text-align:center;
	line-height:96rpx;
	margin:40rpx 0 20rpx 0;
}
.lodingg{
	width:100%;
	height: 100%;
	position: fixed;
	top:0;left:0;
	z-index:999999;
	background:#fff;
}
.teaCon .teaCon_li_b {
	align-items: inherit!important;
}
.teaCon .teaCon_li_b_r {
	top:56rpx;
}
/*signing end*/
.xytc{
	width: 100%;
	height: 100%;
	position: fixed;
	top:0;left:0;
	background:rgba(0, 0, 0, .7);
	z-index: 9999999;
	display: flex;
	align-items:center;
	justify-content:center;
}
.xytcCon{
	width:562rpx;
	height:auto;
	overflow:hidden;
	border-radius:20rpx;
	background:#fff;
}
.xytcCon_a{
	margin:46rpx 20rpx 0 20rpx;
	height:auto;
	overflow:hidden;
	font-size: 36rpx;
	color: #3f3f3f;
	text-align:center;
	font-weight: bold;
}
.xytcCon_b{
	margin:20rpx 40rpx 0 40rpx;
	height:auto;
	overflow:hidden;
	font-size: 24rpx;
	color: #505050;
}
.xytcCon_c{
	margin:20rpx 40rpx 0 40rpx;
	height:auto;
	overflow:hidden;
	background:#f4f5fa;
	border-radius:10rpx;
}
.xytcCon_c view{
	margin:14rpx;
	/* height:300rpx; */
	/* overflow:auto; */
}
.xytcCon_c scroll-view{
	width: 100%;
}
.xytcCon_f{
	margin:30rpx 40rpx 20rpx 40rpx;
	height:auto;
	overflow:hidden;
}
.xytcCon_f .ty{
	width: 100%;
	height: 100rpx;
	text-align:center;
	line-height: 100rpx;
	font-size:30rpx;
	color:#fff;
	background:#0ece92;
	border-radius: 10rpx;
}
.xytcCon_f .noty{
	margin-top: 10rpx;
	text-align:center;
	width: 100%;
	height:100rpx;
	font-size:28rpx;
	color:#8d8d8d;
	line-height: 100rpx;
}
@charset "UTF-8";
/*!
 * animate.css - https://animate.style/
 * Version - 4.1.1
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2020 Animate.css
 */
page{--animate-duration:1s;--animate-delay:1s;--animate-repeat:1}
.animate__animated{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-duration:var(--animate-duration);animation-duration:var(--animate-duration);-webkit-animation-fill-mode:both;animation-fill-mode:both}
.animate__animated.animate__infinite{-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}
.animate__animated.animate__repeat-1{-webkit-animation-iteration-count:1;animation-iteration-count:1;-webkit-animation-iteration-count:var(--animate-repeat);animation-iteration-count:var(--animate-repeat)}
.animate__animated.animate__repeat-2{-webkit-animation-iteration-count:2;animation-iteration-count:2;-webkit-animation-iteration-count:calc(var(--animate-repeat)*2);animation-iteration-count:calc(var(--animate-repeat)*2)}
.animate__animated.animate__repeat-3{-webkit-animation-iteration-count:3;animation-iteration-count:3;-webkit-animation-iteration-count:calc(var(--animate-repeat)*3);animation-iteration-count:calc(var(--animate-repeat)*3)}
.animate__animated.animate__delay-1s{-webkit-animation-delay:1s;animation-delay:1s;-webkit-animation-delay:var(--animate-delay);animation-delay:var(--animate-delay)}
.animate__animated.animate__delay-2s{-webkit-animation-delay:2s;animation-delay:2s;-webkit-animation-delay:calc(var(--animate-delay)*2);animation-delay:calc(var(--animate-delay)*2)}
.animate__animated.animate__delay-3s{-webkit-animation-delay:3s;animation-delay:3s;-webkit-animation-delay:calc(var(--animate-delay)*3);animation-delay:calc(var(--animate-delay)*3)}
.animate__animated.animate__delay-4s{-webkit-animation-delay:4s;animation-delay:4s;-webkit-animation-delay:calc(var(--animate-delay)*4);animation-delay:calc(var(--animate-delay)*4)}
.animate__animated.animate__delay-5s{-webkit-animation-delay:5s;animation-delay:5s;-webkit-animation-delay:calc(var(--animate-delay)*5);animation-delay:calc(var(--animate-delay)*5)}
.animate__animated.animate__faster{-webkit-animation-duration:.5s;animation-duration:.5s;-webkit-animation-duration:calc(var(--animate-duration)/2);animation-duration:calc(var(--animate-duration)/2)}
.animate__animated.animate__fast{-webkit-animation-duration:.8s;animation-duration:.8s;-webkit-animation-duration:calc(var(--animate-duration)*0.8);animation-duration:calc(var(--animate-duration)*0.8)}
.animate__animated.animate__slow{-webkit-animation-duration:2s;animation-duration:2s;-webkit-animation-duration:calc(var(--animate-duration)*2);animation-duration:calc(var(--animate-duration)*2)}
.animate__animated.animate__slower{-webkit-animation-duration:3s;animation-duration:3s;-webkit-animation-duration:calc(var(--animate-duration)*3);animation-duration:calc(var(--animate-duration)*3)}
@media (prefers-reduced-motion:reduce), print{.animate__animated{-webkit-animation-duration:1ms!important;animation-duration:1ms!important;transition-duration:1ms!important;-webkit-animation-iteration-count:1!important;animation-iteration-count:1!important}.animate__animated[class*=Out]{opacity:0}}
@-webkit-keyframes bounce{0%,20%,53%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1);-webkit-transform:translateZ(0);transform:translateZ(0)}40%,43%{-webkit-animation-timing-function:cubic-bezier(.755,.05,.855,.06);animation-timing-function:cubic-bezier(.755,.05,.855,.06);-webkit-transform:translate3d(0,-30px,0) scaleY(1.1);transform:translate3d(0,-30px,0) scaleY(1.1)}70%{-webkit-animation-timing-function:cubic-bezier(.755,.05,.855,.06);animation-timing-function:cubic-bezier(.755,.05,.855,.06);-webkit-transform:translate3d(0,-15px,0) scaleY(1.05);transform:translate3d(0,-15px,0) scaleY(1.05)}80%{transition-timing-function:cubic-bezier(.215,.61,.355,1);-webkit-transform:translateZ(0) scaleY(.95);transform:translateZ(0) scaleY(.95)}90%{-webkit-transform:translate3d(0,-4px,0) scaleY(1.02);transform:translate3d(0,-4px,0) scaleY(1.02)}}
@keyframes bounce{0%,20%,53%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1);-webkit-transform:translateZ(0);transform:translateZ(0)}40%,43%{-webkit-animation-timing-function:cubic-bezier(.755,.05,.855,.06);animation-timing-function:cubic-bezier(.755,.05,.855,.06);-webkit-transform:translate3d(0,-30px,0) scaleY(1.1);transform:translate3d(0,-30px,0) scaleY(1.1)}70%{-webkit-animation-timing-function:cubic-bezier(.755,.05,.855,.06);animation-timing-function:cubic-bezier(.755,.05,.855,.06);-webkit-transform:translate3d(0,-15px,0) scaleY(1.05);transform:translate3d(0,-15px,0) scaleY(1.05)}80%{transition-timing-function:cubic-bezier(.215,.61,.355,1);-webkit-transform:translateZ(0) scaleY(.95);transform:translateZ(0) scaleY(.95)}90%{-webkit-transform:translate3d(0,-4px,0) scaleY(1.02);transform:translate3d(0,-4px,0) scaleY(1.02)}}
.animate__bounce{-webkit-animation-name:bounce;animation-name:bounce;-webkit-transform-origin:center bottom;transform-origin:center bottom}
@-webkit-keyframes flash{0%,50%,to{opacity:1}25%,75%{opacity:0}}
@keyframes flash{0%,50%,to{opacity:1}25%,75%{opacity:0}}
.animate__flash{-webkit-animation-name:flash;animation-name:flash}
@-webkit-keyframes pulse{0%{-webkit-transform:scaleX(1);transform:scaleX(1)}50%{-webkit-transform:scale3d(1.05,1.05,1.05);transform:scale3d(1.05,1.05,1.05)}to{-webkit-transform:scaleX(1);transform:scaleX(1)}}
@keyframes pulse{0%{-webkit-transform:scaleX(1);transform:scaleX(1)}50%{-webkit-transform:scale3d(1.05,1.05,1.05);transform:scale3d(1.05,1.05,1.05)}to{-webkit-transform:scaleX(1);transform:scaleX(1)}}
.animate__pulse{-webkit-animation-name:pulse;animation-name:pulse;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}
@-webkit-keyframes rubberBand{0%{-webkit-transform:scaleX(1);transform:scaleX(1)}30%{-webkit-transform:scale3d(1.25,.75,1);transform:scale3d(1.25,.75,1)}40%{-webkit-transform:scale3d(.75,1.25,1);transform:scale3d(.75,1.25,1)}50%{-webkit-transform:scale3d(1.15,.85,1);transform:scale3d(1.15,.85,1)}65%{-webkit-transform:scale3d(.95,1.05,1);transform:scale3d(.95,1.05,1)}75%{-webkit-transform:scale3d(1.05,.95,1);transform:scale3d(1.05,.95,1)}to{-webkit-transform:scaleX(1);transform:scaleX(1)}}
@keyframes rubberBand{0%{-webkit-transform:scaleX(1);transform:scaleX(1)}30%{-webkit-transform:scale3d(1.25,.75,1);transform:scale3d(1.25,.75,1)}40%{-webkit-transform:scale3d(.75,1.25,1);transform:scale3d(.75,1.25,1)}50%{-webkit-transform:scale3d(1.15,.85,1);transform:scale3d(1.15,.85,1)}65%{-webkit-transform:scale3d(.95,1.05,1);transform:scale3d(.95,1.05,1)}75%{-webkit-transform:scale3d(1.05,.95,1);transform:scale3d(1.05,.95,1)}to{-webkit-transform:scaleX(1);transform:scaleX(1)}}
.animate__rubberBand{-webkit-animation-name:rubberBand;animation-name:rubberBand}
@-webkit-keyframes shakeX{0%,to{-webkit-transform:translateZ(0);transform:translateZ(0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}20%,40%,60%,80%{-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}}
@keyframes shakeX{0%,to{-webkit-transform:translateZ(0);transform:translateZ(0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(-10px,0,0);transform:translate3d(-10px,0,0)}20%,40%,60%,80%{-webkit-transform:translate3d(10px,0,0);transform:translate3d(10px,0,0)}}
.animate__shakeX{-webkit-animation-name:shakeX;animation-name:shakeX}
@-webkit-keyframes shakeY{0%,to{-webkit-transform:translateZ(0);transform:translateZ(0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}20%,40%,60%,80%{-webkit-transform:translate3d(0,10px,0);transform:translate3d(0,10px,0)}}
@keyframes shakeY{0%,to{-webkit-transform:translateZ(0);transform:translateZ(0)}10%,30%,50%,70%,90%{-webkit-transform:translate3d(0,-10px,0);transform:translate3d(0,-10px,0)}20%,40%,60%,80%{-webkit-transform:translate3d(0,10px,0);transform:translate3d(0,10px,0)}}
.animate__shakeY{-webkit-animation-name:shakeY;animation-name:shakeY}
@-webkit-keyframes headShake{0%{-webkit-transform:translateX(0);transform:translateX(0)}6.5%{-webkit-transform:translateX(-6px) rotateY(-9deg);transform:translateX(-6px) rotateY(-9deg)}18.5%{-webkit-transform:translateX(5px) rotateY(7deg);transform:translateX(5px) rotateY(7deg)}31.5%{-webkit-transform:translateX(-3px) rotateY(-5deg);transform:translateX(-3px) rotateY(-5deg)}43.5%{-webkit-transform:translateX(2px) rotateY(3deg);transform:translateX(2px) rotateY(3deg)}50%{-webkit-transform:translateX(0);transform:translateX(0)}}
@keyframes headShake{0%{-webkit-transform:translateX(0);transform:translateX(0)}6.5%{-webkit-transform:translateX(-6px) rotateY(-9deg);transform:translateX(-6px) rotateY(-9deg)}18.5%{-webkit-transform:translateX(5px) rotateY(7deg);transform:translateX(5px) rotateY(7deg)}31.5%{-webkit-transform:translateX(-3px) rotateY(-5deg);transform:translateX(-3px) rotateY(-5deg)}43.5%{-webkit-transform:translateX(2px) rotateY(3deg);transform:translateX(2px) rotateY(3deg)}50%{-webkit-transform:translateX(0);transform:translateX(0)}}
.animate__headShake{-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;-webkit-animation-name:headShake;animation-name:headShake}
@-webkit-keyframes swing{20%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}40%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}60%{-webkit-transform:rotate(5deg);transform:rotate(5deg)}80%{-webkit-transform:rotate(-5deg);transform:rotate(-5deg)}to{-webkit-transform:rotate(0deg);transform:rotate(0deg)}}
@keyframes swing{20%{-webkit-transform:rotate(15deg);transform:rotate(15deg)}40%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}60%{-webkit-transform:rotate(5deg);transform:rotate(5deg)}80%{-webkit-transform:rotate(-5deg);transform:rotate(-5deg)}to{-webkit-transform:rotate(0deg);transform:rotate(0deg)}}
.animate__swing{-webkit-transform-origin:top center;transform-origin:top center;-webkit-animation-name:swing;animation-name:swing}
@-webkit-keyframes tada{0%{-webkit-transform:scaleX(1);transform:scaleX(1)}10%,20%{-webkit-transform:scale3d(.9,.9,.9) rotate(-3deg);transform:scale3d(.9,.9,.9) rotate(-3deg)}30%,50%,70%,90%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate(3deg);transform:scale3d(1.1,1.1,1.1) rotate(3deg)}40%,60%,80%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate(-3deg);transform:scale3d(1.1,1.1,1.1) rotate(-3deg)}to{-webkit-transform:scaleX(1);transform:scaleX(1)}}
@keyframes tada{0%{-webkit-transform:scaleX(1);transform:scaleX(1)}10%,20%{-webkit-transform:scale3d(.9,.9,.9) rotate(-3deg);transform:scale3d(.9,.9,.9) rotate(-3deg)}30%,50%,70%,90%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate(3deg);transform:scale3d(1.1,1.1,1.1) rotate(3deg)}40%,60%,80%{-webkit-transform:scale3d(1.1,1.1,1.1) rotate(-3deg);transform:scale3d(1.1,1.1,1.1) rotate(-3deg)}to{-webkit-transform:scaleX(1);transform:scaleX(1)}}
.animate__tada{-webkit-animation-name:tada;animation-name:tada}
@-webkit-keyframes wobble{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-25%,0,0) rotate(-5deg);transform:translate3d(-25%,0,0) rotate(-5deg)}30%{-webkit-transform:translate3d(20%,0,0) rotate(3deg);transform:translate3d(20%,0,0) rotate(3deg)}45%{-webkit-transform:translate3d(-15%,0,0) rotate(-3deg);transform:translate3d(-15%,0,0) rotate(-3deg)}60%{-webkit-transform:translate3d(10%,0,0) rotate(2deg);transform:translate3d(10%,0,0) rotate(2deg)}75%{-webkit-transform:translate3d(-5%,0,0) rotate(-1deg);transform:translate3d(-5%,0,0) rotate(-1deg)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes wobble{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-25%,0,0) rotate(-5deg);transform:translate3d(-25%,0,0) rotate(-5deg)}30%{-webkit-transform:translate3d(20%,0,0) rotate(3deg);transform:translate3d(20%,0,0) rotate(3deg)}45%{-webkit-transform:translate3d(-15%,0,0) rotate(-3deg);transform:translate3d(-15%,0,0) rotate(-3deg)}60%{-webkit-transform:translate3d(10%,0,0) rotate(2deg);transform:translate3d(10%,0,0) rotate(2deg)}75%{-webkit-transform:translate3d(-5%,0,0) rotate(-1deg);transform:translate3d(-5%,0,0) rotate(-1deg)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__wobble{-webkit-animation-name:wobble;animation-name:wobble}
@-webkit-keyframes jello{0%,11.1%,to{-webkit-transform:translateZ(0);transform:translateZ(0)}22.2%{-webkit-transform:skewX(-12.5deg) skewY(-12.5deg);transform:skewX(-12.5deg) skewY(-12.5deg)}33.3%{-webkit-transform:skewX(6.25deg) skewY(6.25deg);transform:skewX(6.25deg) skewY(6.25deg)}44.4%{-webkit-transform:skewX(-3.125deg) skewY(-3.125deg);transform:skewX(-3.125deg) skewY(-3.125deg)}55.5%{-webkit-transform:skewX(1.5625deg) skewY(1.5625deg);transform:skewX(1.5625deg) skewY(1.5625deg)}66.6%{-webkit-transform:skewX(-.78125deg) skewY(-.78125deg);transform:skewX(-.78125deg) skewY(-.78125deg)}77.7%{-webkit-transform:skewX(.390625deg) skewY(.390625deg);transform:skewX(.390625deg) skewY(.390625deg)}88.8%{-webkit-transform:skewX(-.1953125deg) skewY(-.1953125deg);transform:skewX(-.1953125deg) skewY(-.1953125deg)}}
@keyframes jello{0%,11.1%,to{-webkit-transform:translateZ(0);transform:translateZ(0)}22.2%{-webkit-transform:skewX(-12.5deg) skewY(-12.5deg);transform:skewX(-12.5deg) skewY(-12.5deg)}33.3%{-webkit-transform:skewX(6.25deg) skewY(6.25deg);transform:skewX(6.25deg) skewY(6.25deg)}44.4%{-webkit-transform:skewX(-3.125deg) skewY(-3.125deg);transform:skewX(-3.125deg) skewY(-3.125deg)}55.5%{-webkit-transform:skewX(1.5625deg) skewY(1.5625deg);transform:skewX(1.5625deg) skewY(1.5625deg)}66.6%{-webkit-transform:skewX(-.78125deg) skewY(-.78125deg);transform:skewX(-.78125deg) skewY(-.78125deg)}77.7%{-webkit-transform:skewX(.390625deg) skewY(.390625deg);transform:skewX(.390625deg) skewY(.390625deg)}88.8%{-webkit-transform:skewX(-.1953125deg) skewY(-.1953125deg);transform:skewX(-.1953125deg) skewY(-.1953125deg)}}
.animate__jello{-webkit-animation-name:jello;animation-name:jello;-webkit-transform-origin:center;transform-origin:center}
@-webkit-keyframes heartBeat{0%{-webkit-transform:scale(1);transform:scale(1)}14%{-webkit-transform:scale(1.3);transform:scale(1.3)}28%{-webkit-transform:scale(1);transform:scale(1)}42%{-webkit-transform:scale(1.3);transform:scale(1.3)}70%{-webkit-transform:scale(1);transform:scale(1)}}
@keyframes heartBeat{0%{-webkit-transform:scale(1);transform:scale(1)}14%{-webkit-transform:scale(1.3);transform:scale(1.3)}28%{-webkit-transform:scale(1);transform:scale(1)}42%{-webkit-transform:scale(1.3);transform:scale(1.3)}70%{-webkit-transform:scale(1);transform:scale(1)}}
.animate__heartBeat{-webkit-animation-name:heartBeat;animation-name:heartBeat;-webkit-animation-duration:1.3s;animation-duration:1.3s;-webkit-animation-duration:calc(var(--animate-duration)*1.3);animation-duration:calc(var(--animate-duration)*1.3);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}
@-webkit-keyframes backInDown{0%{-webkit-transform:translateY(-1200px) scale(.7);transform:translateY(-1200px) scale(.7);opacity:.7}80%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
@keyframes backInDown{0%{-webkit-transform:translateY(-1200px) scale(.7);transform:translateY(-1200px) scale(.7);opacity:.7}80%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
.animate__backInDown{-webkit-animation-name:backInDown;animation-name:backInDown}
@-webkit-keyframes backInLeft{0%{-webkit-transform:translateX(-2000px) scale(.7);transform:translateX(-2000px) scale(.7);opacity:.7}80%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
@keyframes backInLeft{0%{-webkit-transform:translateX(-2000px) scale(.7);transform:translateX(-2000px) scale(.7);opacity:.7}80%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
.animate__backInLeft{-webkit-animation-name:backInLeft;animation-name:backInLeft}
@-webkit-keyframes backInRight{0%{-webkit-transform:translateX(2000px) scale(.7);transform:translateX(2000px) scale(.7);opacity:.7}80%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
@keyframes backInRight{0%{-webkit-transform:translateX(2000px) scale(.7);transform:translateX(2000px) scale(.7);opacity:.7}80%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
.animate__backInRight{-webkit-animation-name:backInRight;animation-name:backInRight}
@-webkit-keyframes backInUp{0%{-webkit-transform:translateY(1200px) scale(.7);transform:translateY(1200px) scale(.7);opacity:.7}80%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
@keyframes backInUp{0%{-webkit-transform:translateY(1200px) scale(.7);transform:translateY(1200px) scale(.7);opacity:.7}80%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}
.animate__backInUp{-webkit-animation-name:backInUp;animation-name:backInUp}
@-webkit-keyframes backOutDown{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:translateY(700px) scale(.7);transform:translateY(700px) scale(.7);opacity:.7}}
@keyframes backOutDown{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:translateY(700px) scale(.7);transform:translateY(700px) scale(.7);opacity:.7}}
.animate__backOutDown{-webkit-animation-name:backOutDown;animation-name:backOutDown}
@-webkit-keyframes backOutLeft{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:translateX(-2000px) scale(.7);transform:translateX(-2000px) scale(.7);opacity:.7}}
@keyframes backOutLeft{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:translateX(-2000px) scale(.7);transform:translateX(-2000px) scale(.7);opacity:.7}}
.animate__backOutLeft{-webkit-animation-name:backOutLeft;animation-name:backOutLeft}
@-webkit-keyframes backOutRight{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:translateX(2000px) scale(.7);transform:translateX(2000px) scale(.7);opacity:.7}}
@keyframes backOutRight{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateX(0) scale(.7);transform:translateX(0) scale(.7);opacity:.7}to{-webkit-transform:translateX(2000px) scale(.7);transform:translateX(2000px) scale(.7);opacity:.7}}
.animate__backOutRight{-webkit-animation-name:backOutRight;animation-name:backOutRight}
@-webkit-keyframes backOutUp{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:translateY(-700px) scale(.7);transform:translateY(-700px) scale(.7);opacity:.7}}
@keyframes backOutUp{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}20%{-webkit-transform:translateY(0) scale(.7);transform:translateY(0) scale(.7);opacity:.7}to{-webkit-transform:translateY(-700px) scale(.7);transform:translateY(-700px) scale(.7);opacity:.7}}
.animate__backOutUp{-webkit-animation-name:backOutUp;animation-name:backOutUp}
@-webkit-keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}20%{-webkit-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}40%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}60%{opacity:1;-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}80%{-webkit-transform:scale3d(.97,.97,.97);transform:scale3d(.97,.97,.97)}to{opacity:1;-webkit-transform:scaleX(1);transform:scaleX(1)}}
@keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}20%{-webkit-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}40%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}60%{opacity:1;-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}80%{-webkit-transform:scale3d(.97,.97,.97);transform:scale3d(.97,.97,.97)}to{opacity:1;-webkit-transform:scaleX(1);transform:scaleX(1)}}
.animate__bounceIn{-webkit-animation-duration:.75s;animation-duration:.75s;-webkit-animation-duration:calc(var(--animate-duration)*0.75);animation-duration:calc(var(--animate-duration)*0.75);-webkit-animation-name:bounceIn;animation-name:bounceIn}
@-webkit-keyframes bounceInDown{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,-3000px,0) scaleY(3);transform:translate3d(0,-3000px,0) scaleY(3)}60%{opacity:1;-webkit-transform:translate3d(0,25px,0) scaleY(.9);transform:translate3d(0,25px,0) scaleY(.9)}75%{-webkit-transform:translate3d(0,-10px,0) scaleY(.95);transform:translate3d(0,-10px,0) scaleY(.95)}90%{-webkit-transform:translate3d(0,5px,0) scaleY(.985);transform:translate3d(0,5px,0) scaleY(.985)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes bounceInDown{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,-3000px,0) scaleY(3);transform:translate3d(0,-3000px,0) scaleY(3)}60%{opacity:1;-webkit-transform:translate3d(0,25px,0) scaleY(.9);transform:translate3d(0,25px,0) scaleY(.9)}75%{-webkit-transform:translate3d(0,-10px,0) scaleY(.95);transform:translate3d(0,-10px,0) scaleY(.95)}90%{-webkit-transform:translate3d(0,5px,0) scaleY(.985);transform:translate3d(0,5px,0) scaleY(.985)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__bounceInDown{-webkit-animation-name:bounceInDown;animation-name:bounceInDown}
@-webkit-keyframes bounceInLeft{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(-3000px,0,0) scaleX(3);transform:translate3d(-3000px,0,0) scaleX(3)}60%{opacity:1;-webkit-transform:translate3d(25px,0,0) scaleX(1);transform:translate3d(25px,0,0) scaleX(1)}75%{-webkit-transform:translate3d(-10px,0,0) scaleX(.98);transform:translate3d(-10px,0,0) scaleX(.98)}90%{-webkit-transform:translate3d(5px,0,0) scaleX(.995);transform:translate3d(5px,0,0) scaleX(.995)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes bounceInLeft{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(-3000px,0,0) scaleX(3);transform:translate3d(-3000px,0,0) scaleX(3)}60%{opacity:1;-webkit-transform:translate3d(25px,0,0) scaleX(1);transform:translate3d(25px,0,0) scaleX(1)}75%{-webkit-transform:translate3d(-10px,0,0) scaleX(.98);transform:translate3d(-10px,0,0) scaleX(.98)}90%{-webkit-transform:translate3d(5px,0,0) scaleX(.995);transform:translate3d(5px,0,0) scaleX(.995)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__bounceInLeft{-webkit-animation-name:bounceInLeft;animation-name:bounceInLeft}
@-webkit-keyframes bounceInRight{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(3000px,0,0) scaleX(3);transform:translate3d(3000px,0,0) scaleX(3)}60%{opacity:1;-webkit-transform:translate3d(-25px,0,0) scaleX(1);transform:translate3d(-25px,0,0) scaleX(1)}75%{-webkit-transform:translate3d(10px,0,0) scaleX(.98);transform:translate3d(10px,0,0) scaleX(.98)}90%{-webkit-transform:translate3d(-5px,0,0) scaleX(.995);transform:translate3d(-5px,0,0) scaleX(.995)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes bounceInRight{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(3000px,0,0) scaleX(3);transform:translate3d(3000px,0,0) scaleX(3)}60%{opacity:1;-webkit-transform:translate3d(-25px,0,0) scaleX(1);transform:translate3d(-25px,0,0) scaleX(1)}75%{-webkit-transform:translate3d(10px,0,0) scaleX(.98);transform:translate3d(10px,0,0) scaleX(.98)}90%{-webkit-transform:translate3d(-5px,0,0) scaleX(.995);transform:translate3d(-5px,0,0) scaleX(.995)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__bounceInRight{-webkit-animation-name:bounceInRight;animation-name:bounceInRight}
@-webkit-keyframes bounceInUp{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,3000px,0) scaleY(5);transform:translate3d(0,3000px,0) scaleY(5)}60%{opacity:1;-webkit-transform:translate3d(0,-20px,0) scaleY(.9);transform:translate3d(0,-20px,0) scaleY(.9)}75%{-webkit-transform:translate3d(0,10px,0) scaleY(.95);transform:translate3d(0,10px,0) scaleY(.95)}90%{-webkit-transform:translate3d(0,-5px,0) scaleY(.985);transform:translate3d(0,-5px,0) scaleY(.985)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes bounceInUp{0%,60%,75%,90%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;-webkit-transform:translate3d(0,3000px,0) scaleY(5);transform:translate3d(0,3000px,0) scaleY(5)}60%{opacity:1;-webkit-transform:translate3d(0,-20px,0) scaleY(.9);transform:translate3d(0,-20px,0) scaleY(.9)}75%{-webkit-transform:translate3d(0,10px,0) scaleY(.95);transform:translate3d(0,10px,0) scaleY(.95)}90%{-webkit-transform:translate3d(0,-5px,0) scaleY(.985);transform:translate3d(0,-5px,0) scaleY(.985)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__bounceInUp{-webkit-animation-name:bounceInUp;animation-name:bounceInUp}
@-webkit-keyframes bounceOut{20%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}50%,55%{opacity:1;-webkit-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}to{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}}
@keyframes bounceOut{20%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}50%,55%{opacity:1;-webkit-transform:scale3d(1.1,1.1,1.1);transform:scale3d(1.1,1.1,1.1)}to{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}}
.animate__bounceOut{-webkit-animation-duration:.75s;animation-duration:.75s;-webkit-animation-duration:calc(var(--animate-duration)*0.75);animation-duration:calc(var(--animate-duration)*0.75);-webkit-animation-name:bounceOut;animation-name:bounceOut}
@-webkit-keyframes bounceOutDown{20%{-webkit-transform:translate3d(0,10px,0) scaleY(.985);transform:translate3d(0,10px,0) scaleY(.985)}40%,45%{opacity:1;-webkit-transform:translate3d(0,-20px,0) scaleY(.9);transform:translate3d(0,-20px,0) scaleY(.9)}to{opacity:0;-webkit-transform:translate3d(0,2000px,0) scaleY(3);transform:translate3d(0,2000px,0) scaleY(3)}}
@keyframes bounceOutDown{20%{-webkit-transform:translate3d(0,10px,0) scaleY(.985);transform:translate3d(0,10px,0) scaleY(.985)}40%,45%{opacity:1;-webkit-transform:translate3d(0,-20px,0) scaleY(.9);transform:translate3d(0,-20px,0) scaleY(.9)}to{opacity:0;-webkit-transform:translate3d(0,2000px,0) scaleY(3);transform:translate3d(0,2000px,0) scaleY(3)}}
.animate__bounceOutDown{-webkit-animation-name:bounceOutDown;animation-name:bounceOutDown}
@-webkit-keyframes bounceOutLeft{20%{opacity:1;-webkit-transform:translate3d(20px,0,0) scaleX(.9);transform:translate3d(20px,0,0) scaleX(.9)}to{opacity:0;-webkit-transform:translate3d(-2000px,0,0) scaleX(2);transform:translate3d(-2000px,0,0) scaleX(2)}}
@keyframes bounceOutLeft{20%{opacity:1;-webkit-transform:translate3d(20px,0,0) scaleX(.9);transform:translate3d(20px,0,0) scaleX(.9)}to{opacity:0;-webkit-transform:translate3d(-2000px,0,0) scaleX(2);transform:translate3d(-2000px,0,0) scaleX(2)}}
.animate__bounceOutLeft{-webkit-animation-name:bounceOutLeft;animation-name:bounceOutLeft}
@-webkit-keyframes bounceOutRight{20%{opacity:1;-webkit-transform:translate3d(-20px,0,0) scaleX(.9);transform:translate3d(-20px,0,0) scaleX(.9)}to{opacity:0;-webkit-transform:translate3d(2000px,0,0) scaleX(2);transform:translate3d(2000px,0,0) scaleX(2)}}
@keyframes bounceOutRight{20%{opacity:1;-webkit-transform:translate3d(-20px,0,0) scaleX(.9);transform:translate3d(-20px,0,0) scaleX(.9)}to{opacity:0;-webkit-transform:translate3d(2000px,0,0) scaleX(2);transform:translate3d(2000px,0,0) scaleX(2)}}
.animate__bounceOutRight{-webkit-animation-name:bounceOutRight;animation-name:bounceOutRight}
@-webkit-keyframes bounceOutUp{20%{-webkit-transform:translate3d(0,-10px,0) scaleY(.985);transform:translate3d(0,-10px,0) scaleY(.985)}40%,45%{opacity:1;-webkit-transform:translate3d(0,20px,0) scaleY(.9);transform:translate3d(0,20px,0) scaleY(.9)}to{opacity:0;-webkit-transform:translate3d(0,-2000px,0) scaleY(3);transform:translate3d(0,-2000px,0) scaleY(3)}}
@keyframes bounceOutUp{20%{-webkit-transform:translate3d(0,-10px,0) scaleY(.985);transform:translate3d(0,-10px,0) scaleY(.985)}40%,45%{opacity:1;-webkit-transform:translate3d(0,20px,0) scaleY(.9);transform:translate3d(0,20px,0) scaleY(.9)}to{opacity:0;-webkit-transform:translate3d(0,-2000px,0) scaleY(3);transform:translate3d(0,-2000px,0) scaleY(3)}}
.animate__bounceOutUp{-webkit-animation-name:bounceOutUp;animation-name:bounceOutUp}
@-webkit-keyframes fadeIn{0%{opacity:0}to{opacity:1}}
@keyframes fadeIn{0%{opacity:0}to{opacity:1}}
.animate__fadeIn{-webkit-animation-name:fadeIn;animation-name:fadeIn}
@-webkit-keyframes fadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInDown{-webkit-animation-name:fadeInDown;animation-name:fadeInDown}
@-webkit-keyframes fadeInDownBig{0%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInDownBig{0%{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInDownBig{-webkit-animation-name:fadeInDownBig;animation-name:fadeInDownBig}
@-webkit-keyframes fadeInLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInLeft{-webkit-animation-name:fadeInLeft;animation-name:fadeInLeft}
@-webkit-keyframes fadeInLeftBig{0%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInLeftBig{0%{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInLeftBig{-webkit-animation-name:fadeInLeftBig;animation-name:fadeInLeftBig}
@-webkit-keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInRight{0%{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInRight{-webkit-animation-name:fadeInRight;animation-name:fadeInRight}
@-webkit-keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInRightBig{0%{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInRightBig{-webkit-animation-name:fadeInRightBig;animation-name:fadeInRightBig}
@-webkit-keyframes fadeInUp{0%{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInUp{0%{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInUp{-webkit-animation-name:fadeInUp;animation-name:fadeInUp}
@-webkit-keyframes fadeInUpBig{0%{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInUpBig{0%{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInUpBig{-webkit-animation-name:fadeInUpBig;animation-name:fadeInUpBig}
@-webkit-keyframes fadeInTopLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,-100%,0);transform:translate3d(-100%,-100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInTopLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,-100%,0);transform:translate3d(-100%,-100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInTopLeft{-webkit-animation-name:fadeInTopLeft;animation-name:fadeInTopLeft}
@-webkit-keyframes fadeInTopRight{0%{opacity:0;-webkit-transform:translate3d(100%,-100%,0);transform:translate3d(100%,-100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInTopRight{0%{opacity:0;-webkit-transform:translate3d(100%,-100%,0);transform:translate3d(100%,-100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInTopRight{-webkit-animation-name:fadeInTopRight;animation-name:fadeInTopRight}
@-webkit-keyframes fadeInBottomLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,100%,0);transform:translate3d(-100%,100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInBottomLeft{0%{opacity:0;-webkit-transform:translate3d(-100%,100%,0);transform:translate3d(-100%,100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInBottomLeft{-webkit-animation-name:fadeInBottomLeft;animation-name:fadeInBottomLeft}
@-webkit-keyframes fadeInBottomRight{0%{opacity:0;-webkit-transform:translate3d(100%,100%,0);transform:translate3d(100%,100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes fadeInBottomRight{0%{opacity:0;-webkit-transform:translate3d(100%,100%,0);transform:translate3d(100%,100%,0)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__fadeInBottomRight{-webkit-animation-name:fadeInBottomRight;animation-name:fadeInBottomRight}
@-webkit-keyframes fadeOut{0%{opacity:1}to{opacity:0}}
@keyframes fadeOut{0%{opacity:1}to{opacity:0}}
.animate__fadeOut{-webkit-animation-name:fadeOut;animation-name:fadeOut}
@-webkit-keyframes fadeOutDown{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}
@keyframes fadeOutDown{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}
.animate__fadeOutDown{-webkit-animation-name:fadeOutDown;animation-name:fadeOutDown}
@-webkit-keyframes fadeOutDownBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}}
@keyframes fadeOutDownBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,2000px,0);transform:translate3d(0,2000px,0)}}
.animate__fadeOutDownBig{-webkit-animation-name:fadeOutDownBig;animation-name:fadeOutDownBig}
@-webkit-keyframes fadeOutLeft{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}}
@keyframes fadeOutLeft{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}}
.animate__fadeOutLeft{-webkit-animation-name:fadeOutLeft;animation-name:fadeOutLeft}
@-webkit-keyframes fadeOutLeftBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}}
@keyframes fadeOutLeftBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(-2000px,0,0);transform:translate3d(-2000px,0,0)}}
.animate__fadeOutLeftBig{-webkit-animation-name:fadeOutLeftBig;animation-name:fadeOutLeftBig}
@-webkit-keyframes fadeOutRight{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}
@keyframes fadeOutRight{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}
.animate__fadeOutRight{-webkit-animation-name:fadeOutRight;animation-name:fadeOutRight}
@-webkit-keyframes fadeOutRightBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}}
@keyframes fadeOutRightBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(2000px,0,0);transform:translate3d(2000px,0,0)}}
.animate__fadeOutRightBig{-webkit-animation-name:fadeOutRightBig;animation-name:fadeOutRightBig}
@-webkit-keyframes fadeOutUp{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}
@keyframes fadeOutUp{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}
.animate__fadeOutUp{-webkit-animation-name:fadeOutUp;animation-name:fadeOutUp}
@-webkit-keyframes fadeOutUpBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}}
@keyframes fadeOutUpBig{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-2000px,0);transform:translate3d(0,-2000px,0)}}
.animate__fadeOutUpBig{-webkit-animation-name:fadeOutUpBig;animation-name:fadeOutUpBig}
@-webkit-keyframes fadeOutTopLeft{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(-100%,-100%,0);transform:translate3d(-100%,-100%,0)}}
@keyframes fadeOutTopLeft{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(-100%,-100%,0);transform:translate3d(-100%,-100%,0)}}
.animate__fadeOutTopLeft{-webkit-animation-name:fadeOutTopLeft;animation-name:fadeOutTopLeft}
@-webkit-keyframes fadeOutTopRight{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(100%,-100%,0);transform:translate3d(100%,-100%,0)}}
@keyframes fadeOutTopRight{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(100%,-100%,0);transform:translate3d(100%,-100%,0)}}
.animate__fadeOutTopRight{-webkit-animation-name:fadeOutTopRight;animation-name:fadeOutTopRight}
@-webkit-keyframes fadeOutBottomRight{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(100%,100%,0);transform:translate3d(100%,100%,0)}}
@keyframes fadeOutBottomRight{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(100%,100%,0);transform:translate3d(100%,100%,0)}}
.animate__fadeOutBottomRight{-webkit-animation-name:fadeOutBottomRight;animation-name:fadeOutBottomRight}
@-webkit-keyframes fadeOutBottomLeft{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(-100%,100%,0);transform:translate3d(-100%,100%,0)}}
@keyframes fadeOutBottomLeft{0%{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}to{opacity:0;-webkit-transform:translate3d(-100%,100%,0);transform:translate3d(-100%,100%,0)}}
.animate__fadeOutBottomLeft{-webkit-animation-name:fadeOutBottomLeft;animation-name:fadeOutBottomLeft}
@-webkit-keyframes flip{0%{-webkit-transform:perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn);transform:perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}40%{-webkit-transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg);transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}50%{-webkit-transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg);transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}80%{-webkit-transform:perspective(400px) scale3d(.95,.95,.95) translateZ(0) rotateY(0deg);transform:perspective(400px) scale3d(.95,.95,.95) translateZ(0) rotateY(0deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}to{-webkit-transform:perspective(400px) scaleX(1) translateZ(0) rotateY(0deg);transform:perspective(400px) scaleX(1) translateZ(0) rotateY(0deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}}
@keyframes flip{0%{-webkit-transform:perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn);transform:perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}40%{-webkit-transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg);transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}50%{-webkit-transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg);transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}80%{-webkit-transform:perspective(400px) scale3d(.95,.95,.95) translateZ(0) rotateY(0deg);transform:perspective(400px) scale3d(.95,.95,.95) translateZ(0) rotateY(0deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}to{-webkit-transform:perspective(400px) scaleX(1) translateZ(0) rotateY(0deg);transform:perspective(400px) scaleX(1) translateZ(0) rotateY(0deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}}
.animate__animated.animate__flip{-webkit-backface-visibility:visible;backface-visibility:visible;-webkit-animation-name:flip;animation-name:flip}
@-webkit-keyframes flipInX{0%{-webkit-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotateX(-20deg);transform:perspective(400px) rotateX(-20deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotateX(10deg);transform:perspective(400px) rotateX(10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotateX(-5deg);transform:perspective(400px) rotateX(-5deg)}to{-webkit-transform:perspective(400px);transform:perspective(400px)}}
@keyframes flipInX{0%{-webkit-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotateX(-20deg);transform:perspective(400px) rotateX(-20deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotateX(10deg);transform:perspective(400px) rotateX(10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotateX(-5deg);transform:perspective(400px) rotateX(-5deg)}to{-webkit-transform:perspective(400px);transform:perspective(400px)}}
.animate__flipInX{-webkit-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipInX;animation-name:flipInX}
@-webkit-keyframes flipInY{0%{-webkit-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotateY(-20deg);transform:perspective(400px) rotateY(-20deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotateY(10deg);transform:perspective(400px) rotateY(10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotateY(-5deg);transform:perspective(400px) rotateY(-5deg)}to{-webkit-transform:perspective(400px);transform:perspective(400px)}}
@keyframes flipInY{0%{-webkit-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotateY(-20deg);transform:perspective(400px) rotateY(-20deg);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotateY(10deg);transform:perspective(400px) rotateY(10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotateY(-5deg);transform:perspective(400px) rotateY(-5deg)}to{-webkit-transform:perspective(400px);transform:perspective(400px)}}
.animate__flipInY{-webkit-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipInY;animation-name:flipInY}
@-webkit-keyframes flipOutX{0%{-webkit-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotateX(-20deg);transform:perspective(400px) rotateX(-20deg);opacity:1}to{-webkit-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);opacity:0}}
@keyframes flipOutX{0%{-webkit-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotateX(-20deg);transform:perspective(400px) rotateX(-20deg);opacity:1}to{-webkit-transform:perspective(400px) rotateX(90deg);transform:perspective(400px) rotateX(90deg);opacity:0}}
.animate__flipOutX{-webkit-animation-duration:.75s;animation-duration:.75s;-webkit-animation-duration:calc(var(--animate-duration)*0.75);animation-duration:calc(var(--animate-duration)*0.75);-webkit-animation-name:flipOutX;animation-name:flipOutX;-webkit-backface-visibility:visible!important;backface-visibility:visible!important}
@-webkit-keyframes flipOutY{0%{-webkit-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotateY(-15deg);transform:perspective(400px) rotateY(-15deg);opacity:1}to{-webkit-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);opacity:0}}
@keyframes flipOutY{0%{-webkit-transform:perspective(400px);transform:perspective(400px)}30%{-webkit-transform:perspective(400px) rotateY(-15deg);transform:perspective(400px) rotateY(-15deg);opacity:1}to{-webkit-transform:perspective(400px) rotateY(90deg);transform:perspective(400px) rotateY(90deg);opacity:0}}
.animate__flipOutY{-webkit-animation-duration:.75s;animation-duration:.75s;-webkit-animation-duration:calc(var(--animate-duration)*0.75);animation-duration:calc(var(--animate-duration)*0.75);-webkit-backface-visibility:visible!important;backface-visibility:visible!important;-webkit-animation-name:flipOutY;animation-name:flipOutY}
@-webkit-keyframes lightSpeedInRight{0%{-webkit-transform:translate3d(100%,0,0) skewX(-30deg);transform:translate3d(100%,0,0) skewX(-30deg);opacity:0}60%{-webkit-transform:skewX(20deg);transform:skewX(20deg);opacity:1}80%{-webkit-transform:skewX(-5deg);transform:skewX(-5deg)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes lightSpeedInRight{0%{-webkit-transform:translate3d(100%,0,0) skewX(-30deg);transform:translate3d(100%,0,0) skewX(-30deg);opacity:0}60%{-webkit-transform:skewX(20deg);transform:skewX(20deg);opacity:1}80%{-webkit-transform:skewX(-5deg);transform:skewX(-5deg)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__lightSpeedInRight{-webkit-animation-name:lightSpeedInRight;animation-name:lightSpeedInRight;-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}
@-webkit-keyframes lightSpeedInLeft{0%{-webkit-transform:translate3d(-100%,0,0) skewX(30deg);transform:translate3d(-100%,0,0) skewX(30deg);opacity:0}60%{-webkit-transform:skewX(-20deg);transform:skewX(-20deg);opacity:1}80%{-webkit-transform:skewX(5deg);transform:skewX(5deg)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes lightSpeedInLeft{0%{-webkit-transform:translate3d(-100%,0,0) skewX(30deg);transform:translate3d(-100%,0,0) skewX(30deg);opacity:0}60%{-webkit-transform:skewX(-20deg);transform:skewX(-20deg);opacity:1}80%{-webkit-transform:skewX(5deg);transform:skewX(5deg)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__lightSpeedInLeft{-webkit-animation-name:lightSpeedInLeft;animation-name:lightSpeedInLeft;-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}
@-webkit-keyframes lightSpeedOutRight{0%{opacity:1}to{-webkit-transform:translate3d(100%,0,0) skewX(30deg);transform:translate3d(100%,0,0) skewX(30deg);opacity:0}}
@keyframes lightSpeedOutRight{0%{opacity:1}to{-webkit-transform:translate3d(100%,0,0) skewX(30deg);transform:translate3d(100%,0,0) skewX(30deg);opacity:0}}
.animate__lightSpeedOutRight{-webkit-animation-name:lightSpeedOutRight;animation-name:lightSpeedOutRight;-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}
@-webkit-keyframes lightSpeedOutLeft{0%{opacity:1}to{-webkit-transform:translate3d(-100%,0,0) skewX(-30deg);transform:translate3d(-100%,0,0) skewX(-30deg);opacity:0}}
@keyframes lightSpeedOutLeft{0%{opacity:1}to{-webkit-transform:translate3d(-100%,0,0) skewX(-30deg);transform:translate3d(-100%,0,0) skewX(-30deg);opacity:0}}
.animate__lightSpeedOutLeft{-webkit-animation-name:lightSpeedOutLeft;animation-name:lightSpeedOutLeft;-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}
@-webkit-keyframes rotateIn{0%{-webkit-transform:rotate(-200deg);transform:rotate(-200deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
@keyframes rotateIn{0%{-webkit-transform:rotate(-200deg);transform:rotate(-200deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
.animate__rotateIn{-webkit-animation-name:rotateIn;animation-name:rotateIn;-webkit-transform-origin:center;transform-origin:center}
@-webkit-keyframes rotateInDownLeft{0%{-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
@keyframes rotateInDownLeft{0%{-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
.animate__rotateInDownLeft{-webkit-animation-name:rotateInDownLeft;animation-name:rotateInDownLeft;-webkit-transform-origin:left bottom;transform-origin:left bottom}
@-webkit-keyframes rotateInDownRight{0%{-webkit-transform:rotate(45deg);transform:rotate(45deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
@keyframes rotateInDownRight{0%{-webkit-transform:rotate(45deg);transform:rotate(45deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
.animate__rotateInDownRight{-webkit-animation-name:rotateInDownRight;animation-name:rotateInDownRight;-webkit-transform-origin:right bottom;transform-origin:right bottom}
@-webkit-keyframes rotateInUpLeft{0%{-webkit-transform:rotate(45deg);transform:rotate(45deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
@keyframes rotateInUpLeft{0%{-webkit-transform:rotate(45deg);transform:rotate(45deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
.animate__rotateInUpLeft{-webkit-animation-name:rotateInUpLeft;animation-name:rotateInUpLeft;-webkit-transform-origin:left bottom;transform-origin:left bottom}
@-webkit-keyframes rotateInUpRight{0%{-webkit-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
@keyframes rotateInUpRight{0%{-webkit-transform:rotate(-90deg);transform:rotate(-90deg);opacity:0}to{-webkit-transform:translateZ(0);transform:translateZ(0);opacity:1}}
.animate__rotateInUpRight{-webkit-animation-name:rotateInUpRight;animation-name:rotateInUpRight;-webkit-transform-origin:right bottom;transform-origin:right bottom}
@-webkit-keyframes rotateOut{0%{opacity:1}to{-webkit-transform:rotate(200deg);transform:rotate(200deg);opacity:0}}
@keyframes rotateOut{0%{opacity:1}to{-webkit-transform:rotate(200deg);transform:rotate(200deg);opacity:0}}
.animate__rotateOut{-webkit-animation-name:rotateOut;animation-name:rotateOut;-webkit-transform-origin:center;transform-origin:center}
@-webkit-keyframes rotateOutDownLeft{0%{opacity:1}to{-webkit-transform:rotate(45deg);transform:rotate(45deg);opacity:0}}
@keyframes rotateOutDownLeft{0%{opacity:1}to{-webkit-transform:rotate(45deg);transform:rotate(45deg);opacity:0}}
.animate__rotateOutDownLeft{-webkit-animation-name:rotateOutDownLeft;animation-name:rotateOutDownLeft;-webkit-transform-origin:left bottom;transform-origin:left bottom}
@-webkit-keyframes rotateOutDownRight{0%{opacity:1}to{-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:0}}
@keyframes rotateOutDownRight{0%{opacity:1}to{-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:0}}
.animate__rotateOutDownRight{-webkit-animation-name:rotateOutDownRight;animation-name:rotateOutDownRight;-webkit-transform-origin:right bottom;transform-origin:right bottom}
@-webkit-keyframes rotateOutUpLeft{0%{opacity:1}to{-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:0}}
@keyframes rotateOutUpLeft{0%{opacity:1}to{-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:0}}
.animate__rotateOutUpLeft{-webkit-animation-name:rotateOutUpLeft;animation-name:rotateOutUpLeft;-webkit-transform-origin:left bottom;transform-origin:left bottom}
@-webkit-keyframes rotateOutUpRight{0%{opacity:1}to{-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:0}}
@keyframes rotateOutUpRight{0%{opacity:1}to{-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:0}}
.animate__rotateOutUpRight{-webkit-animation-name:rotateOutUpRight;animation-name:rotateOutUpRight;-webkit-transform-origin:right bottom;transform-origin:right bottom}
@-webkit-keyframes hinge{0%{-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}20%,60%{-webkit-transform:rotate(80deg);transform:rotate(80deg);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}40%,80%{-webkit-transform:rotate(60deg);transform:rotate(60deg);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;opacity:1}to{-webkit-transform:translate3d(0,700px,0);transform:translate3d(0,700px,0);opacity:0}}
@keyframes hinge{0%{-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}20%,60%{-webkit-transform:rotate(80deg);transform:rotate(80deg);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}40%,80%{-webkit-transform:rotate(60deg);transform:rotate(60deg);-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;opacity:1}to{-webkit-transform:translate3d(0,700px,0);transform:translate3d(0,700px,0);opacity:0}}
.animate__hinge{-webkit-animation-duration:2s;animation-duration:2s;-webkit-animation-duration:calc(var(--animate-duration)*2);animation-duration:calc(var(--animate-duration)*2);-webkit-animation-name:hinge;animation-name:hinge;-webkit-transform-origin:top left;transform-origin:top left}
@-webkit-keyframes jackInTheBox{0%{opacity:0;-webkit-transform:scale(.1) rotate(30deg);transform:scale(.1) rotate(30deg);-webkit-transform-origin:center bottom;transform-origin:center bottom}50%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}70%{-webkit-transform:rotate(3deg);transform:rotate(3deg)}to{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}
@keyframes jackInTheBox{0%{opacity:0;-webkit-transform:scale(.1) rotate(30deg);transform:scale(.1) rotate(30deg);-webkit-transform-origin:center bottom;transform-origin:center bottom}50%{-webkit-transform:rotate(-10deg);transform:rotate(-10deg)}70%{-webkit-transform:rotate(3deg);transform:rotate(3deg)}to{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}
.animate__jackInTheBox{-webkit-animation-name:jackInTheBox;animation-name:jackInTheBox}
@-webkit-keyframes rollIn{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0) rotate(-120deg);transform:translate3d(-100%,0,0) rotate(-120deg)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes rollIn{0%{opacity:0;-webkit-transform:translate3d(-100%,0,0) rotate(-120deg);transform:translate3d(-100%,0,0) rotate(-120deg)}to{opacity:1;-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__rollIn{-webkit-animation-name:rollIn;animation-name:rollIn}
@-webkit-keyframes rollOut{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(100%,0,0) rotate(120deg);transform:translate3d(100%,0,0) rotate(120deg)}}
@keyframes rollOut{0%{opacity:1}to{opacity:0;-webkit-transform:translate3d(100%,0,0) rotate(120deg);transform:translate3d(100%,0,0) rotate(120deg)}}
.animate__rollOut{-webkit-animation-name:rollOut;animation-name:rollOut}
@-webkit-keyframes zoomIn{0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}50%{opacity:1}}
@keyframes zoomIn{0%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}50%{opacity:1}}
.animate__zoomIn{-webkit-animation-name:zoomIn;animation-name:zoomIn}
@-webkit-keyframes zoomInDown{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
@keyframes zoomInDown{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-1000px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
.animate__zoomInDown{-webkit-animation-name:zoomInDown;animation-name:zoomInDown}
@-webkit-keyframes zoomInLeft{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(10px,0,0);transform:scale3d(.475,.475,.475) translate3d(10px,0,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
@keyframes zoomInLeft{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(-1000px,0,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(10px,0,0);transform:scale3d(.475,.475,.475) translate3d(10px,0,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
.animate__zoomInLeft{-webkit-animation-name:zoomInLeft;animation-name:zoomInLeft}
@-webkit-keyframes zoomInRight{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
@keyframes zoomInRight{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);transform:scale3d(.1,.1,.1) translate3d(1000px,0,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);transform:scale3d(.475,.475,.475) translate3d(-10px,0,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
.animate__zoomInRight{-webkit-animation-name:zoomInRight;animation-name:zoomInRight}
@-webkit-keyframes zoomInUp{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
@keyframes zoomInUp{0%{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);transform:scale3d(.1,.1,.1) translate3d(0,1000px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}60%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
.animate__zoomInUp{-webkit-animation-name:zoomInUp;animation-name:zoomInUp}
@-webkit-keyframes zoomOut{0%{opacity:1}50%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}to{opacity:0}}
@keyframes zoomOut{0%{opacity:1}50%{opacity:0;-webkit-transform:scale3d(.3,.3,.3);transform:scale3d(.3,.3,.3)}to{opacity:0}}
.animate__zoomOut{-webkit-animation-name:zoomOut;animation-name:zoomOut}
@-webkit-keyframes zoomOutDown{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}to{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
@keyframes zoomOutDown{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);transform:scale3d(.475,.475,.475) translate3d(0,-60px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}to{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,2000px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
.animate__zoomOutDown{-webkit-animation-name:zoomOutDown;animation-name:zoomOutDown;-webkit-transform-origin:center bottom;transform-origin:center bottom}
@-webkit-keyframes zoomOutLeft{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(42px,0,0);transform:scale3d(.475,.475,.475) translate3d(42px,0,0)}to{opacity:0;-webkit-transform:scale(.1) translate3d(-2000px,0,0);transform:scale(.1) translate3d(-2000px,0,0)}}
@keyframes zoomOutLeft{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(42px,0,0);transform:scale3d(.475,.475,.475) translate3d(42px,0,0)}to{opacity:0;-webkit-transform:scale(.1) translate3d(-2000px,0,0);transform:scale(.1) translate3d(-2000px,0,0)}}
.animate__zoomOutLeft{-webkit-animation-name:zoomOutLeft;animation-name:zoomOutLeft;-webkit-transform-origin:left center;transform-origin:left center}
@-webkit-keyframes zoomOutRight{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-42px,0,0);transform:scale3d(.475,.475,.475) translate3d(-42px,0,0)}to{opacity:0;-webkit-transform:scale(.1) translate3d(2000px,0,0);transform:scale(.1) translate3d(2000px,0,0)}}
@keyframes zoomOutRight{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(-42px,0,0);transform:scale3d(.475,.475,.475) translate3d(-42px,0,0)}to{opacity:0;-webkit-transform:scale(.1) translate3d(2000px,0,0);transform:scale(.1) translate3d(2000px,0,0)}}
.animate__zoomOutRight{-webkit-animation-name:zoomOutRight;animation-name:zoomOutRight;-webkit-transform-origin:right center;transform-origin:right center}
@-webkit-keyframes zoomOutUp{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}to{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
@keyframes zoomOutUp{40%{opacity:1;-webkit-transform:scale3d(.475,.475,.475) translate3d(0,60px,0);transform:scale3d(.475,.475,.475) translate3d(0,60px,0);-webkit-animation-timing-function:cubic-bezier(.55,.055,.675,.19);animation-timing-function:cubic-bezier(.55,.055,.675,.19)}to{opacity:0;-webkit-transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);transform:scale3d(.1,.1,.1) translate3d(0,-2000px,0);-webkit-animation-timing-function:cubic-bezier(.175,.885,.32,1);animation-timing-function:cubic-bezier(.175,.885,.32,1)}}
.animate__zoomOutUp{-webkit-animation-name:zoomOutUp;animation-name:zoomOutUp;-webkit-transform-origin:center bottom;transform-origin:center bottom}
@-webkit-keyframes slideInDown{0%{-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes slideInDown{0%{-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__slideInDown{-webkit-animation-name:slideInDown;animation-name:slideInDown}
@-webkit-keyframes slideInLeft{0%{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes slideInLeft{0%{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__slideInLeft{-webkit-animation-name:slideInLeft;animation-name:slideInLeft}
@-webkit-keyframes slideInRight{0%{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes slideInRight{0%{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__slideInRight{-webkit-animation-name:slideInRight;animation-name:slideInRight}
@-webkit-keyframes slideInUp{0%{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
@keyframes slideInUp{0%{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);visibility:visible}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}
.animate__slideInUp{-webkit-animation-name:slideInUp;animation-name:slideInUp}
@-webkit-keyframes slideOutDown{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}
@keyframes slideOutDown{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}
.animate__slideOutDown{-webkit-animation-name:slideOutDown;animation-name:slideOutDown}
@-webkit-keyframes slideOutLeft{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}}
@keyframes slideOutLeft{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}}
.animate__slideOutLeft{-webkit-animation-name:slideOutLeft;animation-name:slideOutLeft}
@-webkit-keyframes slideOutRight{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}
@keyframes slideOutRight{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}}
.animate__slideOutRight{-webkit-animation-name:slideOutRight;animation-name:slideOutRight}
@-webkit-keyframes slideOutUp{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}
@keyframes slideOutUp{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{visibility:hidden;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}
.animate__slideOutUp{-webkit-animation-name:slideOutUp;animation-name:slideOutUp}
.u-relative,
.u-rela {
  position: relative;
}
.u-absolute,
.u-abso {
  position: absolute;
}
image {
  display: inline-block;
}
view,
text {
  box-sizing: border-box;
}
.u-font-xs {
  font-size: 22rpx;
}
.u-font-sm {
  font-size: 26rpx;
}
.u-font-md {
  font-size: 28rpx;
}
.u-font-lg {
  font-size: 30rpx;
}
.u-font-xl {
  font-size: 34rpx;
}
.u-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-flex-wrap {
  flex-wrap: wrap;
}
.u-flex-nowrap {
  flex-wrap: nowrap;
}
.u-col-center {
  align-items: center;
}
.u-col-top {
  align-items: flex-start;
}
.u-col-bottom {
  align-items: flex-end;
}
.u-row-center {
  justify-content: center;
}
.u-row-left {
  justify-content: flex-start;
}
.u-row-right {
  justify-content: flex-end;
}
.u-row-between {
  justify-content: space-between;
}
.u-row-around {
  justify-content: space-around;
}
.u-text-left {
  text-align: left;
}
.u-text-center {
  text-align: center;
}
.u-text-right {
  text-align: right;
}
.u-flex-col {
  display: flex;
  flex-direction: column;
}
.u-flex-0 {
  flex: 0;
}
.u-flex-1 {
  flex: 1;
}
.u-flex-2 {
  flex: 2;
}
.u-flex-3 {
  flex: 3;
}
.u-flex-4 {
  flex: 4;
}
.u-flex-5 {
  flex: 5;
}
.u-flex-6 {
  flex: 6;
}
.u-flex-7 {
  flex: 7;
}
.u-flex-8 {
  flex: 8;
}
.u-flex-9 {
  flex: 9;
}
.u-flex-10 {
  flex: 10;
}
.u-flex-11 {
  flex: 11;
}
.u-flex-12 {
  flex: 12;
}
.u-font-9 {
  font-size: 9px;
}
.u-font-10 {
  font-size: 10px;
}
.u-font-11 {
  font-size: 11px;
}
.u-font-12 {
  font-size: 12px;
}
.u-font-13 {
  font-size: 13px;
}
.u-font-14 {
  font-size: 14px;
}
.u-font-15 {
  font-size: 15px;
}
.u-font-16 {
  font-size: 16px;
}
.u-font-17 {
  font-size: 17px;
}
.u-font-18 {
  font-size: 18px;
}
.u-font-19 {
  font-size: 19px;
}
.u-font-20 {
  font-size: 20rpx;
}
.u-font-21 {
  font-size: 21rpx;
}
.u-font-22 {
  font-size: 22rpx;
}
.u-font-23 {
  font-size: 23rpx;
}
.u-font-24 {
  font-size: 24rpx;
}
.u-font-25 {
  font-size: 25rpx;
}
.u-font-26 {
  font-size: 26rpx;
}
.u-font-27 {
  font-size: 27rpx;
}
.u-font-28 {
  font-size: 28rpx;
}
.u-font-29 {
  font-size: 29rpx;
}
.u-font-30 {
  font-size: 30rpx;
}
.u-font-31 {
  font-size: 31rpx;
}
.u-font-32 {
  font-size: 32rpx;
}
.u-font-33 {
  font-size: 33rpx;
}
.u-font-34 {
  font-size: 34rpx;
}
.u-font-35 {
  font-size: 35rpx;
}
.u-font-36 {
  font-size: 36rpx;
}
.u-font-37 {
  font-size: 37rpx;
}
.u-font-38 {
  font-size: 38rpx;
}
.u-font-39 {
  font-size: 39rpx;
}
.u-font-40 {
  font-size: 40rpx;
}
.u-margin-0, .u-m-0 {
  margin: 0rpx !important;
}
.u-padding-0, .u-p-0 {
  padding: 0rpx !important;
}
.u-m-l-0 {
  margin-left: 0rpx !important;
}
.u-p-l-0 {
  padding-left: 0rpx !important;
}
.u-margin-left-0 {
  margin-left: 0rpx !important;
}
.u-padding-left-0 {
  padding-left: 0rpx !important;
}
.u-m-t-0 {
  margin-top: 0rpx !important;
}
.u-p-t-0 {
  padding-top: 0rpx !important;
}
.u-margin-top-0 {
  margin-top: 0rpx !important;
}
.u-padding-top-0 {
  padding-top: 0rpx !important;
}
.u-m-r-0 {
  margin-right: 0rpx !important;
}
.u-p-r-0 {
  padding-right: 0rpx !important;
}
.u-margin-right-0 {
  margin-right: 0rpx !important;
}
.u-padding-right-0 {
  padding-right: 0rpx !important;
}
.u-m-b-0 {
  margin-bottom: 0rpx !important;
}
.u-p-b-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-bottom-0 {
  margin-bottom: 0rpx !important;
}
.u-padding-bottom-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-2, .u-m-2 {
  margin: 2rpx !important;
}
.u-padding-2, .u-p-2 {
  padding: 2rpx !important;
}
.u-m-l-2 {
  margin-left: 2rpx !important;
}
.u-p-l-2 {
  padding-left: 2rpx !important;
}
.u-margin-left-2 {
  margin-left: 2rpx !important;
}
.u-padding-left-2 {
  padding-left: 2rpx !important;
}
.u-m-t-2 {
  margin-top: 2rpx !important;
}
.u-p-t-2 {
  padding-top: 2rpx !important;
}
.u-margin-top-2 {
  margin-top: 2rpx !important;
}
.u-padding-top-2 {
  padding-top: 2rpx !important;
}
.u-m-r-2 {
  margin-right: 2rpx !important;
}
.u-p-r-2 {
  padding-right: 2rpx !important;
}
.u-margin-right-2 {
  margin-right: 2rpx !important;
}
.u-padding-right-2 {
  padding-right: 2rpx !important;
}
.u-m-b-2 {
  margin-bottom: 2rpx !important;
}
.u-p-b-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-bottom-2 {
  margin-bottom: 2rpx !important;
}
.u-padding-bottom-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-4, .u-m-4 {
  margin: 4rpx !important;
}
.u-padding-4, .u-p-4 {
  padding: 4rpx !important;
}
.u-m-l-4 {
  margin-left: 4rpx !important;
}
.u-p-l-4 {
  padding-left: 4rpx !important;
}
.u-margin-left-4 {
  margin-left: 4rpx !important;
}
.u-padding-left-4 {
  padding-left: 4rpx !important;
}
.u-m-t-4 {
  margin-top: 4rpx !important;
}
.u-p-t-4 {
  padding-top: 4rpx !important;
}
.u-margin-top-4 {
  margin-top: 4rpx !important;
}
.u-padding-top-4 {
  padding-top: 4rpx !important;
}
.u-m-r-4 {
  margin-right: 4rpx !important;
}
.u-p-r-4 {
  padding-right: 4rpx !important;
}
.u-margin-right-4 {
  margin-right: 4rpx !important;
}
.u-padding-right-4 {
  padding-right: 4rpx !important;
}
.u-m-b-4 {
  margin-bottom: 4rpx !important;
}
.u-p-b-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-bottom-4 {
  margin-bottom: 4rpx !important;
}
.u-padding-bottom-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-5, .u-m-5 {
  margin: 5rpx !important;
}
.u-padding-5, .u-p-5 {
  padding: 5rpx !important;
}
.u-m-l-5 {
  margin-left: 5rpx !important;
}
.u-p-l-5 {
  padding-left: 5rpx !important;
}
.u-margin-left-5 {
  margin-left: 5rpx !important;
}
.u-padding-left-5 {
  padding-left: 5rpx !important;
}
.u-m-t-5 {
  margin-top: 5rpx !important;
}
.u-p-t-5 {
  padding-top: 5rpx !important;
}
.u-margin-top-5 {
  margin-top: 5rpx !important;
}
.u-padding-top-5 {
  padding-top: 5rpx !important;
}
.u-m-r-5 {
  margin-right: 5rpx !important;
}
.u-p-r-5 {
  padding-right: 5rpx !important;
}
.u-margin-right-5 {
  margin-right: 5rpx !important;
}
.u-padding-right-5 {
  padding-right: 5rpx !important;
}
.u-m-b-5 {
  margin-bottom: 5rpx !important;
}
.u-p-b-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-bottom-5 {
  margin-bottom: 5rpx !important;
}
.u-padding-bottom-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-6, .u-m-6 {
  margin: 6rpx !important;
}
.u-padding-6, .u-p-6 {
  padding: 6rpx !important;
}
.u-m-l-6 {
  margin-left: 6rpx !important;
}
.u-p-l-6 {
  padding-left: 6rpx !important;
}
.u-margin-left-6 {
  margin-left: 6rpx !important;
}
.u-padding-left-6 {
  padding-left: 6rpx !important;
}
.u-m-t-6 {
  margin-top: 6rpx !important;
}
.u-p-t-6 {
  padding-top: 6rpx !important;
}
.u-margin-top-6 {
  margin-top: 6rpx !important;
}
.u-padding-top-6 {
  padding-top: 6rpx !important;
}
.u-m-r-6 {
  margin-right: 6rpx !important;
}
.u-p-r-6 {
  padding-right: 6rpx !important;
}
.u-margin-right-6 {
  margin-right: 6rpx !important;
}
.u-padding-right-6 {
  padding-right: 6rpx !important;
}
.u-m-b-6 {
  margin-bottom: 6rpx !important;
}
.u-p-b-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-bottom-6 {
  margin-bottom: 6rpx !important;
}
.u-padding-bottom-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-8, .u-m-8 {
  margin: 8rpx !important;
}
.u-padding-8, .u-p-8 {
  padding: 8rpx !important;
}
.u-m-l-8 {
  margin-left: 8rpx !important;
}
.u-p-l-8 {
  padding-left: 8rpx !important;
}
.u-margin-left-8 {
  margin-left: 8rpx !important;
}
.u-padding-left-8 {
  padding-left: 8rpx !important;
}
.u-m-t-8 {
  margin-top: 8rpx !important;
}
.u-p-t-8 {
  padding-top: 8rpx !important;
}
.u-margin-top-8 {
  margin-top: 8rpx !important;
}
.u-padding-top-8 {
  padding-top: 8rpx !important;
}
.u-m-r-8 {
  margin-right: 8rpx !important;
}
.u-p-r-8 {
  padding-right: 8rpx !important;
}
.u-margin-right-8 {
  margin-right: 8rpx !important;
}
.u-padding-right-8 {
  padding-right: 8rpx !important;
}
.u-m-b-8 {
  margin-bottom: 8rpx !important;
}
.u-p-b-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-bottom-8 {
  margin-bottom: 8rpx !important;
}
.u-padding-bottom-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-10, .u-m-10 {
  margin: 10rpx !important;
}
.u-padding-10, .u-p-10 {
  padding: 10rpx !important;
}
.u-m-l-10 {
  margin-left: 10rpx !important;
}
.u-p-l-10 {
  padding-left: 10rpx !important;
}
.u-margin-left-10 {
  margin-left: 10rpx !important;
}
.u-padding-left-10 {
  padding-left: 10rpx !important;
}
.u-m-t-10 {
  margin-top: 10rpx !important;
}
.u-p-t-10 {
  padding-top: 10rpx !important;
}
.u-margin-top-10 {
  margin-top: 10rpx !important;
}
.u-padding-top-10 {
  padding-top: 10rpx !important;
}
.u-m-r-10 {
  margin-right: 10rpx !important;
}
.u-p-r-10 {
  padding-right: 10rpx !important;
}
.u-margin-right-10 {
  margin-right: 10rpx !important;
}
.u-padding-right-10 {
  padding-right: 10rpx !important;
}
.u-m-b-10 {
  margin-bottom: 10rpx !important;
}
.u-p-b-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-bottom-10 {
  margin-bottom: 10rpx !important;
}
.u-padding-bottom-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-12, .u-m-12 {
  margin: 12rpx !important;
}
.u-padding-12, .u-p-12 {
  padding: 12rpx !important;
}
.u-m-l-12 {
  margin-left: 12rpx !important;
}
.u-p-l-12 {
  padding-left: 12rpx !important;
}
.u-margin-left-12 {
  margin-left: 12rpx !important;
}
.u-padding-left-12 {
  padding-left: 12rpx !important;
}
.u-m-t-12 {
  margin-top: 12rpx !important;
}
.u-p-t-12 {
  padding-top: 12rpx !important;
}
.u-margin-top-12 {
  margin-top: 12rpx !important;
}
.u-padding-top-12 {
  padding-top: 12rpx !important;
}
.u-m-r-12 {
  margin-right: 12rpx !important;
}
.u-p-r-12 {
  padding-right: 12rpx !important;
}
.u-margin-right-12 {
  margin-right: 12rpx !important;
}
.u-padding-right-12 {
  padding-right: 12rpx !important;
}
.u-m-b-12 {
  margin-bottom: 12rpx !important;
}
.u-p-b-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-bottom-12 {
  margin-bottom: 12rpx !important;
}
.u-padding-bottom-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-14, .u-m-14 {
  margin: 14rpx !important;
}
.u-padding-14, .u-p-14 {
  padding: 14rpx !important;
}
.u-m-l-14 {
  margin-left: 14rpx !important;
}
.u-p-l-14 {
  padding-left: 14rpx !important;
}
.u-margin-left-14 {
  margin-left: 14rpx !important;
}
.u-padding-left-14 {
  padding-left: 14rpx !important;
}
.u-m-t-14 {
  margin-top: 14rpx !important;
}
.u-p-t-14 {
  padding-top: 14rpx !important;
}
.u-margin-top-14 {
  margin-top: 14rpx !important;
}
.u-padding-top-14 {
  padding-top: 14rpx !important;
}
.u-m-r-14 {
  margin-right: 14rpx !important;
}
.u-p-r-14 {
  padding-right: 14rpx !important;
}
.u-margin-right-14 {
  margin-right: 14rpx !important;
}
.u-padding-right-14 {
  padding-right: 14rpx !important;
}
.u-m-b-14 {
  margin-bottom: 14rpx !important;
}
.u-p-b-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-bottom-14 {
  margin-bottom: 14rpx !important;
}
.u-padding-bottom-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-15, .u-m-15 {
  margin: 15rpx !important;
}
.u-padding-15, .u-p-15 {
  padding: 15rpx !important;
}
.u-m-l-15 {
  margin-left: 15rpx !important;
}
.u-p-l-15 {
  padding-left: 15rpx !important;
}
.u-margin-left-15 {
  margin-left: 15rpx !important;
}
.u-padding-left-15 {
  padding-left: 15rpx !important;
}
.u-m-t-15 {
  margin-top: 15rpx !important;
}
.u-p-t-15 {
  padding-top: 15rpx !important;
}
.u-margin-top-15 {
  margin-top: 15rpx !important;
}
.u-padding-top-15 {
  padding-top: 15rpx !important;
}
.u-m-r-15 {
  margin-right: 15rpx !important;
}
.u-p-r-15 {
  padding-right: 15rpx !important;
}
.u-margin-right-15 {
  margin-right: 15rpx !important;
}
.u-padding-right-15 {
  padding-right: 15rpx !important;
}
.u-m-b-15 {
  margin-bottom: 15rpx !important;
}
.u-p-b-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-bottom-15 {
  margin-bottom: 15rpx !important;
}
.u-padding-bottom-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-16, .u-m-16 {
  margin: 16rpx !important;
}
.u-padding-16, .u-p-16 {
  padding: 16rpx !important;
}
.u-m-l-16 {
  margin-left: 16rpx !important;
}
.u-p-l-16 {
  padding-left: 16rpx !important;
}
.u-margin-left-16 {
  margin-left: 16rpx !important;
}
.u-padding-left-16 {
  padding-left: 16rpx !important;
}
.u-m-t-16 {
  margin-top: 16rpx !important;
}
.u-p-t-16 {
  padding-top: 16rpx !important;
}
.u-margin-top-16 {
  margin-top: 16rpx !important;
}
.u-padding-top-16 {
  padding-top: 16rpx !important;
}
.u-m-r-16 {
  margin-right: 16rpx !important;
}
.u-p-r-16 {
  padding-right: 16rpx !important;
}
.u-margin-right-16 {
  margin-right: 16rpx !important;
}
.u-padding-right-16 {
  padding-right: 16rpx !important;
}
.u-m-b-16 {
  margin-bottom: 16rpx !important;
}
.u-p-b-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-bottom-16 {
  margin-bottom: 16rpx !important;
}
.u-padding-bottom-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-18, .u-m-18 {
  margin: 18rpx !important;
}
.u-padding-18, .u-p-18 {
  padding: 18rpx !important;
}
.u-m-l-18 {
  margin-left: 18rpx !important;
}
.u-p-l-18 {
  padding-left: 18rpx !important;
}
.u-margin-left-18 {
  margin-left: 18rpx !important;
}
.u-padding-left-18 {
  padding-left: 18rpx !important;
}
.u-m-t-18 {
  margin-top: 18rpx !important;
}
.u-p-t-18 {
  padding-top: 18rpx !important;
}
.u-margin-top-18 {
  margin-top: 18rpx !important;
}
.u-padding-top-18 {
  padding-top: 18rpx !important;
}
.u-m-r-18 {
  margin-right: 18rpx !important;
}
.u-p-r-18 {
  padding-right: 18rpx !important;
}
.u-margin-right-18 {
  margin-right: 18rpx !important;
}
.u-padding-right-18 {
  padding-right: 18rpx !important;
}
.u-m-b-18 {
  margin-bottom: 18rpx !important;
}
.u-p-b-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-bottom-18 {
  margin-bottom: 18rpx !important;
}
.u-padding-bottom-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-20, .u-m-20 {
  margin: 20rpx !important;
}
.u-padding-20, .u-p-20 {
  padding: 20rpx !important;
}
.u-m-l-20 {
  margin-left: 20rpx !important;
}
.u-p-l-20 {
  padding-left: 20rpx !important;
}
.u-margin-left-20 {
  margin-left: 20rpx !important;
}
.u-padding-left-20 {
  padding-left: 20rpx !important;
}
.u-m-t-20 {
  margin-top: 20rpx !important;
}
.u-p-t-20 {
  padding-top: 20rpx !important;
}
.u-margin-top-20 {
  margin-top: 20rpx !important;
}
.u-padding-top-20 {
  padding-top: 20rpx !important;
}
.u-m-r-20 {
  margin-right: 20rpx !important;
}
.u-p-r-20 {
  padding-right: 20rpx !important;
}
.u-margin-right-20 {
  margin-right: 20rpx !important;
}
.u-padding-right-20 {
  padding-right: 20rpx !important;
}
.u-m-b-20 {
  margin-bottom: 20rpx !important;
}
.u-p-b-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-bottom-20 {
  margin-bottom: 20rpx !important;
}
.u-padding-bottom-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-22, .u-m-22 {
  margin: 22rpx !important;
}
.u-padding-22, .u-p-22 {
  padding: 22rpx !important;
}
.u-m-l-22 {
  margin-left: 22rpx !important;
}
.u-p-l-22 {
  padding-left: 22rpx !important;
}
.u-margin-left-22 {
  margin-left: 22rpx !important;
}
.u-padding-left-22 {
  padding-left: 22rpx !important;
}
.u-m-t-22 {
  margin-top: 22rpx !important;
}
.u-p-t-22 {
  padding-top: 22rpx !important;
}
.u-margin-top-22 {
  margin-top: 22rpx !important;
}
.u-padding-top-22 {
  padding-top: 22rpx !important;
}
.u-m-r-22 {
  margin-right: 22rpx !important;
}
.u-p-r-22 {
  padding-right: 22rpx !important;
}
.u-margin-right-22 {
  margin-right: 22rpx !important;
}
.u-padding-right-22 {
  padding-right: 22rpx !important;
}
.u-m-b-22 {
  margin-bottom: 22rpx !important;
}
.u-p-b-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-bottom-22 {
  margin-bottom: 22rpx !important;
}
.u-padding-bottom-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-24, .u-m-24 {
  margin: 24rpx !important;
}
.u-padding-24, .u-p-24 {
  padding: 24rpx !important;
}
.u-m-l-24 {
  margin-left: 24rpx !important;
}
.u-p-l-24 {
  padding-left: 24rpx !important;
}
.u-margin-left-24 {
  margin-left: 24rpx !important;
}
.u-padding-left-24 {
  padding-left: 24rpx !important;
}
.u-m-t-24 {
  margin-top: 24rpx !important;
}
.u-p-t-24 {
  padding-top: 24rpx !important;
}
.u-margin-top-24 {
  margin-top: 24rpx !important;
}
.u-padding-top-24 {
  padding-top: 24rpx !important;
}
.u-m-r-24 {
  margin-right: 24rpx !important;
}
.u-p-r-24 {
  padding-right: 24rpx !important;
}
.u-margin-right-24 {
  margin-right: 24rpx !important;
}
.u-padding-right-24 {
  padding-right: 24rpx !important;
}
.u-m-b-24 {
  margin-bottom: 24rpx !important;
}
.u-p-b-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-bottom-24 {
  margin-bottom: 24rpx !important;
}
.u-padding-bottom-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-25, .u-m-25 {
  margin: 25rpx !important;
}
.u-padding-25, .u-p-25 {
  padding: 25rpx !important;
}
.u-m-l-25 {
  margin-left: 25rpx !important;
}
.u-p-l-25 {
  padding-left: 25rpx !important;
}
.u-margin-left-25 {
  margin-left: 25rpx !important;
}
.u-padding-left-25 {
  padding-left: 25rpx !important;
}
.u-m-t-25 {
  margin-top: 25rpx !important;
}
.u-p-t-25 {
  padding-top: 25rpx !important;
}
.u-margin-top-25 {
  margin-top: 25rpx !important;
}
.u-padding-top-25 {
  padding-top: 25rpx !important;
}
.u-m-r-25 {
  margin-right: 25rpx !important;
}
.u-p-r-25 {
  padding-right: 25rpx !important;
}
.u-margin-right-25 {
  margin-right: 25rpx !important;
}
.u-padding-right-25 {
  padding-right: 25rpx !important;
}
.u-m-b-25 {
  margin-bottom: 25rpx !important;
}
.u-p-b-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-bottom-25 {
  margin-bottom: 25rpx !important;
}
.u-padding-bottom-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-26, .u-m-26 {
  margin: 26rpx !important;
}
.u-padding-26, .u-p-26 {
  padding: 26rpx !important;
}
.u-m-l-26 {
  margin-left: 26rpx !important;
}
.u-p-l-26 {
  padding-left: 26rpx !important;
}
.u-margin-left-26 {
  margin-left: 26rpx !important;
}
.u-padding-left-26 {
  padding-left: 26rpx !important;
}
.u-m-t-26 {
  margin-top: 26rpx !important;
}
.u-p-t-26 {
  padding-top: 26rpx !important;
}
.u-margin-top-26 {
  margin-top: 26rpx !important;
}
.u-padding-top-26 {
  padding-top: 26rpx !important;
}
.u-m-r-26 {
  margin-right: 26rpx !important;
}
.u-p-r-26 {
  padding-right: 26rpx !important;
}
.u-margin-right-26 {
  margin-right: 26rpx !important;
}
.u-padding-right-26 {
  padding-right: 26rpx !important;
}
.u-m-b-26 {
  margin-bottom: 26rpx !important;
}
.u-p-b-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-bottom-26 {
  margin-bottom: 26rpx !important;
}
.u-padding-bottom-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-28, .u-m-28 {
  margin: 28rpx !important;
}
.u-padding-28, .u-p-28 {
  padding: 28rpx !important;
}
.u-m-l-28 {
  margin-left: 28rpx !important;
}
.u-p-l-28 {
  padding-left: 28rpx !important;
}
.u-margin-left-28 {
  margin-left: 28rpx !important;
}
.u-padding-left-28 {
  padding-left: 28rpx !important;
}
.u-m-t-28 {
  margin-top: 28rpx !important;
}
.u-p-t-28 {
  padding-top: 28rpx !important;
}
.u-margin-top-28 {
  margin-top: 28rpx !important;
}
.u-padding-top-28 {
  padding-top: 28rpx !important;
}
.u-m-r-28 {
  margin-right: 28rpx !important;
}
.u-p-r-28 {
  padding-right: 28rpx !important;
}
.u-margin-right-28 {
  margin-right: 28rpx !important;
}
.u-padding-right-28 {
  padding-right: 28rpx !important;
}
.u-m-b-28 {
  margin-bottom: 28rpx !important;
}
.u-p-b-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-bottom-28 {
  margin-bottom: 28rpx !important;
}
.u-padding-bottom-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-30, .u-m-30 {
  margin: 30rpx !important;
}
.u-padding-30, .u-p-30 {
  padding: 30rpx !important;
}
.u-m-l-30 {
  margin-left: 30rpx !important;
}
.u-p-l-30 {
  padding-left: 30rpx !important;
}
.u-margin-left-30 {
  margin-left: 30rpx !important;
}
.u-padding-left-30 {
  padding-left: 30rpx !important;
}
.u-m-t-30 {
  margin-top: 30rpx !important;
}
.u-p-t-30 {
  padding-top: 30rpx !important;
}
.u-margin-top-30 {
  margin-top: 30rpx !important;
}
.u-padding-top-30 {
  padding-top: 30rpx !important;
}
.u-m-r-30 {
  margin-right: 30rpx !important;
}
.u-p-r-30 {
  padding-right: 30rpx !important;
}
.u-margin-right-30 {
  margin-right: 30rpx !important;
}
.u-padding-right-30 {
  padding-right: 30rpx !important;
}
.u-m-b-30 {
  margin-bottom: 30rpx !important;
}
.u-p-b-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-bottom-30 {
  margin-bottom: 30rpx !important;
}
.u-padding-bottom-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-32, .u-m-32 {
  margin: 32rpx !important;
}
.u-padding-32, .u-p-32 {
  padding: 32rpx !important;
}
.u-m-l-32 {
  margin-left: 32rpx !important;
}
.u-p-l-32 {
  padding-left: 32rpx !important;
}
.u-margin-left-32 {
  margin-left: 32rpx !important;
}
.u-padding-left-32 {
  padding-left: 32rpx !important;
}
.u-m-t-32 {
  margin-top: 32rpx !important;
}
.u-p-t-32 {
  padding-top: 32rpx !important;
}
.u-margin-top-32 {
  margin-top: 32rpx !important;
}
.u-padding-top-32 {
  padding-top: 32rpx !important;
}
.u-m-r-32 {
  margin-right: 32rpx !important;
}
.u-p-r-32 {
  padding-right: 32rpx !important;
}
.u-margin-right-32 {
  margin-right: 32rpx !important;
}
.u-padding-right-32 {
  padding-right: 32rpx !important;
}
.u-m-b-32 {
  margin-bottom: 32rpx !important;
}
.u-p-b-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-bottom-32 {
  margin-bottom: 32rpx !important;
}
.u-padding-bottom-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-34, .u-m-34 {
  margin: 34rpx !important;
}
.u-padding-34, .u-p-34 {
  padding: 34rpx !important;
}
.u-m-l-34 {
  margin-left: 34rpx !important;
}
.u-p-l-34 {
  padding-left: 34rpx !important;
}
.u-margin-left-34 {
  margin-left: 34rpx !important;
}
.u-padding-left-34 {
  padding-left: 34rpx !important;
}
.u-m-t-34 {
  margin-top: 34rpx !important;
}
.u-p-t-34 {
  padding-top: 34rpx !important;
}
.u-margin-top-34 {
  margin-top: 34rpx !important;
}
.u-padding-top-34 {
  padding-top: 34rpx !important;
}
.u-m-r-34 {
  margin-right: 34rpx !important;
}
.u-p-r-34 {
  padding-right: 34rpx !important;
}
.u-margin-right-34 {
  margin-right: 34rpx !important;
}
.u-padding-right-34 {
  padding-right: 34rpx !important;
}
.u-m-b-34 {
  margin-bottom: 34rpx !important;
}
.u-p-b-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-bottom-34 {
  margin-bottom: 34rpx !important;
}
.u-padding-bottom-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-35, .u-m-35 {
  margin: 35rpx !important;
}
.u-padding-35, .u-p-35 {
  padding: 35rpx !important;
}
.u-m-l-35 {
  margin-left: 35rpx !important;
}
.u-p-l-35 {
  padding-left: 35rpx !important;
}
.u-margin-left-35 {
  margin-left: 35rpx !important;
}
.u-padding-left-35 {
  padding-left: 35rpx !important;
}
.u-m-t-35 {
  margin-top: 35rpx !important;
}
.u-p-t-35 {
  padding-top: 35rpx !important;
}
.u-margin-top-35 {
  margin-top: 35rpx !important;
}
.u-padding-top-35 {
  padding-top: 35rpx !important;
}
.u-m-r-35 {
  margin-right: 35rpx !important;
}
.u-p-r-35 {
  padding-right: 35rpx !important;
}
.u-margin-right-35 {
  margin-right: 35rpx !important;
}
.u-padding-right-35 {
  padding-right: 35rpx !important;
}
.u-m-b-35 {
  margin-bottom: 35rpx !important;
}
.u-p-b-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-bottom-35 {
  margin-bottom: 35rpx !important;
}
.u-padding-bottom-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-36, .u-m-36 {
  margin: 36rpx !important;
}
.u-padding-36, .u-p-36 {
  padding: 36rpx !important;
}
.u-m-l-36 {
  margin-left: 36rpx !important;
}
.u-p-l-36 {
  padding-left: 36rpx !important;
}
.u-margin-left-36 {
  margin-left: 36rpx !important;
}
.u-padding-left-36 {
  padding-left: 36rpx !important;
}
.u-m-t-36 {
  margin-top: 36rpx !important;
}
.u-p-t-36 {
  padding-top: 36rpx !important;
}
.u-margin-top-36 {
  margin-top: 36rpx !important;
}
.u-padding-top-36 {
  padding-top: 36rpx !important;
}
.u-m-r-36 {
  margin-right: 36rpx !important;
}
.u-p-r-36 {
  padding-right: 36rpx !important;
}
.u-margin-right-36 {
  margin-right: 36rpx !important;
}
.u-padding-right-36 {
  padding-right: 36rpx !important;
}
.u-m-b-36 {
  margin-bottom: 36rpx !important;
}
.u-p-b-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-bottom-36 {
  margin-bottom: 36rpx !important;
}
.u-padding-bottom-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-38, .u-m-38 {
  margin: 38rpx !important;
}
.u-padding-38, .u-p-38 {
  padding: 38rpx !important;
}
.u-m-l-38 {
  margin-left: 38rpx !important;
}
.u-p-l-38 {
  padding-left: 38rpx !important;
}
.u-margin-left-38 {
  margin-left: 38rpx !important;
}
.u-padding-left-38 {
  padding-left: 38rpx !important;
}
.u-m-t-38 {
  margin-top: 38rpx !important;
}
.u-p-t-38 {
  padding-top: 38rpx !important;
}
.u-margin-top-38 {
  margin-top: 38rpx !important;
}
.u-padding-top-38 {
  padding-top: 38rpx !important;
}
.u-m-r-38 {
  margin-right: 38rpx !important;
}
.u-p-r-38 {
  padding-right: 38rpx !important;
}
.u-margin-right-38 {
  margin-right: 38rpx !important;
}
.u-padding-right-38 {
  padding-right: 38rpx !important;
}
.u-m-b-38 {
  margin-bottom: 38rpx !important;
}
.u-p-b-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-bottom-38 {
  margin-bottom: 38rpx !important;
}
.u-padding-bottom-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-40, .u-m-40 {
  margin: 40rpx !important;
}
.u-padding-40, .u-p-40 {
  padding: 40rpx !important;
}
.u-m-l-40 {
  margin-left: 40rpx !important;
}
.u-p-l-40 {
  padding-left: 40rpx !important;
}
.u-margin-left-40 {
  margin-left: 40rpx !important;
}
.u-padding-left-40 {
  padding-left: 40rpx !important;
}
.u-m-t-40 {
  margin-top: 40rpx !important;
}
.u-p-t-40 {
  padding-top: 40rpx !important;
}
.u-margin-top-40 {
  margin-top: 40rpx !important;
}
.u-padding-top-40 {
  padding-top: 40rpx !important;
}
.u-m-r-40 {
  margin-right: 40rpx !important;
}
.u-p-r-40 {
  padding-right: 40rpx !important;
}
.u-margin-right-40 {
  margin-right: 40rpx !important;
}
.u-padding-right-40 {
  padding-right: 40rpx !important;
}
.u-m-b-40 {
  margin-bottom: 40rpx !important;
}
.u-p-b-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-bottom-40 {
  margin-bottom: 40rpx !important;
}
.u-padding-bottom-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-42, .u-m-42 {
  margin: 42rpx !important;
}
.u-padding-42, .u-p-42 {
  padding: 42rpx !important;
}
.u-m-l-42 {
  margin-left: 42rpx !important;
}
.u-p-l-42 {
  padding-left: 42rpx !important;
}
.u-margin-left-42 {
  margin-left: 42rpx !important;
}
.u-padding-left-42 {
  padding-left: 42rpx !important;
}
.u-m-t-42 {
  margin-top: 42rpx !important;
}
.u-p-t-42 {
  padding-top: 42rpx !important;
}
.u-margin-top-42 {
  margin-top: 42rpx !important;
}
.u-padding-top-42 {
  padding-top: 42rpx !important;
}
.u-m-r-42 {
  margin-right: 42rpx !important;
}
.u-p-r-42 {
  padding-right: 42rpx !important;
}
.u-margin-right-42 {
  margin-right: 42rpx !important;
}
.u-padding-right-42 {
  padding-right: 42rpx !important;
}
.u-m-b-42 {
  margin-bottom: 42rpx !important;
}
.u-p-b-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-bottom-42 {
  margin-bottom: 42rpx !important;
}
.u-padding-bottom-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-44, .u-m-44 {
  margin: 44rpx !important;
}
.u-padding-44, .u-p-44 {
  padding: 44rpx !important;
}
.u-m-l-44 {
  margin-left: 44rpx !important;
}
.u-p-l-44 {
  padding-left: 44rpx !important;
}
.u-margin-left-44 {
  margin-left: 44rpx !important;
}
.u-padding-left-44 {
  padding-left: 44rpx !important;
}
.u-m-t-44 {
  margin-top: 44rpx !important;
}
.u-p-t-44 {
  padding-top: 44rpx !important;
}
.u-margin-top-44 {
  margin-top: 44rpx !important;
}
.u-padding-top-44 {
  padding-top: 44rpx !important;
}
.u-m-r-44 {
  margin-right: 44rpx !important;
}
.u-p-r-44 {
  padding-right: 44rpx !important;
}
.u-margin-right-44 {
  margin-right: 44rpx !important;
}
.u-padding-right-44 {
  padding-right: 44rpx !important;
}
.u-m-b-44 {
  margin-bottom: 44rpx !important;
}
.u-p-b-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-bottom-44 {
  margin-bottom: 44rpx !important;
}
.u-padding-bottom-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-45, .u-m-45 {
  margin: 45rpx !important;
}
.u-padding-45, .u-p-45 {
  padding: 45rpx !important;
}
.u-m-l-45 {
  margin-left: 45rpx !important;
}
.u-p-l-45 {
  padding-left: 45rpx !important;
}
.u-margin-left-45 {
  margin-left: 45rpx !important;
}
.u-padding-left-45 {
  padding-left: 45rpx !important;
}
.u-m-t-45 {
  margin-top: 45rpx !important;
}
.u-p-t-45 {
  padding-top: 45rpx !important;
}
.u-margin-top-45 {
  margin-top: 45rpx !important;
}
.u-padding-top-45 {
  padding-top: 45rpx !important;
}
.u-m-r-45 {
  margin-right: 45rpx !important;
}
.u-p-r-45 {
  padding-right: 45rpx !important;
}
.u-margin-right-45 {
  margin-right: 45rpx !important;
}
.u-padding-right-45 {
  padding-right: 45rpx !important;
}
.u-m-b-45 {
  margin-bottom: 45rpx !important;
}
.u-p-b-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-bottom-45 {
  margin-bottom: 45rpx !important;
}
.u-padding-bottom-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-46, .u-m-46 {
  margin: 46rpx !important;
}
.u-padding-46, .u-p-46 {
  padding: 46rpx !important;
}
.u-m-l-46 {
  margin-left: 46rpx !important;
}
.u-p-l-46 {
  padding-left: 46rpx !important;
}
.u-margin-left-46 {
  margin-left: 46rpx !important;
}
.u-padding-left-46 {
  padding-left: 46rpx !important;
}
.u-m-t-46 {
  margin-top: 46rpx !important;
}
.u-p-t-46 {
  padding-top: 46rpx !important;
}
.u-margin-top-46 {
  margin-top: 46rpx !important;
}
.u-padding-top-46 {
  padding-top: 46rpx !important;
}
.u-m-r-46 {
  margin-right: 46rpx !important;
}
.u-p-r-46 {
  padding-right: 46rpx !important;
}
.u-margin-right-46 {
  margin-right: 46rpx !important;
}
.u-padding-right-46 {
  padding-right: 46rpx !important;
}
.u-m-b-46 {
  margin-bottom: 46rpx !important;
}
.u-p-b-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-bottom-46 {
  margin-bottom: 46rpx !important;
}
.u-padding-bottom-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-48, .u-m-48 {
  margin: 48rpx !important;
}
.u-padding-48, .u-p-48 {
  padding: 48rpx !important;
}
.u-m-l-48 {
  margin-left: 48rpx !important;
}
.u-p-l-48 {
  padding-left: 48rpx !important;
}
.u-margin-left-48 {
  margin-left: 48rpx !important;
}
.u-padding-left-48 {
  padding-left: 48rpx !important;
}
.u-m-t-48 {
  margin-top: 48rpx !important;
}
.u-p-t-48 {
  padding-top: 48rpx !important;
}
.u-margin-top-48 {
  margin-top: 48rpx !important;
}
.u-padding-top-48 {
  padding-top: 48rpx !important;
}
.u-m-r-48 {
  margin-right: 48rpx !important;
}
.u-p-r-48 {
  padding-right: 48rpx !important;
}
.u-margin-right-48 {
  margin-right: 48rpx !important;
}
.u-padding-right-48 {
  padding-right: 48rpx !important;
}
.u-m-b-48 {
  margin-bottom: 48rpx !important;
}
.u-p-b-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-bottom-48 {
  margin-bottom: 48rpx !important;
}
.u-padding-bottom-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-50, .u-m-50 {
  margin: 50rpx !important;
}
.u-padding-50, .u-p-50 {
  padding: 50rpx !important;
}
.u-m-l-50 {
  margin-left: 50rpx !important;
}
.u-p-l-50 {
  padding-left: 50rpx !important;
}
.u-margin-left-50 {
  margin-left: 50rpx !important;
}
.u-padding-left-50 {
  padding-left: 50rpx !important;
}
.u-m-t-50 {
  margin-top: 50rpx !important;
}
.u-p-t-50 {
  padding-top: 50rpx !important;
}
.u-margin-top-50 {
  margin-top: 50rpx !important;
}
.u-padding-top-50 {
  padding-top: 50rpx !important;
}
.u-m-r-50 {
  margin-right: 50rpx !important;
}
.u-p-r-50 {
  padding-right: 50rpx !important;
}
.u-margin-right-50 {
  margin-right: 50rpx !important;
}
.u-padding-right-50 {
  padding-right: 50rpx !important;
}
.u-m-b-50 {
  margin-bottom: 50rpx !important;
}
.u-p-b-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-bottom-50 {
  margin-bottom: 50rpx !important;
}
.u-padding-bottom-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-52, .u-m-52 {
  margin: 52rpx !important;
}
.u-padding-52, .u-p-52 {
  padding: 52rpx !important;
}
.u-m-l-52 {
  margin-left: 52rpx !important;
}
.u-p-l-52 {
  padding-left: 52rpx !important;
}
.u-margin-left-52 {
  margin-left: 52rpx !important;
}
.u-padding-left-52 {
  padding-left: 52rpx !important;
}
.u-m-t-52 {
  margin-top: 52rpx !important;
}
.u-p-t-52 {
  padding-top: 52rpx !important;
}
.u-margin-top-52 {
  margin-top: 52rpx !important;
}
.u-padding-top-52 {
  padding-top: 52rpx !important;
}
.u-m-r-52 {
  margin-right: 52rpx !important;
}
.u-p-r-52 {
  padding-right: 52rpx !important;
}
.u-margin-right-52 {
  margin-right: 52rpx !important;
}
.u-padding-right-52 {
  padding-right: 52rpx !important;
}
.u-m-b-52 {
  margin-bottom: 52rpx !important;
}
.u-p-b-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-bottom-52 {
  margin-bottom: 52rpx !important;
}
.u-padding-bottom-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-54, .u-m-54 {
  margin: 54rpx !important;
}
.u-padding-54, .u-p-54 {
  padding: 54rpx !important;
}
.u-m-l-54 {
  margin-left: 54rpx !important;
}
.u-p-l-54 {
  padding-left: 54rpx !important;
}
.u-margin-left-54 {
  margin-left: 54rpx !important;
}
.u-padding-left-54 {
  padding-left: 54rpx !important;
}
.u-m-t-54 {
  margin-top: 54rpx !important;
}
.u-p-t-54 {
  padding-top: 54rpx !important;
}
.u-margin-top-54 {
  margin-top: 54rpx !important;
}
.u-padding-top-54 {
  padding-top: 54rpx !important;
}
.u-m-r-54 {
  margin-right: 54rpx !important;
}
.u-p-r-54 {
  padding-right: 54rpx !important;
}
.u-margin-right-54 {
  margin-right: 54rpx !important;
}
.u-padding-right-54 {
  padding-right: 54rpx !important;
}
.u-m-b-54 {
  margin-bottom: 54rpx !important;
}
.u-p-b-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-bottom-54 {
  margin-bottom: 54rpx !important;
}
.u-padding-bottom-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-55, .u-m-55 {
  margin: 55rpx !important;
}
.u-padding-55, .u-p-55 {
  padding: 55rpx !important;
}
.u-m-l-55 {
  margin-left: 55rpx !important;
}
.u-p-l-55 {
  padding-left: 55rpx !important;
}
.u-margin-left-55 {
  margin-left: 55rpx !important;
}
.u-padding-left-55 {
  padding-left: 55rpx !important;
}
.u-m-t-55 {
  margin-top: 55rpx !important;
}
.u-p-t-55 {
  padding-top: 55rpx !important;
}
.u-margin-top-55 {
  margin-top: 55rpx !important;
}
.u-padding-top-55 {
  padding-top: 55rpx !important;
}
.u-m-r-55 {
  margin-right: 55rpx !important;
}
.u-p-r-55 {
  padding-right: 55rpx !important;
}
.u-margin-right-55 {
  margin-right: 55rpx !important;
}
.u-padding-right-55 {
  padding-right: 55rpx !important;
}
.u-m-b-55 {
  margin-bottom: 55rpx !important;
}
.u-p-b-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-bottom-55 {
  margin-bottom: 55rpx !important;
}
.u-padding-bottom-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-56, .u-m-56 {
  margin: 56rpx !important;
}
.u-padding-56, .u-p-56 {
  padding: 56rpx !important;
}
.u-m-l-56 {
  margin-left: 56rpx !important;
}
.u-p-l-56 {
  padding-left: 56rpx !important;
}
.u-margin-left-56 {
  margin-left: 56rpx !important;
}
.u-padding-left-56 {
  padding-left: 56rpx !important;
}
.u-m-t-56 {
  margin-top: 56rpx !important;
}
.u-p-t-56 {
  padding-top: 56rpx !important;
}
.u-margin-top-56 {
  margin-top: 56rpx !important;
}
.u-padding-top-56 {
  padding-top: 56rpx !important;
}
.u-m-r-56 {
  margin-right: 56rpx !important;
}
.u-p-r-56 {
  padding-right: 56rpx !important;
}
.u-margin-right-56 {
  margin-right: 56rpx !important;
}
.u-padding-right-56 {
  padding-right: 56rpx !important;
}
.u-m-b-56 {
  margin-bottom: 56rpx !important;
}
.u-p-b-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-bottom-56 {
  margin-bottom: 56rpx !important;
}
.u-padding-bottom-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-58, .u-m-58 {
  margin: 58rpx !important;
}
.u-padding-58, .u-p-58 {
  padding: 58rpx !important;
}
.u-m-l-58 {
  margin-left: 58rpx !important;
}
.u-p-l-58 {
  padding-left: 58rpx !important;
}
.u-margin-left-58 {
  margin-left: 58rpx !important;
}
.u-padding-left-58 {
  padding-left: 58rpx !important;
}
.u-m-t-58 {
  margin-top: 58rpx !important;
}
.u-p-t-58 {
  padding-top: 58rpx !important;
}
.u-margin-top-58 {
  margin-top: 58rpx !important;
}
.u-padding-top-58 {
  padding-top: 58rpx !important;
}
.u-m-r-58 {
  margin-right: 58rpx !important;
}
.u-p-r-58 {
  padding-right: 58rpx !important;
}
.u-margin-right-58 {
  margin-right: 58rpx !important;
}
.u-padding-right-58 {
  padding-right: 58rpx !important;
}
.u-m-b-58 {
  margin-bottom: 58rpx !important;
}
.u-p-b-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-bottom-58 {
  margin-bottom: 58rpx !important;
}
.u-padding-bottom-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-60, .u-m-60 {
  margin: 60rpx !important;
}
.u-padding-60, .u-p-60 {
  padding: 60rpx !important;
}
.u-m-l-60 {
  margin-left: 60rpx !important;
}
.u-p-l-60 {
  padding-left: 60rpx !important;
}
.u-margin-left-60 {
  margin-left: 60rpx !important;
}
.u-padding-left-60 {
  padding-left: 60rpx !important;
}
.u-m-t-60 {
  margin-top: 60rpx !important;
}
.u-p-t-60 {
  padding-top: 60rpx !important;
}
.u-margin-top-60 {
  margin-top: 60rpx !important;
}
.u-padding-top-60 {
  padding-top: 60rpx !important;
}
.u-m-r-60 {
  margin-right: 60rpx !important;
}
.u-p-r-60 {
  padding-right: 60rpx !important;
}
.u-margin-right-60 {
  margin-right: 60rpx !important;
}
.u-padding-right-60 {
  padding-right: 60rpx !important;
}
.u-m-b-60 {
  margin-bottom: 60rpx !important;
}
.u-p-b-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-bottom-60 {
  margin-bottom: 60rpx !important;
}
.u-padding-bottom-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-62, .u-m-62 {
  margin: 62rpx !important;
}
.u-padding-62, .u-p-62 {
  padding: 62rpx !important;
}
.u-m-l-62 {
  margin-left: 62rpx !important;
}
.u-p-l-62 {
  padding-left: 62rpx !important;
}
.u-margin-left-62 {
  margin-left: 62rpx !important;
}
.u-padding-left-62 {
  padding-left: 62rpx !important;
}
.u-m-t-62 {
  margin-top: 62rpx !important;
}
.u-p-t-62 {
  padding-top: 62rpx !important;
}
.u-margin-top-62 {
  margin-top: 62rpx !important;
}
.u-padding-top-62 {
  padding-top: 62rpx !important;
}
.u-m-r-62 {
  margin-right: 62rpx !important;
}
.u-p-r-62 {
  padding-right: 62rpx !important;
}
.u-margin-right-62 {
  margin-right: 62rpx !important;
}
.u-padding-right-62 {
  padding-right: 62rpx !important;
}
.u-m-b-62 {
  margin-bottom: 62rpx !important;
}
.u-p-b-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-bottom-62 {
  margin-bottom: 62rpx !important;
}
.u-padding-bottom-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-64, .u-m-64 {
  margin: 64rpx !important;
}
.u-padding-64, .u-p-64 {
  padding: 64rpx !important;
}
.u-m-l-64 {
  margin-left: 64rpx !important;
}
.u-p-l-64 {
  padding-left: 64rpx !important;
}
.u-margin-left-64 {
  margin-left: 64rpx !important;
}
.u-padding-left-64 {
  padding-left: 64rpx !important;
}
.u-m-t-64 {
  margin-top: 64rpx !important;
}
.u-p-t-64 {
  padding-top: 64rpx !important;
}
.u-margin-top-64 {
  margin-top: 64rpx !important;
}
.u-padding-top-64 {
  padding-top: 64rpx !important;
}
.u-m-r-64 {
  margin-right: 64rpx !important;
}
.u-p-r-64 {
  padding-right: 64rpx !important;
}
.u-margin-right-64 {
  margin-right: 64rpx !important;
}
.u-padding-right-64 {
  padding-right: 64rpx !important;
}
.u-m-b-64 {
  margin-bottom: 64rpx !important;
}
.u-p-b-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-bottom-64 {
  margin-bottom: 64rpx !important;
}
.u-padding-bottom-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-65, .u-m-65 {
  margin: 65rpx !important;
}
.u-padding-65, .u-p-65 {
  padding: 65rpx !important;
}
.u-m-l-65 {
  margin-left: 65rpx !important;
}
.u-p-l-65 {
  padding-left: 65rpx !important;
}
.u-margin-left-65 {
  margin-left: 65rpx !important;
}
.u-padding-left-65 {
  padding-left: 65rpx !important;
}
.u-m-t-65 {
  margin-top: 65rpx !important;
}
.u-p-t-65 {
  padding-top: 65rpx !important;
}
.u-margin-top-65 {
  margin-top: 65rpx !important;
}
.u-padding-top-65 {
  padding-top: 65rpx !important;
}
.u-m-r-65 {
  margin-right: 65rpx !important;
}
.u-p-r-65 {
  padding-right: 65rpx !important;
}
.u-margin-right-65 {
  margin-right: 65rpx !important;
}
.u-padding-right-65 {
  padding-right: 65rpx !important;
}
.u-m-b-65 {
  margin-bottom: 65rpx !important;
}
.u-p-b-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-bottom-65 {
  margin-bottom: 65rpx !important;
}
.u-padding-bottom-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-66, .u-m-66 {
  margin: 66rpx !important;
}
.u-padding-66, .u-p-66 {
  padding: 66rpx !important;
}
.u-m-l-66 {
  margin-left: 66rpx !important;
}
.u-p-l-66 {
  padding-left: 66rpx !important;
}
.u-margin-left-66 {
  margin-left: 66rpx !important;
}
.u-padding-left-66 {
  padding-left: 66rpx !important;
}
.u-m-t-66 {
  margin-top: 66rpx !important;
}
.u-p-t-66 {
  padding-top: 66rpx !important;
}
.u-margin-top-66 {
  margin-top: 66rpx !important;
}
.u-padding-top-66 {
  padding-top: 66rpx !important;
}
.u-m-r-66 {
  margin-right: 66rpx !important;
}
.u-p-r-66 {
  padding-right: 66rpx !important;
}
.u-margin-right-66 {
  margin-right: 66rpx !important;
}
.u-padding-right-66 {
  padding-right: 66rpx !important;
}
.u-m-b-66 {
  margin-bottom: 66rpx !important;
}
.u-p-b-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-bottom-66 {
  margin-bottom: 66rpx !important;
}
.u-padding-bottom-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-68, .u-m-68 {
  margin: 68rpx !important;
}
.u-padding-68, .u-p-68 {
  padding: 68rpx !important;
}
.u-m-l-68 {
  margin-left: 68rpx !important;
}
.u-p-l-68 {
  padding-left: 68rpx !important;
}
.u-margin-left-68 {
  margin-left: 68rpx !important;
}
.u-padding-left-68 {
  padding-left: 68rpx !important;
}
.u-m-t-68 {
  margin-top: 68rpx !important;
}
.u-p-t-68 {
  padding-top: 68rpx !important;
}
.u-margin-top-68 {
  margin-top: 68rpx !important;
}
.u-padding-top-68 {
  padding-top: 68rpx !important;
}
.u-m-r-68 {
  margin-right: 68rpx !important;
}
.u-p-r-68 {
  padding-right: 68rpx !important;
}
.u-margin-right-68 {
  margin-right: 68rpx !important;
}
.u-padding-right-68 {
  padding-right: 68rpx !important;
}
.u-m-b-68 {
  margin-bottom: 68rpx !important;
}
.u-p-b-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-bottom-68 {
  margin-bottom: 68rpx !important;
}
.u-padding-bottom-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-70, .u-m-70 {
  margin: 70rpx !important;
}
.u-padding-70, .u-p-70 {
  padding: 70rpx !important;
}
.u-m-l-70 {
  margin-left: 70rpx !important;
}
.u-p-l-70 {
  padding-left: 70rpx !important;
}
.u-margin-left-70 {
  margin-left: 70rpx !important;
}
.u-padding-left-70 {
  padding-left: 70rpx !important;
}
.u-m-t-70 {
  margin-top: 70rpx !important;
}
.u-p-t-70 {
  padding-top: 70rpx !important;
}
.u-margin-top-70 {
  margin-top: 70rpx !important;
}
.u-padding-top-70 {
  padding-top: 70rpx !important;
}
.u-m-r-70 {
  margin-right: 70rpx !important;
}
.u-p-r-70 {
  padding-right: 70rpx !important;
}
.u-margin-right-70 {
  margin-right: 70rpx !important;
}
.u-padding-right-70 {
  padding-right: 70rpx !important;
}
.u-m-b-70 {
  margin-bottom: 70rpx !important;
}
.u-p-b-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-bottom-70 {
  margin-bottom: 70rpx !important;
}
.u-padding-bottom-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-72, .u-m-72 {
  margin: 72rpx !important;
}
.u-padding-72, .u-p-72 {
  padding: 72rpx !important;
}
.u-m-l-72 {
  margin-left: 72rpx !important;
}
.u-p-l-72 {
  padding-left: 72rpx !important;
}
.u-margin-left-72 {
  margin-left: 72rpx !important;
}
.u-padding-left-72 {
  padding-left: 72rpx !important;
}
.u-m-t-72 {
  margin-top: 72rpx !important;
}
.u-p-t-72 {
  padding-top: 72rpx !important;
}
.u-margin-top-72 {
  margin-top: 72rpx !important;
}
.u-padding-top-72 {
  padding-top: 72rpx !important;
}
.u-m-r-72 {
  margin-right: 72rpx !important;
}
.u-p-r-72 {
  padding-right: 72rpx !important;
}
.u-margin-right-72 {
  margin-right: 72rpx !important;
}
.u-padding-right-72 {
  padding-right: 72rpx !important;
}
.u-m-b-72 {
  margin-bottom: 72rpx !important;
}
.u-p-b-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-bottom-72 {
  margin-bottom: 72rpx !important;
}
.u-padding-bottom-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-74, .u-m-74 {
  margin: 74rpx !important;
}
.u-padding-74, .u-p-74 {
  padding: 74rpx !important;
}
.u-m-l-74 {
  margin-left: 74rpx !important;
}
.u-p-l-74 {
  padding-left: 74rpx !important;
}
.u-margin-left-74 {
  margin-left: 74rpx !important;
}
.u-padding-left-74 {
  padding-left: 74rpx !important;
}
.u-m-t-74 {
  margin-top: 74rpx !important;
}
.u-p-t-74 {
  padding-top: 74rpx !important;
}
.u-margin-top-74 {
  margin-top: 74rpx !important;
}
.u-padding-top-74 {
  padding-top: 74rpx !important;
}
.u-m-r-74 {
  margin-right: 74rpx !important;
}
.u-p-r-74 {
  padding-right: 74rpx !important;
}
.u-margin-right-74 {
  margin-right: 74rpx !important;
}
.u-padding-right-74 {
  padding-right: 74rpx !important;
}
.u-m-b-74 {
  margin-bottom: 74rpx !important;
}
.u-p-b-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-bottom-74 {
  margin-bottom: 74rpx !important;
}
.u-padding-bottom-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-75, .u-m-75 {
  margin: 75rpx !important;
}
.u-padding-75, .u-p-75 {
  padding: 75rpx !important;
}
.u-m-l-75 {
  margin-left: 75rpx !important;
}
.u-p-l-75 {
  padding-left: 75rpx !important;
}
.u-margin-left-75 {
  margin-left: 75rpx !important;
}
.u-padding-left-75 {
  padding-left: 75rpx !important;
}
.u-m-t-75 {
  margin-top: 75rpx !important;
}
.u-p-t-75 {
  padding-top: 75rpx !important;
}
.u-margin-top-75 {
  margin-top: 75rpx !important;
}
.u-padding-top-75 {
  padding-top: 75rpx !important;
}
.u-m-r-75 {
  margin-right: 75rpx !important;
}
.u-p-r-75 {
  padding-right: 75rpx !important;
}
.u-margin-right-75 {
  margin-right: 75rpx !important;
}
.u-padding-right-75 {
  padding-right: 75rpx !important;
}
.u-m-b-75 {
  margin-bottom: 75rpx !important;
}
.u-p-b-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-bottom-75 {
  margin-bottom: 75rpx !important;
}
.u-padding-bottom-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-76, .u-m-76 {
  margin: 76rpx !important;
}
.u-padding-76, .u-p-76 {
  padding: 76rpx !important;
}
.u-m-l-76 {
  margin-left: 76rpx !important;
}
.u-p-l-76 {
  padding-left: 76rpx !important;
}
.u-margin-left-76 {
  margin-left: 76rpx !important;
}
.u-padding-left-76 {
  padding-left: 76rpx !important;
}
.u-m-t-76 {
  margin-top: 76rpx !important;
}
.u-p-t-76 {
  padding-top: 76rpx !important;
}
.u-margin-top-76 {
  margin-top: 76rpx !important;
}
.u-padding-top-76 {
  padding-top: 76rpx !important;
}
.u-m-r-76 {
  margin-right: 76rpx !important;
}
.u-p-r-76 {
  padding-right: 76rpx !important;
}
.u-margin-right-76 {
  margin-right: 76rpx !important;
}
.u-padding-right-76 {
  padding-right: 76rpx !important;
}
.u-m-b-76 {
  margin-bottom: 76rpx !important;
}
.u-p-b-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-bottom-76 {
  margin-bottom: 76rpx !important;
}
.u-padding-bottom-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-78, .u-m-78 {
  margin: 78rpx !important;
}
.u-padding-78, .u-p-78 {
  padding: 78rpx !important;
}
.u-m-l-78 {
  margin-left: 78rpx !important;
}
.u-p-l-78 {
  padding-left: 78rpx !important;
}
.u-margin-left-78 {
  margin-left: 78rpx !important;
}
.u-padding-left-78 {
  padding-left: 78rpx !important;
}
.u-m-t-78 {
  margin-top: 78rpx !important;
}
.u-p-t-78 {
  padding-top: 78rpx !important;
}
.u-margin-top-78 {
  margin-top: 78rpx !important;
}
.u-padding-top-78 {
  padding-top: 78rpx !important;
}
.u-m-r-78 {
  margin-right: 78rpx !important;
}
.u-p-r-78 {
  padding-right: 78rpx !important;
}
.u-margin-right-78 {
  margin-right: 78rpx !important;
}
.u-padding-right-78 {
  padding-right: 78rpx !important;
}
.u-m-b-78 {
  margin-bottom: 78rpx !important;
}
.u-p-b-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-bottom-78 {
  margin-bottom: 78rpx !important;
}
.u-padding-bottom-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-80, .u-m-80 {
  margin: 80rpx !important;
}
.u-padding-80, .u-p-80 {
  padding: 80rpx !important;
}
.u-m-l-80 {
  margin-left: 80rpx !important;
}
.u-p-l-80 {
  padding-left: 80rpx !important;
}
.u-margin-left-80 {
  margin-left: 80rpx !important;
}
.u-padding-left-80 {
  padding-left: 80rpx !important;
}
.u-m-t-80 {
  margin-top: 80rpx !important;
}
.u-p-t-80 {
  padding-top: 80rpx !important;
}
.u-margin-top-80 {
  margin-top: 80rpx !important;
}
.u-padding-top-80 {
  padding-top: 80rpx !important;
}
.u-m-r-80 {
  margin-right: 80rpx !important;
}
.u-p-r-80 {
  padding-right: 80rpx !important;
}
.u-margin-right-80 {
  margin-right: 80rpx !important;
}
.u-padding-right-80 {
  padding-right: 80rpx !important;
}
.u-m-b-80 {
  margin-bottom: 80rpx !important;
}
.u-p-b-80 {
  padding-bottom: 80rpx !important;
}
.u-margin-bottom-80 {
  margin-bottom: 80rpx !important;
}
.u-padding-bottom-80 {
  padding-bottom: 80rpx !important;
}
.u-reset-nvue {
  flex-direction: row;
  align-items: center;
}
.u-type-primary-light {
  color: #ecf5ff;
}
.u-type-warning-light {
  color: #fdf6ec;
}
.u-type-success-light {
  color: #dbf1e1;
}
.u-type-error-light {
  color: #fef0f0;
}
.u-type-info-light {
  color: #f4f4f5;
}
.u-type-primary-light-bg {
  background-color: #ecf5ff;
}
.u-type-warning-light-bg {
  background-color: #fdf6ec;
}
.u-type-success-light-bg {
  background-color: #dbf1e1;
}
.u-type-error-light-bg {
  background-color: #fef0f0;
}
.u-type-info-light-bg {
  background-color: #f4f4f5;
}
.u-type-primary-dark {
  color: #2b85e4;
}
.u-type-warning-dark {
  color: #f29100;
}
.u-type-success-dark {
  color: #18b566;
}
.u-type-error-dark {
  color: #dd6161;
}
.u-type-info-dark {
  color: #82848a;
}
.u-type-primary-dark-bg {
  background-color: #2b85e4;
}
.u-type-warning-dark-bg {
  background-color: #f29100;
}
.u-type-success-dark-bg {
  background-color: #18b566;
}
.u-type-error-dark-bg {
  background-color: #dd6161;
}
.u-type-info-dark-bg {
  background-color: #82848a;
}
.u-type-primary-disabled {
  color: #a0cfff;
}
.u-type-warning-disabled {
  color: #fcbd71;
}
.u-type-success-disabled {
  color: #71d5a1;
}
.u-type-error-disabled {
  color: #fab6b6;
}
.u-type-info-disabled {
  color: #c8c9cc;
}
.u-type-primary {
  color: #ff2c3c;
}
.u-type-warning {
  color: #ff9900;
}
.u-type-success {
  color: #19be6b;
}
.u-type-error {
  color: #fa3534;
}
.u-type-info {
  color: #909399;
}
.u-type-primary-bg {
  background-color: #ff2c3c;
}
.u-type-warning-bg {
  background-color: #ff9900;
}
.u-type-success-bg {
  background-color: #19be6b;
}
.u-type-error-bg {
  background-color: #fa3534;
}
.u-type-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909399;
}
.u-light-color {
  color: #c0c4cc;
}
page {
  color: #303133;
  font-size: 28rpx;
}
/* start--去除webkit的默认样式--start */
.u-fix-ios-appearance {
  -webkit-appearance: none;
}
/* end--去除webkit的默认样式--end */
/* start--icon图标外层套一个view，让其达到更好的垂直居中的效果--start */
.u-icon-wrap {
  display: flex;
  align-items: center;
}
/* end-icon图标外层套一个view，让其达到更好的垂直居中的效果--end */
/* start--iPhoneX底部安全区定义--start */
.safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
/* end-iPhoneX底部安全区定义--end */
/* start--各种hover点击反馈相关的类名-start */
.u-hover-class {
  opacity: 0.6;
}
.u-cell-hover {
  background-color: #f7f8f9 !important;
}
/* end--各种hover点击反馈相关的类名--end */
/* start--文本行数限制--start */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  -webkit-line-clamp: 2;
}
.u-line-3 {
  -webkit-line-clamp: 3;
}
.u-line-4 {
  -webkit-line-clamp: 4;
}
.u-line-5 {
  -webkit-line-clamp: 5;
}
.u-line-2, .u-line-3, .u-line-4, .u-line-5 {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
/* end--文本行数限制--end */
/* start--Retina 屏幕下的 1px 边框--start */
.u-border,
.u-border-bottom,
.u-border-left,
.u-border-right,
.u-border-top,
.u-border-top-bottom {
  position: relative;
}
.u-border-bottom:after,
.u-border-left:after,
.u-border-right:after,
.u-border-top-bottom:after,
.u-border-top:after,
.u-border:after {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  box-sizing: border-box;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  width: 199.8%;
  height: 199.7%;
  -webkit-transform: scale(0.5, 0.5);
          transform: scale(0.5, 0.5);
  border: 0 solid #e4e7ed;
  z-index: 2;
}
.u-border-top:after {
  border-top-width: 1px;
}
.u-border-left:after {
  border-left-width: 1px;
}
.u-border-right:after {
  border-right-width: 1px;
}
.u-border-bottom:after {
  border-bottom-width: 1px;
}
.u-border-top-bottom:after {
  border-width: 1px 0;
}
.u-border:after {
  border-width: 1px;
}
/* end--Retina 屏幕下的 1px 边框--end */
/* start--clearfix--start */
.u-clearfix:after,
.clearfix:after {
  content: '';
  display: table;
  clear: both;
}
/* end--clearfix--end */
/* start--高斯模糊tabbar底部处理--start */
.u-blur-effect-inset {
  width: 750rpx;
  height: 0px;
  background-color: #FFFFFF;
}
/* end--高斯模糊tabbar底部处理--end */
/* start--提升H5端uni.toast()的层级，避免被uView的modal等遮盖--start */
/* end--提升H5端uni.toast()的层级，避免被uView的modal等遮盖--end */
/* start--去除button的所有默认样式--start */
.u-reset-button {
  padding: 0;
  font-size: inherit;
  line-height: inherit;
  background-color: transparent;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
/* end--去除button的所有默认样式--end */
/* start--微信小程序编译后页面有组件名的元素，特别处理--start */
u-td, u-th {
  flex: 1;
  align-self: stretch;
}
.u-td {
  height: 100%;
}
u-icon {
  display: inline-flex;
  align-items: center;
}
u-grid {
  width: 100%;
  flex: 0 0 100%;
}
u-line {
  flex: 1;
}
u-switch {
  display: inline-flex;
  align-items: center;
}
u-dropdown {
  flex: 1;
}
/* end-微信小程序编译后页面有组件名的元素，特别处理--end */
/* start--头条小程序编译后页面有组件名的元素，特别处理--start */
/* end-头条小程序编译后页面有组件名的元素，特别处理--end */
page {
  /* 定义一些主题色及基础样式 */
  font-family: PingFang SC, Arial, Hiragino Sans GB, Microsoft YaHei, sans-serif;
  font-size: 28rpx;
  color: #333333;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #F6F6F6;
}
@font-face {
  font-family: 'Maoken Glitch Sans';
  src: url("https://file.foxdance.com.cn/ttf/Maoken_Glitch_Sans.subset.OTF") format("opentype");
}
@font-face {
  font-family: AaHouDiHei;
  src: url("https://file.foxdance.com.cn/ttf/AaHouDiHei.ttf") format("opentype");
}
@font-face {
  font-family: pht;
  src: url("https://file.foxdance.com.cn/ttf/pht.otf") format("opentype");
}
.bold {
  font-weight: bold;
}
/* 超出隐藏 */
.line1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.line2 {
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
/* 中划线 */
.line-through {
  text-decoration: line-through;
}
/* br60 */
.br60 {
  border-radius: 60rpx;
}
/* 初始化按钮 */
page button {
  padding: 0;
  margin: 0;
  background-color: transparent;
  font-weight: normal;
  font-size: 28rpx;
  overflow: unset;
  margin-left: 0;
  margin-right: 0;
}
page button::after {
  border: none;
}
button[type=primary] {
  background-color: #58B85C;
}
.button-hover[type=primary] {
  background-color: #58B85C;
}
/* 按钮大小 */
button[size="xs"] {
  line-height: 58rpx;
  height: 58rpx;
  font-size: 26rpx;
  padding: 0 30rpx;
}
button[size="sm"] {
  line-height: 62rpx;
  height: 62rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
}
button[size="md"] {
  line-height: 70rpx;
  height: 70rpx;
  font-size: 30rpx;
  padding: 0 30rpx;
}
button[size="lg"] {
  line-height: 80rpx;
  height: 80rpx;
  font-size: 32rpx;
  padding: 0 30rpx;
}
.icon-xs {
  min-height: 28rpx;
  min-width: 28rpx;
  height: 28rpx;
  width: 28rpx;
  vertical-align: middle;
}
.icon-sm {
  min-height: 30rpx;
  min-width: 30rpx;
  height: 30rpx;
  width: 30rpx;
  vertical-align: middle;
}
.icon {
  min-height: 34rpx;
  min-width: 34rpx;
  height: 34rpx;
  width: 34rpx;
  vertical-align: middle;
}
.icon-md {
  min-height: 44rpx;
  min-width: 44rpx;
  height: 44rpx;
  width: 44rpx;
  vertical-align: middle;
}
.icon-lg {
  min-height: 52rpx;
  min-width: 52rpx;
  height: 52rpx;
  width: 52rpx;
  vertical-align: middle;
}
.icon-xl {
  min-height: 64rpx;
  min-width: 64rpx;
  height: 64rpx;
  width: 64rpx;
  vertical-align: middle;
}
.icon-xxl {
  min-height: 120rpx;
  min-width: 120rpx;
  height: 120rpx;
  width: 120rpx;
  vertical-align: middle;
}
.img-null {
  width: 300rpx;
  height: 300rpx;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 单选 */
radio .uni-radio-input {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
}
radio .uni-radio-input {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
}
radio .uni-radio-input.uni-radio-input-checked {
  border: 1px solid #58B85C !important;
  background-color: #58B85C !important;
}
/* 多选 */
checkbox .uni-checkbox-input {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
}
checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  border: 1px solid #58B85C !important;
  background-color: #58B85C !important;
  color: #fff !important;
}
checkbox .uni-checkbox-input.uni-checkbox-input-checked::before {
  font-size: 35rpx;
}
/* 单选 */
radio .wx-radio-input {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
}
radio .wx-radio-input {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
}
radio .wx-radio-input.wx-radio-input-checked {
  border: 1px solid #58B85C !important;
  background-color: #58B85C !important;
}
/* 多选 */
checkbox .wx-checkbox-input {
  border-radius: 50%;
  width: 34rpx;
  height: 34rpx;
}
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #58B85C !important;
  background-color: #58B85C !important;
  color: #fff !important;
}
checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  font-size: 35rpx;
}
/* 定义字体颜色 */
.primary {
  color: #58B85C;
}
.color-9 {
  color: #999;
}
.black {
  color: #101010;
}
.white {
  color: #ffffff;
}
.normal {
  color: #333333;
}
.lighter {
  color: #666666;
}
.muted {
  color: #999999;
}
/* 定义背景颜色 */
.bg-primary {
  background: linear-gradient(90deg, #58B85C, #6DD572);
}
.bg-white {
  background-color: #ffffff;
}
.bg-body {
  background-color: #F6F6F6;
}
.bg-gray {
  background-color: #E5E5E5;
}
/* 定义字体大小 */
.xxl {
  font-size: 36rpx;
}
.xl {
  font-size: 34rpx;
}
.lg {
  font-size: 32rpx;
}
.md {
  font-size: 30rpx;
}
.nr {
  font-size: 28rpx;
}
.sm {
  font-size: 26rpx;
}
.xs {
  font-size: 24rpx;
}
.xxs {
  font-size: 22rpx;
}
.m-0 {
  margin: 0rpx;
}
.p-0 {
  padding: 0rpx;
}
.m-l-0 {
  margin-left: 0rpx;
}
.p-l-0 {
  padding-left: 0rpx;
}
.m-t-0 {
  margin-top: 0rpx;
}
.p-t-0 {
  padding-top: 0rpx;
}
.m-r-0 {
  margin-right: 0rpx;
}
.p-r-0 {
  padding-right: 0rpx;
}
.m-b-0 {
  margin-bottom: 0rpx;
}
.p-b-0 {
  padding-bottom: 0rpx;
}
.m-2 {
  margin: 2rpx;
}
.p-2 {
  padding: 2rpx;
}
.m-l-2 {
  margin-left: 2rpx;
}
.p-l-2 {
  padding-left: 2rpx;
}
.m-t-2 {
  margin-top: 2rpx;
}
.p-t-2 {
  padding-top: 2rpx;
}
.m-r-2 {
  margin-right: 2rpx;
}
.p-r-2 {
  padding-right: 2rpx;
}
.m-b-2 {
  margin-bottom: 2rpx;
}
.p-b-2 {
  padding-bottom: 2rpx;
}
.m-4 {
  margin: 4rpx;
}
.p-4 {
  padding: 4rpx;
}
.m-l-4 {
  margin-left: 4rpx;
}
.p-l-4 {
  padding-left: 4rpx;
}
.m-t-4 {
  margin-top: 4rpx;
}
.p-t-4 {
  padding-top: 4rpx;
}
.m-r-4 {
  margin-right: 4rpx;
}
.p-r-4 {
  padding-right: 4rpx;
}
.m-b-4 {
  margin-bottom: 4rpx;
}
.p-b-4 {
  padding-bottom: 4rpx;
}
.m-5 {
  margin: 5rpx;
}
.p-5 {
  padding: 5rpx;
}
.m-l-5 {
  margin-left: 5rpx;
}
.p-l-5 {
  padding-left: 5rpx;
}
.m-t-5 {
  margin-top: 5rpx;
}
.p-t-5 {
  padding-top: 5rpx;
}
.m-r-5 {
  margin-right: 5rpx;
}
.p-r-5 {
  padding-right: 5rpx;
}
.m-b-5 {
  margin-bottom: 5rpx;
}
.p-b-5 {
  padding-bottom: 5rpx;
}
.m-6 {
  margin: 6rpx;
}
.p-6 {
  padding: 6rpx;
}
.m-l-6 {
  margin-left: 6rpx;
}
.p-l-6 {
  padding-left: 6rpx;
}
.m-t-6 {
  margin-top: 6rpx;
}
.p-t-6 {
  padding-top: 6rpx;
}
.m-r-6 {
  margin-right: 6rpx;
}
.p-r-6 {
  padding-right: 6rpx;
}
.m-b-6 {
  margin-bottom: 6rpx;
}
.p-b-6 {
  padding-bottom: 6rpx;
}
.m-8 {
  margin: 8rpx;
}
.p-8 {
  padding: 8rpx;
}
.m-l-8 {
  margin-left: 8rpx;
}
.p-l-8 {
  padding-left: 8rpx;
}
.m-t-8 {
  margin-top: 8rpx;
}
.p-t-8 {
  padding-top: 8rpx;
}
.m-r-8 {
  margin-right: 8rpx;
}
.p-r-8 {
  padding-right: 8rpx;
}
.m-b-8 {
  margin-bottom: 8rpx;
}
.p-b-8 {
  padding-bottom: 8rpx;
}
.m-10 {
  margin: 10rpx;
}
.p-10 {
  padding: 10rpx;
}
.m-l-10 {
  margin-left: 10rpx;
}
.p-l-10 {
  padding-left: 10rpx;
}
.m-t-10 {
  margin-top: 10rpx;
}
.p-t-10 {
  padding-top: 10rpx;
}
.m-r-10 {
  margin-right: 10rpx;
}
.p-r-10 {
  padding-right: 10rpx;
}
.m-b-10 {
  margin-bottom: 10rpx;
}
.p-b-10 {
  padding-bottom: 10rpx;
}
.m-12 {
  margin: 12rpx;
}
.p-12 {
  padding: 12rpx;
}
.m-l-12 {
  margin-left: 12rpx;
}
.p-l-12 {
  padding-left: 12rpx;
}
.m-t-12 {
  margin-top: 12rpx;
}
.p-t-12 {
  padding-top: 12rpx;
}
.m-r-12 {
  margin-right: 12rpx;
}
.p-r-12 {
  padding-right: 12rpx;
}
.m-b-12 {
  margin-bottom: 12rpx;
}
.p-b-12 {
  padding-bottom: 12rpx;
}
.m-14 {
  margin: 14rpx;
}
.p-14 {
  padding: 14rpx;
}
.m-l-14 {
  margin-left: 14rpx;
}
.p-l-14 {
  padding-left: 14rpx;
}
.m-t-14 {
  margin-top: 14rpx;
}
.p-t-14 {
  padding-top: 14rpx;
}
.m-r-14 {
  margin-right: 14rpx;
}
.p-r-14 {
  padding-right: 14rpx;
}
.m-b-14 {
  margin-bottom: 14rpx;
}
.p-b-14 {
  padding-bottom: 14rpx;
}
.m-15 {
  margin: 15rpx;
}
.p-15 {
  padding: 15rpx;
}
.m-l-15 {
  margin-left: 15rpx;
}
.p-l-15 {
  padding-left: 15rpx;
}
.m-t-15 {
  margin-top: 15rpx;
}
.p-t-15 {
  padding-top: 15rpx;
}
.m-r-15 {
  margin-right: 15rpx;
}
.p-r-15 {
  padding-right: 15rpx;
}
.m-b-15 {
  margin-bottom: 15rpx;
}
.p-b-15 {
  padding-bottom: 15rpx;
}
.m-16 {
  margin: 16rpx;
}
.p-16 {
  padding: 16rpx;
}
.m-l-16 {
  margin-left: 16rpx;
}
.p-l-16 {
  padding-left: 16rpx;
}
.m-t-16 {
  margin-top: 16rpx;
}
.p-t-16 {
  padding-top: 16rpx;
}
.m-r-16 {
  margin-right: 16rpx;
}
.p-r-16 {
  padding-right: 16rpx;
}
.m-b-16 {
  margin-bottom: 16rpx;
}
.p-b-16 {
  padding-bottom: 16rpx;
}
.m-18 {
  margin: 18rpx;
}
.p-18 {
  padding: 18rpx;
}
.m-l-18 {
  margin-left: 18rpx;
}
.p-l-18 {
  padding-left: 18rpx;
}
.m-t-18 {
  margin-top: 18rpx;
}
.p-t-18 {
  padding-top: 18rpx;
}
.m-r-18 {
  margin-right: 18rpx;
}
.p-r-18 {
  padding-right: 18rpx;
}
.m-b-18 {
  margin-bottom: 18rpx;
}
.p-b-18 {
  padding-bottom: 18rpx;
}
.m-20 {
  margin: 20rpx;
}
.p-20 {
  padding: 20rpx;
}
.m-l-20 {
  margin-left: 20rpx;
}
.p-l-20 {
  padding-left: 20rpx;
}
.m-t-20 {
  margin-top: 20rpx;
}
.p-t-20 {
  padding-top: 20rpx;
}
.m-r-20 {
  margin-right: 20rpx;
}
.p-r-20 {
  padding-right: 20rpx;
}
.m-b-20 {
  margin-bottom: 20rpx;
}
.p-b-20 {
  padding-bottom: 20rpx;
}
.m-22 {
  margin: 22rpx;
}
.p-22 {
  padding: 22rpx;
}
.m-l-22 {
  margin-left: 22rpx;
}
.p-l-22 {
  padding-left: 22rpx;
}
.m-t-22 {
  margin-top: 22rpx;
}
.p-t-22 {
  padding-top: 22rpx;
}
.m-r-22 {
  margin-right: 22rpx;
}
.p-r-22 {
  padding-right: 22rpx;
}
.m-b-22 {
  margin-bottom: 22rpx;
}
.p-b-22 {
  padding-bottom: 22rpx;
}
.m-24 {
  margin: 24rpx;
}
.p-24 {
  padding: 24rpx;
}
.m-l-24 {
  margin-left: 24rpx;
}
.p-l-24 {
  padding-left: 24rpx;
}
.m-t-24 {
  margin-top: 24rpx;
}
.p-t-24 {
  padding-top: 24rpx;
}
.m-r-24 {
  margin-right: 24rpx;
}
.p-r-24 {
  padding-right: 24rpx;
}
.m-b-24 {
  margin-bottom: 24rpx;
}
.p-b-24 {
  padding-bottom: 24rpx;
}
.m-25 {
  margin: 25rpx;
}
.p-25 {
  padding: 25rpx;
}
.m-l-25 {
  margin-left: 25rpx;
}
.p-l-25 {
  padding-left: 25rpx;
}
.m-t-25 {
  margin-top: 25rpx;
}
.p-t-25 {
  padding-top: 25rpx;
}
.m-r-25 {
  margin-right: 25rpx;
}
.p-r-25 {
  padding-right: 25rpx;
}
.m-b-25 {
  margin-bottom: 25rpx;
}
.p-b-25 {
  padding-bottom: 25rpx;
}
.m-26 {
  margin: 26rpx;
}
.p-26 {
  padding: 26rpx;
}
.m-l-26 {
  margin-left: 26rpx;
}
.p-l-26 {
  padding-left: 26rpx;
}
.m-t-26 {
  margin-top: 26rpx;
}
.p-t-26 {
  padding-top: 26rpx;
}
.m-r-26 {
  margin-right: 26rpx;
}
.p-r-26 {
  padding-right: 26rpx;
}
.m-b-26 {
  margin-bottom: 26rpx;
}
.p-b-26 {
  padding-bottom: 26rpx;
}
.m-28 {
  margin: 28rpx;
}
.p-28 {
  padding: 28rpx;
}
.m-l-28 {
  margin-left: 28rpx;
}
.p-l-28 {
  padding-left: 28rpx;
}
.m-t-28 {
  margin-top: 28rpx;
}
.p-t-28 {
  padding-top: 28rpx;
}
.m-r-28 {
  margin-right: 28rpx;
}
.p-r-28 {
  padding-right: 28rpx;
}
.m-b-28 {
  margin-bottom: 28rpx;
}
.p-b-28 {
  padding-bottom: 28rpx;
}
.m-30 {
  margin: 30rpx;
}
.p-30 {
  padding: 30rpx;
}
.m-l-30 {
  margin-left: 30rpx;
}
.p-l-30 {
  padding-left: 30rpx;
}
.m-t-30 {
  margin-top: 30rpx;
}
.p-t-30 {
  padding-top: 30rpx;
}
.m-r-30 {
  margin-right: 30rpx;
}
.p-r-30 {
  padding-right: 30rpx;
}
.m-b-30 {
  margin-bottom: 30rpx;
}
.p-b-30 {
  padding-bottom: 30rpx;
}
.m-32 {
  margin: 32rpx;
}
.p-32 {
  padding: 32rpx;
}
.m-l-32 {
  margin-left: 32rpx;
}
.p-l-32 {
  padding-left: 32rpx;
}
.m-t-32 {
  margin-top: 32rpx;
}
.p-t-32 {
  padding-top: 32rpx;
}
.m-r-32 {
  margin-right: 32rpx;
}
.p-r-32 {
  padding-right: 32rpx;
}
.m-b-32 {
  margin-bottom: 32rpx;
}
.p-b-32 {
  padding-bottom: 32rpx;
}
.m-34 {
  margin: 34rpx;
}
.p-34 {
  padding: 34rpx;
}
.m-l-34 {
  margin-left: 34rpx;
}
.p-l-34 {
  padding-left: 34rpx;
}
.m-t-34 {
  margin-top: 34rpx;
}
.p-t-34 {
  padding-top: 34rpx;
}
.m-r-34 {
  margin-right: 34rpx;
}
.p-r-34 {
  padding-right: 34rpx;
}
.m-b-34 {
  margin-bottom: 34rpx;
}
.p-b-34 {
  padding-bottom: 34rpx;
}
.m-35 {
  margin: 35rpx;
}
.p-35 {
  padding: 35rpx;
}
.m-l-35 {
  margin-left: 35rpx;
}
.p-l-35 {
  padding-left: 35rpx;
}
.m-t-35 {
  margin-top: 35rpx;
}
.p-t-35 {
  padding-top: 35rpx;
}
.m-r-35 {
  margin-right: 35rpx;
}
.p-r-35 {
  padding-right: 35rpx;
}
.m-b-35 {
  margin-bottom: 35rpx;
}
.p-b-35 {
  padding-bottom: 35rpx;
}
.m-36 {
  margin: 36rpx;
}
.p-36 {
  padding: 36rpx;
}
.m-l-36 {
  margin-left: 36rpx;
}
.p-l-36 {
  padding-left: 36rpx;
}
.m-t-36 {
  margin-top: 36rpx;
}
.p-t-36 {
  padding-top: 36rpx;
}
.m-r-36 {
  margin-right: 36rpx;
}
.p-r-36 {
  padding-right: 36rpx;
}
.m-b-36 {
  margin-bottom: 36rpx;
}
.p-b-36 {
  padding-bottom: 36rpx;
}
.m-38 {
  margin: 38rpx;
}
.p-38 {
  padding: 38rpx;
}
.m-l-38 {
  margin-left: 38rpx;
}
.p-l-38 {
  padding-left: 38rpx;
}
.m-t-38 {
  margin-top: 38rpx;
}
.p-t-38 {
  padding-top: 38rpx;
}
.m-r-38 {
  margin-right: 38rpx;
}
.p-r-38 {
  padding-right: 38rpx;
}
.m-b-38 {
  margin-bottom: 38rpx;
}
.p-b-38 {
  padding-bottom: 38rpx;
}
.m-40 {
  margin: 40rpx;
}
.p-40 {
  padding: 40rpx;
}
.m-l-40 {
  margin-left: 40rpx;
}
.p-l-40 {
  padding-left: 40rpx;
}
.m-t-40 {
  margin-top: 40rpx;
}
.p-t-40 {
  padding-top: 40rpx;
}
.m-r-40 {
  margin-right: 40rpx;
}
.p-r-40 {
  padding-right: 40rpx;
}
.m-b-40 {
  margin-bottom: 40rpx;
}
.p-b-40 {
  padding-bottom: 40rpx;
}
.m-42 {
  margin: 42rpx;
}
.p-42 {
  padding: 42rpx;
}
.m-l-42 {
  margin-left: 42rpx;
}
.p-l-42 {
  padding-left: 42rpx;
}
.m-t-42 {
  margin-top: 42rpx;
}
.p-t-42 {
  padding-top: 42rpx;
}
.m-r-42 {
  margin-right: 42rpx;
}
.p-r-42 {
  padding-right: 42rpx;
}
.m-b-42 {
  margin-bottom: 42rpx;
}
.p-b-42 {
  padding-bottom: 42rpx;
}
.m-44 {
  margin: 44rpx;
}
.p-44 {
  padding: 44rpx;
}
.m-l-44 {
  margin-left: 44rpx;
}
.p-l-44 {
  padding-left: 44rpx;
}
.m-t-44 {
  margin-top: 44rpx;
}
.p-t-44 {
  padding-top: 44rpx;
}
.m-r-44 {
  margin-right: 44rpx;
}
.p-r-44 {
  padding-right: 44rpx;
}
.m-b-44 {
  margin-bottom: 44rpx;
}
.p-b-44 {
  padding-bottom: 44rpx;
}
.m-45 {
  margin: 45rpx;
}
.p-45 {
  padding: 45rpx;
}
.m-l-45 {
  margin-left: 45rpx;
}
.p-l-45 {
  padding-left: 45rpx;
}
.m-t-45 {
  margin-top: 45rpx;
}
.p-t-45 {
  padding-top: 45rpx;
}
.m-r-45 {
  margin-right: 45rpx;
}
.p-r-45 {
  padding-right: 45rpx;
}
.m-b-45 {
  margin-bottom: 45rpx;
}
.p-b-45 {
  padding-bottom: 45rpx;
}
.m-46 {
  margin: 46rpx;
}
.p-46 {
  padding: 46rpx;
}
.m-l-46 {
  margin-left: 46rpx;
}
.p-l-46 {
  padding-left: 46rpx;
}
.m-t-46 {
  margin-top: 46rpx;
}
.p-t-46 {
  padding-top: 46rpx;
}
.m-r-46 {
  margin-right: 46rpx;
}
.p-r-46 {
  padding-right: 46rpx;
}
.m-b-46 {
  margin-bottom: 46rpx;
}
.p-b-46 {
  padding-bottom: 46rpx;
}
.m-48 {
  margin: 48rpx;
}
.p-48 {
  padding: 48rpx;
}
.m-l-48 {
  margin-left: 48rpx;
}
.p-l-48 {
  padding-left: 48rpx;
}
.m-t-48 {
  margin-top: 48rpx;
}
.p-t-48 {
  padding-top: 48rpx;
}
.m-r-48 {
  margin-right: 48rpx;
}
.p-r-48 {
  padding-right: 48rpx;
}
.m-b-48 {
  margin-bottom: 48rpx;
}
.p-b-48 {
  padding-bottom: 48rpx;
}
.m-50 {
  margin: 50rpx;
}
.p-50 {
  padding: 50rpx;
}
.m-l-50 {
  margin-left: 50rpx;
}
.p-l-50 {
  padding-left: 50rpx;
}
.m-t-50 {
  margin-top: 50rpx;
}
.p-t-50 {
  padding-top: 50rpx;
}
.m-r-50 {
  margin-right: 50rpx;
}
.p-r-50 {
  padding-right: 50rpx;
}
.m-b-50 {
  margin-bottom: 50rpx;
}
.p-b-50 {
  padding-bottom: 50rpx;
}
.m-52 {
  margin: 52rpx;
}
.p-52 {
  padding: 52rpx;
}
.m-l-52 {
  margin-left: 52rpx;
}
.p-l-52 {
  padding-left: 52rpx;
}
.m-t-52 {
  margin-top: 52rpx;
}
.p-t-52 {
  padding-top: 52rpx;
}
.m-r-52 {
  margin-right: 52rpx;
}
.p-r-52 {
  padding-right: 52rpx;
}
.m-b-52 {
  margin-bottom: 52rpx;
}
.p-b-52 {
  padding-bottom: 52rpx;
}
.m-54 {
  margin: 54rpx;
}
.p-54 {
  padding: 54rpx;
}
.m-l-54 {
  margin-left: 54rpx;
}
.p-l-54 {
  padding-left: 54rpx;
}
.m-t-54 {
  margin-top: 54rpx;
}
.p-t-54 {
  padding-top: 54rpx;
}
.m-r-54 {
  margin-right: 54rpx;
}
.p-r-54 {
  padding-right: 54rpx;
}
.m-b-54 {
  margin-bottom: 54rpx;
}
.p-b-54 {
  padding-bottom: 54rpx;
}
.m-55 {
  margin: 55rpx;
}
.p-55 {
  padding: 55rpx;
}
.m-l-55 {
  margin-left: 55rpx;
}
.p-l-55 {
  padding-left: 55rpx;
}
.m-t-55 {
  margin-top: 55rpx;
}
.p-t-55 {
  padding-top: 55rpx;
}
.m-r-55 {
  margin-right: 55rpx;
}
.p-r-55 {
  padding-right: 55rpx;
}
.m-b-55 {
  margin-bottom: 55rpx;
}
.p-b-55 {
  padding-bottom: 55rpx;
}
.m-56 {
  margin: 56rpx;
}
.p-56 {
  padding: 56rpx;
}
.m-l-56 {
  margin-left: 56rpx;
}
.p-l-56 {
  padding-left: 56rpx;
}
.m-t-56 {
  margin-top: 56rpx;
}
.p-t-56 {
  padding-top: 56rpx;
}
.m-r-56 {
  margin-right: 56rpx;
}
.p-r-56 {
  padding-right: 56rpx;
}
.m-b-56 {
  margin-bottom: 56rpx;
}
.p-b-56 {
  padding-bottom: 56rpx;
}
.m-58 {
  margin: 58rpx;
}
.p-58 {
  padding: 58rpx;
}
.m-l-58 {
  margin-left: 58rpx;
}
.p-l-58 {
  padding-left: 58rpx;
}
.m-t-58 {
  margin-top: 58rpx;
}
.p-t-58 {
  padding-top: 58rpx;
}
.m-r-58 {
  margin-right: 58rpx;
}
.p-r-58 {
  padding-right: 58rpx;
}
.m-b-58 {
  margin-bottom: 58rpx;
}
.p-b-58 {
  padding-bottom: 58rpx;
}
.m-60 {
  margin: 60rpx;
}
.p-60 {
  padding: 60rpx;
}
.m-l-60 {
  margin-left: 60rpx;
}
.p-l-60 {
  padding-left: 60rpx;
}
.m-t-60 {
  margin-top: 60rpx;
}
.p-t-60 {
  padding-top: 60rpx;
}
.m-r-60 {
  margin-right: 60rpx;
}
.p-r-60 {
  padding-right: 60rpx;
}
.m-b-60 {
  margin-bottom: 60rpx;
}
.p-b-60 {
  padding-bottom: 60rpx;
}
.inline {
  display: inline-block;
}
/* 定义常用的弹性布局 */
.flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.col-baseline {
  align-items: baseline;
}
.col-center {
  align-items: center;
}
.col-top {
  align-items: flex-start;
}
.col-bottom {
  align-items: flex-end;
}
.col-stretch {
  align-items: stretch;
}
.row-center {
  justify-content: center;
}
.row-left {
  justify-content: flex-start;
}
.row-right {
  justify-content: flex-end;
}
.row-between {
  justify-content: space-between;
}
.row-around {
  justify-content: space-around;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-0 {
  flex: 0;
}
.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.flex-5 {
  flex: 5;
}
.flex-none {
  flex: none;
}
.font-size-38 {
  font-size: 38rpx;
}
.font-size-40 {
  font-size: 40rpx;
}
.font-size-42 {
  font-size: 42rpx;
}
.font-size-44 {
  font-size: 44rpx;
}
.font-size-46 {
  font-size: 46rpx;
}
.font-size-48 {
  font-size: 48rpx;
}
.font-size-50 {
  font-size: 50rpx;
}
.font-size-52 {
  font-size: 52rpx;
}
.font-size-54 {
  font-size: 54rpx;
}
.font-size-56 {
  font-size: 56rpx;
}
.font-size-58 {
  font-size: 58rpx;
}
.font-size-60 {
  font-size: 60rpx;
}
/* 超出隐藏 */
/* start--文本行数限制--start */
.line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.line-2 {
  -webkit-line-clamp: 2;
}
.line-3 {
  -webkit-line-clamp: 3;
}
.line-2, .line-3 {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
/* 中划线 */
.line-through {
  text-decoration: line-through;
}
/* br60 */
.br60 {
  border-radius: 60rpx;
}
/* 初始化按钮 */
page button {
  padding: 0;
  margin: 0;
  background-color: transparent;
  font-weight: normal;
  font-size: 28rpx;
  overflow: unset;
  margin-left: 0;
  margin-right: 0;
}
page button::after {
  border: none;
}
button[type=primary] {
  background-color: #58B85C;
}
.button-hover[type=primary] {
  background-color: #58B85C;
}
/* 按钮大小 */
button[size="xs"] {
  line-height: 58rpx;
  height: 58rpx;
  font-size: 26rpx;
  padding: 0 30rpx;
}
button[size="sm"] {
  line-height: 62rpx;
  height: 62rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
}
button[size="md"] {
  line-height: 70rpx;
  height: 70rpx;
  font-size: 30rpx;
  padding: 0 30rpx;
}
button[size="lg"] {
  line-height: 80rpx;
  height: 80rpx;
  font-size: 32rpx;
  padding: 0 30rpx;
}
.img-null {
  width: 300rpx;
  height: 300rpx;
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/*wsl 加载中 go*/
.gg_loding {
  margin: 25rpx 0;
}
.ball-clip-rotate > view {
  background-color: #fff;
  width: 15px;
  height: 15px;
  border-radius: 100%;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  border: 2px solid #919199;
  border-bottom-color: transparent;
  height: 25rpx;
  width: 25rpx;
  background: transparent !important;
  display: inline-block;
  -webkit-animation: rotate 0.75s 0s linear infinite;
  animation: rotate 0.75s 0s linear infinite;
}
@-webkit-keyframes rotate {
0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}
100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
}
}
@keyframes rotate {
0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}
100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
}
}
.gg_loding .ball-clip-rotate {
  display: flex;
  align-items: center;
  justify-content: center;
}
.gg_loding .ball-clip-rotate text {
  font-size: 28rpx;
  color: #919199;
  margin-left: 10rpx;
}
.gg_loding_wusj {
  font-size: 26rpx;
  color: #919199;
  text-align: center;
}
.gg_zwsj {
  margin-top: 32rpx;
}
.gg_zwsj_w {
  width: 100%;
  height: auto;
  overflow: hidden;
}
.gg_zwsj_w image {
  display: block;
  width: 300rpx;
  max-height: 300rpx;
  height: 500rpx;
  margin: auto;
}
.gg_zwsj_w text {
  display: block;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  margin-top: -20rpx;
  display: none;
}
.bwqCon .u-btn-picker.u-btn-tips {
  font-size: 32rpx !important;
}
.bwqCon .u-picker-header .flex.flex-col.row-center.col-center image {
  width: 46rpx !important;
  height: 46rpx !important;
}
.home .u-navbar-inner {
  padding-right: 0 !important;
}
.home .u-slot-content > view {
  justify-content: center !important;
}
.aqjlViw {
  width: 100%;
  height: calc(20rpx + env(safe-area-inset-bottom));
  overflow: hidden;
}
.address .empty-icon {
  display: block;
  width: 532rpx !important;
  height: 532rpx !important;
}
.address .empty-tip {
  color: #999 !important;
  margin-top: -30rpx !important;
}
/*wsl 加载中 end*/
page {
  background-color: #f6f6f6;
}
image {
  display: block;
}
/*每个页面公共css */
.btn {
  background-color: #131315;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  z-index: 11;
}
.inputRow {
  height: 110rpx;
  box-sizing: border-box;
  padding: 0 40rpx;
  background-color: #fff;
  border-bottom: 2rpx solid #f8f8f8;
}
.inputRow .laber {
  line-height: 110rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.inputRow .laber image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 14rpx;
  margin-left: 0;
}
.inputRow input {
  flex: 1;
  height: 100%;
  text-align: right;
  font-size: 30rpx;
  font-weight: 500;
  line-height: 110rpx;
}
.inputRow image {
  width: 16rpx;
  height: 28rpx;
  margin-left: 24rpx;
  margin-top: 4rpx;
}
.inputRow text {
  margin-left: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.inputRow .switch {
  width: 113rpx;
  height: 58rpx;
}
.inputRow textarea {
  width: 100%;
  height: 130rpx;
  font-size: 30rpx;
}
.prompt {
  width: 600rpx;
  height: 340rpx;
  background: #FFFFFF;
  box-shadow: 0 0 10rpx 0 rgba(228, 239, 244, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.prompt .prompt_t {
  box-sizing: border-box;
  padding: 22rpx 0 0 25rpx;
  display: flex;
  align-items: center;
}
.prompt .prompt_t image {
  display: block;
  width: 33rpx;
  height: 33rpx;
  margin-right: 24rpx;
}
.prompt .prompt_t .prompt_t_text {
  font-size: 26rpx;
  font-weight: 400;
  color: #E93B3D;
  line-height: 36rpx;
}
.prompt .prompt_c {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 40rpx;
  text-align: center;
}
.prompt .prompt_d {
  height: 113rpx;
  display: flex;
  border-top: 1rpx solid #d5d5d5;
}
.prompt .prompt_d .prompt_d_l {
  width: 50%;
  border-right: 1rpx solid #d5d5d5;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #8D8D8D;
  line-height: 113rpx;
}
.prompt .prompt_d .prompt_d_r {
  text-align: center;
  width: 50%;
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  line-height: 113rpx;
}

