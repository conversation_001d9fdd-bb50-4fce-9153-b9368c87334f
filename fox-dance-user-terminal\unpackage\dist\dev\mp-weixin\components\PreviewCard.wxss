
.product-card.data-v-b981d626 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 66rpx;
  overflow: visible; /* 改为visible使阴影可以超出容器 */
  background-color: #fff;
  position: relative;
  /* 添加阴影效果 */
  box-shadow: 0 25rpx 50rpx rgba(0,0,0,0.25);
  /* 确保阴影能在动画过程中正确显示 */
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  /* 增加z-index确保下拉时不被其他元素遮挡 */
  z-index: 1000;
}
.product-image-container.data-v-b981d626 {
  width: 85%;
  height: 45%;
  margin: 49rpx auto 0;
  overflow: hidden;
  position: relative;
  border-radius: 66rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.product-image.data-v-b981d626 {
  width: 100%; /* 设置为100%宽度填满容器 */
  height: 100%;
  object-fit: cover;
  border-radius: 66rpx;
  margin: 0;
  background-color: #fff;
  transition: -webkit-transform 0.1s ease;
  transition: transform 0.1s ease;
  transition: transform 0.1s ease, -webkit-transform 0.1s ease;
  -webkit-transform-origin: center center;
          transform-origin: center center;
}
.product-info.data-v-b981d626 {
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.product-name.data-v-b981d626 {
  font-size: 54rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.product-tag.data-v-b981d626 {
  font-size: 23rpx;
  font-weight: 500;
  color: #666;
  padding: 20rpx;
}

/* 下拉提示样式 */
.swipe-hint.data-v-b981d626 {
  margin-top: auto;
  margin-bottom: 56rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.swipe-hint-text.data-v-b981d626 {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
  margin-bottom: 10rpx;
}
.arrow-container.data-v-b981d626 {
  height: 30rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}
.arrow-down.data-v-b981d626 {
  width: 50rpx;
  height: 50rpx;
  -webkit-animation: bounce-data-v-b981d626 1.5s infinite ease-in-out;
          animation: bounce-data-v-b981d626 1.5s infinite ease-in-out;
}

/* 微信小程序兼容动画 */
.arrow-down.pulse.data-v-b981d626 {
  -webkit-animation: pulse-data-v-b981d626 0.8s infinite ease-in-out;
          animation: pulse-data-v-b981d626 0.8s infinite ease-in-out;
}
@-webkit-keyframes bounce-data-v-b981d626 {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
50% {
    -webkit-transform: translateY(6rpx);
            transform: translateY(6rpx);
}
100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes bounce-data-v-b981d626 {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
50% {
    -webkit-transform: translateY(6rpx);
            transform: translateY(6rpx);
}
100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes pulse-data-v-b981d626 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes pulse-data-v-b981d626 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}

