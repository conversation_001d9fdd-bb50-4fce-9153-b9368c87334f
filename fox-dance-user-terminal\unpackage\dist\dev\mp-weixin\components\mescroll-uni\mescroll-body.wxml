<wxs src="./wxs/wxs.wxs" module="wxsBiz"></wxs>
<view class="{{['mescroll-body','mescroll-render-touch',(sticky)?'mescorll-sticky':'']}}" style="{{'min-height:'+(minHeight)+';'+('padding-top:'+(padTop)+';')+('padding-bottom:'+(padBottom)+';')}}" change:prop="{{wxsBiz.propObserver}}" prop="{{wxsProp}}" bindtouchstart="{{wxsBiz.touchstartEvent}}" bindtouchmove="{{wxsBiz.touchmoveEvent}}" bindtouchend="{{wxsBiz.touchendEvent}}" bindtouchcancel="{{wxsBiz.touchendEvent}}"><block wx:if="{{topbar&&statusBarHeight}}"><view class="mescroll-topbar" style="{{'height:'+(statusBarHeight+'px')+';'+('background:'+(topbar)+';')}}"></view></block><view class="mescroll-body-content mescroll-wxs-content" style="{{'transform:'+(translateY)+';'+('transition:'+(transition)+';')}}" change:prop="{{wxsBiz.callObserver}}" prop="{{callProp}}"><block wx:if="{{mescroll.optDown.use}}"><view class="mescroll-downwarp" style="{{'background:'+(mescroll.optDown.bgColor)+';'+('color:'+(mescroll.optDown.textColor)+';')}}"><view class="downwarp-content"><view class="{{['downwarp-progress','mescroll-wxs-progress',(isDownLoading)?'mescroll-rotate':'']}}" style="{{'border-color:'+(mescroll.optDown.textColor)+';'+('transform:'+(downRotate)+';')}}"></view><view class="downwarp-tip">{{downText}}</view></view></view></block><slot></slot><block wx:if="{{mescroll.optUp.use&&!isDownLoading&&upLoadType!==2}}"><view class="mescroll-upwarp" style="{{'background:'+(mescroll.optUp.bgColor)+';'+('color:'+(mescroll.optUp.textColor)+';')}}"><view hidden="{{!(upLoadType===1)}}"><view class="upwarp-progress mescroll-rotate" style="{{'border-color:'+(mescroll.optUp.textColor)+';'}}"></view><view class="upwarp-tip">{{mescroll.optUp.textLoading}}</view></view></view></block></view><block wx:if="{{safearea}}"><view class="mescroll-safearea"></view></block><mescroll-top vue-id="440ed908-1" option="{{mescroll.optUp.toTop}}" value="{{isShowToTop}}" data-event-opts="{{[['^click',[['toTopClick']]],['^input',[['__set_model',['','isShowToTop','$event',[]]]]]]}}" bind:click="__e" bind:input="__e" bind:__l="__l"></mescroll-top><view change:prop="{{renderBiz.propObserver}}" prop="{{wxsProp}}"></view></view>