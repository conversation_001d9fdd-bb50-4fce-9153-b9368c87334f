@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 预览遮罩 - 全屏覆盖，点击关闭预览 */
.preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 997;
  background-color: transparent;
}

/* 全屏底层白色背景 */
.fullscreen-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FFFFFF;
  z-index: 997;
  animation: fadeIn 300ms ease;
  touch-action: none;
  transition: opacity 300ms ease-out;
}

/* 背景退场动画 */
.background-exit {
  opacity: 0;
}

/* 底部提示样式 */
.bottom-hint {
  width: 100%;
  position: absolute;
  bottom: 80rpx;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.fox-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  width: 100%;
  padding: 10rpx 0;
}
.fox-line {
  width: 125rpx;
  height: 1px;
  background-color: #CCCCCC;
  margin: 0 16rpx;
}
.fox-text {
  font-size: 26rpx;
  color: #666666;
  letter-spacing: 2rpx;
  font-weight: 500;
}
.fox-subtext {
  font-size: 22rpx;
  color: #999999;
  letter-spacing: 0.5rpx;
}
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}

/* 整体卡片容器 - 包含卡片和按钮 */
.card-container-with-buttons {
  position: fixed;
  top: 56%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* 修改为-50%确保垂直居中 */
  width: 100%;
  margin-top: 0;
  /* 移除上边距，让transform居中生效 */
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999;
  /* 确保卡片在点击层(998)之上 */
  will-change: transform, opacity;
  height: auto;
  opacity: 0;
  pointer-events: auto;
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
  touch-action: none;
  overflow: visible;
  transform: translate(-50%, -50%) translateY(20px);
  /* 修改为-50%确保垂直居中 */
}
.card-container-with-buttons.show {
  opacity: 1;
  transform: translate(-50%, -50%) translateY(0);
  /* 向上淡入浮现 */
  pointer-events: auto;
  overflow: visible;
}

/* swiper样式 */
.card-swiper {
  width: 100%;
  height: 73vh;
  touch-action: pan-x pan-y;
  overflow: visible;
  /* 确保卡片可以超出容器 */
  position: relative;
  top: -55rpx;
  z-index: 1000;
  background-color: transparent;
  /* 蓝色调试背景 */
}
.card-swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  top: -90rpx;
  /* 改为0，让其位于垂直中心 */
  box-sizing: border-box;
  transform: scale(1);
  /* 为阴影添加额外空间 */
  overflow: visible;
  /* 确保卡片可以超出容器 */
}
.card-item {
  width: 82%;
  height: 100%;
  max-height: 900rpx;
  display: inline-block;
  border-radius: 66rpx;
  box-shadow: none;
  /* 移除阴影效果 */
  transition: all 0.3s ease;
  transform: scale(0.85) translateY(5rpx);
  opacity: 0.9;
  position: relative;
  /* 确保为绝对定位的子元素提供参考 */
  z-index: 1002;
  /* 提高卡片的z-index确保在背景区域之上 */
  overflow: visible;
  /* 改为visible使阴影可以超出容器 */
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}
.card-item.active-card {
  transform: scale(1) translateY(-15rpx);
  opacity: 1;
  box-shadow: none;
  overflow: visible;
  z-index: 1003;
}
.card-item.near-active {
  transform: scale(0.9) translateY(0);
  opacity: 0.9;
  box-shadow: none;
}

/* 修改swiper组件样式，确保其不会裁剪子元素 */
.card-swiper .wx-swiper-dots {
  position: relative;
  z-index: 98;
}

/* TabBar样式，包含入场和退场动画 */
.tab-bar {
  display: flex;
  justify-content: center;
  align-items: end;
  width: 702rpx;
  height: 170rpx;
  height: 98rpx;
  border-radius: 66rpx;
  z-index: 996;
  position: fixed;
  bottom: 56rpx;
  left: 50%;
  transform: translateX(-50%) translateY(0);
  opacity: 1;
  transition: transform 400ms ease-in-out, opacity 400ms ease-in-out;
  will-change: transform, opacity;
}
.tab-bar .tab_bgi {
  position: absolute;
  z-index: -1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(data:image/png;base64,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);
  background-size: 100% 100%;
}
.tab-bar .tab-bar-item {
  flex: 1;
  height: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.tab-bar .tab-bar-item:nth-child(4) {
  padding-top: 0;
}
.tab-bar .tab-bar-item .tab_img {
  width: 44rpx;
  height: 44rpx;
  display: block;
  margin: auto;
}
.tab-bar .tab-bar-item .tab_text {
  margin-top: 2rpx;
  color: #333;
  font-size: 26rpx;
  color: #945048;
  line-height: 30rpx;
  text-align: center;
}

/* TabBar退出动画 */
.tab-bar-exit {
  transform: translateX(-50%) translateY(100%);
  /* 向下淡出位移 */
  opacity: 0;
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画持续400ms */
  will-change: transform, opacity;
}

/* TabBar返回动画 */
.tab-bar-enter {
  transform: translateX(-50%) translateY(0);
  /* 向上淡入复位 */
  opacity: 1;
  transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
  /* 确保动画持续400ms */
  will-change: transform, opacity;
}

/* 添加图标可见的样式 */
.tab-bar-icon-visible .tab-bar-item:nth-child(3) {
  opacity: 1 !important;
  z-index: 998;
}

/* 固定显示的tab图标 */
.tab-fixed-visible {
  position: relative;
  z-index: 998;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* 卡片通用样式 */
.card-preview-container {
  background: #FFF;
  border-radius: 66rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transform-style: preserve-3d;
  user-select: none;
  touch-action: pan-x pan-y;
  overflow: visible;
  /* 改为visible使阴影可以超出容器 */
  position: relative;
  z-index: 1002;
  /* 提高卡片的z-index确保在点击层之上 */
  /* 确保显示在白色背景之上 */
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 156rpx;
  left: 0;
  right: 0;
  width: 100%;
  height: 100rpx;
  z-index: 998;
}
.header-image {
  width: 100rpx;
  height: 100rpx;
}
.header-left image {
  width: 48rpx;
  height: 48rpx;
  padding-left: 20rpx;
}
.header-right image {
  width: 48rpx;
  height: 48rpx;
  padding-right: 20rpx;
}

/* 简单稳定的分页指示器样式 */
.page-dots {
  position: fixed;
  bottom: 120rpx;
  left: 0;
  width: 100%;
  height: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: -1;
}
.page-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin: 0 10rpx;
}

/* 卡片退场动画 - 更自然的淡出效果 */
.card-container-with-buttons.exit {
  opacity: 0;
  transform: translate(-50%, -50%);
  transition: opacity 300ms ease-out;
}

