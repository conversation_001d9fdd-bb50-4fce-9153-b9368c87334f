@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.u-drawer.data-v-67a9dfc6 {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.u-drawer-content.data-v-67a9dfc6 {
  display: block;
  position: absolute;
  z-index: 1003;
  transition: all 0.25s linear;
}
.u-drawer__scroll-view.data-v-67a9dfc6 {
  width: 100%;
  height: 100%;
}
.u-drawer-left.data-v-67a9dfc6 {
  top: 0;
  bottom: 0;
  left: 0;
  background-color: #ffffff;
}
.u-drawer-right.data-v-67a9dfc6 {
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #ffffff;
}
.u-drawer-top.data-v-67a9dfc6 {
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
}
.u-drawer-bottom.data-v-67a9dfc6 {
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
}
.u-drawer-center.data-v-67a9dfc6 {
  display: flex;
  flex-direction: row;
  flex-direction: column;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  opacity: 0;
  z-index: 99999;
}
.u-mode-center-box.data-v-67a9dfc6 {
  min-width: 100rpx;
  min-height: 100rpx;
  display: block;
  position: relative;
  background-color: #ffffff;
}
.u-drawer-content-visible.u-drawer-center.data-v-67a9dfc6 {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}
.u-animation-zoom.data-v-67a9dfc6 {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.u-drawer-content-visible.data-v-67a9dfc6 {
  -webkit-transform: translate3D(0px, 0px, 0px) !important;
          transform: translate3D(0px, 0px, 0px) !important;
}
.u-close.data-v-67a9dfc6 {
  position: absolute;
  z-index: 3;
}
.u-close--top-left.data-v-67a9dfc6 {
  top: 30rpx;
  left: 30rpx;
}
.u-close--top-right.data-v-67a9dfc6 {
  top: 30rpx;
  right: 30rpx;
}
.u-close--bottom-left.data-v-67a9dfc6 {
  bottom: 30rpx;
  left: 30rpx;
}
.u-close--bottom-right.data-v-67a9dfc6 {
  right: 30rpx;
  bottom: 30rpx;
}

