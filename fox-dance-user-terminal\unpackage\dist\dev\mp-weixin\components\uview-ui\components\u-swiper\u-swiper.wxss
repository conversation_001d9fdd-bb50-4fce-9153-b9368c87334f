@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.u-swiper-wrap.data-v-10d34c9a {
  position: relative;
  overflow: hidden;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.u-swiper-image.data-v-10d34c9a {
  width: 100%;
  will-change: transform;
  height: 100%;
  display: block;
}
.u-swiper-indicator.data-v-10d34c9a {
  padding: 0 24rpx;
  position: absolute;
  display: flex;
  flex-direction: row;
  width: 100%;
  z-index: 1;
}
.u-indicator-item-rect.data-v-10d34c9a {
  width: 26rpx;
  height: 8rpx;
  margin: 0 6rpx;
  transition: all 0.5s;
  background-color: rgba(0, 0, 0, 0.3);
}
.u-indicator-item-rect-active.data-v-10d34c9a {
  background-color: rgba(255, 255, 255, 0.8);
}
.u-indicator-item-dot.data-v-10d34c9a {
  width: 14rpx;
  height: 14rpx;
  margin: 0 6rpx;
  border-radius: 20rpx;
  transition: all 0.5s;
  background-color: rgba(0, 0, 0, 0.3);
}
.u-indicator-item-dot-active.data-v-10d34c9a {
  background-color: rgba(255, 255, 255, 0.8);
}
.u-indicator-item-round.data-v-10d34c9a {
  width: 14rpx;
  height: 14rpx;
  margin: 0 6rpx;
  border-radius: 20rpx;
  transition: all 0.5s;
  background-color: rgba(0, 0, 0, 0.3);
}
.u-indicator-item-round-active.data-v-10d34c9a {
  width: 34rpx;
  background-color: rgba(255, 255, 255, 0.8);
}
.u-indicator-item-number.data-v-10d34c9a {
  padding: 6rpx 16rpx;
  line-height: 1;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 100rpx;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.u-list-scale.data-v-10d34c9a {
  -webkit-transform-origin: center center;
          transform-origin: center center;
}
.u-list-image-wrap.data-v-10d34c9a {
  width: 100%;
  height: 100%;
  flex: 1;
  transition: all 0.5s;
  overflow: hidden;
  box-sizing: content-box;
  position: relative;
}
.u-swiper-title.data-v-10d34c9a {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
  bottom: 0;
  left: 0;
  width: 100%;
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  color: rgba(255, 255, 255, 0.9);
}
.u-swiper-item.data-v-10d34c9a {
  display: flex;
  flex-direction: row;
  overflow: hidden;
  align-items: center;
}

