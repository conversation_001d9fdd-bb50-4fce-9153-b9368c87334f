@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* ========================================
   共通样式文件 - Xiaohongshu风格设计语言
   适用于pagesSub/switch/和pagesSub/store/目录
   ======================================== */
/* ========================================
   1. 主题色彩变量 - Xiaohongshu风格
   ======================================== */
/* 基础颜色 */
/* 阴影效果 */
/* 动画变量 */
/* ========================================
   2. 容器和布局样式
   ======================================== */
.container.data-v-daf52aa6 {
  padding: 0 24rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
  min-height: 100vh;
}
/* 移除点击高亮效果 */
.no-highlight.data-v-daf52aa6 {
  -webkit-tap-highlight-color: transparent;
}
/* ========================================
   3. 卡片基础样式
   ======================================== */
.card.data-v-daf52aa6 {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.2s ease-out, -webkit-transform 0.2s ease-out;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out, -webkit-transform 0.2s ease-out;
}
.card.data-v-daf52aa6:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}
/* 小红书风格卡片 */
.xiaohongshu-card.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.xiaohongshu-card.data-v-daf52aa6:active {
  -webkit-transform: translateY(-2rpx) scale(0.98);
          transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 12rpx 36rpx rgba(255, 105, 135, 0.15);
}
/* 话题卡片样式 */
.topic-card.data-v-daf52aa6 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}
.topic-card.data-v-daf52aa6:active {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}
/* 店铺卡片样式 */
.store-card.data-v-daf52aa6, .store-list .store-card.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 30rpx 20rpx 25rpx 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 48%;
}
.store-card.data-v-daf52aa6:active {
  -webkit-transform: translateY(-4rpx) scale(0.98);
          transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 16rpx 48rpx rgba(255, 105, 135, 0.2);
}
/* ========================================
   4. 按钮样式
   ======================================== */
.btn-primary.data-v-daf52aa6 {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  color: white;
  font-size: 32rpx;
  border: none;
  border-radius: 44rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease-out;
  position: relative;
  overflow: hidden;
}
.btn-primary.data-v-daf52aa6:not(.btn-disabled):active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 135, 0.4);
}
.btn-primary.btn-disabled.data-v-daf52aa6 {
  background: #e9ecef;
  color: #b2bec3;
  box-shadow: none;
}
.btn-primary.btn-success.data-v-daf52aa6 {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  -webkit-animation: successPulse-data-v-daf52aa6 0.6s ease-out;
          animation: successPulse-data-v-daf52aa6 0.6s ease-out;
}
/* 浮动按钮 */
.floating-btn.data-v-daf52aa6 {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.4);
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
}
.floating-btn.data-v-daf52aa6:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.4);
}
.floating-btn .icon.data-v-daf52aa6 {
  font-size: 68rpx;
  color: #ffffff;
  font-weight: bold;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
/* ========================================
   5. 搜索栏样式
   ======================================== */
.search-bar.data-v-daf52aa6 {
  padding: 24rpx 0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 238, 248, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
}
.search-bar .search-box.data-v-daf52aa6 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}
.search-bar .search-box.data-v-daf52aa6:focus-within {
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
  border-color: rgba(255, 107, 135, 0.3);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.search-bar .search-box .u-icon.data-v-daf52aa6 {
  margin-right: 16rpx;
  color: #ff6b87;
}
.search-bar .search-box input.data-v-daf52aa6 {
  flex: 1;
  font-size: 30rpx;
  color: #4a4a4a;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}
.search-bar .search-box .clear-icon.data-v-daf52aa6 {
  margin-left: 10rpx;
  padding: 10rpx;
  opacity: 0.7;
  transition: all 0.2s ease;
}
.search-bar .search-box .clear-icon.data-v-daf52aa6:active {
  opacity: 1;
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
/* ========================================
   6. 筛选栏样式
   ======================================== */
.filter-bar.data-v-daf52aa6 {
  padding: 20rpx 0 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.filter-bar .sort-options .van-tabs.data-v-daf52aa6 {
  position: relative;
  display: flex;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}
.filter-bar .sort-options .van-tabs__wrap.data-v-daf52aa6 {
  overflow: hidden;
  position: relative;
  padding: 0;
  border-radius: 48rpx;
}
.filter-bar .sort-options .van-tabs__nav.data-v-daf52aa6 {
  position: relative;
  display: flex;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  height: 96rpx;
  border-radius: 48rpx;
  -webkit-user-select: none;
          user-select: none;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  padding: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
}
.filter-bar .sort-options .van-tab.data-v-daf52aa6 {
  cursor: pointer;
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  color: #8a8a8a;
  font-weight: 500;
  border-radius: 40rpx;
  transition: all 0.3s ease-out;
  font-size: 28rpx;
  height: 80rpx;
  letter-spacing: 0.3rpx;
}
.filter-bar .sort-options .van-tab.data-v-daf52aa6:active {
  background: rgba(255, 107, 135, 0.1);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.filter-bar .sort-options .van-tab--active.data-v-daf52aa6 {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
  -webkit-transform: translateY(-2rpx) scale(1.02);
          transform: translateY(-2rpx) scale(1.02);
}
.filter-bar .sort-options .van-tab--active .van-tab__text.data-v-daf52aa6 {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
/* ========================================
   7. 加载状态样式
   ======================================== */
.loading.data-v-daf52aa6 {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading .loading-text.data-v-daf52aa6 {
  margin-top: 32rpx;
  font-size: 30rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
/* 空状态样式 */
.empty-list.data-v-daf52aa6 {
  padding: 160rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
  border-radius: 32rpx;
  margin: 24rpx 0;
}
.empty-list .empty-image.data-v-daf52aa6 {
  width: 280rpx;
  height: 280rpx;
  opacity: 0.6;
  border-radius: 24rpx;
}
.empty-list .empty-text.data-v-daf52aa6 {
  margin-top: 40rpx;
  font-size: 36rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}
/* ========================================
   8. 表单和输入框样式
   ======================================== */
.form-item.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease-out;
  -webkit-animation: fadeInUp-data-v-daf52aa6 0.6s ease-out;
          animation: fadeInUp-data-v-daf52aa6 0.6s ease-out;
}
.form-item.data-v-daf52aa6:focus-within {
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
  border-color: rgba(255, 107, 135, 0.3);
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
}
.form-item .form-label.data-v-daf52aa6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 20rpx;
  display: block;
  letter-spacing: 0.3rpx;
}
.form-item .input.data-v-daf52aa6 {
  width: 100%;
  font-size: 30rpx;
  color: #4a4a4a;
  background: transparent;
  border: none;
  outline: none;
  font-weight: 400;
  letter-spacing: 0.3rpx;
  line-height: 1.6;
}
.form-item .input.data-v-daf52aa6::-webkit-input-placeholder {
  color: #b2bec3;
  font-weight: 400;
}
.form-item .input.data-v-daf52aa6::placeholder {
  color: #b2bec3;
  font-weight: 400;
}
.form-item .textarea.data-v-daf52aa6 {
  width: 92%;
  height: 360rpx;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 28rpx;
  font-size: 30rpx;
  color: #4a4a4a;
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);
  transition: all 0.3s ease;
  line-height: 1.7;
  letter-spacing: 0.3rpx;
}
.form-item .textarea.data-v-daf52aa6:focus {
  border-color: rgba(255, 107, 135, 0.5);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
/* ========================================
   9. 文字和排版样式
   ======================================== */
.page-title.data-v-daf52aa6 {
  font-size: 30rpx;
  font-weight: 600;
  text-align: center;
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  letter-spacing: 0.5rpx;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.page-title.data-v-daf52aa6 {
    color: #ff6b87 !important;
    background: none;
}
}
.section-title.data-v-daf52aa6, .store-list-title.data-v-daf52aa6 {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
  text-align: center;
}
.text.data-v-daf52aa6 {
  font-size: 30rpx;
  line-height: 1.8;
  margin-bottom: 20rpx;
  color: #4a4a4a;
  word-break: break-all;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}
.expand-btn.data-v-daf52aa6 {
  color: #ff6b87;
  font-size: 26rpx;
  display: inline-block;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.expand-btn.data-v-daf52aa6:active {
  background: rgba(255, 107, 135, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 渐变文字效果 - 微信小程序兼容版本 */
.gradient-text.data-v-daf52aa6 {
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.gradient-text.data-v-daf52aa6 {
    color: #ff6b87 !important;
    background: none;
}
}
/* ========================================
   10. 动画效果
   ======================================== */
@-webkit-keyframes fadeInUp-data-v-daf52aa6 {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-daf52aa6 {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes successPulse-data-v-daf52aa6 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes successPulse-data-v-daf52aa6 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@-webkit-keyframes shimmer-data-v-daf52aa6 {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
@keyframes shimmer-data-v-daf52aa6 {
0% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
100% {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
}
@-webkit-keyframes progressSlide-data-v-daf52aa6 {
from {
    width: 0;
}
}
@keyframes progressSlide-data-v-daf52aa6 {
from {
    width: 0;
}
}
/* ========================================
   11. 特效样式
   ======================================== */
/* 毛玻璃效果 */
.glass-effect.data-v-daf52aa6 {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}
/* 悬浮阴影效果 */
.floating-shadow.data-v-daf52aa6 {
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);
  transition: box-shadow 0.3s ease;
}
/* 性能优化的毛玻璃效果 */
.glass-effect-optimized.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.9);
}
/* ========================================
   12. 用户头像和图标样式
   ======================================== */
.user-avatar.data-v-daf52aa6 {
  position: relative;
  margin-right: 28rpx;
}
.user-avatar .avatar-image.data-v-daf52aa6 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.2);
}
.user-avatar .level-badge.data-v-daf52aa6 {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  font-size: 18rpx;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  padding: 0 8rpx;
}
.store-icon.data-v-daf52aa6 {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
}
/* ========================================
   13. 响应式和适配样式
   ======================================== */
/* 小屏幕适配 */
@media screen and (max-width: 750rpx) {
.container.data-v-daf52aa6 {
    padding: 0 16rpx;
}
.xiaohongshu-card.data-v-daf52aa6 {
    padding: 24rpx;
}
.form-item.data-v-daf52aa6 {
    padding: 32rpx 24rpx;
}
}
.filter-bar .sort-options .van-tab.data-v-daf52aa6 {
  min-width: 180rpx;
  margin: 0 6rpx;
}
.filter-bar .sort-options .van-tab__text.data-v-daf52aa6 {
  font-size: 30rpx;
  padding: 0 24rpx;
}
.store-list.data-v-daf52aa6 {
  padding-bottom: 48rpx;
  display: flex;
  flex-wrap: wrap;
}
.store-list .store-card.data-v-daf52aa6:active {
  -webkit-transform: translateY(-4rpx) scale(0.98);
          transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
}
.store-list .store-card.data-v-daf52aa6:nth-child(odd) {
  margin-right: 4%;
}
.store-list .store-card .store-card-content.data-v-daf52aa6 {
  display: flex;
  align-items: center;
}
.store-list .store-card .store-card-content .store-icon.data-v-daf52aa6 {
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  overflow: hidden;
}
.store-list .store-card .store-card-content .store-icon .icon.data-v-daf52aa6 {
  display: block;
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  object-fit: cover;
  transition: all 0.3s ease;
}
.store-list .store-card .store-card-content .store-name.data-v-daf52aa6 {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  letter-spacing: 0.3rpx;
}
.store-list .store-card .store-card-content .store-status.data-v-daf52aa6 {
  font-size: 24rpx;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

