@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.container.data-v-daf52aa6 {
  padding: 0 24rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
  min-height: 100vh;
}
.store-list-title.data-v-daf52aa6 {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
  text-align: center;
}
.search-bar.data-v-daf52aa6 {
  padding: 24rpx 0;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 238, 248, 0.95);
  backdrop-filter: blur(20rpx);
}
.search-bar .search-box.data-v-daf52aa6 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}
.search-bar .search-box.data-v-daf52aa6:focus-within {
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
  border-color: rgba(255, 107, 135, 0.3);
  transform: translateY(-2rpx);
}
.search-bar .search-box .u-icon.data-v-daf52aa6 {
  margin-right: 16rpx;
  color: #ff6b87;
}
.search-bar .search-box input.data-v-daf52aa6 {
  flex: 1;
  font-size: 30rpx;
  color: #4a4a4a;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}
.search-bar .search-box .clear-icon.data-v-daf52aa6 {
  margin-left: 10rpx;
  padding: 10rpx;
  opacity: 0.7;
  transition: all 0.2s ease;
}
.search-bar .search-box .clear-icon.data-v-daf52aa6:active {
  opacity: 1;
  transform: scale(1.1);
}
.filter-bar.data-v-daf52aa6 {
  padding: 20rpx 0 32rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.filter-bar .sort-options .van-tabs.data-v-daf52aa6 {
  position: relative;
  display: flex;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}
.filter-bar .sort-options .van-tabs__wrap.data-v-daf52aa6 {
  overflow: hidden;
  position: relative;
  padding: 0;
  border-radius: 48rpx;
}
.filter-bar .sort-options .van-tabs__nav.data-v-daf52aa6 {
  position: relative;
  display: flex;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  height: 96rpx;
  border-radius: 48rpx;
  user-select: none;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  padding: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
}
.filter-bar .sort-options .van-tab.data-v-daf52aa6 {
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  min-width: 180rpx;
  height: 80rpx;
  margin: 0 6rpx;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.filter-bar .sort-options .van-tab__text.data-v-daf52aa6 {
  font-size: 30rpx;
  color: #8a8a8a;
  line-height: 1.2;
  padding: 0 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
}
.filter-bar .sort-options .van-tab--active.data-v-daf52aa6 {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
  transform: translateY(-2rpx) scale(1.02);
}
.filter-bar .sort-options .van-tab--active .van-tab__text.data-v-daf52aa6 {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}
.store-list.data-v-daf52aa6 {
  padding-bottom: 48rpx;
  display: flex;
  flex-wrap: wrap;
}
.store-list .loading.data-v-daf52aa6 {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.store-list .loading .loading-text.data-v-daf52aa6 {
  margin-top: 32rpx;
  font-size: 30rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.store-list .empty-list.data-v-daf52aa6 {
  padding: 160rpx 40rpx;
  display: flex;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
  border-radius: 32rpx;
  margin: 24rpx 0;
}
.store-list .empty-list .empty-image.data-v-daf52aa6 {
  width: 280rpx;
  height: 280rpx;
  opacity: 0.6;
  border-radius: 24rpx;
}
.store-list .empty-list .empty-text.data-v-daf52aa6 {
  margin-top: 40rpx;
  font-size: 36rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}
.store-list .store-card.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 30rpx 20rpx 25rpx 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 48%;
}
.store-list .store-card.data-v-daf52aa6:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.15);
}
.store-list .store-card.data-v-daf52aa6:nth-child(odd) {
  margin-right: 4%;
}
.store-list .store-card .store-card-content.data-v-daf52aa6 {
  display: flex;
  align-items: center;
}
.store-list .store-card .store-card-content .store-icon.data-v-daf52aa6 {
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  overflow: hidden;
}
.store-list .store-card .store-card-content .store-icon .icon.data-v-daf52aa6 {
  display: block;
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  object-fit: cover;
  transition: all 0.3s ease;
}
.store-list .store-card .store-card-content .store-name.data-v-daf52aa6 {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  letter-spacing: 0.3rpx;
}
.store-list .store-card .store-card-content .store-status.data-v-daf52aa6 {
  font-size: 24rpx;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

