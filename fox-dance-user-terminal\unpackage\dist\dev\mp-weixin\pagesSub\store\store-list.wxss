@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.store-list-container.data-v-daf52aa6 {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b87 0%, #a8727e 100%);
  padding: 40rpx 30rpx;
}
.page-header.data-v-daf52aa6 {
  text-align: center;
  margin-bottom: 60rpx;
}
.page-header .title.data-v-daf52aa6 {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}
.page-header .subtitle.data-v-daf52aa6 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  opacity: 0.8;
}
.loading-container.data-v-daf52aa6 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.store-grid.data-v-daf52aa6 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 60rpx;
}
.store-card.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.store-card.data-v-daf52aa6:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}
.store-card-content.data-v-daf52aa6 {
  text-align: center;
}
.store-card-content .store-icon.data-v-daf52aa6 {
  margin-bottom: 20rpx;
}
.store-card-content .store-icon .icon.data-v-daf52aa6 {
  font-size: 60rpx;
  display: block;
}
.store-card-content .store-name.data-v-daf52aa6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  word-break: break-all;
}
.store-card-content .store-status.data-v-daf52aa6 {
  font-size: 24rpx;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}
.empty-state.data-v-daf52aa6 {
  grid-column: 1 / -1;
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state .empty-icon.data-v-daf52aa6 {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-state .empty-text.data-v-daf52aa6 {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
  font-weight: bold;
}
.empty-state .empty-desc.data-v-daf52aa6 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}
.refresh-container.data-v-daf52aa6 {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.refresh-btn.data-v-daf52aa6 {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}
.refresh-btn.data-v-daf52aa6:active {
  background: rgba(255, 255, 255, 0.3);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.refresh-btn[disabled].data-v-daf52aa6 {
  opacity: 0.6;
}
.refresh-btn .refresh-icon.data-v-daf52aa6 {
  font-size: 32rpx;
  color: #ffffff;
}
.refresh-btn .refresh-text.data-v-daf52aa6 {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}
/* 响应式设计 */
@media (max-width: 750rpx) {
.store-grid.data-v-daf52aa6 {
    gap: 20rpx;
}
.store-card.data-v-daf52aa6 {
    padding: 30rpx 15rpx;
}
.store-card-content .store-name.data-v-daf52aa6 {
    font-size: 28rpx;
}
}

