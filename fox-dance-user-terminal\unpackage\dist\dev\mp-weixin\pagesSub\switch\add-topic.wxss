@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.uni-forms-item {
  position: relative;
  display: flex;
  margin-bottom: 22px;
  flex-direction: row;
}
.uni-forms-item__label {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  font-size: 14px;
  color: #606266;
  height: 36px;
  padding: 0 12px 0 0;
  vertical-align: middle;
  flex-shrink: 0;
  box-sizing: border-box;
}
.uni-forms-item__label.no-label {
  padding: 0;
}
.uni-forms-item__content {
  position: relative;
  font-size: 14px;
  flex: 1;
  box-sizing: border-box;
  flex-direction: row;
}
.uni-forms-item .uni-forms-item__nuve-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.uni-forms-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
  opacity: 0;
}
.uni-forms-item__error .error-text {
  color: #f56c6c;
  font-size: 12px;
}
.uni-forms-item__error.msg--active {
  opacity: 1;
  -webkit-transform: translateY(0%);
          transform: translateY(0%);
}
.uni-forms-item.is-direction-left {
  flex-direction: row;
}
.uni-forms-item.is-direction-top {
  flex-direction: column;
}
.uni-forms-item.is-direction-top .uni-forms-item__label {
  padding: 0 0 8px;
  line-height: 1.5715;
  text-align: left;
  white-space: initial;
}
.uni-forms-item .is-required {
  color: #dd524d;
  font-weight: bold;
}
.uni-forms-item--border {
  margin-bottom: 0;
  padding: 10px 0;
  border-top: 1px #eee solid;
}
.uni-forms-item--border .uni-forms-item__content {
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.uni-forms-item--border .uni-forms-item__content .uni-forms-item__error {
  position: relative;
  top: 5px;
  left: 0;
  padding-top: 0;
}
.is-first-border {
  border: none;
}

@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.container.data-v-5b362a9d {
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
  min-height: 100vh;
}
.form-item.data-v-5b362a9d {
  margin-bottom: 48rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.form-item .label.data-v-5b362a9d {
  display: block;
  font-size: 32rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  margin-bottom: 28rpx;
  letter-spacing: 0.5rpx;
}
.form-item .label .label-tip.data-v-5b362a9d {
  font-size: 26rpx;
  color: #8a8a8a;
  font-weight: 400;
  opacity: 0.8;
}
.form-item .input.data-v-5b362a9d {
  width: 92%;
  height: 104rpx;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 0 28rpx;
  font-size: 30rpx;
  color: #4a4a4a;
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.form-item .input.data-v-5b362a9d:focus {
  border-color: rgba(255, 107, 135, 0.5);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.form-item .textarea.data-v-5b362a9d {
  width: 92%;
  height: 360rpx;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 28rpx;
  font-size: 30rpx;
  color: #4a4a4a;
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.06);
  transition: all 0.3s ease;
  line-height: 1.7;
  letter-spacing: 0.3rpx;
}
.form-item .textarea.data-v-5b362a9d:focus {
  border-color: rgba(255, 107, 135, 0.5);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.15);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.image-upload-container .image-list.data-v-5b362a9d {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.image-upload-container .image-item.data-v-5b362a9d {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border: 2rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.1);
  transition: all 0.3s ease;
}
.image-upload-container .image-item.data-v-5b362a9d:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.image-upload-container .image-item .uploaded-image.data-v-5b362a9d {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.image-upload-container .image-item .image-delete.data-v-5b362a9d {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  width: 44rpx;
  height: 44rpx;
  background: rgba(255, 107, 135, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.3);
}
.image-upload-container .image-item .image-delete .delete-icon.data-v-5b362a9d {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: bold;
}
.image-upload-container .image-item .cover-badge.data-v-5b362a9d {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  color: #ffffff;
  font-size: 22rpx;
  text-align: center;
  padding: 10rpx 0;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}
.image-upload-container .add-image-btn.data-v-5b362a9d {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed rgba(255, 107, 135, 0.4);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
  transition: all 0.3s ease;
}
.image-upload-container .add-image-btn.data-v-5b362a9d:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border-color: rgba(255, 107, 135, 0.6);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.image-upload-container .add-image-btn .add-icon.data-v-5b362a9d {
  font-size: 52rpx;
  color: #ff6b87;
  margin-bottom: 12rpx;
  font-weight: bold;
}
.image-upload-container .add-image-btn .add-text.data-v-5b362a9d {
  font-size: 26rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}
.image-upload-container .upload-progress.data-v-5b362a9d {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 107, 135, 0.1);
}
.image-upload-container .upload-progress .progress-text.data-v-5b362a9d {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}
.submit-btn.data-v-5b362a9d {
  margin-top: 80rpx;
}
.submit-btn .btn.data-v-5b362a9d {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  color: #ffffff;
  font-weight: 600;
  border: none;
  border-radius: 32rpx;
  height: 104rpx;
  font-size: 34rpx;
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 135, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 1rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.submit-btn .btn.data-v-5b362a9d:active {
  -webkit-transform: translateY(2rpx) scale(0.98);
          transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 135, 0.3);
}
.submit-btn .btn[disabled].data-v-5b362a9d {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #94a3b8;
  box-shadow: none;
  -webkit-transform: none;
          transform: none;
}

/* 小红书风格动画效果 */
@-webkit-keyframes fadeInUp-data-v-5b362a9d {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-5b362a9d {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.form-item.data-v-5b362a9d {
  -webkit-animation: fadeInUp-data-v-5b362a9d 0.6s ease-out;
          animation: fadeInUp-data-v-5b362a9d 0.6s ease-out;
}

/* 毛玻璃效果 */
.glass-effect.data-v-5b362a9d {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

