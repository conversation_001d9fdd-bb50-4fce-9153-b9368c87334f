(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesSub/switch/comment-detail"],{

/***/ 676:
/*!********************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/main.js?{"page":"pagesSub%2Fswitch%2Fcomment-detail"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _commentDetail = _interopRequireDefault(__webpack_require__(/*! ./pagesSub/switch/comment-detail.vue */ 677));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_commentDetail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 677:
/*!*************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./comment-detail.vue?vue&type=template&id=4c2dc355&scoped=true& */ 678);
/* harmony import */ var _comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./comment-detail.vue?vue&type=script&lang=js& */ 680);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true& */ 682);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 75);

var renderjs





/* normalize component */

var component = Object(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "4c2dc355",
  null,
  false,
  _comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesSub/switch/comment-detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 678:
/*!********************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?vue&type=template&id=4c2dc355&scoped=true& ***!
  \********************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=template&id=4c2dc355&scoped=true& */ 679);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_template_id_4c2dc355_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 679:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?vue&type=template&id=4c2dc355&scoped=true& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uLoading: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-loading/u-loading */ "components/uview-ui/components/u-loading/u-loading").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-loading/u-loading.vue */ 830))
    },
    uIcon: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-icon/u-icon */ "components/uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-icon/u-icon.vue */ 727))
    },
    uPopup: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-popup/u-popup */ "components/uview-ui/components/u-popup/u-popup").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-popup/u-popup.vue */ 741))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 =
    !_vm.loading && _vm.comment.user.level >= 0
      ? _vm.getLevelColor(_vm.comment.user.level)
      : null
  var m1 = !_vm.loading ? _vm.formatTime(_vm.comment.created_at) : null
  var g0 =
    !_vm.loading && !_vm.showFullContent ? _vm.comment.content.length : null
  var g1 =
    !_vm.loading && !_vm.showFullContent && g0 > 100
      ? _vm.comment.content.slice(0, 100)
      : null
  var g2 = !_vm.loading ? _vm.comment.content.length : null
  var g3 = !_vm.loading ? _vm.replies.length : null
  var l0 =
    !_vm.loading && !!(g3 > 0)
      ? _vm.__map(_vm.replies, function (reply, index) {
          var $orig = _vm.__get_orig(reply)
          var m2 = _vm.formatTime(reply.created_at)
          var g4 = !reply.showFullContent ? reply.content.length : null
          var g5 =
            !reply.showFullContent && g4 > 100
              ? reply.content.slice(0, 100)
              : null
          var g6 = reply.content.length
          return {
            $orig: $orig,
            m2: m2,
            g4: g4,
            g5: g5,
            g6: g6,
          }
        })
      : null
  var g7 =
    !_vm.loading && !_vm.pagination.loading
      ? !_vm.pagination.hasMore && _vm.replies.length > 0
      : null
  var m3 = _vm.currentMoreComment && _vm.isReplyOwner(_vm.currentMoreComment)
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.isReplying ? _vm.cancelReply() : _vm.hideMaskAndKeyboard()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        l0: l0,
        g7: g7,
        m3: m3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 680:
/*!**************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=script&lang=js& */ 681);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 681:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _commentApi = _interopRequireDefault(__webpack_require__(/*! @/config/comment.api.js */ 666));
var _topicApi = _interopRequireDefault(__webpack_require__(/*! @/config/topic.api.js */ 668));
var _dayjs = _interopRequireDefault(__webpack_require__(/*! dayjs */ 671));
var _relativeTime = _interopRequireDefault(__webpack_require__(/*! dayjs/plugin/relativeTime */ 672));
__webpack_require__(/*! dayjs/locale/zh-cn */ 673);
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var CommentInput = function CommentInput() {
  __webpack_require__.e(/*! require.ensure | pagesSub/switch/components/CommentInput */ "pagesSub/switch/components/CommentInput").then((function () {
    return resolve(__webpack_require__(/*! ../../../../用户端/fox-dance-user-terminal/pagesSub/switch/components/CommentInput.vue */ 837));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var ReplySkeleton = function ReplySkeleton() {
  __webpack_require__.e(/*! require.ensure | pagesSub/switch/components/ReplySkeleton */ "pagesSub/switch/components/ReplySkeleton").then((function () {
    return resolve(__webpack_require__(/*! ./components/ReplySkeleton.vue */ 851));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
_dayjs.default.extend(_relativeTime.default);
_dayjs.default.locale('zh-cn');
var _default = {
  components: {
    CommentInput: CommentInput,
    ReplySkeleton: ReplySkeleton
  },
  data: function data() {
    return {
      commentId: null,
      userId: '',
      loading: true,
      isRefreshing: false,
      loadingMore: false,
      hasMore: true,
      page: 1,
      limit: 10,
      comment: {
        id: '',
        content: '',
        created_at: '',
        likes: 0,
        is_liked: false,
        user: {
          id: '',
          nickname: '',
          avatar: '',
          level: 0
        }
      },
      replies: [],
      replyCount: 0,
      replyText: '',
      currentReplyTo: null,
      inputPlaceholder: '发表您的评论...',
      sortBy: 'hot',
      scrollViewHeight: 'calc(90vh - 110rpx)',
      showFullContent: false,
      showMorePopup: false,
      // 更多操作弹窗
      currentMoreComment: null,
      // 当前操作的评论
      isReplying: false,
      // 是否处于回复状态
      keyboardHeight: 0,
      // 键盘高度
      inputContainerBottom: 0,
      // 输入框容器底部距离
      isKeyboardShow: false,
      // 键盘是否显示
      scrollTop: 0,
      // scroll-view的滚动位置
      scrollIntoView: '',
      // scroll-view的滚动到指定元素
      // 分页相关数据
      pagination: {
        page: 1,
        pageSize: 10,
        hasMore: true,
        loading: false
      },
      isLoadingMore: false,
      // 是否正在加载更多
      loadingText: '加载中...' // 加载提示文本
    };
  },

  computed: {
    // 判断当前用户是否是评论所有者
    isCommentOwner: function isCommentOwner() {
      console.log(this.comment.user, this.userId);
      return this.comment.user && this.userId && String(this.comment.user.id) == String(this.userId);
    }
  },
  onLoad: function onLoad(options) {
    if (options.id) {
      // 修复：将commentId转换为Long兼容的数字类型
      this.commentId = Number(options.id);

      // 修复：将userId转换为Long兼容的数字类型
      var userIdStr = options.userId || uni.getStorageSync('userid') || '18';
      this.userId = Number(userIdStr);
      console.log('🔍 评论详情页参数:', {
        commentId: this.commentId,
        userId: this.userId,
        commentIdType: (0, _typeof2.default)(this.commentId),
        userIdType: (0, _typeof2.default)(this.userId),
        commentIdValue: this.commentId,
        userIdValue: this.userId
      });
      this.fetchCommentDetail();
      this.setScrollViewHeight();
      this.setupKeyboardListener();
    } else {
      uni.showToast({
        title: '评论ID不存在',
        icon: 'none'
      });
      setTimeout(function () {
        uni.navigateBack();
      }, 1500);
    }
  },
  // 添加生命周期钩子来管理键盘监听
  onShow: function onShow() {
    // 页面显示时，确保只有一个有效的键盘监听
    uni.offKeyboardHeightChange(); // 先移除可能存在的监听
    this.setupKeyboardListener();
  },
  onHide: function onHide() {
    // 页面隐藏时移除监听
    uni.offKeyboardHeightChange();
    console.log('页面隐藏，取消键盘高度监听');
  },
  onUnload: function onUnload() {
    // 页面卸载时移除监听
    uni.offKeyboardHeightChange();
    console.log('页面卸载，取消键盘高度监听');
  },
  methods: (_methods = {
    // 兼容性时间戳函数 - 替代performance.now()
    getTimestamp: function getTimestamp() {
      // 微信小程序环境使用Date.now()
      if (typeof performance !== 'undefined' && performance.now) {
        return performance.now();
      }
      return Date.now();
    },
    // 设置键盘高度监听器
    setupKeyboardListener: function setupKeyboardListener() {
      var _this = this;
      uni.onKeyboardHeightChange(function (res) {
        console.log('键盘高度变化:', res.height);
        _this.keyboardHeight = res.height;
        _this.isKeyboardShow = res.height > 0;
        if (res.height > 0) {
          // 键盘弹出，调整输入框位置
          _this.inputContainerBottom = res.height;
        } else {
          // 键盘收起，恢复输入框位置
          _this.inputContainerBottom = 0;
        }
      });
      console.log('键盘高度监听器已设置');
    },
    // 输入框获取焦点
    onInputFocus: function onInputFocus(e) {
      var _this2 = this;
      console.log('输入框获取焦点');
      this.isKeyboardShow = true;

      // 微信小程序中，键盘弹出时的额外处理

      // 延时获取键盘高度，因为键盘弹出需要时间
      setTimeout(function () {
        if (_this2.keyboardHeight === 0) {
          // 如果监听器没有获取到键盘高度，使用默认值
          _this2.keyboardHeight = 280; // 微信小程序默认键盘高度
          _this2.inputContainerBottom = _this2.keyboardHeight;
        }
      }, 300);
    },
    // 输入框失去焦点
    onInputBlur: function onInputBlur(e) {
      var _this3 = this;
      console.log('输入框失去焦点');
      this.isKeyboardShow = false;

      // 延时重置，确保键盘完全收起
      setTimeout(function () {
        if (!_this3.isKeyboardShow) {
          _this3.keyboardHeight = 0;
          _this3.inputContainerBottom = 0;
        }
      }, 100);
    },
    // 隐藏蒙版层并收起键盘
    hideMaskAndKeyboard: function hideMaskAndKeyboard() {
      console.log('点击蒙版层，收起键盘');

      // 让输入框失去焦点
      if (this.$refs.commentInput) {
        this.$refs.commentInput.blur();
      }

      // 强制隐藏键盘
      uni.hideKeyboard();

      // 重置键盘状态
      this.isKeyboardShow = false;
      this.keyboardHeight = 0;
      this.inputContainerBottom = 0;
    },
    // 取消回复
    cancelReply: function cancelReply() {
      // 重置回复状态和相关数据
      this.isReplying = false;
      this.currentReplyTo = null;
      this.replyText = '';
      this.inputPlaceholder = '发表您的评论...';
      // 确保键盘收起
      uni.hideKeyboard();
      // 模拟失去焦点，确保下次可以重新获取焦点
      if (this.$refs.commentInput) {
        this.$refs.commentInput.$emit('blur');
      }
    },
    // 判断当前用户是否是回复所有者
    isReplyOwner: function isReplyOwner(reply) {
      return reply && reply.user && this.userId && String(reply.user.id) == String(this.userId);
    },
    // 显示删除评论确认框
    showDeleteCommentConfirm: function showDeleteCommentConfirm() {
      var _this4 = this;
      uni.showModal({
        title: '删除评论',
        content: '确认要删除这条评论吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: function success(res) {
          if (res.confirm) {
            _this4.deleteComment();
          }
        }
      });
    },
    // 显示更多操作弹窗
    showMoreOptions: function showMoreOptions(item) {
      this.currentMoreComment = item;
      this.showMorePopup = true;
    },
    // 从更多操作弹窗中点击回复
    replyFromMore: function replyFromMore() {
      var _this5 = this;
      if (this.currentMoreComment) {
        this.showMorePopup = false;
        // 等待更多操作弹窗关闭后再打开回复弹窗
        setTimeout(function () {
          // 先确保重置状态
          if (_this5.$refs.commentInput) {
            _this5.$refs.commentInput.autoFocus = false;
          }

          // 设置回复状态
          _this5.replyToComment(_this5.currentMoreComment);
          _this5.isReplying = true;

          // 再次延时确保输入框聚焦，因为replyToComment中的聚焦可能因为弹窗动画而失效
          setTimeout(function () {
            if (_this5.$refs.commentInput) {
              _this5.$refs.commentInput.focus();
            }
          }, 150);
        }, 300);
      }
    },
    // 复制评论内容
    copyComment: function copyComment() {
      var _this6 = this;
      if (!this.currentMoreComment) return;
      uni.setClipboardData({
        data: this.currentMoreComment.content,
        success: function success() {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
          _this6.showMorePopup = false;
        }
      });
    },
    // 删除回复
    deleteReply: function deleteReply() {
      var _this7 = this;
      if (!this.currentMoreComment) return;
      uni.showModal({
        title: '删除回复',
        content: '确认要删除这条回复吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: function success(res) {
          if (res.confirm) {
            // 调用API删除回复
            _commentApi.default.deleteReply(Number(_this7.currentMoreComment.id), {
              userId: _this7.userId
            }).then(function (res) {
              console.log('删除回复API返回数据:', JSON.stringify(res));
              if (res.code === 0) {
                // 从列表中移除已删除的回复
                var index = _this7.replies.findIndex(function (item) {
                  return item.id == _this7.currentMoreComment.id;
                });
                if (index > -1) {
                  _this7.replies.splice(index, 1);
                }
                // 减少回复计数
                _this7.replyCount--;
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });

                // 关闭弹窗
                _this7.showMorePopup = false;
              } else {
                uni.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                });
              }
            }).catch(function (err) {
              console.error('删除回复失败:', err);
              uni.showToast({
                title: '网络请求错误',
                icon: 'none'
              });
            });
          }
        }
      });
    },
    // 删除评论
    deleteComment: function deleteComment() {
      console.log('删除评论ID:', this.commentId, '类型:', (0, _typeof2.default)(this.commentId));
      _commentApi.default.deleteComment(Number(this.commentId), {
        userId: this.userId
      }).then(function (res) {
        console.log('删除评论API返回数据:', JSON.stringify(res));
        if (res.code === 0) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });

          // 删除成功后返回上一页
          setTimeout(function () {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.message || '删除失败',
            icon: 'none'
          });
        }
      }).catch(function (err) {
        console.error('删除评论失败:', err);
        uni.showToast({
          title: '网络请求错误',
          icon: 'none'
        });
      });
    },
    // 显示删除回复确认框
    showDeleteReplyConfirm: function showDeleteReplyConfirm(reply, index) {
      var _this8 = this;
      uni.showModal({
        title: '删除回复',
        content: '确认要删除这条回复吗？删除后无法恢复',
        confirmText: '删除',
        confirmColor: '#f56c6c',
        success: function success(res) {
          if (res.confirm) {
            _this8.deleteReply(reply, index);
          }
        }
      });
    }
  }, (0, _defineProperty2.default)(_methods, "deleteReply", function deleteReply(reply, index) {
    var _this9 = this;
    console.log('删除回复:', reply, index);
    _commentApi.default.deleteReply(Number(reply.id), {
      userId: this.userId
    }).then(function (res) {
      console.log('删除回复API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        // 从列表中移除已删除的回复
        _this9.replies.splice(index, 1);
        // 减少回复计数
        _this9.replyCount--;
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        uni.showToast({
          title: res.message || '删除失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('删除回复失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    });
  }), (0, _defineProperty2.default)(_methods, "setScrollViewHeight", function setScrollViewHeight() {
    var statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 25;
    var inputBoxHeight = 110; // 底部输入框高度（rpx转px）
    var filterBarHeight = 100; // 筛选栏高度（rpx转px）

    // 修改计算方式，不再减去输入框高度，因为输入框现在是固定定位
    var scrollHeight = "calc(90vh - ".concat(statusBarHeight, "px - 20rpx)");
    this.scrollViewHeight = scrollHeight;
    console.log('设置滚动视图高度:', scrollHeight);
  }), (0, _defineProperty2.default)(_methods, "goBack", function goBack() {
    uni.navigateBack();
  }), (0, _defineProperty2.default)(_methods, "fetchCommentDetail", function fetchCommentDetail() {
    var _this10 = this;
    this.loading = true;

    // 调用API获取评论详情
    _commentApi.default.getCommentDetail(this.commentId, {
      userId: this.userId,
      sort: this.sortBy,
      current: 1,
      pageSize: 1 // 只获取评论信息，不包含回复
    }).then(function (res) {
      console.log('评论详情API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        // 设置评论详情
        var commentData = res.data.comment || {};
        console.log('原始评论数据:', JSON.stringify(commentData));

        // 转换字段名
        commentData.created_at = commentData.createdAt;
        commentData.is_liked = commentData.isLiked;
        commentData.reply_count = commentData.replyCount;

        // 处理用户头像为空的情况
        if (!commentData.user.avatar) {
          commentData.user.avatar = '/static/images/toux.png';
        }
        console.log('处理后的评论数据:', JSON.stringify(commentData));
        _this10.comment = commentData;
        _this10.replyCount = commentData.replyCount || 0;

        // 获取评论回复列表
        _this10.fetchRepliesOnly();

        // 设置loading为false，因为主评论已经加载完成
        _this10.loading = false;
      } else {
        uni.showToast({
          title: res.message || '获取评论详情失败',
          icon: 'none'
        });

        // 失败后返回上一页
        setTimeout(function () {
          uni.navigateBack();
        }, 1500);
      }
    }).catch(function (err) {
      console.error('获取评论详情失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });

      // 失败后返回上一页
      setTimeout(function () {
        uni.navigateBack();
      }, 1500);
    });
  }), (0, _defineProperty2.default)(_methods, "fetchReplies", function fetchReplies() {
    var _this11 = this;
    if (this.page === 1) {
      // 第一页不显示加载更多
      this.loading = true;
    } else {
      this.loadingMore = true;
    }

    // 调用API获取回复列表
    _commentApi.default.getCommentDetail(this.commentId, {
      userId: this.userId,
      sort: this.sortBy,
      current: this.page,
      pageSize: this.limit
    }).then(function (res) {
      console.log('评论回复API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        var data = res.data;

        // 获取回复列表，可能在replies.items或replies.records中
        var replyItems = data.replies.items || [];
        console.log('原始回复列表:', JSON.stringify(replyItems));
        if (replyItems.length === 0) {
          _this11.replies = [];
          console.log('replies.length=', _this11.replies.length);
          return;
        }

        // 转换回复列表中的字段名
        replyItems.forEach(function (reply) {
          reply.created_at = reply.createdAt;
          reply.is_liked = reply.isLiked;
          // 添加控制内容展开/收起的属性
          reply.showFullContent = false;

          // 处理replyTo字段
          if (reply.replyTo) {
            reply.reply_to = {
              id: reply.replyTo.id,
              nickname: reply.replyTo.nickname
            };
          } else {
            reply.reply_to = undefined;
          }

          // 处理用户头像为空的情况
          if (!reply.user.avatar) {
            reply.user.avatar = '/static/images/toux.png';
          }
        });
        console.log('处理后的回复列表:', JSON.stringify(replyItems));
        if (_this11.page === 1) {
          // 第一页直接替换
          _this11.replies = replyItems;
          _this11.replyCount = data.replies && data.replies.total || 0;
        } else {
          // 加载更多，追加数据
          _this11.replies = [].concat((0, _toConsumableArray2.default)(_this11.replies), (0, _toConsumableArray2.default)(replyItems));
        }

        // 是否还有更多数据
        _this11.hasMore = data.replies && data.replies.has_more;
        if (_this11.hasMore) {
          _this11.page++;
        }
      } else {
        uni.showToast({
          title: res.message || '获取回复列表失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('获取回复列表失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    }).finally(function () {
      _this11.loading = false;
      _this11.loadingMore = false;
      _this11.isRefreshing = false;
    });
  }), (0, _defineProperty2.default)(_methods, "loadMoreReplies", function loadMoreReplies() {
    var _this12 = this;
    console.log('🔄 触发回复懒加载');

    // 防抖处理，避免重复请求
    if (this.pagination.loading || !this.pagination.hasMore) {
      console.log('⚠️ 回复正在加载或已无更多数据，跳过请求');
      return;
    }

    // 性能优化：增强防抖处理，减少低端设备的请求频率
    var now = Date.now();
    var lastRequestTime = this.lastRequestTime || 0;
    if (now - lastRequestTime < 600) {
      // 增加到600ms，减少低端设备的负担
      console.log('⚠️ 请求过于频繁，跳过回复懒加载');
      return;
    }
    this.lastRequestTime = now;

    // 设置加载状态
    this.pagination.loading = true;
    this.loadingText = '加载更多回复...';

    // 计算下一页页码
    var nextPage = this.pagination.page + 1;
    var startTime = this.getTimestamp();
    console.log("\uD83D\uDCC4 \u56DE\u590D\u5F53\u524D\u9875\u7801: ".concat(this.pagination.page, ", \u8BF7\u6C42\u9875\u7801: ").concat(nextPage));

    // 使用正确的API方法获取回复分页数据
    // 修复：使用getCommentDetail而不是getCommentReplies，因为后者可能不存在
    var params = {
      userId: Number(this.userId),
      // 使用Number确保Long兼容性
      sort: this.sortBy,
      current: nextPage,
      // 使用current而不是page
      pageSize: this.pagination.pageSize
    };
    console.log('📋 回复分页请求参数:', JSON.stringify(params));

    // 调用API获取更多回复 - 修复数据类型为Long兼容
    _commentApi.default.getCommentDetail(Number(this.commentId), params).then(function (res) {
      var endTime = _this12.getTimestamp();
      var loadTime = endTime - startTime;
      console.log("\u2705 \u56DE\u590D\u5206\u9875API\u8FD4\u56DE\uFF0C\u8017\u65F6: ".concat(loadTime.toFixed(2), "ms"));
      if (res.code === 0) {
        var data = res.data;

        // 性能优化：减少日志输出
        console.log('📊 回复分页数据概览:', {
          repliesCount: data.replies && data.replies.items ? data.replies.items.length : 0,
          total: data.replies ? data.replies.total : 0,
          hasMore: data.replies ? data.replies.hasMore : false
        });

        // 获取回复列表，处理不同的数据结构（优化版）
        var rawReplies = [];
        if (data.replies) {
          if (data.replies.items && Array.isArray(data.replies.items)) {
            rawReplies = data.replies.items;
          } else if (data.replies.records && Array.isArray(data.replies.records)) {
            rawReplies = data.replies.records;
          } else if (Array.isArray(data.replies)) {
            rawReplies = data.replies;
          }
        }
        var newReplies = _this12.processReplyDataOptimized(rawReplies);
        if (newReplies && newReplies.length > 0) {
          // 检查是否有重复数据（优化版）
          var existingIds = new Set(_this12.replies.map(function (reply) {
            return reply.id;
          }));
          var filteredReplies = newReplies.filter(function (reply) {
            return !existingIds.has(reply.id);
          });
          console.log("\uD83D\uDD04 \u56DE\u590D\u53BB\u91CD: \u539F\u59CB".concat(newReplies.length, "\u6761\uFF0C\u53BB\u91CD\u540E").concat(filteredReplies.length, "\u6761"));
          if (filteredReplies.length > 0) {
            // 追加新回复到列表（优化：使用concat）
            _this12.replies = _this12.replies.concat(filteredReplies);

            // 更新分页信息
            _this12.pagination.page = nextPage;
            console.log("\u2705 \u56DE\u590D\u52A0\u8F7D\u6210\u529F\uFF0C\u9875\u7801: ".concat(nextPage, "\uFF0C\u65B0\u589E: ").concat(filteredReplies.length, "\u6761"));
          }

          // 检查是否还有更多数据
          if (data.replies && data.replies.hasMore === false || newReplies.length < _this12.pagination.pageSize) {
            _this12.pagination.hasMore = false;
            console.log('🔚 回复已加载完毕');
          }
        } else {
          // 没有更多数据
          _this12.pagination.hasMore = false;
          console.log('🔚 回复无更多数据');
        }
      } else {
        // API返回错误
        console.error('❌ 回复API返回错误:', res.message);
        uni.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('❌ 回复懒加载失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    }).finally(function () {
      // 重置加载状态
      _this12.pagination.loading = false;
      _this12.loadingText = '加载中...';
      console.log('🔄 回复加载状态重置');
    });
  }), (0, _defineProperty2.default)(_methods, "onRefresh", function onRefresh() {
    var _this13 = this;
    this.isRefreshing = true;

    // 重置分页状态
    this.pagination = {
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: false
    };

    // 下拉刷新时仅刷新回复列表
    this.fetchRepliesOnly();
    setTimeout(function () {
      _this13.isRefreshing = false;
    }, 500);
  }), (0, _defineProperty2.default)(_methods, "likeComment", function likeComment() {
    var _this14 = this;
    // 点赞/取消点赞主评论
    var action = this.comment.is_liked ? 'unlike' : 'like';
    _commentApi.default.likeComment(Number(this.comment.id), {
      userId: Number(this.userId),
      // 使用Number确保Long兼容性
      action: action
    }).then(function (res) {
      console.log('点赞主评论API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        // 更新评论点赞状态和数量
        _this14.comment.is_liked = res.data.isLiked || res.data.is_liked;
        _this14.comment.likes = res.data.likes;
      } else {
        uni.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('点赞操作失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    });
  }), (0, _defineProperty2.default)(_methods, "likeReply", function likeReply(reply, index) {
    var _this15 = this;
    // 点赞/取消点赞回复
    var action = reply.is_liked ? 'unlike' : 'like';
    _commentApi.default.likeReply(Number(reply.id), {
      userId: Number(this.userId),
      // 使用Number确保Long兼容性
      action: action
    }).then(function (res) {
      console.log('点赞回复API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        // 更新回复点赞状态和数量
        _this15.replies[index].is_liked = res.data.isLiked || res.data.is_liked;
        _this15.replies[index].likes = res.data.likes;
      } else {
        uni.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('点赞回复操作失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    });
  }), (0, _defineProperty2.default)(_methods, "replyToMain", function replyToMain() {
    var _this16 = this;
    // 回复主评论
    // 先重置当前状态
    if (this.$refs.commentInput) {
      this.$refs.commentInput.autoFocus = false;
    }
    this.currentReplyTo = null;
    this.inputPlaceholder = '发表您的评论...';
    this.isReplying = true; // 设置为回复状态

    // 自动聚焦输入框 - 使用延时确保在DOM更新后执行
    setTimeout(function () {
      if (_this16.$refs.commentInput) {
        _this16.$refs.commentInput.focus();
      }
    }, 150);
  }), (0, _defineProperty2.default)(_methods, "replyToComment", function replyToComment(reply) {
    var _this17 = this;
    // 回复某条回复
    // 先重置当前状态
    if (this.$refs.commentInput) {
      this.$refs.commentInput.autoFocus = false;
    }
    this.currentReplyTo = reply;
    this.isReplying = true; // 设置为回复状态
    console.log('replyToComment:', JSON.stringify(reply));
    console.log('currentReplyTo:', JSON.stringify(this.currentReplyTo));

    // 确保reply.user存在且能访问
    if (reply && reply.user) {
      console.log('回复用户信息:', JSON.stringify(reply.user));
      this.inputPlaceholder = "@ ".concat(reply.user.nickname, ":");
    } else {
      console.log('回复用户信息不存在');
      this.inputPlaceholder = '回复评论...';
    }

    // 自动聚焦输入框 - 使用延时确保在DOM更新后执行
    setTimeout(function () {
      if (_this17.$refs.commentInput) {
        _this17.$refs.commentInput.focus();
      }
    }, 150);
  }), (0, _defineProperty2.default)(_methods, "sendReply", function sendReply() {
    var _this18 = this;
    if (this.replyText.length > 1000) {
      uni.showToast({
        title: '评论字数不能超过1000字',
        icon: 'none'
      });
      return;
    }
    if (!this.replyText.trim()) return;
    var replyData = {
      userId: Number(this.userId),
      // 使用Number确保Long兼容性
      commentId: Number(this.commentId),
      // 使用Number确保Long兼容性
      content: this.replyText.trim(),
      replyToId: this.currentReplyTo ? Number(this.currentReplyTo.userId) : null // 使用Number确保Long兼容性
    };

    console.log('🚀 发送回复数据:', JSON.stringify(replyData));
    console.log('📊 回复数据类型检查:', {
      userId: (0, _typeof2.default)(replyData.userId),
      userIdValue: replyData.userId,
      commentId: (0, _typeof2.default)(replyData.commentId),
      commentIdValue: replyData.commentId,
      replyToId: (0, _typeof2.default)(replyData.replyToId),
      replyToIdValue: replyData.replyToId,
      content: (0, _typeof2.default)(replyData.content)
    });
    _commentApi.default.replyComment(replyData.commentId, replyData).then(function (res) {
      console.log('发送回复API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        uni.showToast({
          title: '回复成功',
          icon: 'success'
        });

        // 清空输入框
        if (_this18.$refs.commentInput) {
          _this18.$refs.commentInput.clear();
        } else {
          _this18.replyText = '';
        }

        // 重置回复状态
        _this18.currentReplyTo = null;
        _this18.inputPlaceholder = '发表您的评论...';
        _this18.isReplying = false;

        // 刷新回复列表 - 重置分页状态
        _this18.pagination = {
          page: 1,
          pageSize: 10,
          hasMore: true,
          loading: false
        };
        _this18.fetchRepliesOnly();
      } else {
        uni.showToast({
          title: res.message || '回复失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('发送回复失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    });
  }), (0, _defineProperty2.default)(_methods, "changeSort", function changeSort(type) {
    // 不再添加触觉反馈

    if (this.sortBy === type) return;
    this.sortBy = type;

    // 重置分页状态
    this.pagination = {
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: true
    };

    // 切换排序方式，只重新加载回复列表，不刷新整个页面
    this.loading = false; // 确保不显示整页加载状态
    this.fetchRepliesOnly();
  }), (0, _defineProperty2.default)(_methods, "fetchRepliesOnly", function fetchRepliesOnly() {
    var _this19 = this;
    console.log('🔄 开始获取回复列表，当前页码:', this.pagination.page);

    // 调用API获取回复列表 - 修复数据类型为Long兼容
    _commentApi.default.getCommentDetail(Number(this.commentId), {
      userId: Number(this.userId),
      // 使用Number确保Long兼容性
      sort: this.sortBy,
      current: this.pagination.page,
      // 使用当前页码，不是page+1
      pageSize: this.pagination.pageSize
    }).then(function (res) {
      console.log('✅ 评论回复API返回数据:', JSON.stringify(res));
      if (res.code === 0) {
        var data = res.data;

        // 获取回复列表，处理不同的数据结构
        var replyItems = [];
        if (data.replies) {
          if (data.replies.items && Array.isArray(data.replies.items)) {
            replyItems = data.replies.items;
          } else if (data.replies.records && Array.isArray(data.replies.records)) {
            replyItems = data.replies.records;
          } else if (Array.isArray(data.replies)) {
            replyItems = data.replies;
          }
        }
        console.log('📊 原始回复列表数量:', replyItems.length);

        // 使用processReplyData处理回复数据
        var processedReplies = _this19.processReplyData(replyItems);
        if (_this19.pagination.page === 1) {
          // 第一页，直接替换
          _this19.replies = processedReplies;
          console.log('📝 第一页回复数据已替换，数量:', processedReplies.length);
        } else {
          // 后续页，追加到现有列表
          var existingIds = _this19.replies.map(function (reply) {
            return reply.id;
          });
          var filteredReplies = processedReplies.filter(function (reply) {
            return !existingIds.includes(reply.id);
          });
          _this19.replies = [].concat((0, _toConsumableArray2.default)(_this19.replies), (0, _toConsumableArray2.default)(filteredReplies));
          console.log('📝 后续页回复数据已追加，去重后数量:', filteredReplies.length);
        }

        // 更新回复总数
        _this19.replyCount = data.replies && data.replies.total || 0;

        // 检查是否还有更多数据
        if (processedReplies.length < _this19.pagination.pageSize) {
          _this19.pagination.hasMore = false;
          console.log('🔚 回复数据已加载完毕');
        } else {
          _this19.pagination.hasMore = true;
          console.log('📄 还有更多回复数据');
        }
        console.log("\u2705 \u56DE\u590D\u5217\u8868\u52A0\u8F7D\u6210\u529F\uFF0C\u5F53\u524D\u9875:".concat(_this19.pagination.page, "\uFF0C\u603B\u6570:").concat(_this19.replies.length, "\u6761"));
      } else {
        console.error('❌ 获取回复列表API错误:', res.message);
        uni.showToast({
          title: res.message || '获取回复列表失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('❌ 获取回复列表失败:', err);
      uni.showToast({
        title: '网络请求错误',
        icon: 'none'
      });
    }).finally(function () {
      _this19.pagination.loading = false;
      console.log('🔄 回复列表加载状态重置');
    });
  }), (0, _defineProperty2.default)(_methods, "formatTime", function formatTime(timeString) {
    if (!timeString) return '';
    return (0, _dayjs.default)(timeString).fromNow();
  }), (0, _defineProperty2.default)(_methods, "getLevelColor", function getLevelColor(level) {
    var colors = {
      0: '#cccbc8',
      // 灰色
      1: '#c6ffe6',
      2: '#61bc84',
      // 绿色
      3: '#4d648d',
      4: '#1F3A5F',
      5: '#9c27b0',
      6: '#6c35de',
      7: '#ffd299',
      // 橙色
      8: '#FF7F50',
      // 红色
      9: '#f35d74',
      // 紫色
      10: '#bb2649' // 粉色
    };

    return colors[level] || '#8dc63f';
  }), (0, _defineProperty2.default)(_methods, "toggleContent", function toggleContent() {
    var wasExpanded = this.showFullContent;
    this.showFullContent = !this.showFullContent;

    // 如果是从展开状态收起，则滚动到主评论顶部
    if (wasExpanded) {
      this.scrollToMainComment();
    }
  }), (0, _defineProperty2.default)(_methods, "toggleReplyContent", function toggleReplyContent(reply, index) {
    var wasExpanded = reply.showFullContent;

    // Vue无法直接检测到通过索引设置的数组变化，需要使用Vue.set或$set方法
    if (!reply.showFullContent) {
      this.$set(reply, 'showFullContent', true);
    } else {
      this.$set(reply, 'showFullContent', false);
    }

    // 如果是从展开状态收起，则滚动到回复顶部
    if (wasExpanded) {
      this.scrollToReply(index);
    }
  }), (0, _defineProperty2.default)(_methods, "scrollToMainComment", function scrollToMainComment() {
    var _this20 = this;
    console.log('🎯 开始滚动到主评论');

    // 方法1: 使用scrollIntoView
    this.scrollToElementByScrollIntoView('main-comment');

    // 方法2: 备用方案
    setTimeout(function () {
      _this20.scrollToElementByScrollTop('main-comment');
    }, 100);
  }), (0, _defineProperty2.default)(_methods, "scrollToReply", function scrollToReply(index) {
    var _this21 = this;
    var replyId = "reply-".concat(index);
    console.log("\uD83C\uDFAF \u5F00\u59CB\u6EDA\u52A8\u5230\u56DE\u590D - ".concat(replyId));

    // 方法1: 使用scrollIntoView
    this.scrollToElementByScrollIntoView(replyId);

    // 方法2: 备用方案
    setTimeout(function () {
      _this21.scrollToElementByScrollTop(replyId);
    }, 100);
  }), (0, _defineProperty2.default)(_methods, "scrollToElementByScrollIntoView", function scrollToElementByScrollIntoView(elementId) {
    var _this22 = this;
    console.log("\uD83D\uDCCD \u4F7F\u7528scrollIntoView\u6EDA\u52A8\u5230 - ".concat(elementId));
    this.$nextTick(function () {
      setTimeout(function () {
        // 设置scroll-into-view属性
        _this22.scrollIntoView = elementId;

        // 清除scrollIntoView，避免影响后续滚动
        setTimeout(function () {
          _this22.scrollIntoView = '';
        }, 500);
        console.log("\u2705 scrollIntoView\u8BBE\u7F6E\u6210\u529F - ".concat(elementId));
      }, 150);
    });
  }), (0, _defineProperty2.default)(_methods, "scrollToElementByScrollTop", function scrollToElementByScrollTop(elementId) {
    var _this23 = this;
    console.log("\uD83D\uDCCD \u4F7F\u7528scroll-top\u6EDA\u52A8\u5230 - ".concat(elementId));
    this.$nextTick(function () {
      var query = uni.createSelectorQuery().in(_this23);
      query.select('.comment-container').boundingClientRect();
      query.select("#".concat(elementId)).boundingClientRect();
      query.exec(function (res) {
        console.log("\uD83D\uDCCA scroll-top\u67E5\u8BE2\u7ED3\u679C - ".concat(elementId, ":"), res);
        if (res && res.length >= 2) {
          var scrollViewRect = res[0];
          var elementRect = res[1];
          if (scrollViewRect && elementRect) {
            var relativeTop = elementRect.top - scrollViewRect.top;
            var topOffset = elementId === 'main-comment' ? 60 : 80;
            var targetScrollTop = Math.max(0, relativeTop - topOffset);
            console.log("\uD83D\uDCD0 scroll-top\u8BA1\u7B97 - ".concat(elementId, ":"), {
              scrollViewTop: scrollViewRect.top,
              elementTop: elementRect.top,
              relativeTop: relativeTop,
              targetScrollTop: targetScrollTop
            });

            // 强制更新scrollTop
            _this23.scrollTop = 0;
            _this23.$nextTick(function () {
              _this23.scrollTop = targetScrollTop;
              console.log("\u2705 scroll-top\u8BBE\u7F6E\u6210\u529F - ".concat(elementId, ", \u4F4D\u7F6E: ").concat(targetScrollTop));
            });
          }
        }
      });
    });
  }), (0, _defineProperty2.default)(_methods, "debugScrollElements", function debugScrollElements() {
    var elementId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'main-comment';
    console.log("\uD83D\uDD0D \u8C03\u8BD5\u6EDA\u52A8\u5143\u7D20 - ".concat(elementId));
    var query = uni.createSelectorQuery().in(this);
    query.select("#".concat(elementId)).boundingClientRect();
    query.select('.comment-container').boundingClientRect();
    query.exec(function (res) {
      console.log('🔍 调试结果:', {
        elementId: elementId,
        targetElement: res[0],
        scrollViewElement: res[1],
        hasTarget: !!res[0],
        hasScrollView: !!res[1]
      });
    });
  }), (0, _defineProperty2.default)(_methods, "processReplyData", function processReplyData(replies) {
    if (!replies || !Array.isArray(replies)) {
      console.warn('回复数据为空或格式错误');
      return [];
    }
    console.log("\u5904\u7406\u56DE\u590D\u6570\u636E\uFF0C\u56DE\u590D\u6570\u91CF:", replies.length);
    return replies.map(function (reply) {
      if (!reply) return null;

      // 转换字段名
      reply.created_at = reply.createdAt || reply.created_at || new Date().toISOString();
      reply.is_liked = reply.isLiked || reply.is_liked || false;
      reply.likes = reply.likes || 0;

      // 确保user对象存在
      if (!reply.user) {
        reply.user = {
          id: 0,
          nickname: '未知用户',
          avatar: '/static/images/toux.png',
          level: 0
        };
      } else {
        // 处理用户头像为空的情况
        if (!reply.user.avatar) {
          reply.user.avatar = '/static/images/toux.png';
        }

        // 确保其他用户字段存在
        reply.user.nickname = reply.user.nickname || '未知用户';
        reply.user.level = reply.user.level || 0;
      }

      // 确保reply_to存在
      if (reply.replyTo) {
        reply.reply_to = reply.replyTo;
      }
      return reply;
    }).filter(function (reply) {
      return reply !== null;
    });
  }), (0, _defineProperty2.default)(_methods, "processReplyDataOptimized", function processReplyDataOptimized(replies) {
    var startTime = this.getTimestamp();
    if (!replies || !Array.isArray(replies)) {
      console.warn('⚠️ 回复数据为空或格式错误');
      return [];
    }
    console.log("\uD83D\uDD04 \u5F00\u59CB\u5904\u7406\u56DE\u590D\u6570\u636E\uFF0C\u6570\u91CF: ".concat(replies.length));
    var processedReplies = replies.map(function (reply) {
      if (!reply) return null;

      // 优化：减少对象创建和属性复制
      var processedReply = _objectSpread(_objectSpread({}, reply), {}, {
        created_at: reply.createdAt || reply.created_at || new Date().toISOString(),
        is_liked: reply.isLiked || reply.is_liked || false,
        likes: reply.likes || 0,
        showFullContent: false
      });

      // 确保user对象存在
      if (!processedReply.user) {
        processedReply.user = {
          id: 0,
          nickname: '未知用户',
          avatar: '/static/images/toux.png',
          level: 0
        };
      } else {
        // 处理用户头像为空的情况
        if (!processedReply.user.avatar) {
          processedReply.user.avatar = '/static/images/toux.png';
        }
        processedReply.user.nickname = processedReply.user.nickname || '未知用户';
        processedReply.user.level = processedReply.user.level || 0;
      }

      // 确保reply_to存在
      if (reply.replyTo) {
        processedReply.reply_to = reply.replyTo;
      }
      return processedReply;
    }).filter(function (reply) {
      return reply !== null;
    });
    var endTime = this.getTimestamp();
    console.log("\u2705 \u56DE\u590D\u6570\u636E\u5904\u7406\u5B8C\u6210\uFF0C\u8017\u65F6: ".concat((endTime - startTime).toFixed(2), "ms\uFF0C\u5904\u7406\u6570\u91CF: ").concat(processedReplies.length));
    return processedReplies;
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 682:
/*!***********************************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true& ***!
  \***********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true& */ 683);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_comment_detail_vue_vue_type_style_index_0_id_4c2dc355_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 683:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pagesSub/switch/comment-detail.vue?vue&type=style&index=0&id=4c2dc355&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

throw new Error("Module build failed (from ./node_modules/mini-css-extract-plugin/dist/loader.js):\nModuleBuildError: Module build failed (from ./node_modules/postcss-loader/src/index.js):\nError: PostCSS plugin postcss-replace requires PostCSS 8.\nMigration guide for end-users:\nhttps://github.com/postcss/postcss/wiki/PostCSS-8-for-end-users\n    at Processor.normalize (D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\postcss\\lib\\processor.js:167:15)\n    at new Processor (D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\postcss\\lib\\processor.js:56:25)\n    at postcss (D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\postcss\\lib\\postcss.js:55:10)\n    at D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\postcss-loader\\src\\index.js:140:12\n    at D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\webpack\\lib\\NormalModule.js:316:20\n    at D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\loader-runner\\lib\\LoaderRunner.js:367:11\n    at D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\loader-runner\\lib\\LoaderRunner.js:233:18\n    at context.callback (D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\loader-runner\\lib\\LoaderRunner.js:111:13)\n    at D:\\DevolopmentTools\\HBuilderX\\plugins\\uniapp-cli\\node_modules\\postcss-loader\\src\\index.js:208:9");

/***/ })

},[[676,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesSub/switch/comment-detail.js.map