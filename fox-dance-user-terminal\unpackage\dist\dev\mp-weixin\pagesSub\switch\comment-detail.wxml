<view class="comment-detail data-v-4c2dc355"><scroll-view class="comment-container data-v-4c2dc355 vue-ref" style="{{'height:'+(scrollViewHeight)+';'}}" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" scroll-top="{{scrollTop}}" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="{{true}}" data-ref="replyScrollView" data-event-opts="{{[['scrolltolower',[['loadMoreReplies',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="safe-area-inset data-v-4c2dc355"></view><block wx:if="{{loading}}"><view class="loading data-v-4c2dc355"><u-loading vue-id="398b2851-1" mode="flower" size="50" class="data-v-4c2dc355" bind:__l="__l"></u-loading></view></block><block wx:else><block class="data-v-4c2dc355"><view class="main-comment data-v-4c2dc355" id="main-comment"><view class="user-info data-v-4c2dc355"><view class="avatar-wrap data-v-4c2dc355"><image class="avatar data-v-4c2dc355" src="{{comment.user.avatar}}" mode="aspectFill" lazy-load="{{true}}"></image></view><view class="user-meta data-v-4c2dc355"><view class="nickname data-v-4c2dc355">{{''+comment.user.nickname+''}}<block wx:if="{{comment.user.level>=0}}"><view class="user-level data-v-4c2dc355" style="{{'background-color:'+($root.m0)+';'}}">{{"Lv"+comment.user.level}}</view></block></view><view class="time data-v-4c2dc355">{{$root.m1}}</view></view><view data-event-opts="{{[['tap',[['likeComment',['$event']]]]]}}" class="like-btn data-v-4c2dc355" bindtap="__e"><u-icon vue-id="398b2851-2" name="{{comment.is_liked?'heart-fill':'heart'}}" color="{{comment.is_liked?'#f56c6c':'#999'}}" size="28" class="data-v-4c2dc355" bind:__l="__l"></u-icon><text class="data-v-4c2dc355">{{comment.likes}}</text></view></view><view class="content data-v-4c2dc355"><text class="data-v-4c2dc355">{{showFullContent?comment.content:$root.g0>100?$root.g1+'...':comment.content}}</text><block wx:if="{{$root.g2>100}}"><view data-event-opts="{{[['tap',[['toggleContent',['$event']]]]]}}" class="expand-btn data-v-4c2dc355" bindtap="__e">{{''+(showFullContent?'收起':'展开')+''}}</view></block></view><view class="actions data-v-4c2dc355"><block wx:if="{{isCommentOwner}}"><view data-event-opts="{{[['tap',[['showDeleteCommentConfirm',['$event']]]]]}}" class="delete-btn data-v-4c2dc355" bindtap="__e"><u-icon vue-id="398b2851-3" name="trash" color="#999" size="28" class="data-v-4c2dc355" bind:__l="__l"></u-icon></view></block></view></view><view class="replies-container data-v-4c2dc355"><view class="replies-header data-v-4c2dc355"><text class="data-v-4c2dc355">{{"回复 ("+replyCount+")"}}</text><view class="sort-options data-v-4c2dc355"><view class="van-tabs data-v-4c2dc355"><view class="van-tabs__wrap data-v-4c2dc355"><view class="van-tabs__nav data-v-4c2dc355"><view data-event-opts="{{[['tap',[['changeSort',['hot']]]]]}}" class="{{['van-tab','no-highlight','data-v-4c2dc355',(sortBy==='hot')?'van-tab--active':'']}}" bindtap="__e"><view class="van-tab__text data-v-4c2dc355">最热</view></view><view data-event-opts="{{[['tap',[['changeSort',['new']]]]]}}" class="{{['van-tab','no-highlight','data-v-4c2dc355',(sortBy==='new')?'van-tab--active':'']}}" bindtap="__e"><view class="van-tab__text data-v-4c2dc355">最新</view></view><view data-event-opts="{{[['tap',[['changeSort',['my']]]]]}}" class="{{['van-tab','no-highlight','data-v-4c2dc355',(sortBy==='my')?'van-tab--active':'']}}" bindtap="__e"><view class="van-tab__text data-v-4c2dc355">我的</view></view></view></view></view></view></view><view class="reply-list data-v-4c2dc355"><block wx:if="{{!($root.g3>0)}}"><view class="empty-replies data-v-4c2dc355"><image class="empty-image data-v-4c2dc355" src="/static/icon/no-messages.png" mode></image></view></block><block wx:else><block wx:for="{{$root.l0}}" wx:for-item="reply" wx:for-index="index" wx:key="index"><view class="reply-item data-v-4c2dc355" id="{{'reply-'+index}}"><view class="reply-user-info data-v-4c2dc355"><image class="reply-avatar data-v-4c2dc355" src="{{reply.$orig.user.avatar}}" mode="aspectFill" lazy-load="{{true}}"></image><view class="reply-user-meta data-v-4c2dc355"><view class="reply-nickname data-v-4c2dc355">{{''+reply.$orig.user.nickname+''}}<block wx:if="{{reply.$orig.reply_to}}"><text class="reply-text data-v-4c2dc355">回复</text></block><block wx:if="{{reply.$orig.reply_to}}"><text class="reply-to data-v-4c2dc355">{{reply.$orig.reply_to.nickname}}</text></block></view><view class="reply-time data-v-4c2dc355">{{reply.m2}}</view></view><view data-event-opts="{{[['tap',[['likeReply',['$0',index],[[['replies','',index]]]]]]]}}" class="reply-like data-v-4c2dc355" bindtap="__e"><u-icon vue-id="{{'398b2851-4-'+index}}" name="{{reply.$orig.is_liked?'heart-fill':'heart'}}" color="{{reply.$orig.is_liked?'#f56c6c':'#999'}}" size="24" class="data-v-4c2dc355" bind:__l="__l"></u-icon><text class="data-v-4c2dc355">{{reply.$orig.likes}}</text></view></view><view data-event-opts="{{[['tap',[['replyToComment',['$0'],[[['replies','',index]]]]]]]}}" class="reply-content data-v-4c2dc355" bindtap="__e"><text class="data-v-4c2dc355">{{reply.$orig.showFullContent?reply.$orig.content:reply.g4>100?reply.g5+'...':reply.$orig.content}}</text><block wx:if="{{reply.g6>100}}"><view data-event-opts="{{[['tap',[['toggleReplyContent',['$0',index],[[['replies','',index]]]]]]]}}" class="expand-btn data-v-4c2dc355" catchtap="__e">{{''+(reply.$orig.showFullContent?'收起':'展开')+''}}</view></block></view><view class="reply-actions data-v-4c2dc355"><view data-event-opts="{{[['tap',[['replyToComment',['$0'],[[['replies','',index]]]]]]]}}" class="reply-reply data-v-4c2dc355" bindtap="__e"><image src="/static/icon/chat.png" mode="aspectFill" class="data-v-4c2dc355"></image></view><view data-event-opts="{{[['tap',[['showMoreOptions',['$0'],[[['replies','',index]]]]]]]}}" class="more-btn data-v-4c2dc355" catchtap="__e"><image src="/static/icon/more.png" mode="aspectFill" class="data-v-4c2dc355"></image></view></view></view></block></block><block wx:if="{{pagination.loading}}"><view class="loading-more-skeleton data-v-4c2dc355"><block wx:for="{{4}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><reply-skeleton vue-id="{{'398b2851-5-'+__i0__}}" class="data-v-4c2dc355" bind:__l="__l"></reply-skeleton></block></view></block><block wx:else><block wx:if="{{$root.g7}}"><view class="no-more data-v-4c2dc355"><text class="data-v-4c2dc355">没有更多回复了</text></view></block></block></view></view></block></block></scroll-view><block wx:if="{{isReplying||isKeyboardShow}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="mask-layer data-v-4c2dc355" bindtap="__e"></view></block><view class="input-container data-v-4c2dc355" style="{{'bottom:'+(inputContainerBottom+'px')+';'}}"><comment-input vue-id="398b2851-6" placeholder="{{inputPlaceholder}}" use-image-button="{{true}}" data-ref="commentInput" value="{{replyText}}" data-event-opts="{{[['^send',[['sendReply']]],['^focus',[['onInputFocus']]],['^blur',[['onInputBlur']]],['^input',[['__set_model',['','replyText','$event',[]]]]]]}}" bind:send="__e" bind:focus="__e" bind:blur="__e" bind:input="__e" class="data-v-4c2dc355 vue-ref" bind:__l="__l"></comment-input></view><u-popup bind:input="__e" vue-id="398b2851-7" mode="bottom" border-radius="30" value="{{showMorePopup}}" data-event-opts="{{[['^input',[['__set_model',['','showMorePopup','$event',[]]]]]]}}" class="data-v-4c2dc355" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-popup data-v-4c2dc355"><view data-event-opts="{{[['tap',[['replyFromMore',['$event']]]]]}}" class="action-item reply data-v-4c2dc355" bindtap="__e"><view class="action-icon data-v-4c2dc355"><image src="/static/icon/chat-1.png" mode="aspectFill" class="data-v-4c2dc355"></image></view><text class="data-v-4c2dc355">回复</text></view><view data-event-opts="{{[['tap',[['copyComment',['$event']]]]]}}" class="action-item copy data-v-4c2dc355" bindtap="__e"><view class="action-icon data-v-4c2dc355"><image src="/static/icon/copy.png" mode="aspectFill" class="data-v-4c2dc355"></image></view><text class="data-v-4c2dc355">复制</text></view><block wx:if="{{$root.m3}}"><view data-event-opts="{{[['tap',[['deleteReply',['$event']]]]]}}" class="action-item report block data-v-4c2dc355" bindtap="__e"><view class="action-icon data-v-4c2dc355"><image src="/static/icon/delete.png" mode="aspectFill" class="data-v-4c2dc355"></image></view><text class="data-v-4c2dc355">删除</text></view></block></view></u-popup></view>