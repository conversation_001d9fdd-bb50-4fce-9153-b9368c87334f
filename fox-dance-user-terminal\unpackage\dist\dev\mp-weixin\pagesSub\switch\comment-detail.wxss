@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.comment-detail.data-v-4c2dc355 {
  display: flex;
  flex-direction: column;
  height: 94vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
}
/* 移除点击高亮效果 */
.no-highlight.data-v-4c2dc355 {
  -webkit-tap-highlight-color: transparent;
}
.filter-bar.data-v-4c2dc355 {
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);
  /* 启用GPU加速 */
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.back-btn.data-v-4c2dc355 {
  padding: 10rpx;
}
.page-title.data-v-4c2dc355 {
  flex: 1;
  font-size: 30rpx;
  /* 优化：从34rpx减小到30rpx，更符合移动端标准 */
  font-weight: 600;
  text-align: center;
  margin-right: 44rpx;
  /* 为了居中，右侧留出与左侧返回按钮相同的空间 */
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  letter-spacing: 0.5rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.page-title.data-v-4c2dc355 {
    color: #ff6b87 !important;
    background: none;
}
}
.comment-container.data-v-4c2dc355 {
  flex: 1;
  position: relative;
  padding: 24rpx;
  width: auto;
}
.loading.data-v-4c2dc355 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}
.loading text.data-v-4c2dc355 {
  margin-top: 32rpx;
  font-size: 30rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.main-comment.data-v-4c2dc355 {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 6rpx 24rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 优化过渡效果，只对transform进行过渡 */
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
  /* 启用GPU加速 */
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.main-comment .user-info.data-v-4c2dc355 {
  display: flex;
  margin-bottom: 24rpx;
}
.main-comment .user-info .avatar-wrap.data-v-4c2dc355 {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 28rpx;
  border: 3rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 135, 0.15);
  /* 优化过渡效果，只对transform进行过渡 */
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.main-comment .user-info .avatar-wrap.data-v-4c2dc355:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.main-comment .user-info .avatar-wrap .avatar.data-v-4c2dc355 {
  width: 100%;
  height: 100%;
}
.main-comment .user-info .user-meta.data-v-4c2dc355 {
  flex: 1;
}
.main-comment .user-info .user-meta .nickname.data-v-4c2dc355 {
  font-size: 28rpx;
  /* 优化：从32rpx减小到28rpx，用户名更精致 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #4a4a4a;
  background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
  display: flex;
  align-items: center;
  letter-spacing: 0.3rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.main-comment .user-info .user-meta .nickname.data-v-4c2dc355 {
    color: #4a4a4a !important;
    background: none;
}
}
.main-comment .user-info .user-meta .nickname .user-level.data-v-4c2dc355 {
  font-size: 22rpx;
  color: #ffffff !important;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  margin-left: 16rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  /* 确保文字显示，移除可能导致兼容性问题的属性 */
  -webkit-text-fill-color: initial;
  -webkit-background-clip: initial;
  background-clip: initial;
}
.main-comment .user-info .user-meta .time.data-v-4c2dc355 {
  font-size: 26rpx;
  color: #8a8a8a;
  margin-top: 12rpx;
  font-weight: 400;
  opacity: 0.8;
}
.main-comment .user-info .like-btn.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);
  align-self: flex-start;
  margin-top: 8rpx;
  /* 添加心形图标的样式 */
}
.main-comment .user-info .like-btn.data-v-4c2dc355:active {
  background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);
}
.main-comment .user-info .like-btn text.data-v-4c2dc355 {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
.main-comment .user-info .like-btn :deep(.u-icon__icon).uicon-heart-fill.data-v-4c2dc355, .main-comment .user-info .like-btn :deep(.u-icon__icon).uicon-heart.data-v-4c2dc355 {
  font-weight: bold;
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.main-comment .user-info .like-btn :deep(.u-icon__icon).uicon-heart-fill.data-v-4c2dc355 {
  /* 移除复杂的心跳动画和阴影效果，提升性能 */
  color: #f56c6c;
}
.main-comment .content.data-v-4c2dc355 {
  font-size: 30rpx;
  /* 优化：从32rpx减小到30rpx，正文更符合移动端标准 */
  line-height: 1.8;
  color: #4a4a4a;
  margin-bottom: 28rpx;
  word-break: break-all;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}
.main-comment .content .expand-btn.data-v-4c2dc355 {
  color: #ff6b87;
  font-size: 28rpx;
  display: inline-block;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.main-comment .content .expand-btn.data-v-4c2dc355:active {
  background: rgba(255, 107, 135, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.main-comment .actions.data-v-4c2dc355 {
  display: flex;
  margin-top: 24rpx;
}
.main-comment .actions .reply-btn.data-v-4c2dc355,
.main-comment .actions .delete-btn.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
  padding: 12rpx 20rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
  /* 添加图标的样式 */
}
.main-comment .actions .reply-btn.data-v-4c2dc355:active,
.main-comment .actions .delete-btn.data-v-4c2dc355:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.main-comment .actions .reply-btn text.data-v-4c2dc355,
.main-comment .actions .delete-btn text.data-v-4c2dc355 {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
.main-comment .actions .reply-btn :deep(.u-icon__icon).uicon-message-circle.data-v-4c2dc355, .main-comment .actions .reply-btn :deep(.u-icon__icon).uicon-trash.data-v-4c2dc355, .main-comment .actions .delete-btn :deep(.u-icon__icon).uicon-message-circle.data-v-4c2dc355, .main-comment .actions .delete-btn :deep(.u-icon__icon).uicon-trash.data-v-4c2dc355 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  transition: all 0.3s ease;
  color: #ff6b87;
}
.replies-container.data-v-4c2dc355 {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 6rpx 24rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 启用GPU加速 */
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
  position: relative;
  overflow: hidden;
}
.replies-container .replies-header.data-v-4c2dc355 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);
}
.replies-container .replies-header text.data-v-4c2dc355 {
  font-size: 28rpx;
  /* 优化：从32rpx减小到28rpx，回复标题更精致 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  letter-spacing: 0.5rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.replies-container .replies-header text.data-v-4c2dc355 {
    color: #ff6b87 !important;
    background: none;
}
}
.replies-container .replies-header .sort-options .van-tabs.data-v-4c2dc355 {
  position: relative;
  display: flex;
  justify-content: flex-end;
  -webkit-tap-highlight-color: transparent;
}
.replies-container .replies-header .sort-options .van-tabs__wrap.data-v-4c2dc355 {
  overflow: hidden;
  position: relative;
  padding: 0;
}
.replies-container .replies-header .sort-options .van-tabs__nav.data-v-4c2dc355 {
  position: relative;
  display: flex;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  height: 72rpx;
  border-radius: 36rpx;
  -webkit-user-select: none;
          user-select: none;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  padding: 6rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.replies-container .replies-header .sort-options .van-tab.data-v-4c2dc355 {
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 26rpx;
  min-width: 80rpx;
  margin: 0 2rpx;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.replies-container .replies-header .sort-options .van-tab__text.data-v-4c2dc355 {
  font-size: 24rpx;
  color: #8a8a8a;
  line-height: 1.2;
  padding: 0 16rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.replies-container .replies-header .sort-options .van-tab--active.data-v-4c2dc355 {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
  -webkit-transform: translateY(-2rpx) scale(1.02);
          transform: translateY(-2rpx) scale(1.02);
}
.replies-container .replies-header .sort-options .van-tab--active .van-tab__text.data-v-4c2dc355 {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.reply-list .empty-image.data-v-4c2dc355 {
  width: 462rpx;
  height: 256rpx;
  margin: 0 auto;
}
.reply-list .empty-replies.data-v-4c2dc355 {
  padding: 80rpx 0;
  text-align: center;
}
.reply-list .empty-replies .empty-text.data-v-4c2dc355 {
  font-size: 32rpx;
  /* 优化：从36rpx减小到32rpx，空状态文字更合理 */
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  font-weight: 600;
  margin-top: 24rpx;
  letter-spacing: 0.5rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.reply-list .empty-replies .empty-text.data-v-4c2dc355 {
    color: #ff6b87 !important;
    background: none;
}
}
.reply-list .empty-replies .empty-subtext.data-v-4c2dc355 {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-top: 16rpx;
  font-weight: 400;
  opacity: 0.8;
}
.reply-list .reply-item.data-v-4c2dc355 {
  padding: 28rpx 0;
  border-bottom: 1rpx solid rgba(255, 105, 135, 0.1);
  transition: all 0.3s ease;
}
.reply-list .reply-item.data-v-4c2dc355:last-child {
  border-bottom: none;
}
.reply-list .reply-item .reply-user-info.data-v-4c2dc355 {
  display: flex;
  margin-bottom: 20rpx;
}
.reply-list .reply-item .reply-user-info .reply-avatar.data-v-4c2dc355 {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 2rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
}
.reply-list .reply-item .reply-user-info .reply-avatar.data-v-4c2dc355:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.reply-list .reply-item .reply-user-info .reply-user-meta.data-v-4c2dc355 {
  flex: 1;
}
.reply-list .reply-item .reply-user-info .reply-user-meta .reply-nickname.data-v-4c2dc355 {
  font-size: 26rpx;
  /* 优化：从28rpx减小到26rpx，回复用户名更精致 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #4a4a4a;
  background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  letter-spacing: 0.3rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.reply-list .reply-item .reply-user-info .reply-user-meta .reply-nickname.data-v-4c2dc355 {
    color: #4a4a4a !important;
    background: none;
}
}
.reply-list .reply-item .reply-user-info .reply-user-meta .reply-nickname .reply-text.data-v-4c2dc355 {
  color: #8a8a8a;
  font-weight: normal;
  margin: 0 10rpx;
  font-size: 26rpx;
}
.reply-list .reply-item .reply-user-info .reply-user-meta .reply-nickname .reply-to.data-v-4c2dc355 {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87 !important;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.reply-list .reply-item .reply-user-info .reply-user-meta .reply-nickname .reply-to.data-v-4c2dc355 {
    color: #ff6b87 !important;
    background: none;
}
}
.reply-list .reply-item .reply-user-info .reply-user-meta .reply-time.data-v-4c2dc355 {
  font-size: 24rpx;
  color: #8a8a8a;
  margin-top: 12rpx;
  font-weight: 400;
  opacity: 0.8;
}
.reply-list .reply-item .reply-user-info .reply-like.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  align-self: flex-start;
  margin-top: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
  /* 添加心形图标的样式 */
}
.reply-list .reply-item .reply-user-info .reply-like.data-v-4c2dc355:active {
  background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(255, 107, 135, 0.15);
}
.reply-list .reply-item .reply-user-info .reply-like text.data-v-4c2dc355 {
  font-size: 24rpx;
  color: #ff6b87;
  margin-left: 8rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
.reply-list .reply-item .reply-user-info .reply-like :deep(.u-icon__icon).uicon-heart-fill.data-v-4c2dc355, .reply-list .reply-item .reply-user-info .reply-like :deep(.u-icon__icon).uicon-heart.data-v-4c2dc355 {
  font-weight: bold;
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.reply-list .reply-item .reply-user-info .reply-like :deep(.u-icon__icon).uicon-heart-fill.data-v-4c2dc355 {
  /* 移除复杂的心跳动画和阴影效果，提升性能 */
  color: #f56c6c;
}
.reply-list .reply-item .reply-content.data-v-4c2dc355 {
  font-size: 28rpx;
  /* 优化：从30rpx减小到28rpx，回复内容更精致 */
  line-height: 1.8;
  color: #4a4a4a;
  margin-bottom: 16rpx;
  padding-left: 96rpx;
  word-break: break-all;
  cursor: pointer;
  font-weight: 400;
  letter-spacing: 0.3rpx;
  /* 添加触摸反馈效果 */
}
.reply-list .reply-item .reply-content.data-v-4c2dc355:active {
  background-color: rgba(255, 107, 135, 0.05);
  border-radius: 12rpx;
}
.reply-list .reply-item .reply-content text.data-v-4c2dc355 {
  display: block;
}
.reply-list .reply-item .reply-content .expand-btn.data-v-4c2dc355 {
  color: #ff6b87;
  font-size: 26rpx;
  display: inline-block;
  font-weight: 600;
  margin-top: 12rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.reply-list .reply-item .reply-content .expand-btn.data-v-4c2dc355:active {
  background: rgba(255, 107, 135, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.reply-list .reply-item .reply-actions.data-v-4c2dc355 {
  display: flex;
  margin-top: 10rpx;
  justify-content: space-between;
}
.reply-list .reply-item .reply-actions .reply-reply.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  margin-left: 84rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
  /* 添加回复图标的样式 */
}
.reply-list .reply-item .reply-actions .reply-reply image.data-v-4c2dc355 {
  width: 28rpx;
  height: 28rpx;
}
.reply-list .reply-item .reply-actions .reply-reply.data-v-4c2dc355:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.reply-list .reply-item .reply-actions .reply-reply text.data-v-4c2dc355 {
  font-size: 22rpx;
  color: #667eea;
  margin-left: 8rpx;
  font-weight: 500;
}
.reply-list .reply-item .reply-actions .reply-reply :deep(.u-icon__icon).uicon-message-circle.data-v-4c2dc355 {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  transition: all 0.3s ease;
}
.reply-list .reply-item .reply-actions .more-btn.data-v-4c2dc355 {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
}
.reply-list .reply-item .reply-actions .more-btn.data-v-4c2dc355:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.reply-list .reply-item .reply-actions .more-btn image.data-v-4c2dc355 {
  width: 100%;
  height: 100%;
}
.reply-list .loading-more.data-v-4c2dc355 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48rpx 0;
}
.reply-list .loading-more text.data-v-4c2dc355 {
  margin-left: 24rpx;
  font-size: 30rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.reply-list .no-more.data-v-4c2dc355 {
  text-align: center;
  padding: 48rpx 0;
}
.reply-list .no-more text.data-v-4c2dc355 {
  font-size: 30rpx;
  color: #b0b0b0;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.input-container.data-v-4c2dc355 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  box-shadow: 0 -8rpx 32rpx rgba(255, 105, 135, 0.08);
  border-top: 1rpx solid rgba(255, 105, 135, 0.1);
  transition: bottom 0.3s ease-in-out;
}
/* 蒙版层样式 */
.mask-layer.data-v-4c2dc355 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  opacity: 0;
  -webkit-animation: maskFadeIn-data-v-4c2dc355 0.3s ease-out forwards;
          animation: maskFadeIn-data-v-4c2dc355 0.3s ease-out forwards;
}
/* 更多操作弹窗样式 - 小红书风格 */
.action-popup.data-v-4c2dc355 {
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  padding: 32rpx 0;
}
.action-item.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  margin: 0 24rpx;
  background: #ffffff;
  transition: all 0.3s ease;
  /* 第一个action-item的样式 */
  /* 第二个action-item的样式 */
  /* 第三个action-item的样式 */
  /* 最后一个action-item的样式 */
}
.action-item.data-v-4c2dc355:active {
  background: #f8fafc;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.action-item .action-icon.data-v-4c2dc355 {
  width: 44rpx;
  height: 44rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-item .action-icon image.data-v-4c2dc355 {
  width: 100%;
  height: 100%;
}
.action-item.reply.data-v-4c2dc355 {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
}
.action-item.copy.data-v-4c2dc355 {
  margin-bottom: 24rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}
.action-item.report.data-v-4c2dc355 {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
}
.action-item.block.data-v-4c2dc355 {
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}
.action-item text.data-v-4c2dc355 {
  font-size: 28rpx;
  color: #334155;
  font-weight: 500;
}
.action-cancel.data-v-4c2dc355 {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-top: 20rpx;
  border-radius: 20rpx;
}
.action-cancel text.data-v-4c2dc355 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.action-cancel.data-v-4c2dc355:active {
  background-color: #f5f5f5;
}
/* 加载更多状态样式 */
.loading-more.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}
.loading-more .loading-text.data-v-4c2dc355 {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #667eea;
}
/* 回复骨架屏加载状态样式 */
.loading-more-skeleton.data-v-4c2dc355 {
  padding: 0 32rpx;
  /* 骨架屏淡入动画 */
  -webkit-animation: replySkeletonFadeIn-data-v-4c2dc355 0.3s ease-out;
          animation: replySkeletonFadeIn-data-v-4c2dc355 0.3s ease-out;
}
.no-more.data-v-4c2dc355 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}
.no-more text.data-v-4c2dc355 {
  font-size: 28rpx;
  color: #94a3b8;
}
/* 蒙版层淡入动画 */
@-webkit-keyframes maskFadeIn-data-v-4c2dc355 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes maskFadeIn-data-v-4c2dc355 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
/* 小红书风格动画效果 */
@-webkit-keyframes heartBeat-data-v-4c2dc355 {
0% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
14% {
    -webkit-transform: scale(1.4);
            transform: scale(1.4);
}
28% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
42% {
    -webkit-transform: scale(1.4);
            transform: scale(1.4);
}
70% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@keyframes heartBeat-data-v-4c2dc355 {
0% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
14% {
    -webkit-transform: scale(1.4);
            transform: scale(1.4);
}
28% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
42% {
    -webkit-transform: scale(1.4);
            transform: scale(1.4);
}
70% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@-webkit-keyframes fadeInUp-data-v-4c2dc355 {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-4c2dc355 {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes shimmer-data-v-4c2dc355 {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}
@keyframes shimmer-data-v-4c2dc355 {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}
/* 性能优化：移除入场动画，避免滚动时重复触发导致卡顿 */
/* 渐变文字效果 - 微信小程序兼容版本 */
.gradient-text.data-v-4c2dc355 {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.gradient-text.data-v-4c2dc355 {
    color: #ff6b87 !important;
    background: none;
}
}
/* 性能优化：移除消耗性能的毛玻璃效果 */
.glass-effect.data-v-4c2dc355 {
  /* backdrop-filter: blur(20rpx); */
  /* -webkit-backdrop-filter: blur(20rpx); */
  background: rgba(255, 255, 255, 0.9);
}
/* 悬浮阴影效果 */
.floating-shadow.data-v-4c2dc355 {
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);
  transition: box-shadow 0.3s ease;
}
.floating-shadow.data-v-4c2dc355:hover {
  box-shadow: 0 12rpx 48rpx rgba(255, 105, 135, 0.2);
}
/* 响应式设计优化 */
@media (max-width: 750rpx) {
.main-comment.data-v-4c2dc355 {
    padding: 32rpx;
}
.replies-container.data-v-4c2dc355 {
    padding: 32rpx;
}
.reply-content.data-v-4c2dc355 {
    font-size: 26rpx;
    /* 小屏幕优化：进一步减小回复内容字体 */
}
}
/* 回复骨架屏动画 */
@-webkit-keyframes replySkeletonFadeIn-data-v-4c2dc355 {
from {
    opacity: 0;
    -webkit-transform: translateY(15rpx);
            transform: translateY(15rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes replySkeletonFadeIn-data-v-4c2dc355 {
from {
    opacity: 0;
    -webkit-transform: translateY(15rpx);
            transform: translateY(15rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}

