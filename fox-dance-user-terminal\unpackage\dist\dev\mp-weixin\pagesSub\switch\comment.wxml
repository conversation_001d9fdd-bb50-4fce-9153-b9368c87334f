<view class="comment-page data-v-7270b30e"><scroll-view class="page-scroll-view data-v-7270b30e vue-ref" style="{{'height:'+(pageHeight)+';'}}" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" lower-threshold="{{100}}" scroll-top="{{scrollTop}}" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="{{true}}" data-ref="commentScrollView" data-event-opts="{{[['refresherrefresh',[['onRefresh',['$event']]]],['scrolltolower',[['loadMoreComments',['$event']]]]]}}" bindrefresherrefresh="__e" bindscrolltolower="__e"><block wx:if="{{$root.g0}}"><view class="topic-images-container data-v-7270b30e"><swiper class="topic-images-swiper data-v-7270b30e" indicator-dots="{{$root.g1>1}}" autoplay="{{false}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#ffffff"><block wx:for="{{$root.l0}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item class="data-v-7270b30e"><image class="topic-image data-v-7270b30e" src="{{image.m0}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleTopicImageError',[index]]]],['tap',[['previewTopicImage',[index]]]]]}}" binderror="__e" bindtap="__e"></image></swiper-item></block></swiper></view></block><block wx:if="{{topicInfo}}"><view class="topic-info-section data-v-7270b30e"><view class="topic-header data-v-7270b30e"><view class="topic-title data-v-7270b30e">{{topicInfo.title}}</view><block wx:if="{{topicInfo.description}}"><view class="topic-desc data-v-7270b30e">{{topicInfo.description}}</view></block><view class="topic-meta data-v-7270b30e"><text class="participants data-v-7270b30e">{{(topicInfo.commentUserCount||0)+"人参与此话题"}}</text><block wx:if="{{topicInfo.createTime}}"><text class="create-time data-v-7270b30e">{{"· "+$root.m1}}</text></block></view></view></view></block><block wx:if="{{storeInfo}}"><view class="store-info-section data-v-7270b30e"><view class="store-header data-v-7270b30e"><view class="store-icon data-v-7270b30e"><image class="icon data-v-7270b30e" src="{{storeInfo.storeImage}}" mode="aspectFit"></image></view><view class="store-content data-v-7270b30e"><view class="store-title data-v-7270b30e">{{storeInfo.title}}</view><block wx:if="{{storeInfo.description}}"><view class="store-desc data-v-7270b30e">{{storeInfo.description}}</view></block></view></view></view></block><view class="filter-bar data-v-7270b30e"><view class="comment-count-section data-v-7270b30e"><text class="comment-count-text data-v-7270b30e">{{"评论("+$root.m2+")"}}</text></view><view class="filter-tabs-section data-v-7270b30e"><view class="van-tabs data-v-7270b30e"><view class="van-tabs__wrap data-v-7270b30e"><view class="van-tabs__nav data-v-7270b30e"><view data-event-opts="{{[['tap',[['changeFilter',['hot']]]]]}}" class="{{['van-tab','data-v-7270b30e',(activeFilter==='hot')?'van-tab--active':'']}}" bindtap="__e"><view class="van-tab__text data-v-7270b30e">最热</view></view><view data-event-opts="{{[['tap',[['changeFilter',['new']]]]]}}" class="{{['van-tab','data-v-7270b30e',(activeFilter==='new')?'van-tab--active':'']}}" bindtap="__e"><view class="van-tab__text data-v-7270b30e">最新</view></view><view data-event-opts="{{[['tap',[['changeFilter',['my']]]]]}}" class="{{['van-tab','data-v-7270b30e',(activeFilter==='my')?'van-tab--active':'']}}" bindtap="__e"><view class="van-tab__text data-v-7270b30e">我的</view></view></view></view></view></view></view><view class="comment-list-container data-v-7270b30e"><block wx:if="{{activeFilter==='hot'}}"><view class="comment-list data-v-7270b30e"><block wx:if="{{loading&&activeFilter==='hot'}}"><view class="loading data-v-7270b30e"><u-loading vue-id="122da9ad-1" mode="flower" size="50" color="#667eea" class="data-v-7270b30e" bind:__l="__l"></u-loading><view class="loading-text data-v-7270b30e">正在加载热门评论...</view></view></block><block wx:else><block wx:if="{{$root.g2==0}}"><view class="empty-tip data-v-7270b30e"><image class="empty-image data-v-7270b30e" src="/static/icon/null.png" mode></image><view class="empty-text data-v-7270b30e">暂无热门评论</view><view class="empty-subtext data-v-7270b30e">快来发表第一条评论吧~</view><view data-event-opts="{{[['tap',[['focusInput',['$event']]]]]}}" class="empty-action data-v-7270b30e" bindtap="__e"><text class="data-v-7270b30e">立即评论</text></view></view></block><block wx:else><block class="data-v-7270b30e"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="comment-item data-v-7270b30e" id="{{'comment-hot-'+index}}" data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['commentListHot','',index]]]]]]]}}" bindtap="__e"><view class="user-avatar data-v-7270b30e"><image src="{{item.m3}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageLoadError',['$0','用户头像'],[[['commentListHot','',index,'user.avatar']]]]]]]}}" binderror="__e" class="data-v-7270b30e"></image></view><view class="comment-content data-v-7270b30e"><view class="user-info-row data-v-7270b30e"><view class="user-info data-v-7270b30e"><view class="user-name data-v-7270b30e">{{''+item.$orig.user.nickname+''}}<block wx:if="{{item.$orig.user.level>=0}}"><view class="user-level data-v-7270b30e" style="{{'background-color:'+(item.m4)+';'}}">{{"Lv."+item.$orig.user.level}}</view></block></view><view class="time data-v-7270b30e">{{item.m5}}</view></view><view data-event-opts="{{[['tap',[['likeComment',['$0',index,'hot'],[[['commentListHot','',index]]]]]]]}}" class="like-btn data-v-7270b30e" catchtap="__e"><u-icon vue-id="{{'122da9ad-2-'+index}}" name="{{item.$orig.is_liked?'heart-fill':'heart'}}" color="{{item.$orig.is_liked?'#f56c6c':'#999'}}" size="28" class="data-v-7270b30e" bind:__l="__l"></u-icon><text class="data-v-7270b30e">{{item.$orig.likes}}</text></view></view><view class="text data-v-7270b30e"><text class="data-v-7270b30e">{{item.$orig.showFullContent?item.$orig.content:item.g3>100?item.g4+'...':item.$orig.content}}</text><block wx:if="{{item.g5>100}}"><view data-event-opts="{{[['tap',[['toggleContent',['$0',index,'hot'],[[['commentListHot','',index]]]]]]]}}" class="expand-btn data-v-7270b30e" catchtap="__e">{{''+(item.$orig.showFullContent?'收起':'展开')+''}}</view></block></view><view class="actions data-v-7270b30e"><view data-event-opts="{{[['tap',[['replyComment',['$0'],[[['commentListHot','',index]]]]]]]}}" class="reply-btn data-v-7270b30e" catchtap="__e"><image src="/static/icon/chat.png" mode="aspectFill" class="data-v-7270b30e"></image><text class="data-v-7270b30e">回复</text></view><view data-event-opts="{{[['tap',[['showMoreOptions',['$0'],[[['commentListHot','',index]]]]]]]}}" class="more-btn data-v-7270b30e" catchtap="__e"><image src="/static/icon/more.png" mode="aspectFill" class="data-v-7270b30e"></image></view></view><block wx:if="{{item.g6}}"><view class="reply-preview data-v-7270b30e"><block wx:for="{{item.l1}}" wx:for-item="reply" wx:for-index="rIndex" wx:key="rIndex"><view class="reply-item data-v-7270b30e"><text class="reply-nickname data-v-7270b30e">{{reply.$orig.user&&reply.$orig.user.nickname}}</text><block wx:if="{{reply.$orig.reply_to}}"><text class="reply-to data-v-7270b30e">{{"@"+reply.$orig.reply_to.nickname}}</text></block><text class="reply-content data-v-7270b30e">{{": "+(reply.g7>50?reply.g8+'...':reply.$orig.content)}}</text></view></block><block wx:if="{{item.$orig.reply_count>2}}"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['commentListHot','',index]]]]]]]}}" class="view-more data-v-7270b30e" catchtap="__e">{{'查看全部'+item.$orig.reply_count+'条回复 >'}}</view></block></view></block></view></view></block><block wx:if="{{pagination.hot.loading}}"><view class="loading-more-skeleton data-v-7270b30e"><block wx:for="{{3}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><comment-skeleton vue-id="{{'122da9ad-3-'+__i0__}}" class="data-v-7270b30e" bind:__l="__l"></comment-skeleton></block></view></block><block wx:else><block wx:if="{{$root.g9}}"><view class="no-more data-v-7270b30e"><text class="data-v-7270b30e">没有更多评论了</text></view></block></block><view class="bottom-space data-v-7270b30e"></view></block></block></block></view></block><block wx:if="{{activeFilter==='new'}}"><view class="comment-list data-v-7270b30e"><block wx:if="{{loading&&activeFilter==='new'}}"><view class="loading data-v-7270b30e"><u-loading vue-id="122da9ad-4" mode="flower" size="50" color="#ff6b87" class="data-v-7270b30e" bind:__l="__l"></u-loading><view class="loading-text data-v-7270b30e">正在加载最新评论...</view></view></block><block wx:else><block wx:if="{{$root.g10==0}}"><view class="empty-tip data-v-7270b30e"><image class="empty-image data-v-7270b30e" src="/static/icon/null.png" mode></image><view class="empty-text data-v-7270b30e">暂无最新评论</view><view class="empty-subtext data-v-7270b30e">快来发表第一条评论吧~</view><view data-event-opts="{{[['tap',[['focusInput',['$event']]]]]}}" class="empty-action data-v-7270b30e" bindtap="__e"><text class="data-v-7270b30e">立即评论</text></view></view></block><block wx:else><block class="data-v-7270b30e"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="comment-item data-v-7270b30e" id="{{'comment-new-'+index}}" data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['commentListNew','',index]]]]]]]}}" bindtap="__e"><view class="user-avatar data-v-7270b30e"><image src="{{item.m6}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageLoadError',['$0','用户头像'],[[['commentListNew','',index,'user.avatar']]]]]]]}}" binderror="__e" class="data-v-7270b30e"></image></view><view class="comment-content data-v-7270b30e"><view class="user-info-row data-v-7270b30e"><view class="user-info data-v-7270b30e"><view class="user-name data-v-7270b30e">{{''+item.$orig.user.nickname+''}}<block wx:if="{{item.$orig.user.level>=0}}"><view class="user-level data-v-7270b30e" style="{{'background-color:'+(item.m7)+';'}}">{{"Lv"+item.$orig.user.level}}</view></block></view><view class="time data-v-7270b30e">{{item.m8}}</view></view><view data-event-opts="{{[['tap',[['likeComment',['$0',index,'new'],[[['commentListNew','',index]]]]]]]}}" class="like-btn data-v-7270b30e" catchtap="__e"><u-icon vue-id="{{'122da9ad-5-'+index}}" name="{{item.$orig.is_liked?'heart-fill':'heart'}}" color="{{item.$orig.is_liked?'#f56c6c':'#999'}}" size="28" class="data-v-7270b30e" bind:__l="__l"></u-icon><text class="data-v-7270b30e">{{item.$orig.likes}}</text></view></view><view class="text data-v-7270b30e"><text class="data-v-7270b30e">{{item.$orig.showFullContent?item.$orig.content:item.g11>100?item.g12+'...':item.$orig.content}}</text><block wx:if="{{item.g13>100}}"><view data-event-opts="{{[['tap',[['toggleContent',['$0',index,'new'],[[['commentListNew','',index]]]]]]]}}" class="expand-btn data-v-7270b30e" catchtap="__e">{{''+(item.$orig.showFullContent?'收起':'展开')+''}}</view></block></view><view class="actions data-v-7270b30e"><view data-event-opts="{{[['tap',[['replyComment',['$0'],[[['commentListNew','',index]]]]]]]}}" class="reply-btn data-v-7270b30e" catchtap="__e"><image src="/static/icon/chat.png" mode="aspectFill" class="data-v-7270b30e"></image><text class="data-v-7270b30e">回复</text></view><view data-event-opts="{{[['tap',[['showMoreOptions',['$0'],[[['commentListNew','',index]]]]]]]}}" class="more-btn data-v-7270b30e" catchtap="__e"><image src="/static/icon/more.png" mode="aspectFill" class="data-v-7270b30e"></image></view></view><block wx:if="{{item.g14}}"><view class="reply-preview data-v-7270b30e"><block wx:for="{{item.l3}}" wx:for-item="reply" wx:for-index="rIndex" wx:key="rIndex"><view class="reply-item data-v-7270b30e"><text class="reply-nickname data-v-7270b30e">{{reply.$orig.user&&reply.$orig.user.nickname}}</text><block wx:if="{{reply.$orig.reply_to}}"><text class="reply-to data-v-7270b30e">{{"@"+reply.$orig.reply_to.nickname}}</text></block><text class="reply-content data-v-7270b30e">{{": "+(reply.g15>50?reply.g16+'...':reply.$orig.content)}}</text></view></block><block wx:if="{{item.$orig.reply_count>2}}"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['commentListNew','',index]]]]]]]}}" class="view-more data-v-7270b30e" catchtap="__e">{{'查看全部'+item.$orig.reply_count+'条回复 >'}}</view></block></view></block></view></view></block><block wx:if="{{pagination.new.loading}}"><view class="loading-more-skeleton data-v-7270b30e"><block wx:for="{{3}}" wx:for-item="n" wx:for-index="__i1__" wx:key="*this"><comment-skeleton vue-id="{{'122da9ad-6-'+__i1__}}" class="data-v-7270b30e" bind:__l="__l"></comment-skeleton></block></view></block><block wx:else><block wx:if="{{$root.g17}}"><view class="no-more data-v-7270b30e"><text class="data-v-7270b30e">没有更多评论了</text></view></block></block></block></block></block></view></block><block wx:if="{{activeFilter==='my'}}"><view class="comment-list data-v-7270b30e"><block wx:if="{{loading&&activeFilter==='my'}}"><view class="loading data-v-7270b30e"><u-loading vue-id="122da9ad-7" mode="flower" size="50" color="#ff6b87" class="data-v-7270b30e" bind:__l="__l"></u-loading><view class="loading-text data-v-7270b30e">正在加载我的评论...</view></view></block><block wx:else><block wx:if="{{$root.g18}}"><view class="empty-tip data-v-7270b30e"><image class="empty-image data-v-7270b30e" src="/static/icon/null.png" mode></image><view class="empty-text data-v-7270b30e">您还没有发表过评论</view><view class="empty-subtext data-v-7270b30e">快来参与互动吧~</view><view data-event-opts="{{[['tap',[['focusInput',['$event']]]]]}}" class="empty-action data-v-7270b30e" bindtap="__e"><text class="data-v-7270b30e">立即评论</text></view></view></block><block wx:else><block class="data-v-7270b30e"><block wx:for="{{$root.l6}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="comment-item data-v-7270b30e" id="{{'comment-my-'+index}}" data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['commentListMy','',index]]]]]]]}}" bindtap="__e"><view class="user-avatar data-v-7270b30e"><image src="{{item.m9}}" mode="aspectFill" lazy-load="{{true}}" data-event-opts="{{[['error',[['handleImageLoadError',['$0','用户头像'],[[['commentListMy','',index,'user.avatar']]]]]]]}}" binderror="__e" class="data-v-7270b30e"></image></view><view class="comment-content data-v-7270b30e"><view class="user-info-row data-v-7270b30e"><view class="user-info data-v-7270b30e"><view class="user-name data-v-7270b30e">{{''+item.$orig.user.nickname+''}}<block wx:if="{{item.$orig.user.level>=0}}"><view class="user-level data-v-7270b30e" style="{{'background-color:'+(item.m10)+';'}}">{{"Lv"+item.$orig.user.level}}</view></block></view><view class="time data-v-7270b30e">{{item.m11}}</view></view><view data-event-opts="{{[['tap',[['likeComment',['$0',index,'my'],[[['commentListMy','',index]]]]]]]}}" class="like-btn data-v-7270b30e" catchtap="__e"><u-icon vue-id="{{'122da9ad-8-'+index}}" name="{{item.$orig.is_liked?'heart-fill':'heart'}}" color="{{item.$orig.is_liked?'#f56c6c':'#999'}}" size="28" class="data-v-7270b30e" bind:__l="__l"></u-icon><text class="data-v-7270b30e">{{item.$orig.likes}}</text></view></view><view class="text data-v-7270b30e"><text class="data-v-7270b30e">{{item.$orig.showFullContent?item.$orig.content:item.g19>100?item.g20+'...':item.$orig.content}}</text><block wx:if="{{item.g21>100}}"><view data-event-opts="{{[['tap',[['toggleContent',['$0',index,'my'],[[['commentListMy','',index]]]]]]]}}" class="expand-btn data-v-7270b30e" catchtap="__e">{{''+(item.$orig.showFullContent?'收起':'展开')+''}}</view></block></view><view class="actions data-v-7270b30e"><view data-event-opts="{{[['tap',[['replyComment',['$0'],[[['commentListMy','',index]]]]]]]}}" class="reply-btn data-v-7270b30e" catchtap="__e"><image src="/static/icon/chat.png" mode="aspectFill" class="data-v-7270b30e"></image><text class="data-v-7270b30e">回复</text></view><view data-event-opts="{{[['tap',[['showMoreOptions',['$0'],[[['commentListMy','',index]]]]]]]}}" class="more-btn data-v-7270b30e" catchtap="__e"><image src="/static/icon/more.png" mode="aspectFill" class="data-v-7270b30e"></image></view></view><block wx:if="{{item.g22}}"><view class="reply-preview data-v-7270b30e"><block wx:for="{{item.l5}}" wx:for-item="reply" wx:for-index="rIndex" wx:key="rIndex"><view class="reply-item data-v-7270b30e"><text class="reply-nickname data-v-7270b30e">{{reply.$orig.user&&reply.$orig.user.nickname}}</text><block wx:if="{{reply.$orig.reply_to}}"><text class="reply-to data-v-7270b30e">{{"@"+reply.$orig.reply_to.nickname}}</text></block><text class="reply-content data-v-7270b30e">{{": "+(reply.g23>50?reply.g24+'...':reply.$orig.content)}}</text></view></block><block wx:if="{{item.$orig.reply_count>2}}"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['commentListMy','',index]]]]]]]}}" class="view-more data-v-7270b30e" catchtap="__e">{{'查看全部'+item.$orig.reply_count+'条回复 >'}}</view></block></view></block></view></view></block><block wx:if="{{pagination.my.loading}}"><view class="loading-more-skeleton data-v-7270b30e"><block wx:for="{{3}}" wx:for-item="n" wx:for-index="__i2__" wx:key="*this"><comment-skeleton vue-id="{{'122da9ad-9-'+__i2__}}" class="data-v-7270b30e" bind:__l="__l"></comment-skeleton></block></view></block><block wx:else><block wx:if="{{$root.g25}}"><view class="no-more data-v-7270b30e"><text class="data-v-7270b30e">没有更多评论了</text></view></block></block></block></block></block></view></block><view class="bottom-space data-v-7270b30e"></view></view></scroll-view><block wx:if="{{isKeyboardShow}}"><view data-event-opts="{{[['tap',[['hideMaskAndKeyboard',['$event']]]]]}}" class="mask-layer data-v-7270b30e" bindtap="__e"></view></block><view class="input-container data-v-7270b30e" style="{{'bottom:'+(inputContainerBottom+'px')+';'}}"><block wx:if="{{isReplyMode}}"><view class="reply-indicator data-v-7270b30e"><view class="reply-info data-v-7270b30e"><text class="reply-text data-v-7270b30e">{{"回复 @"+(currentReply&&currentReply.user&&currentReply.user.nickname?currentReply.user.nickname:'用户')}}</text><view data-event-opts="{{[['tap',[['cancelReplyMode',['$event']]]]]}}" class="cancel-reply-btn data-v-7270b30e" bindtap="__e"><text class="data-v-7270b30e">✕</text></view></view></view></block><comment-input vue-id="122da9ad-10" placeholder="{{inputPlaceholder}}" use-image-button="{{true}}" data-ref="mainCommentInput" value="{{commentText}}" data-event-opts="{{[['^send',[['sendComment']]],['^focus',[['onInputFocus']]],['^blur',[['onInputBlur']]],['^input',[['__set_model',['','commentText','$event',[]]]]]]}}" bind:send="__e" bind:focus="__e" bind:blur="__e" bind:input="__e" class="data-v-7270b30e vue-ref" bind:__l="__l"></comment-input></view><u-popup bind:input="__e" vue-id="122da9ad-11" mode="bottom" border-radius="30" value="{{showMorePopup}}" data-event-opts="{{[['^input',[['__set_model',['','showMorePopup','$event',[]]]]]]}}" class="data-v-7270b30e" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-popup data-v-7270b30e"><view data-event-opts="{{[['tap',[['replyFromMore',['$event']]]]]}}" class="action-item reply data-v-7270b30e" bindtap="__e"><view class="action-icon data-v-7270b30e"><image src="/static/icon/chat-1.png" mode="aspectFill" class="data-v-7270b30e"></image></view><text class="data-v-7270b30e">回复</text></view><view data-event-opts="{{[['tap',[['copyComment',['$event']]]]]}}" class="action-item copy data-v-7270b30e" bindtap="__e"><view class="action-icon data-v-7270b30e"><image src="/static/icon/copy.png" mode="aspectFill" class="data-v-7270b30e"></image></view><text class="data-v-7270b30e">复制</text></view><block wx:if="{{$root.m12}}"><view data-event-opts="{{[['tap',[['deleteComment',['$event']]]]]}}" class="action-item report block data-v-7270b30e" bindtap="__e"><view class="action-icon data-v-7270b30e"><image src="/static/icon/delete.png" mode="aspectFill" class="data-v-7270b30e"></image></view><text class="data-v-7270b30e">删除</text></view></block></view></u-popup></view>