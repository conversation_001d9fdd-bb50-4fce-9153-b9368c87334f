@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.comment-page.data-v-7270b30e {
  height: 100vh;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 50%, #f8f9ff 100%);
  position: relative;
}
/* 页面滚动容器 */
.page-scroll-view.data-v-7270b30e {
  width: 94%;
  padding: 0 24rpx;
}
/* 话题图片轮播样式 - 小红书风格 */
.topic-images-container.data-v-7270b30e {
  position: relative;
  width: 100%;
  height: 520rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(255, 105, 135, 0.15);
  margin-top: 24rpx;
  background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}
.topic-images-swiper.data-v-7270b30e {
  width: 100%;
  height: 100%;
  border-radius: 32rpx;
}
.topic-image.data-v-7270b30e {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  border-radius: 32rpx;
}
.topic-image.data-v-7270b30e:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
/* 话题信息区域样式 - 性能优化版本 */
.topic-info-section.data-v-7270b30e {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  margin: 24rpx 0;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  /* 启用GPU加速 */
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.topic-header.data-v-7270b30e {
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 245, 240, 0.9) 100%);
}
.topic-title.data-v-7270b30e {
  font-size: 32rpx;
  /* 优化：从38rpx减小到32rpx，更符合移动端标准 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  margin-bottom: 20rpx;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.topic-title.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
.topic-desc.data-v-7270b30e {
  font-size: 28rpx;
  /* 优化：从30rpx减小到28rpx，提升精致感 */
  color: #4a4a4a;
  line-height: 1.7;
  margin-bottom: 24rpx;
  letter-spacing: 0.3rpx;
}
.topic-meta.data-v-7270b30e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: #8a8a8a;
  padding: 20rpx 0 0;
  border-top: 1rpx solid rgba(255, 105, 135, 0.1);
}
.topic-meta .participants.data-v-7270b30e {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87 !important;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.topic-meta .participants.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
.topic-meta .create-time.data-v-7270b30e {
  margin-left: 16rpx;
  opacity: 0.8;
}
/* 店铺信息区域样式 */
.store-info-section.data-v-7270b30e {
  background: rgba(255, 255, 255, 0.95);
  margin: 24rpx 0;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.store-header.data-v-7270b30e {
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 245, 240, 0.9) 100%);
  display: flex;
}
.store-icon.data-v-7270b30e {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
}
.store-icon .icon.data-v-7270b30e {
  font-size: 40rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  color: #ffffff;
}
.store-content.data-v-7270b30e {
  flex: 1;
}
.store-title.data-v-7270b30e {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12rpx;
  line-height: 1.4;
  letter-spacing: 0.3rpx;
}
.store-desc.data-v-7270b30e {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  letter-spacing: 0.2rpx;
}
.store-meta.data-v-7270b30e {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #8a8a8a;
  letter-spacing: 0.2rpx;
}
.store-meta .participants.data-v-7270b30e {
  color: #ff6b87;
  font-weight: 500;
}
.store-meta .store-name.data-v-7270b30e {
  color: #8a8a8a;
  font-size: 26rpx;
  margin-left: 8rpx;
}
.filter-bar.data-v-7270b30e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
  /* 减少垂直padding，与comment-detail.vue保持一致 */
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  margin: 0 0 24rpx 0;
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  left: 0;
  /* 启用GPU加速 */
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
  right: 0;
  z-index: 10;
}
/* 评论数量区域样式 */
.comment-count-section.data-v-7270b30e {
  flex: 0 0 auto;
}
.comment-count-text.data-v-7270b30e {
  font-size: 28rpx;
  /* 优化：从30rpx减小到28rpx，更符合移动端标准 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  letter-spacing: 0.5rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.comment-count-text.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
/* 筛选标签区域样式 */
.filter-tabs-section.data-v-7270b30e {
  flex: 0 0 auto;
}
.van-tabs__wrap.data-v-7270b30e {
  padding: 0;
  border-radius: 24rpx;
  overflow: hidden;
}
.van-tabs__nav.data-v-7270b30e {
  display: flex;
  justify-content: flex-end;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border-radius: 48rpx;
  padding: 6rpx;
  /* 减少内边距 */
  height: 70rpx;
  /* 减少高度 */
  box-shadow: inset 0 2rpx 8rpx rgba(255, 105, 135, 0.1);
  width: auto;
  min-width: 100rpx;
}
.van-tab.data-v-7270b30e {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 32rpx;
  margin: 0 4rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 58rpx;
  /* 减少高度 */
  min-width: 80rpx;
  padding: 0 14rpx;
  /* 减少水平内边距 */
}
.van-tab--active.data-v-7270b30e {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 135, 0.3);
  -webkit-transform: translateY(-2rpx) scale(1.02);
          transform: translateY(-2rpx) scale(1.02);
}
.van-tab--active .van-tab__text.data-v-7270b30e {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.van-tab__text.data-v-7270b30e {
  color: #8a8a8a;
  font-size: 24rpx;
  /* 减小字体大小 */
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
  white-space: nowrap;
}
/* 评论列表容器 */
.comment-list-container.data-v-7270b30e {
  width: 100%;
}
.comment-list.data-v-7270b30e {
  width: 100%;
  padding: 0 0 32rpx 0;
}
.comment-list .empty-image.data-v-7270b30e {
  width: 280rpx;
  height: 280rpx;
  opacity: 0.6;
  border-radius: 24rpx;
}
.comment-item.data-v-7270b30e {
  background: rgba(255, 255, 255, 0.95);
  /* 移除高消耗的backdrop-filter */
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  border-radius: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 优化过渡效果，只对transform进行过渡 */
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
  width: 100%;
  position: relative;
  overflow: hidden;
  /* 启用GPU加速 */
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.comment-item.data-v-7270b30e:active {
  -webkit-transform: translateY(-2rpx) scale(0.98);
          transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 12rpx 36rpx rgba(255, 105, 135, 0.15);
}
.user-avatar.data-v-7270b30e {
  position: relative;
  margin-right: 28rpx;
}
.user-avatar image.data-v-7270b30e {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 107, 135, 0.2);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 135, 0.15);
  transition: all 0.3s ease;
}
.user-avatar:active image.data-v-7270b30e {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.comment-content.data-v-7270b30e {
  flex: 1;
}
.user-info-row.data-v-7270b30e {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.user-info.data-v-7270b30e {
  flex: 1;
}
.user-name.data-v-7270b30e {
  font-size: 28rpx;
  /* 优化：从30rpx减小到28rpx，更符合移动端用户名标准 */
  font-weight: 600;
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #4a4a4a;
  background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
  display: flex;
  align-items: center;
  letter-spacing: 0.3rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.user-name.data-v-7270b30e {
    color: #4a4a4a !important;
    background: none;
}
}
.user-level.data-v-7270b30e {
  font-size: 22rpx;
  color: #ffffff !important;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  margin-left: 16rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  /* 确保文字显示，移除可能导致兼容性问题的属性 */
  -webkit-text-fill-color: initial;
  -webkit-background-clip: initial;
  background-clip: initial;
}
.time.data-v-7270b30e {
  font-size: 24rpx;
  color: #8a8a8a;
  margin-top: 8rpx;
  font-weight: 400;
  opacity: 0.8;
}
.like-btn.data-v-7270b30e {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.1);
}
.like-btn.data-v-7270b30e:active {
  background: linear-gradient(135deg, #ffe0f0 0%, #ffede0 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.15);
}
.like-btn text.data-v-7270b30e {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
/* 心形图标样式 - 性能优化版本 */
.u-icon__icon.uicon-heart-fill.data-v-7270b30e,
.u-icon__icon.uicon-heart.data-v-7270b30e {
  font-weight: bold;
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
  /* 优化过渡效果，缩短时长并只对transform进行过渡 */
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
  color: #ff6b87;
}
.u-icon__icon.uicon-heart-fill.data-v-7270b30e {
  /* 移除复杂的心跳动画，提升性能 */
  color: #ff6b87;
}
.text.data-v-7270b30e {
  font-size: 30rpx;
  /* 优化：从32rpx减小到30rpx，更符合移动端正文标准 */
  line-height: 1.8;
  margin-bottom: 20rpx;
  color: #4a4a4a;
  word-break: break-all;
  font-weight: 400;
  letter-spacing: 0.3rpx;
}
.expand-btn.data-v-7270b30e {
  color: #ff6b87;
  font-size: 26rpx;
  /* 优化：从28rpx减小到26rpx，按钮文字更精致 */
  display: inline-block;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 107, 135, 0.1);
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.expand-btn.data-v-7270b30e:active {
  background: rgba(255, 107, 135, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.actions.data-v-7270b30e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}
.reply-btn.data-v-7270b30e {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
}
.reply-btn.data-v-7270b30e:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.reply-btn image.data-v-7270b30e {
  width: 36rpx;
  height: 36rpx;
  -webkit-filter: hue-rotate(320deg) saturate(1.2);
          filter: hue-rotate(320deg) saturate(1.2);
}
.reply-btn text.data-v-7270b30e {
  font-size: 26rpx;
  color: #ff6b87;
  margin-left: 10rpx;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}
.more-btn.data-v-7270b30e {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 135, 0.1);
}
.more-btn.data-v-7270b30e:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.more-btn image.data-v-7270b30e {
  width: 100%;
  height: 100%;
  -webkit-filter: hue-rotate(320deg) saturate(1.2);
          filter: hue-rotate(320deg) saturate(1.2);
}
.reply-preview.data-v-7270b30e {
  margin-top: 24rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.05) 0%, rgba(255, 142, 83, 0.05) 100%);
  border-radius: 20rpx;
  padding: 28rpx;
  border: 1rpx solid rgba(255, 107, 135, 0.1);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.reply-item.data-v-7270b30e {
  font-size: 26rpx;
  margin-bottom: 16rpx;
  line-height: 1.7;
  color: #6a6a6a;
  letter-spacing: 0.3rpx;
}
.reply-nickname.data-v-7270b30e {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87 !important;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.reply-nickname.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
.reply-to.data-v-7270b30e {
  color: #ff8e53;
  margin: 0 8rpx;
  font-weight: 600;
}
.reply-content.data-v-7270b30e {
  color: #4a4a4a;
  font-weight: 400;
}
.view-more.data-v-7270b30e {
  font-size: 26rpx;
  color: #ff6b87;
  text-align: right;
  margin-top: 16rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border: 1rpx solid rgba(255, 107, 135, 0.2);
  display: inline-block;
  transition: all 0.3s ease;
  letter-spacing: 0.3rpx;
}
.view-more.data-v-7270b30e:active {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.2) 0%, rgba(255, 142, 83, 0.2) 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 确保评论列表内容底部有足够空间 */
.comment-list-bottom-space.data-v-7270b30e {
  height: 160rpx;
  /* 比输入框高度大一些 */
}
/* 加载更多状态样式 - 小红书风格 */
.loading-more.data-v-7270b30e {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 0;
}
.loading-more .loading-text.data-v-7270b30e {
  margin-left: 24rpx;
  font-size: 30rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
/* 骨架屏加载状态样式 */
.loading-more-skeleton.data-v-7270b30e {
  padding: 0;
  /* 骨架屏淡入动画 */
  -webkit-animation: skeletonFadeIn-data-v-7270b30e 0.3s ease-out;
          animation: skeletonFadeIn-data-v-7270b30e 0.3s ease-out;
}
.no-more.data-v-7270b30e {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx 0;
}
.no-more text.data-v-7270b30e {
  font-size: 30rpx;
  color: #b0b0b0;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
/* 评论列表的底部留白空间 */
.bottom-space.data-v-7270b30e {
  height: 200rpx;
  width: 100%;
}
.loading.data-v-7270b30e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}
.loading .loading-text.data-v-7270b30e {
  margin-top: 24rpx;
  font-size: 30rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
.empty-tip.data-v-7270b30e {
  padding: 160rpx 40rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.03) 0%, rgba(255, 142, 83, 0.03) 100%);
  border-radius: 32rpx;
  margin: 24rpx;
}
.empty-text.data-v-7270b30e {
  font-size: 32rpx;
  /* 优化：从36rpx减小到32rpx，空状态文字更合理 */
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  margin-top: 32rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.empty-text.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
.empty-subtext.data-v-7270b30e {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-top: 16rpx;
  font-weight: 400;
}
.empty-action.data-v-7270b30e {
  margin-top: 32rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #a8727e 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.empty-action.data-v-7270b30e:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.empty-action text.data-v-7270b30e {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}
/* 蒙版层样式 */
.mask-layer.data-v-7270b30e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  opacity: 0;
  -webkit-animation: maskFadeIn-data-v-7270b30e 0.3s ease-out forwards;
          animation: maskFadeIn-data-v-7270b30e 0.3s ease-out forwards;
}
.input-container.data-v-7270b30e {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background: #ffffff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid #f0f0f0;
  transition: bottom 0.3s ease-in-out;
}
/* 回复状态指示器样式 - 性能优化版本 */
.reply-indicator.data-v-7270b30e {
  background: rgba(255, 107, 135, 0.1);
  border-bottom: 1rpx solid rgba(255, 107, 135, 0.15);
  padding: 16rpx 32rpx;
  /* 移除复杂动画，使用简单的透明度过渡 */
  opacity: 1;
  transition: opacity 0.2s ease;
}
.reply-info.data-v-7270b30e {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.reply-text.data-v-7270b30e {
  font-size: 28rpx;
  color: #ff6b87;
  font-weight: 500;
  letter-spacing: 0.3rpx;
  /* 小红书风格渐变文字 */
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.reply-text.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
.cancel-reply-btn.data-v-7270b30e {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 107, 135, 0.15);
  border-radius: 50%;
  font-size: 24rpx;
  color: #ff6b87;
  font-weight: bold;
  transition: all 0.3s ease;
}
.cancel-reply-btn.data-v-7270b30e:active {
  background: rgba(255, 107, 135, 0.25);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
/* 回复指示器入场动画 */
@-webkit-keyframes replyIndicatorSlideIn-data-v-7270b30e {
from {
    opacity: 0;
    -webkit-transform: translateY(-20rpx);
            transform: translateY(-20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes replyIndicatorSlideIn-data-v-7270b30e {
from {
    opacity: 0;
    -webkit-transform: translateY(-20rpx);
            transform: translateY(-20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.reply-popup.data-v-7270b30e {
  padding: 30rpx;
  background-color: #fff;
  /* 确保弹窗能够平滑跟随键盘调整 - 使用transform */
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
  /* 确保transform生效 */
  position: relative;
  z-index: 1;
}
.reply-header.data-v-7270b30e {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.reply-header text.data-v-7270b30e {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
/* 自定义回复弹窗样式已移除，改为使用底部主输入框进行回复 */
/* 弹窗入场动画已移除，保留键盘适配过渡效果 */
/* 蒙版层淡入动画 */
@-webkit-keyframes maskFadeIn-data-v-7270b30e {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes maskFadeIn-data-v-7270b30e {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
/* 添加心跳动画 */
@-webkit-keyframes heartBeat-data-v-7270b30e {
0% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
}
25% {
    -webkit-transform: scale(1.4) rotate(-5deg);
            transform: scale(1.4) rotate(-5deg);
}
50% {
    -webkit-transform: scale(1.6) rotate(5deg);
            transform: scale(1.6) rotate(5deg);
}
75% {
    -webkit-transform: scale(1.3) rotate(-2deg);
            transform: scale(1.3) rotate(-2deg);
}
100% {
    -webkit-transform: scale(1.1) rotate(0deg);
            transform: scale(1.1) rotate(0deg);
}
}
@keyframes heartBeat-data-v-7270b30e {
0% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
}
25% {
    -webkit-transform: scale(1.4) rotate(-5deg);
            transform: scale(1.4) rotate(-5deg);
}
50% {
    -webkit-transform: scale(1.6) rotate(5deg);
            transform: scale(1.6) rotate(5deg);
}
75% {
    -webkit-transform: scale(1.3) rotate(-2deg);
            transform: scale(1.3) rotate(-2deg);
}
100% {
    -webkit-transform: scale(1.1) rotate(0deg);
            transform: scale(1.1) rotate(0deg);
}
}
/* 更多操作弹窗样式 */
.action-popup.data-v-7270b30e {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  padding: 32rpx 0;
}
.action-item.data-v-7270b30e {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  margin: 0 24rpx;
  background: #ffffff;
  transition: all 0.3s ease;
}
.action-item.data-v-7270b30e:active {
  background: #f8fafc;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.action-icon.data-v-7270b30e {
  width: 44rpx;
  height: 44rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon image.data-v-7270b30e {
  width: 100%;
  height: 100%;
}
/* 第一个action-item的样式 */
.action-item.reply.data-v-7270b30e {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
}
/* 第二个action-item的样式 */
.action-item.copy.data-v-7270b30e {
  margin-bottom: 24rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}
/* 第三个action-item的样式 */
.action-item.report.data-v-7270b30e {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.2);
}
/* 最后一个action-item的样式 */
.action-item.block.data-v-7270b30e {
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}
.action-item text.data-v-7270b30e {
  font-size: 28rpx;
  color: #334155;
  font-weight: 500;
}
.action-cancel.data-v-7270b30e {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-top: 20rpx;
  border-radius: 20rpx;
}
.action-cancel text.data-v-7270b30e {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.action-cancel.data-v-7270b30e:active {
  background-color: #f5f5f5;
}
/* 小红书风格动画效果 */
@keyframes heartBeat-data-v-7270b30e {
0% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
14% {
    -webkit-transform: scale(1.4);
            transform: scale(1.4);
}
28% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
42% {
    -webkit-transform: scale(1.4);
            transform: scale(1.4);
}
70% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@-webkit-keyframes fadeInUp-data-v-7270b30e {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-7270b30e {
from {
    opacity: 0;
    -webkit-transform: translateY(30rpx);
            transform: translateY(30rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes shimmer-data-v-7270b30e {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}
@keyframes shimmer-data-v-7270b30e {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}
/* 性能优化：移除评论项入场动画，避免滚动时重复触发导致卡顿 */
/* 骨架屏动画 */
@-webkit-keyframes skeletonFadeIn-data-v-7270b30e {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes skeletonFadeIn-data-v-7270b30e {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
/* 渐变文字效果 - 微信小程序兼容版本 */
.gradient-text.data-v-7270b30e {
  /* 备用颜色方案，确保在微信小程序中显示 */
  color: #ff6b87;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  /* 渐变文字效果（如果支持） */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 微信小程序兼容性处理 */
}
@supports not (-webkit-background-clip: text) {
.gradient-text.data-v-7270b30e {
    color: #ff6b87 !important;
    background: none;
}
}
/* 毛玻璃效果 */
.glass-effect.data-v-7270b30e {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}
/* 悬浮阴影效果 */
.floating-shadow.data-v-7270b30e {
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.15);
  transition: box-shadow 0.3s ease;
}
.floating-shadow.data-v-7270b30e:hover {
  box-shadow: 0 12rpx 48rpx rgba(255, 105, 135, 0.2);
}
/* 响应式设计优化 */
@media (max-width: 750rpx) {
.comment-item.data-v-7270b30e {
    padding: 28rpx;
    margin-bottom: 16rpx;
}
.topic-title.data-v-7270b30e {
    font-size: 30rpx;
    /* 小屏幕优化：进一步减小标题字体 */
}
.text.data-v-7270b30e {
    font-size: 28rpx;
    /* 小屏幕优化：进一步减小正文字体 */
}
  /* 小屏幕下筛选栏适配 */
.filter-bar.data-v-7270b30e {
    padding: 16rpx 24rpx;
    /* 进一步减少小屏幕下的padding */
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
    /* 减少间距 */
}
.comment-count-text.data-v-7270b30e {
    font-size: 24rpx;
    /* 小屏幕优化：进一步减小评论数量字体 */
}
.van-tabs__nav.data-v-7270b30e {
    min-width: 240rpx;
    height: 60rpx;
    /* 小屏幕下进一步减少高度 */
    padding: 4rpx;
    /* 减少内边距 */
}
.van-tab.data-v-7270b30e {
    height: 52rpx;
    /* 小屏幕下减少标签高度 */
    padding: 0 12rpx;
    /* 减少水平内边距 */
}
.van-tab__text.data-v-7270b30e {
    font-size: 22rpx;
    /* 小屏幕下进一步减小字体 */
}
}

