@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.comment-input-box.data-v-73134684 {
  display: flex;
  padding: 0 30rpx;
  background-color: #fff;
}
.comment-input-box .input-container.data-v-73134684 {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}
.comment-input-box .input-container .input-textarea.data-v-73134684 {
  flex: 1;
  min-height: 80rpx;
  background: #f8fafc;
  border-radius: 24rpx;
  padding: 20rpx 120rpx 20rpx 30rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
  border: 1rpx solid #e2e8f0;
  width: 100%;
  box-sizing: border-box;
  line-height: 40rpx;
  overflow-y: auto;
  color: #1e293b;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.comment-input-box .input-container .input-textarea.data-v-73134684:focus {
  border-color: #667eea;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
  background: #ffffff;
}
.comment-input-box .input-container .measure-box.data-v-73134684 {
  position: absolute;
  visibility: hidden;
  width: calc(100% - 150rpx);
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 20rpx 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  box-sizing: border-box;
  top: -9999px;
  left: -9999px;
  border: 1rpx solid transparent;
}
.comment-input-box .input-container .send-btn-inside.data-v-73134684 {
  position: absolute;
  right: 10rpx;
  top: 25rpx;
  width: 70rpx;
  height: 70rpx;
  border-radius: 30rpx;
  padding: 0;
  z-index: 2;
}
.comment-input-box .input-container .text-btn-inside.data-v-73134684 {
  position: absolute;
  right: 20rpx;
  top: 45rpx;
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  z-index: 2;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}
.comment-input-box .input-container .text-btn-inside.data-v-73134684:active {
  background: rgba(102, 126, 234, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.comment-input-box .input-container .text-btn-inside.disabled.data-v-73134684 {
  color: #94a3b8;
  background: rgba(148, 163, 184, 0.1);
}
.comment-input-box .biaoqingbao-btn-inside.data-v-73134684 {
  position: absolute;
  right: 80rpx;
  width: 55rpx;
  height: 55rpx;
  border-radius: 30rpx;
  margin-right: 10rpx;
}

