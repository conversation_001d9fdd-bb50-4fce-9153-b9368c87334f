@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.comment-skeleton.data-v-74fbe789 {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 135, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: skeletonPulse-data-v-74fbe789 1.5s ease-in-out infinite;
}
.skeleton-avatar.data-v-74fbe789 {
  margin-right: 24rpx;
}
.skeleton-avatar .skeleton-circle.data-v-74fbe789 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonShimmer-data-v-74fbe789 1.5s infinite;
}
.skeleton-content.data-v-74fbe789 {
  flex: 1;
}
.skeleton-header.data-v-74fbe789 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}
.skeleton-line.data-v-74fbe789 {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonShimmer-data-v-74fbe789 1.5s infinite;
  border-radius: 8rpx;
}
.skeleton-name.data-v-74fbe789 {
  width: 120rpx;
  height: 32rpx;
}
.skeleton-time.data-v-74fbe789 {
  width: 80rpx;
  height: 24rpx;
}
.skeleton-text.data-v-74fbe789 {
  margin-bottom: 20rpx;
}
.skeleton-text-line1.data-v-74fbe789 {
  width: 100%;
  height: 32rpx;
  margin-bottom: 12rpx;
}
.skeleton-text-line2.data-v-74fbe789 {
  width: 85%;
  height: 32rpx;
  margin-bottom: 12rpx;
}
.skeleton-text-line3.data-v-74fbe789 {
  width: 60%;
  height: 32rpx;
}
.skeleton-actions.data-v-74fbe789 {
  display: flex;
  gap: 24rpx;
}
.skeleton-action.data-v-74fbe789 {
  width: 80rpx;
  height: 28rpx;
}

/* 骨架屏动画 */
@keyframes skeletonShimmer-data-v-74fbe789 {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}
@keyframes skeletonPulse-data-v-74fbe789 {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.8;
}
}

/* 小红书风格优化 */
.comment-skeleton.data-v-74fbe789 {
  position: relative;
  overflow: hidden;
}
.comment-skeleton.data-v-74fbe789::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 135, 0.1), transparent);
  animation: skeletonSweep-data-v-74fbe789 2s infinite;
}
@keyframes skeletonSweep-data-v-74fbe789 {
0% {
    left: -100%;
}
100% {
    left: 100%;
}
}

/* 响应式适配 */
@media (max-width: 750rpx) {
.comment-skeleton.data-v-74fbe789 {
    padding: 28rpx;
    margin-bottom: 16rpx;
}
.skeleton-avatar .skeleton-circle.data-v-74fbe789 {
    width: 72rpx;
    height: 72rpx;
}
}

