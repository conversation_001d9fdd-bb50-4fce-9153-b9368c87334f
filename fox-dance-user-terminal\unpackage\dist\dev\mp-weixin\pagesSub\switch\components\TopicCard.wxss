@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.topic-card.data-v-4c84b6dc {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}
.topic-card.data-v-4c84b6dc:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}
.topic-card .topic-content.data-v-4c84b6dc {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24rpx;
}
.topic-card .topic-content.no-image .topic-left.data-v-4c84b6dc {
  width: 100%;
  flex: none;
}
.topic-card .topic-content.no-image .topic-left .topic-desc.data-v-4c84b6dc {
  -webkit-line-clamp: 4;
  max-height: 120rpx;
}
.topic-card .topic-content.no-image .topic-left .topic-stat.data-v-4c84b6dc {
  margin-top: 24rpx;
}
.topic-card .topic-content .topic-left.data-v-4c84b6dc {
  flex: 1;
  min-width: 0;
}
.topic-card .topic-content .topic-left .topic-title.data-v-4c84b6dc {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.topic-card .topic-content .topic-left .topic-title .topic-icon.data-v-4c84b6dc {
  width: 45rpx;
  height: 45rpx;
  margin-right: 10rpx;
  border-radius: 50%;
}
.topic-card .topic-content .topic-left .topic-title .title-text.data-v-4c84b6dc {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  line-height: 1.3;
}
.topic-card .topic-content .topic-left .topic-desc.data-v-4c84b6dc {
  font-size: 26rpx;
  color: #64748b;
  margin-bottom: 20rpx;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}
.topic-card .topic-content .topic-left .topic-stat.data-v-4c84b6dc {
  display: flex;
  align-items: center;
}
.topic-card .topic-content .topic-left .topic-stat .dot.data-v-4c84b6dc {
  width: 10rpx;
  height: 10rpx;
  background-color: #ff6b87;
  border-radius: 50%;
  margin-right: 10rpx;
}
.topic-card .topic-content .topic-left .topic-stat .participants.data-v-4c84b6dc {
  font-size: 24rpx;
  color: #999999;
}
.topic-card .topic-content .topic-left .topic-stat .time.data-v-4c84b6dc {
  font-size: 24rpx;
  color: #999999;
  margin-left: 8rpx;
}
.topic-card .topic-content .topic-left .topic-stat .arrow.data-v-4c84b6dc {
  width: 20rpx;
  height: 20rpx;
  margin-left: 4rpx;
}
.topic-card .topic-content .topic-right.data-v-4c84b6dc {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
}
.topic-card .topic-content .topic-right .cover-image.data-v-4c84b6dc {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

