@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.vote-container.data-v-de3fba80 {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 0 24rpx 120rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}
.header-section.data-v-de3fba80 {
  padding: 40rpx 0 32rpx;
  text-align: center;
  margin: 0 -24rpx 32rpx;
  border-radius: 0 0 32rpx 32rpx;
  color: #2d3436;
  position: relative;
  overflow: hidden;
}
.header-section.data-v-de3fba80::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.3;
}
.vote-title.data-v-de3fba80 {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}
.vote-info.data-v-de3fba80 {
  font-size: 20rpx;
  opacity: 0.9;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}
.card.data-v-de3fba80 {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}
.card.data-v-de3fba80:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}
.card-header.data-v-de3fba80 {
  padding: 32rpx 32rpx 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e9ecef;
}
.card-title.data-v-de3fba80 {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}
.total-votes.data-v-de3fba80 {
  font-size: 24rpx;
  color: #636e72;
  background: linear-gradient(135deg, #ff6b87, #ff8e53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}
.dropdown-card .picker.data-v-de3fba80 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  min-height: 80rpx;
}
.dropdown-card .picker-text.data-v-de3fba80 {
  font-size: 32rpx;
  color: #2d3436;
  font-weight: 500;
}
.dropdown-card .picker-loading.data-v-de3fba80 {
  font-size: 32rpx;
  color: #b2bec3;
}
.dropdown-card .picker-arrow.data-v-de3fba80 {
  color: #636e72;
  font-size: 28rpx;
  transition: transform 0.2s ease-out;
}
.notice-card.data-v-de3fba80 {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 20%, #ff7675 100%);
}
.notice-card .notice-content.data-v-de3fba80 {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
}
.notice-card .notice-icon.data-v-de3fba80 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  border-radius: 50%;
}
.notice-card .notice-text.data-v-de3fba80 {
  flex: 1;
}
.notice-card .notice-title.data-v-de3fba80 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}
.notice-card .notice-desc.data-v-de3fba80 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}
.vote-list-card .vote-list.data-v-de3fba80 {
  padding: 0;
  margin-bottom: 120rpx;
}
.vote-item.data-v-de3fba80 {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #e9ecef;
  transition: all 0.2s ease-out;
  position: relative;
}
.vote-item.data-v-de3fba80:last-child {
  border-bottom: none;
}
.vote-item.data-v-de3fba80:active {
  background: #f8f9fa;
}
.vote-item.selected.data-v-de3fba80 {
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  border-left: 6rpx solid #ff6b87;
}
.vote-item.selected .station-name.data-v-de3fba80 {
  color: #ff6b87;
  font-weight: 600;
}
.vote-item.voted.data-v-de3fba80 {
  background: linear-gradient(135deg, rgba(255, 234, 167, 0.3) 0%, rgba(250, 177, 160, 0.3) 100%);
}
.vote-item.voted.data-v-de3fba80::after {
  content: '已投票';
  position: absolute;
  top: 8rpx;
  right: 32rpx;
  font-size: 20rpx;
  color: #ff8e53;
  background: rgba(255, 142, 83, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.vote-item.rank-top .rank-badge.data-v-de3fba80 {
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
}
.rank-badge.data-v-de3fba80 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 135, 0.3);
}
.rank-badge .rank-number.data-v-de3fba80 {
  font-size: 24rpx;
  font-weight: 700;
  color: white;
}
.rank-number-normal.data-v-de3fba80 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #636e72;
}
.station-info.data-v-de3fba80 {
  flex: 1;
  margin-right: 24rpx;
}
.station-name.data-v-de3fba80 {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3436;
  margin-bottom: 12rpx;
  transition: color 0.2s ease-out;
}
.vote-progress.data-v-de3fba80 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.vote-bar.data-v-de3fba80 {
  flex: 1;
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;
}
.vote-bar-inner.data-v-de3fba80 {
  height: 100%;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease-out;
  animation: progressSlide-data-v-de3fba80 0.8s ease-out;
  position: relative;
}
.vote-bar-inner.data-v-de3fba80::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer-data-v-de3fba80 2s infinite;
}
@keyframes progressSlide-data-v-de3fba80 {
from {
    width: 0 !important;
}
}
@keyframes shimmer-data-v-de3fba80 {
0% {
    transform: translateX(-100%);
}
100% {
    transform: translateX(100%);
}
}
.vote-percentage.data-v-de3fba80 {
  font-size: 24rpx;
  color: #636e72;
  font-weight: 500;
  min-width: 60rpx;
  text-align: right;
}
.vote-count-container.data-v-de3fba80 {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
}
.vote-count.data-v-de3fba80 {
  font-size: 32rpx;
  font-weight: 700;
  color: #ff6b87;
  transition: all 0.2s ease-out;
}
.vote-count.count-animate.data-v-de3fba80 {
  animation: countPulse-data-v-de3fba80 0.6s ease-out;
}
@keyframes countPulse-data-v-de3fba80 {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.2);
    color: #ff8e53;
}
100% {
    transform: scale(1);
}
}
.vote-unit.data-v-de3fba80 {
  font-size: 20rpx;
  color: #b2bec3;
  margin-top: 4rpx;
}
.selection-indicator.data-v-de3fba80 {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  animation: checkBounce-data-v-de3fba80 0.3s ease-out;
}
@keyframes checkBounce-data-v-de3fba80 {
0% {
    transform: scale(0);
}
50% {
    transform: scale(1.2);
}
100% {
    transform: scale(1);
}
}
.check-icon.data-v-de3fba80 {
  font-size: 20rpx;
  color: white;
  font-weight: 700;
}
.more-button.data-v-de3fba80 {
  padding: 32rpx;
  text-align: center;
  border-top: 1rpx solid #e9ecef;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s ease-out;
}
.more-button.data-v-de3fba80:active {
  background-color: #f8f9fa;
}
.more-text.data-v-de3fba80 {
  font-size: 28rpx;
  color: #ff6b87;
  font-weight: 500;
}
.more-icon.data-v-de3fba80 {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #ff6b87;
  transition: transform 0.2s ease-out;
}
.more-icon.icon-rotate.data-v-de3fba80 {
  transform: rotate(180deg);
}
.loading-card .loading-content.data-v-de3fba80 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
}
.loading-card .loading-spinner.data-v-de3fba80 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e9ecef;
  border-top: 4rpx solid #ff6b87;
  border-radius: 50%;
  animation: spin-data-v-de3fba80 1s linear infinite;
  margin-bottom: 24rpx;
}
.loading-card .loading-text.data-v-de3fba80 {
  font-size: 28rpx;
  color: #636e72;
}
@keyframes spin-data-v-de3fba80 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.bottom-section.data-v-de3fba80 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 24rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}
.vote-tip.data-v-de3fba80 {
  text-align: center;
  color: #636e72;
  font-size: 24rpx;
  margin-bottom: 24rpx;
  line-height: 1.4;
}
.vote-action.data-v-de3fba80 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.vote-btn.data-v-de3fba80 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b87 0%, #ff8e53 100%);
  color: white;
  font-size: 32rpx;
  border: none;
  border-radius: 44rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease-out;
  position: relative;
  overflow: hidden;
}
.vote-btn.data-v-de3fba80:not(.btn-disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 135, 0.4);
}
.vote-btn.btn-disabled.data-v-de3fba80 {
  background: #e9ecef;
  color: #b2bec3;
  box-shadow: none;
}
.vote-btn.btn-success.data-v-de3fba80 {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  animation: successPulse-data-v-de3fba80 0.6s ease-out;
}
@keyframes successPulse-data-v-de3fba80 {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.05);
}
100% {
    transform: scale(1);
}
}
.btn-text.data-v-de3fba80 {
  position: relative;
  z-index: 2;
}
.btn-ripple.data-v-de3fba80 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  animation: ripple-data-v-de3fba80 0.6s ease-out;
}
@keyframes ripple-data-v-de3fba80 {
to {
    width: 300rpx;
    height: 300rpx;
    opacity: 0;
}
}
.remaining-votes.data-v-de3fba80 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
}
.votes-label.data-v-de3fba80 {
  font-size: 24rpx;
  color: #636e72;
}
.votes-count.data-v-de3fba80 {
  font-size: 28rpx;
  font-weight: 700;
  color: #ff6b87;
  background: linear-gradient(135deg, rgba(255, 107, 135, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  min-width: 48rpx;
  text-align: center;
}
@media screen and (max-width: 750rpx) {
.vote-item.data-v-de3fba80 {
    padding: 20rpx 24rpx;
}
.station-name.data-v-de3fba80 {
    font-size: 30rpx;
}
.vote-count.data-v-de3fba80 {
    font-size: 28rpx;
}
}

