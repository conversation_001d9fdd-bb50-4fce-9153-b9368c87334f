<view class="searchResults" style="{{'--qjbutton-color:'+(qjbutton)+';'+('--qjziti-color:'+(qjziti)+';')}}"><view class="les_search"><view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="请搜索课程" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$0'],['keywords']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchTap',['$0'],['keywords']]]]]}}" class="les_search_r" bindtap="__e">搜索</view></view><view class="teaCon"><block wx:for="{{storeCourseLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['storesxqTap',['$0'],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li" bindtap="__e"><view class="teaCon_li_a">{{item.course.name}}</view><view class="teaCon_li_b"><image class="teaCon_li_b_l" src="{{imgbaseUrl+item.teacher.image}}" mode="aspectFill"></image><view class="teaCon_li_b_c"><view class="teaCon_li_b_c_a">{{item.start_time+"-"+item.end_time}}</view><view class="teaCon_li_b_c_b">{{"上课老师："+item.teacher.name}}</view><block wx:if="{{item.frequency*1>0}}"><view class="teaCon_li_b_c_b">{{"次卡消耗："+item.frequency*1+"次"}}</view></block><view class="teaCon_li_b_c_c"><block wx:if="{{item.level_name}}"><text>{{item.level_name}}</text></block><text>{{item.dance_name}}</text></view></view><block wx:if="{{item.status==1}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">待开课</view></block><block wx:else><block wx:if="{{item.status==2}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">授课中</view></block><block wx:else><block wx:if="{{item.status==3}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">已完成</view></block><block wx:else><block wx:if="{{item.status==4}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">等位中</view></block><block wx:else><block wx:if="{{item.status==6}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r yysj" style="background:#BEBEBE;" catchtap="__e"><text>{{item.start_reservation}}</text><text>开始预约</text></view></block><block wx:else><block wx:if="{{item.status==7}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">截止预约</view></block><block wx:else><block wx:if="{{item.equivalent*1==0&&item.appointment_number*1>=item.maximum_reservation*1}}"><view data-event-opts="{{[['tap',[['kqhyts',['$event']]]]]}}" class="teaCon_li_b_r" style="background:#BEBEBE;" catchtap="__e">预约</view></block><block wx:else><block wx:if="{{item.member==0}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="teaCon_li_b_r" style="{{(item.member==0?'background:#BEBEBE':'')}}" catchtap="__e">预约</view></block><block wx:else><view data-event-opts="{{[['tap',[['yypdTo',['$0','/pages/Schedule/Schedulexq?id'+item.id],[[['storeCourseLists','',index]]]]]]]}}" class="teaCon_li_b_r" catchtap="__e">{{item.waiting_number*1>0?'去排队':'预约'}}</view></block></block></block></block></block></block></block></block></view><block wx:if="{{item.appointment_number>0}}"><view class="teaCon_li_c"><view class="teaCon_li_c_l"><block wx:for="{{item.appointment_people}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image src="{{imgbaseUrl+item.avatar}}" mode="aspectFit"></image></block></view><view class="teaCon_li_c_r">已预约：<text>{{item.appointment_number}}</text>人;<block wx:if="{{item.waiting_number*1>0}}"><text>{{item.waiting_number}}</text>人在等位</block></view></view></block></view></block></view><block wx:if="{{$root.g0==0}}"><view class="gg_zwsj" style="margin-bottom:60rpx;"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无课程</text></view></view></block><block wx:if="{{ljtkToggle}}"><view class="yytnCon"><view class="yytnCon_n"><image src="{{imgbaseUrlOss+'/userreport/icon55.png'}}"></image><text data-event-opts="{{[['tap',[['ljktTap',['$event']]]]]}}" bindtap="__e"></text></view><image src="/static/images/icon56.png" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e"></image></view></block><view class="aqjlViw"></view></view>