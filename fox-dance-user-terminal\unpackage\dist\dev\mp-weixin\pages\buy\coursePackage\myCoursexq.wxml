<block wx:if="{{coursePackageInfo.id}}"><view class="myCoursexq" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="kcxq_video"><swiper class="swiper" autoplay="1500" indicator-dots="{{true}}" circular="{{true}}" indicator-active-color="#ffffff" indicator-color="#cccccc"><block wx:for="{{coursePackageInfo.images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="swiper-wrap"><image src="{{imgbaseUrl+item}}" mode="aspectFill"></image></swiper-item></block></swiper></view><view class="kcxq_one"><view class="kcxq_one_a">{{coursePackageInfo.name}}</view><view class="kcxq_one_a kcxq_one_a_bq"><block wx:if="{{coursePackageInfo.levelTable}}"><text>{{coursePackageInfo.levelTable.name}}</text></block><block wx:if="{{coursePackageInfo.danceTable}}"><text>{{coursePackageInfo.danceTable.name}}</text></block></view><block wx:if="{{coursePackageInfo.teacher}}"><view class="kcxq_one_b"><image class="kcxq_one_b_l" src="{{imgbaseUrl+coursePackageInfo.teacher.image}}"></image><view class="kcxq_one_b_r"><view class="kcxq_one_b_r_l"><view>{{coursePackageInfo.teacher.name}}</view><block wx:if="{{coursePackageInfo.teacher.work_year*1>0}}"><text>{{coursePackageInfo.teacher.work_year+"年经验"}}</text></block></view><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/coursePackage/teacherDetails?id='+coursePackageInfo.teacher.id,'1']]]]]}}" class="kcxq_one_b_r_r" bindtap="__e">讲师详情<image src="/static/images/introduce_more.png"></image></view></view></view></block><block wx:if="{{coursePackageInfo.teacher}}"><view class="kcxq_one_c"><view>{{"课程时长："+coursePackageInfo.duration*1+"分钟"}}</view></view></block></view><view class="kcxq_two"><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>课程介绍</text><text></text></view></view><view class="kcxq_thr_b"><text style="display:block;margin:20rpx;font-size:26rpx;color:#333;">{{coursePackageInfo.introduce}}</text><block wx:if="{{coursePackageInfo.introduce_video}}"><video style="display:block;width:100%;" src="{{coursePackageInfo.isoss?coursePackageInfo.introduce_video:imgbaseUrl+coursePackageInfo.introduce_video}}" controls="{{true}}"></video></block></view></view><view class="kcxq_ten"><view class="kcxq_two_t"><view class="kcxq_two_t_n"><text>部分目录</text><text></text></view></view><view class="kbxq_ml"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="kbxq_ml_li"><view data-event-opts="{{[['tap',[['mlTap',[index]]]]]}}" class="kbxq_ml_li_t" bindtap="__e"><view>{{index+1+"."+item.$orig.name}}</view><text style="{{(item.$orig.toggle?'border-bottom: 14rpx solid #999;border-top:none':'')}}"></text></view><block wx:if="{{item.$orig.toggle}}"><view class="kbxq_ml_li_b"><block wx:for="{{item.l0}}" wx:for-item="itemerj" wx:for-index="indexerj" wx:key="indexerj"><block wx:if="{{itemerj.g0>0}}"><view class="kbxq_ml_li_b_li"><view>{{itemerj.$orig.name}}</view></view></block></block><block wx:if="{{item.g1==0}}"><view style="width:100%;text-align:center;font-size:26rpx;margin-top:30rpx;">暂无目录</view></block></view></block></view></block></view></view><view class="aqjlViw"></view><view class="kcxq_foo"><view class="kcxq_foo_l"><view data-event-opts="{{[['tap',[['homeTap',['$event']]]]]}}" bindtap="__e"><image src="/static/tabbar/tab_home.png"></image>首页</view></view><view class="kcxq_foo_r"><view class="kcxq_foo_r_sj">仅售：<text>{{"￥"+coursePackageInfo.price*1}}</text></view><block wx:if="{{coursePackageInfo.buy_state==0}}"><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/coursePackage/confirmOrder?id='+coursePackageInfo.id]]]]]}}" class="back" bindtap="__e">购买</view></block><block wx:else><view style="background:#e6e6e6!important;">已购</view></block></view></view></view></block>