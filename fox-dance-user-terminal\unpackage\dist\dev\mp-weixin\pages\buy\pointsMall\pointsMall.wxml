<view class="pointsMall"><view class="poi_two"><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/order/order']]]]]}}" class="poi_two_l" bindtap="__e">商城订单</view><view class="poi_two_r"><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/pointsMall/search','1']]]]]}}" class="les_search_l" bindtap="__e"><image src="/static/images/search.png"></image><input type="text" disabled="{{true}}" placeholder-style="color:#999999" placeholder="请搜索你想要的商品"/></view></view></view><view class="{{['poi_thr',scrollTop>=225?'poi_thr_fixed':'']}}"><view class="poi_thr_l"><block wx:for="{{shopCate}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['shopCateTap',[index]]]]]}}" class="{{['poi_thr_l_li',shopCateIndex==index?'poi_thr_l_li_ac':'']}}" bindtap="__e"><text></text><view>{{item.name}}</view><block wx:if="{{shopCateIndex==index}}"><image class="yj1" src="/static/images/yj1.png"></image></block><block wx:if="{{shopCateIndex==index}}"><image class="yj2" src="/static/images/yj2.png"></image></block></view></block></view><view class="poi_thr_r"><block wx:for="{{mallLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/buy/pointsMall/productDetails?id='+item.id,'1']]]]]}}" class="poi_thr_r_li" bindtap="__e"><image class="poi_thr_r_li_l" src="{{imgbaseUrl+item.image}}" mode="aspectFill"></image><view class="poi_thr_r_li_r"><view class="poi_thr_r_li_r_a">{{item.name}}</view><view class="poi_thr_r_li_r_b"><text>{{"￥"+item.redeem_points}}</text><view data-event-opts="{{[['tap',[['dhTap',['$0'],[[['mallLists','',index]]]]]]]}}" catchtap="__e">购买</view></view></view></view></block><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block></view></view><view class="aqjlViw"></view><shoppingselect class="vue-ref" vue-id="fed4e8ee-1" data-ref="shopCar" bind:__l="__l"></shoppingselect></view>