<block wx:if="{{goodsDetail.id}}"><view class="productDetails" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="pro_ban"><swiper class="swiper" circular="{{true}}" autoplay="{{true}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{goodsDetail.images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><image src="{{imgbaseUrl+item}}" mode="aspectFill"></image></swiper-item></block></swiper><view class="pro_ban_xf"><text>{{swiperIndex+1+"/"+$root.g0}}</text></view></view><view class="pro_one"><view class="pro_one_a">{{"￥"+goodsDetail.redeem_points*1}}</view><view class="pro_one_b"><view class="pro_one_b_t">{{goodsDetail.name}}</view></view></view><view class="pro_thr" style="margin-bottom:0;"><view class="pro_thr_t">商品信息</view><block wx:if="{{$root.g1>0}}"><view class="pro_thr_c"><block wx:for="{{goodsDetail.parameter}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pro_thr_c_li"><view>{{item.key}}</view><text>{{item.value}}</text></view></block></view></block></view><view class="pro_fou"><rich-text nodes="{{kcjsDetail}}"></rich-text></view><view class="peode_foo"><view data-event-opts="{{[['tap',[['dhTap']]]]}}" bindtap="__e">立即购买</view></view><view class="aqjlViw"></view><shoppingselect class="vue-ref" vue-id="2e5a9966-1" data-ref="shopCar" bind:__l="__l"></shoppingselect></view></block>