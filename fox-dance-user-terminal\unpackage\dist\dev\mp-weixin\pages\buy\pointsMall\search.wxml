<view class="search"><view class="les_search"><view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="请搜索你想要的商品" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$0'],['keywords']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchTap',['$0'],['keywords']]]]]}}" class="les_search_r" bindtap="__e">搜索</view></view><block wx:if="{{$root.g0>0}}"><view class="sear_one"><view class="sear_one_t">历史搜索<image src="/static/images/icon32.png" data-event-opts="{{[['tap',[['clearTap',['$event']]]]]}}" bindtap="__e"></image></view><view class="sear_one_b"><block wx:for="{{keywordsLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['searchTap',['$0'],[[['keywordsLists','',index,'name']]]]]]]}}" bindtap="__e">{{item.name}}</text></block></view></view></block><block wx:if="{{$root.g1==0}}"><view class="sear_one"><view class="sear_one_t">历史搜索</view><view class="sear_one_b" style="text-align:center;font-size:26rpx;color:#999;">暂无历史记录</view></view></block><view class="aqjlViw"></view></view>