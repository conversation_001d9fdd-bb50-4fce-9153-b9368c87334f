<block wx:if="{{gwcToggle}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="specification" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="spe_n" catchtap="__e"><view class="spe_n_a"><view class="spe_n_a_l"><image src="{{imgbaseUrl+(dqggItem.id==0?goodsDetial.image:dqggItem.image)}}" mode="aspectFill"></image></view><view class="spe_n_a_r"><view class="spe_n_a_r_a"><view>{{goodsDetial.name}}</view><image src="/static/images/gb1.png" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e"></image></view><view class="spe_n_a_r_b">{{"已选："+(xuanzText==''?'请选择':xuanzText)}}</view><view class="spe_n_a_r_c"><view>{{"￥"+price}}</view><block wx:if="{{false}}"><text>x1</text></block></view></view></view><view class="spe_n_b"><view class="spe_n_b_l">数量</view><view class="spe_n_b_r"><view data-event-opts="{{[['tap',[['jian',['$0'],['index']]]]]}}" bindtap="__e">-</view><input type="number" disabled="{{false}}" maxlength="3" data-event-opts="{{[['input',[['__set_model',['','selnum','$event',[]]]]]]}}" value="{{selnum}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['add',['$0'],['index']]]]]}}" bindtap="__e">+</view></view></view><view class="spe_n_c"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.g0!=0}}"><view class="spe_n_c_li"><view class="spe_n_c_t">{{item.$orig.name}}</view><view class="spe_n_c_t_b"><block wx:for="{{item.$orig.value}}" wx:for-item="erjitem" wx:for-index="erjindex" wx:key="erjindex"><view data-event-opts="{{[['tap',[['selguigClick',[index,erjindex]]]]]}}" class="{{[item.$orig.indexSel==erjindex?'sho_con_b_a_b_ac':'']}}" bindtap="__e">{{erjitem}}</view></block></view></view></block></block></view><view data-event-opts="{{[['tap',[['ljdhTap',['$event']]]]]}}" class="spe_n_d" bindtap="__e">立即购买</view></view><view class="aqjlViw"></view></view></block>