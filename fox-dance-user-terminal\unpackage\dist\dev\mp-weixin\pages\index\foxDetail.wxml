<block wx:if="{{loding}}"><view style="{{'overflow:hidden;'+('--qjbutton-color:'+(qjbutton)+';')}}"><view class="fox"><view class="title"><view class="title_text">Fox介绍</view><view class="title_bottom"></view></view><view class="fox_cont flex col-top"><view class="fox_cont_text">{{foxInfo.introduce}}</view><view class="fox_cont_img"><image mode="aspectFill" src="{{imgbaseUrl+foxInfo.introduce_image}}"></image></view></view></view><view class="fox"><view class="title"><view class="title_text">主理人介绍</view><view class="title_bottom"></view></view><view class="fox_cont flex col-top"><view class="fox_cont_img" style="margin-left:0;margin-right:32rpx;"><image mode="aspectFill" src="{{imgbaseUrl+foxInfo.host_image}}"></image></view><view class="fox_cont_text">{{foxInfo.host}}</view></view></view><view class="fox"><view class="title"><view class="title_text">成立背景</view><view class="title_bottom"></view></view><view class="fox_cont flex col-top"><view class="fox_cont_text" style="padding-top:0;">{{foxInfo.establish_background}}</view></view></view><block wx:if="{{$root.g0>0}}"><view class="fox teach"><view class="flex row-between" style="padding:26rpx;"><view class="title"><view class="title_text">旗下师资</view><view class="title_bottom"></view></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/teacherDetail']]]]]}}" class="more flex" bindtap="__e">查看更多<image src="/static/images/introduce_more.png" mode="scaleToFill"></image></view></view><view class="teach_list"><block wx:for="{{foxInfo.teacher}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/index/teacherDetail']]]]]}}" class="teach_li flex" bindtap="__e"><view class="teach_li_l"><image src="{{imgbaseUrl+item.image}}" mode="aspectFill"></image></view><view class="teach_li_r flex-1"><view class="teach_li_r_t flex"><view class="teach_li_r_name">{{item.name+"老师"}}</view><block wx:for="{{item.levelTable.name}}" wx:for-item="itemerj" wx:for-index="indexerj" wx:key="indexerj"><view class="teach_li_r_tag">{{itemerj}}</view></block></view><view class="teach_li_r_c">{{"擅长舞种："+item.skilled_dance}}</view><block wx:if="{{item.work_year*1>0}}"><view class="teach_li_r_c">{{"工作年限："+item.work_year+"年"}}</view></block></view></view></block></view></view></block><block wx:if="{{$root.g1>0}}"><view class="fox teach store"><view class="flex row-between" style="padding:26rpx;"><view class="title"><view class="title_text">所有门店</view><view class="title_bottom"></view></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/switchStores?mdlist=1']]]]]}}" class="more flex" bindtap="__e">查看更多<image src="/static/images/introduce_more.png" mode="scaleToFill"></image></view></view><view class="store_list"><block wx:for="{{foxInfo.store}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/index/storesDetail?id='+item.id]]]]]}}" class="store_li" bindtap="__e"><view class="store_li_t flex"><view class="store_li_t_l"><image src="{{imgbaseUrl+item.image}}" mode="aspectFill"></image></view><view class="store_li_t_r flex-1"><view>{{item.name}}</view><view>{{item.introduce}}</view><view class="flex"><image src="/static/images/store_map_icon.png" mode="scaleToFill"></image>{{'距离你'+item.distance+'km'}}</view></view></view><view data-event-opts="{{[['tap',[['dhTap',['$0'],[[['foxInfo.store','',index]]]]]]]}}" class="store_li_d flex row-between" catchtap="__e"><view class="line-1">{{item.address}}</view><view class="btn">导航前往</view></view></view></block></view></view></block></view></block>