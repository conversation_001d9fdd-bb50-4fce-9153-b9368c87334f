@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.title {
  position: relative;
}
.title .title_text {
  position: relative;
  z-index: 11;
  font-family: Maoken Glitch Sans, Maoken Glitch Sans;
  font-size: 32rpx;
  color: #333333;
  line-height: 38rpx;
}
.title .title_bottom {
  position: absolute;
  left: 0;
  bottom: 6rpx;
  width: 126rpx;
  height: 10rpx;
  background: linear-gradient(90deg, #131315 28%, rgba(255, 255, 255, 0) 100%);
}
.fox {
  margin: 20rpx auto 0;
  width: 698rpx;
  padding: 26rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}
.fox .fox_cont {
  margin-top: 26rpx;
}
.fox .fox_cont .fox_cont_text {
  padding-top: 16rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.fox .fox_cont .fox_cont_img {
  margin-left: 32rpx;
}
.fox .fox_cont .fox_cont_img image {
  width: 266rpx;
  height: 174rpx;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  background-color: pink;
}
.teach {
  background-color: transparent;
  padding: 0;
}
.teach .more {
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.teach .more image {
  width: 10.46rpx;
  height: 16rpx;
  margin-left: 12rpx;
}
.teach .teach_list .teach_li {
  margin: 20rpx auto 0;
  padding: 42rpx 26rpx 40rpx;
  width: 698rpx;
  height: 200rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}
.teach .teach_list .teach_li:nth-child(1) {
  margin-top: 0;
}
.teach .teach_list .teach_li .teach_li_l image {
  width: 118rpx;
  height: 118rpx;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  background-color: pink;
}
.teach .teach_list .teach_li .teach_li_r {
  margin-left: 26rpx;
}
.teach .teach_list .teach_li .teach_li_r .teach_li_r_t .teach_li_r_name {
  font-weight: bold;
  font-size: 32rpx;
  color: #333333;
  line-height: 38rpx;
  margin-right: 28rpx;
}
.teach .teach_list .teach_li .teach_li_r .teach_li_r_t .teach_li_r_tag {
  margin-right: 10rpx;
  padding: 0 6rpx;
  height: 34rpx;
  background: #131315;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  line-height: 34rpx;
}
.teach .teach_list .teach_li .teach_li_r .teach_li_r_c {
  margin-top: 4rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.teach .teach_list .teach_li .teach_li_r .teach_li_r_c:nth-child(2) {
  margin-top: 8rpx;
}
.store {
  margin-top: 28rpx;
  margin-bottom: 28rpx;
}
.store .store_list .store_li {
  margin: 22rpx auto 0;
  width: 698rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
}
.store .store_list .store_li:nth-child(1) {
  margin-top: 0rpx;
}
.store .store_list .store_li .store_li_t {
  padding: 18rpx 26rpx;
  border-bottom: 2rpx solid rgba(153, 153, 153, 0.2);
}
.store .store_list .store_li .store_li_t .store_li_t_l image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  background-color: pink;
}
.store .store_list .store_li .store_li_t .store_li_t_r {
  margin-left: 26rpx;
}
.store .store_list .store_li .store_li_t .store_li_t_r view:nth-child(1) {
  font-weight: bold;
  font-size: 32rpx;
  color: #333;
  line-height: 38rpx;
}
.store .store_list .store_li .store_li_t .store_li_t_r view:nth-child(2) {
  margin-top: 6rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 36rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.store .store_list .store_li .store_li_t .store_li_t_r view:nth-child(3) {
  margin-top: 6rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.store .store_list .store_li .store_li_t .store_li_t_r view:nth-child(3) image {
  width: 32rpx;
  height: 32rpx;
}
.store .store_list .store_li .store_li_d {
  width: 698rpx;
  height: 112rpx;
  padding: 0 26rpx;
}
.store .store_list .store_li .store_li_d view:nth-child(1) {
  max-width: 470rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.store .store_list .store_li .store_li_d view:nth-child(2) {
  width: 144rpx;
  height: 60rpx;
  border-radius: 82rpx 82rpx 82rpx 82rpx;
  font-size: 26rpx;
}

