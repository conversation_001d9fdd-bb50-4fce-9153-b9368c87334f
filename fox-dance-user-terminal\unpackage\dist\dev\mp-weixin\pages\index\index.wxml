<view class="index" style="{{('background:'+pageBj)}}"><view class="ind_ban"><view class="ind_swiper_bj"><block wx:for="{{topBanBj}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image style="{{(topBanIndex==index?'opacity:1;':'opacity:0;')}}" src="{{imgbaseUrl+item.carousel_background}}" mode="aspectFill" data-event-opts="{{[['tap',[['goBannerTap',['$0'],[[['topBanBj','',index,'jump_address']]]]]]]}}" bindtap="__e"></image></block></view><swiper circular="true" data-event-opts="{{[['change',[['topChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{topBan}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><view><image src="{{imgbaseUrl+item.carousel}}" mode="aspectFill" data-event-opts="{{[['tap',[['goBannerTap',['$0'],[[['topBan','',index,'jump_address']]]]]]]}}" bindtap="__e"></image></view></swiper-item></block></swiper><view class="ind_ban_box" style="{{('background: linear-gradient(to top, '+pageBj+' 0%, transparent 100%)')}}"></view></view><view class="ind_one"><view style="width:100%;height:auto;overflow:hidden;"></view><view class="ind_one_tx"><block wx:if="{{!isLogined}}"><image src="/static/images/toux.png" mode="aspectFill"></image></block><block wx:if="{{isLogined}}"><image src="{{userInfo.avatar==''?'/static/images/toux.png':imgbaseUrl+userInfo.avatar}}" mode="aspectFill"></image></block></view><view class="ind_one_a"><view>{{"HI，"+(isLogined?userInfo.nickname==''?'微信用户':userInfo.nickname:'请先登录')}}</view><text>{{userInfo.level_name}}</text></view><view class="ind_one_b"><view data-event-opts="{{[['tap',[['navTo',['/pages/prizedraw/prizedraw']]]]]}}" class="ind_one_b_li" style="width:50%;" bindtap="__e"><view class="ind_one_b_li_a">抽奖次数</view><view class="ind_one_b_li_b">{{(isLogined?userInfo.luck_draw_frequency*1:0)+"次"}}</view><view class="xian"></view></view><view data-event-opts="{{[['tap',[['navTo',['/pages/prizedraw/prizedraw']]]]]}}" class="ind_one_b_li" style="width:50%;" bindtap="__e"><view class="ind_one_b_li_a">惊喜抽奖</view><view class="ind_one_b_li_b">可抽奖</view></view></view><view class="ind_one_c"><view data-event-opts="{{[['tap',[['navTo',['/pages/prizedraw/dengji']]]]]}}" class="ind_one_c_l" bindtap="__e">经验值<image src="/static/images/icon72.png"></image></view><view class="ind_one_c_c"><text>{{userInfo.experience_value}}</text>{{"/"+userInfo.upgrade}}</view><view class="ind_one_c_r"><view style="{{('width:'+userInfo.experience_value*1/userInfo.upgrade*100+'%;')}}"></view></view></view></view><view class="ind_two"><view data-event-opts="{{[['tap',[['navTo',['/pages/index/teacherDetail',1]]]]]}}" class="ind_two_li" bindtap="__e"><image src="/static/images/icon73.png"></image><view class="ind_two_li_a">导师介绍</view><view class="ind_two_li_b">130+位广州实力导师</view><text></text></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/foxDetail',1]]]]]}}" class="ind_two_li" bindtap="__e"><image src="/static/images/icon74.png"></image><view class="ind_two_li_a">FOX介绍</view><view class="ind_two_li_b">F5.周年品牌沉跑</view><text></text></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/storesDetail?id='+userInfo.store.id,1]]]]]}}" class="ind_two_li" bindtap="__e"><image src="/static/images/icon75.png"></image><view class="ind_two_li_a">门店介绍</view><view class="ind_two_li_b">灵动舞之坊</view></view></view><view class="ind_thr"><view class="ind_thr_l"><view>当前门店</view><text>ADDRE55</text><text class="xian"></text></view><view class="ind_thr_c"><u-icon vue-id="8dd740cc-1" name="map-fill" color="{{pageBj}}" size="40" bind:__l="__l"></u-icon><view style="{{('color:'+pageBj)}}">{{userInfo.store.name}}</view><text class="xian"></text></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/switchStores']]]]]}}" class="ind_thr_r" bindtap="__e"><view class="ind_thr_r_a"><image src="/static/images/icon77.png"></image>切换门店<image src="/static/images/icon77.png"></image></view><view class="ind_thr_r_b">在此切换门店</view></view></view><view class="ind_fou"><image class="ind_fou_l" src="/static/images/icon78.png"></image><view class="ind_fou_r"><uni-notice-bar vue-id="8dd740cc-2" scrollable="{{true}}" single="{{true}}" text="{{userInfo.notice}}" background-color="transparent" color="#333" bind:__l="__l"></uni-notice-bar></view></view><block wx:if="{{$root.g0>0}}"><view class="ind_fiv"><view class="ind_fiv_t"><view>最新课程咨询</view><text>不定期福利等你领取</text></view><view class="ind_fiv_b"><swiper circular="true" data-event-opts="{{[['change',[['kczxChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{kczxBan}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><image src="{{imgbaseUrl+item.carousel}}" mode="aspectFill" data-event-opts="{{[['tap',[['goBannerTap',['$0'],[[['kczxBan','',index,'jump_address']]]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper><view class="ind_fiv_b_yd"><block wx:for="{{kczxBan}}" wx:for-item="item" wx:for-index="index"><text class="{{[kczxIndex==index?'ind_fiv_b_yd_ac':'']}}"></text></block></view></view></view></block><view style="height:300rpx;"></view><view class="use"><view class="share"><image src="/static/images/index_share.png" mode></image><button open-type="share"></button></view><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/tzgl']]]]]}}" class="concat" bindtap="__e"><image src="/static/images/index_concat_kf1.png" mode="scaleToFill"></image><view><text>消息</text><text>推送</text></view></view></view><u-popup bind:input="__e" vue-id="8dd740cc-3" border-radius="20" mode="center" value="{{showConcat}}" data-event-opts="{{[['^input',[['__set_model',['','showConcat','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="concat_box"><view class="concat_box_title flex col-top row-between"><view>选择门店</view><image src="/static/images/popup_close.png" mode="scaleToFill" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e"></image></view><view class="concat_box_list"><block wx:for="{{storesLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="concat_box_li flex row-between"><view class="concat_box_li_l flex"><view class="concat_box_li_l_img"><image src="{{imgbaseUrl+item.image}}" mode="scaleToFill"></image></view><view class="concat_box_li_l_name"><view class="text">{{item.name}}<view class="title_bottom"></view></view></view></view><view data-event-opts="{{[['tap',[['navTo',['/pages/index/service?id='+item.id]]]]]}}" class="concat_box_li_r btn" bindtap="__e">联系客服</view></view></block></view></view></u-popup><tabbar class="vue-ref" vue-id="8dd740cc-4" current="{{0}}" data-ref="tabbar" bind:__l="__l"></tabbar><block wx:if="{{loding}}"><view class="lodingg"></view></block></view>