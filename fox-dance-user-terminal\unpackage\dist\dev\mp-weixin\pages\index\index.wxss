@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
page {
  padding-bottom: 0;
  background: #FADAFF;
}
.index {
  -background-image: url(https://dance-1329875799.cos.ap-guangzhou.myqcloud.com/userreport/login_top_bgi.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  min-height: 100vh;
  transition: 0.8s;
}
.use {
  position: fixed;
  right: 14rpx;
  bottom: 30vh;
  z-index: 11;
}
.use .share {
  position: relative;
}
.use .share button {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}
.use .concat {
  position: relative;
}
.use .concat view {
  width: 100%;
  height: 82rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
}
.use .concat view text {
  display: block;
  font-size: 22rpx;
  color: #333;
  letter-spacing: 1px;
  position: relative;
  left: 1px;
}
.use .concat button {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}
.use image {
  width: 106rpx;
  height: 106rpx;
}
.use image:nth-child(1) {
  margin-bottom: 34rpx;
}
.userInfo {
  margin: 494rpx auto 0;
  padding: 42rpx 32rpx 32rpx;
  width: 700rpx;
  background: #FFFFFF;
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  position: relative;
}
.userInfo .userInfo_t .userInfo_t_l {
  padding-left: 20rpx;
}
.userInfo .userInfo_t .userInfo_t_l .userInfo_t_l_t {
  font-family: Maoken Glitch Sans;
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.userInfo .userInfo_t .userInfo_t_l .userInfo_t_l_d {
  margin-top: 12rpx;
  font-weight: Medium;
  font-size: 28rpx;
  color: #333333;
  line-height: 33rpx;
}
.userInfo .userInfo_t .userInfo_t_r {
  position: absolute;
  right: 52rpx;
  top: -44rpx;
}
.userInfo .userInfo_t .userInfo_t_r image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  border: 4rpx solid #FFFFFF;
  background-color: pink;
}
.userInfo .userInfo_count {
  margin-top: 26rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid rgba(160, 160, 160, 0.2);
}
.userInfo .userInfo_count .userInfo_count_li {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.userInfo .userInfo_count .userInfo_count_li .userInfo_count_li_num {
  font-weight: 500;
  font-size: 40rpx;
  color: #333333;
  line-height: 47rpx;
}
.userInfo .userInfo_count .userInfo_count_li .userInfo_count_li_num text {
  font-size: 26rpx;
  line-height: 33rpx;
}
.userInfo .userInfo_count .userInfo_count_li .userInfo_count_li_text {
  margin-top: 26rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.userInfo .userInfo_shop {
  padding-top: 32rpx;
}
.userInfo .userInfo_shop .userInfo_shop_l .userInfo_shop_l_t {
  font-weight: 500;
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
  display: flex;
  align-items: center;
}
.userInfo .userInfo_shop .userInfo_shop_l .userInfo_shop_l_t image {
  width: 10.46rpx;
  height: 16rpx;
}
.userInfo .userInfo_shop .userInfo_shop_l .userInfo_shop_l_d {
  margin-top: 12rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.userInfo .userInfo_shop .userInfo_shop_r .btn {
  width: 144rpx;
  font-size: 26rpx;
  height: 56rpx;
  background: #945048;
  border-radius: 124rpx 124rpx 124rpx 124rpx;
}
.notice {
  margin: 24rpx auto 0;
  width: 670rpx;
  height: 80rpx;
  background: #fff;
  padding: 0 26rpx;
  height: 72rpx;
  background: #FFFFFF;
  border-radius: 90rpx 90rpx 90rpx 90rpx;
}
.notice .notice_l image {
  width: 32rpx;
  height: 32rpx;
}
.notice .notice_r {
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.notice .notice_r .uni-noticebar {
  margin-bottom: 0 !important;
}
.nav {
  padding: 26rpx 22rpx 0 26rpx;
}
.nav .title_bottom {
  position: absolute;
  left: 0;
  bottom: 6rpx;
  width: 126rpx;
  height: 10rpx;
  background: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);
}
.nav .nav_l {
  width: 362rpx;
  height: 308rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}
.nav .nav_l .nav_l_t {
  position: relative;
}
.nav .nav_l .nav_l_t .nav_l_text {
  position: relative;
  z-index: 11;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 38rpx;
}
.nav .nav_l .nav_l_d image {
  width: 202rpx;
  height: 202rpx;
  margin: 30rpx auto 0;
}
.nav .nav_r {
  width: 314rpx;
  height: 308rpx;
}
.nav .nav_r .nav_r_t {
  width: 314rpx;
  height: 136rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
}
.nav .nav_r .nav_r_t .nav_r_t_text {
  position: relative;
}
.nav .nav_r .nav_r_t .nav_r_t_text .text {
  position: relative;
  z-index: 11;
  font-family: Maoken Glitch Sans;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 38rpx;
}
.nav .nav_r .nav_r_t .nav_r_t_img image {
  width: 108rpx;
  height: 108rpx;
  margin-left: 26rpx;
}
.poster image {
  width: 698rpx;
  height: 238rpx;
  margin: 20rpx auto 0;
}
.poster image:nth-child(1) {
  margin-top: 28rpx;
}
.concat_box {
  padding: 26rpx 66rpx;
  width: 662rpx;
  height: 770rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  overflow: auto;
}
.concat_box .concat_box_title {
  padding-bottom: 26rpx;
  border-bottom: 2rpx solid rgba(148, 80, 72, 0.2);
}
.concat_box .concat_box_title view {
  font-size: 32rpx;
  color: #333333;
  line-height: 38rpx;
}
.concat_box .concat_box_title image {
  width: 40rpx;
  height: 40rpx;
}
.concat_box .concat_box_list {
  -height: calc(100% - 100rpx);
  -overflow: auto;
}
.concat_box .concat_box_list .concat_box_li {
  padding: 14rpx 30rpx;
  padding: 14rpx 0;
  margin: 26rpx 0  0;
}
.concat_box .concat_box_list .concat_box_li .concat_box_li_l .concat_box_li_l_img image {
  width: 108rpx;
  height: 108rpx;
}
.concat_box .concat_box_list .concat_box_li .concat_box_li_l .concat_box_li_l_name {
  width: 200rpx;
  position: relative;
  margin-left: 40rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  margin-right: 20rpx;
}
.concat_box .concat_box_list .concat_box_li .concat_box_li_l .concat_box_li_l_name .text {
  position: relative;
  z-index: 11;
  color: #333333;
  font-family: Maoken Glitch Sans;
  font-size: 26rpx;
  line-height: 30rpx;
  float: left;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
.concat_box .concat_box_list .concat_box_li .concat_box_li_l .concat_box_li_l_name .title_bottom {
  position: absolute;
  left: 0;
  bottom: 6rpx;
  -width: 126rpx;
  height: 10rpx;
  background: linear-gradient(90deg, #945048 28%, rgba(255, 255, 255, 0) 100%);
  float: left;
  width: 100%;
  z-index: -1;
}
.concat_box .concat_box_list .concat_box_li .concat_box_li_r {
  width: 152rpx;
  height: 56rpx;
  background: #945048;
  border-radius: 92rpx 92rpx 92rpx 92rpx;
  font-size: 26rpx;
}

