<view class="login" style="{{'background-size:'+(bgs)+';'}}"><u-navbar vue-id="35a7246c-1" title="FOX舞蹈" back-icon-color="#333" back-icon-size="42" background="{{({background:'transparent'})}}" border-bottom="{{false}}" title-color="#333" title-size="32" bind:__l="__l"></u-navbar><view class="logt_bj" style="{{('top:'+(safeAreaTop+10+menuButtonInfoHeight)+'px;')}}"><image src="/static/images/loginbj.png"></image></view><view class="acount-login"><view class="acount-login_a"><text>手机号</text><text>登陆 / 注册</text></view><view class="acount-login_b">未注册的手机将会自动注册</view><view class="btn">手机号一键登陆<block wx:if="{{isAgreement}}"><button open-type="getPhoneNumber" size="lg" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e"></button></block><block wx:else><button size="lg" data-event-opts="{{[['tap',[['tisTap',['$event']]]]]}}" bindtap="__e"></button></block></view><view data-event-opts="{{[['tap',[['IsAgree',['$event']]]]]}}" class="m-b-30 sm flex row-center" style="height:56rpx;" bindtap="__e"><block wx:if="{{!isAgreement}}"><image src="/static/images/Mr.png" mode="scaleToFill"></image></block><block wx:else><image src="/static/images/Mr_x.png" mode="scaleToFill"></image></block><view class="flex">我已阅读并同意授权<view data-event-opts="{{[['tap',[['navTo',['/pages/login/xieYi?type=1']]]]]}}" style="color:#945048;" catchtap="__e">《用户注册购卡协议》</view></view></view></view><view class="logo"><image src="/static/images/logo.png"></image></view><block wx:if="{{editzlToggle}}"><view class="logsetTc"><view class="logsetTc_n"><view class="logsetTc_t"><view>请先设置头像和昵称</view><text>注册、登录小程序</text></view><view class="logsetTc_c"><view class="qyrz_con_li tx"><button open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['chooseAvatarsc',['$event']]]]]}}" bindchooseavatar="__e">获取头像</button><view class="qyrz_con_li_l">头像</view><view class="qyrz_con_li_r"><view class="uni-input" style="height:auto;"><image class="edma_one_a" src="{{avatar==''?'/static/images/toux.png':imgbaseUrl+avatar}}" mode="aspectFill"></image><image src="/static/images/right_more.png"></image></view></view></view><view class="qyrz_con_li"><view class="qyrz_con_li_l">昵称</view><view class="qyrz_con_li_r"><input type="nickname" placeholder="请输入昵称" maxlength="8" data-event-opts="{{[['input',[['__set_model',['','nickname','$event',[]]]]]]}}" value="{{nickname}}" bindinput="__e"/></view></view></view><view class="logsetTc_f"><view data-event-opts="{{[['tap',[['qxTap',['$event']]]]]}}" bindtap="__e">跳过</view><view data-event-opts="{{[['tap',[['subTap',['$event']]]]]}}" bindtap="__e">确定</view></view></view></view></block></view>