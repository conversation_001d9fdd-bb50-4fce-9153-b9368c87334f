<block wx:if="{{loding}}"><view style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="cont"><view class="cont_row flex col-top"><view class="cont_row_l">收货人</view><view class="cont_row_r flex-1"><input maxlength="11" type="text" placeholder="收货人姓名" data-event-opts="{{[['input',[['__set_model',['','name','$event',[]]]]]]}}" value="{{name}}" bindinput="__e"/><view class="check_sex flex"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['check_sex_li','flex',sex==1?'check_sex_li_ac':'']}}" bindtap="__e"><block wx:if="{{sex==1}}"><image src="/static/images/dzxz-11.png" mode="scaleToFill"></image></block><block wx:else><image src="/static/images/dzxz.png" mode="scaleToFill"></image></block>男士</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['check_sex_li','flex',sex==2?'check_sex_li_ac':'']}}" bindtap="__e"><block wx:if="{{sex==2}}"><image src="/static/images/dzxz-11.png" mode="scaleToFill"></image></block><block wx:else><image src="/static/images/dzxz.png" mode="scaleToFill"></image></block>女士</view></view></view></view><view data-event-opts="{{[['tap',[['choose',['$event']]]]]}}" class="cont_row flex col-top" bindtap="__e"><view class="cont_row_l">选择地址</view><view class="cont_row_r flex-1 flex row-between"><view class="select line-1" style="{{'width:450rpx;'+('color:'+(address?'#333':'#999')+';')}}">{{''+(address?address:'选择详细地址')+''}}</view><image class="select_img" src="/static/images/index_help_more.png" mode="scaleToFill"></image></view></view><view class="cont_row flex"><view class="cont_row_l">详细地址</view><view class="cont_row_r flex-1 flex row-between"><textarea placeholder="请输入省市区县、乡镇" auto-height="{{true}}" placeholder-style="color:#999999;" data-event-opts="{{[['input',[['__set_model',['','address_detail','$event',[]]]]]]}}" value="{{address_detail}}" bindinput="__e"></textarea></view></view><view class="cont_row flex"><view class="cont_row_l">手机号</view><view class="cont_row_r flex-1 flex row-between"><input maxlength="11" type="number" placeholder="收货人手机号" placeholder-class="color9" data-event-opts="{{[['input',[['__set_model',['','mobile','$event',[]]]]]]}}" value="{{mobile}}" bindinput="__e"/></view></view></view><view class="default flex row-between"><view class="default_t"><view>设置为默认地址</view><view class="default_d">启动时将优先定位在默认地址，避免选错</view></view><view><u-switch vue-id="162a055a-1" active-color="{{qjbutton}}" size="40" value="{{switchVal}}" data-event-opts="{{[['^change',[['change']]],['^input',[['__set_model',['','switchVal','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-switch></view></view><view class="aqjlViw"></view><block wx:if="{{!Edit_id}}"><view class="add_foo"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">添加地址</view></view></block><block wx:if="{{Edit_id}}"><view class="add_foo add_fooEdit"><view data-event-opts="{{[['tap',[['delAddress',['$event']]]]]}}" bindtap="__e">删除</view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">保存</view></view></block><u-popup bind:input="__e" vue-id="162a055a-2" mode="center" border-radius="20" value="{{showPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPopup','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="prompt"><view class="prompt_t"><view class="prompt_t_img"><image src="/static/images/popup-icon.png" mode="scaleToFill"></image></view><view class="prompt_t_text">提示</view></view><view class="prompt_c">确定退出当前删除？</view><view class="prompt_d"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="prompt_d_l" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmDel',['$event']]]]]}}" class="prompt_d_r" bindtap="__e">确定</view></view></view></u-popup></view></block>