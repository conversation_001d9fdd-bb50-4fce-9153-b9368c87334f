@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.cont {
  margin: 22rpx auto 0;
  width: 698rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 26rpx 26rpx 0;
}
.cont .cont_row {
  padding-top: 26rpx;
  border-bottom: 2rpx solid rgba(167, 167, 167, 0.2);
  padding-bottom: 26rpx;
}
.cont .cont_row:last-child {
  border-bottom: none;
}
.cont .cont_row:nth-child(1) {
  padding-top: 0;
}
.cont .cont_row .cont_row_l {
  width: 144rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.cont .cont_row .cont_row_r input {
  width: 100%;
  height: 100%;
  font-size: 26rpx;
  color: #333;
  line-height: 30rpx;
}
.cont .cont_row .cont_row_r .color9 {
  color: #999;
}
.cont .cont_row .cont_row_r textarea {
  width: 100%;
  font-size: 26rpx;
  color: #333;
  line-height: 30rpx;
}
.cont .cont_row .cont_row_r .select {
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.cont .cont_row .cont_row_r .select_img {
  width: 32rpx;
  height: 32rpx;
}
.cont .cont_row .cont_row_r .check_sex {
  margin-top: 26rpx;
}
.cont .cont_row .cont_row_r .check_sex .check_sex_li {
  font-size: 26rpx;
  color: #999;
  line-height: 30rpx;
  margin-right: 26rpx;
}
.cont .cont_row .cont_row_r .check_sex .check_sex_li image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}
.default {
  margin: 30rpx auto 0;
  width: 698rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 26rpx;
}
.default .default_t view:nth-child(1) {
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.default .default_t .default_d {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.foot {
  position: fixed;
  width: 100%;
  bottom: 68rpx;
}
.foot .btn {
  width: 608rpx;
  height: 90rpx;
  border-radius: 94rpx 94rpx 94rpx 94rpx;
  color: #333333;
  font-size: 32rpx;
}
.foot .edit_use {
  padding: 0 26rpx;
}
.foot .edit_use .btn:nth-child(1) {
  width: 336rpx;
  height: 88rpx;
  border-radius: 88rpx 88rpx 88rpx 88rpx;
  border: 2rpx solid #09C867;
  background: transparent;
  font-size: 26rpx;
  color: #09C867;
}
.foot .edit_use .btn:nth-child(2) {
  width: 336rpx;
  height: 88rpx;
  border-radius: 88rpx 88rpx 88rpx 88rpx;
  font-size: 26rpx;
  color: #fff;
}
.qu_box {
  width: 480rpx;
  height: 314rpx;
  padding: 50rpx;
}
.qu_box .qu_box_title {
  text-align: center;
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  line-height: 39rpx;
}
.qu_box .qu_box_cont {
  margin-top: 26rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 39rpx;
  text-align: center;
}
.qu_box .qu_box_use {
  padding: 0 26rpx;
  margin-top: 50rpx;
}
.qu_box .qu_box_use .qu_box_use_li {
  width: 144rpx;
  height: 60rpx;
  background: #09C867;
  border-radius: 88rpx 88rpx 88rpx 88rpx;
  font-size: 26rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 60rpx;
}
.qu_box .qu_box_use .qu_box_use_li:nth-child(1) {
  background: #CBF1DE;
}
.check_sex_li_ac {
  color: #333 !important;
  font-weight: bold;
}

