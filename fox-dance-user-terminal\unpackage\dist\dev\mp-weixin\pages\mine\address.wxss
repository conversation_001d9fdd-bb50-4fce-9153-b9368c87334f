@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.list .li {
  margin: 28rpx auto 0;
  width: 686rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 26rpx;
}
.list .li .li_t {
  padding-bottom: 34rpx;
  border-bottom: 2rpx dashed rgba(167, 167, 167, 0.2);
}
.list .li .li_t .li_t_info {
  font-size: 26rpx;
  color: #333333;
  line-height: 30rpx;
}
.list .li .li_t .li_t_detail {
  margin-top: 8rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.list .li .li_d {
  padding-top: 30rpx;
}
.list .li .li_d .li_d_l {
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
}
.list .li .li_d .li_d_l image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}
.list .li .li_d .li_d_use .li_d_li {
  font-size: 26rpx;
  color: #999999;
  line-height: 30rpx;
  margin-left: 20rpx;
}
.list .li .li_d .li_d_use .li_d_li image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}
.tip_box {
  width: 480rpx;
  height: 416rpx;
  padding: 50rpx 36rpx;
}
.tip_box .tip_box_title {
  text-align: center;
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  line-height: 39rpx;
}
.tip_box .tip_box_cont {
  margin: 0 auto;
  width: 380rpx;
  text-align: center;
  margin-top: 54rpx;
  font-size: 26rpx;
  color: #818181;
  line-height: 36rpx;
}
.tip_box .tip_box_btn {
  margin: 0 auto;
  margin-top: 54rpx;
  font-size: 26rpx;
  color: #818181;
  line-height: 30rpx;
  color: #fff;
  width: 144rpx;
  height: 60rpx;
  border-radius: 30rpx;
}

