<block wx:if="{{loding}}"><view class="editinformation" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="edi_ban"><image src="{{baseUrl_admin+'/static/images/icon23.jpg'}}"></image></view><view class="edi_one"><image class="edma_one_a" src="{{avatar==''?'/static/images/toux.png':imgbaseUrl+avatar}}" mode="aspectFill"></image><text>点击修改头像</text><button class="btn" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['chooseAvatarsc',['$event']]]]]}}" bindchooseavatar="__e">获取头像</button></view><view class="edma_two"><view class="edma_two_li"><view>昵称：</view><input type="nickname" placeholder="请输入昵称" disabled="{{is_store>0?true:false}}" maxlength="8" placeholder-style="color:#999999;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['','nickname','$event',[]]]]]]}}" value="{{nickname}}" bindinput="__e"/></view><view class="edma_two_li"><view>个人简介：</view><input style="width:calc(100% - 146rpx);" type="text" placeholder="请输入个人简介" placeholder-style="color:#999999;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['','rek','$event',[]]]]]]}}" value="{{rek}}" bindinput="__e"/></view></view><view class="edma_thr"><view data-event-opts="{{[['tap',[['tjxxTap',['$event']]]]]}}" bindtap="__e">提交修改</view><view data-event-opts="{{[['tap',[['logoutTap',['$event']]]]]}}" bindtap="__e">退出登录</view></view></view></block>