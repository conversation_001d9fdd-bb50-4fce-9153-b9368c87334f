<view class="integral" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="int_one"><view class="int_one_a">我的积分</view><view class="int_one_b">{{score}}</view><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/order/order']]]]]}}" class="int_one_c" bindtap="__e">积分订单</view></view><view class="int_two"><view class="int_two_l"><view data-event-opts="{{[['tap',[['tabTap',[0]]]]]}}" class="{{[type==0?'int_two_l_ac':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['tabTap',[1]]]]]}}" class="{{[type==1?'int_two_l_ac':'']}}" bindtap="__e">获得</view><view data-event-opts="{{[['tap',[['tabTap',[2]]]]]}}" class="{{[type==2?'int_two_l_ac':'']}}" bindtap="__e">消耗</view></view><view class="int_two_r"><picker mode="date" value="{{date_sj}}" fields="month" data-event-opts="{{[['change',[['bindDateChange_sj',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{date_sj}}<text></text></view></picker></view></view><block wx:if="{{!zanwsj}}"><view class="int_thr"><block wx:for="{{scoreLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="int_thr_li"><view class="int_thr_li_a">{{item.type==1?'兑换扣除':item.type==2?'邀请增加':item.type==3?'系统增加':item.type==4?'系统扣除':''}}</view><view class="int_thr_li_b">{{item.create_time}}</view><view class="int_thr_li_c">{{(item.type==1?'-':item.type==2?'+':item.type==3?'+':item.type==4?'-':'+')+item.score*1}}</view></view></block><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block><view class="aqjlViw"></view></view>