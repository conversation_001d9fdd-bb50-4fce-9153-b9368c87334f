<view class="lessonPackage"><view class="les_search"><view class="les_search_l"><image src="/static/images/search.png"></image><input type="text" placeholder-style="color:#999999" placeholder="搜索课包名称" confirm-type="search" data-event-opts="{{[['confirm',[['searchTap',['$event']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchTap',['$event']]]]]}}" class="les_search_r" bindtap="__e">搜索</view></view><view class="les_con"><block wx:for="{{coursePackageLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['/pages/mine/lessonPackage/lessonPackagexq?id='+item.course_package_id]]]]]}}" class="les_con_li" bindtap="__e"><image class="les_con_li_l" src="{{imgbaseUrl+item.package.image}}" mode="aspectFill"></image><view class="les_con_li_r"><view class="les_con_li_r_a">{{item.package.name}}</view><view class="les_con_li_r_b">{{"课程时长："+item.duration*1+"分钟"}}</view><view class="les_con_li_r_c">{{"讲师:"+item.teacher_name}}</view></view></view></block></view><block wx:if="{{!zanwsj}}"><view class="gg_loding"><block wx:if="{{status=='loading'}}"><view class="loader-inner ball-clip-rotate"><view></view><text>加载中</text></view></block><block wx:else><view class="gg_loding_wusj">─── 没有更多数据了 ───</view></block></view></block><block wx:if="{{zanwsj}}"><view class="gg_zwsj"><view class="gg_zwsj_w"><image src="/static/images/wusj.png" mode="widthFix"></image><text>暂无数据</text></view></view></block><view class="aqjlViw"></view></view>