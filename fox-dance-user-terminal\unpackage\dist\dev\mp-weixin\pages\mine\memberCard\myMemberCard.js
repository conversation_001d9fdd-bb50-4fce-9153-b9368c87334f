(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/mine/memberCard/myMemberCard"],{

/***/ 170:
/*!**************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/main.js?{"page":"pages%2Fmine%2FmemberCard%2FmyMemberCard"} ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _myMemberCard = _interopRequireDefault(__webpack_require__(/*! ./pages/mine/memberCard/myMemberCard.vue */ 171));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_myMemberCard.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 171:
/*!*****************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue ***!
  \*****************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./myMemberCard.vue?vue&type=template&id=56167e78&scoped=true& */ 172);
/* harmony import */ var _myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./myMemberCard.vue?vue&type=script&lang=js& */ 174);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 73);

var renderjs




/* normalize component */

var component = Object(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "56167e78",
  null,
  false,
  _myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/mine/memberCard/myMemberCard.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 172:
/*!************************************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?vue&type=template&id=56167e78&scoped=true& ***!
  \************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCard.vue?vue&type=template&id=56167e78&scoped=true& */ 173);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_template_id_56167e78_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 173:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?vue&type=template&id=56167e78&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 174:
/*!******************************************************************************************************************!*\
  !*** D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../DevolopmentTools/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./myMemberCard.vue?vue&type=script&lang=js& */ 175);
/* harmony import */ var _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_DevolopmentTools_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_myMemberCard_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 175:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Project/fox/用户端/fox-dance-user-terminal/pages/mine/memberCard/myMemberCard.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _httpAchieve = __webpack_require__(/*! @/config/http.achieve.js */ 30);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    var _ref;
    return _ref = {
      isLogined: true,
      navBg: '',
      avatar: '',
      zsewmToggle: false,
      //专属二维码
      cardsLists: [],
      page: 1,
      //当前页数
      total_pages: 1,
      //总页数
      zanwsj: false,
      //是否有数据
      status: 'loading',
      //底部loding是否显示
      loadingText: '努力加载中',
      loadmoreText: '轻轻上拉',
      nomoreText: '实在没有了'
    }, (0, _defineProperty2.default)(_ref, "isLogined", false), (0, _defineProperty2.default)(_ref, "imgbaseUrl", ''), (0, _defineProperty2.default)(_ref, "array_lb", ['卡种类别', '次卡', '时长卡']), (0, _defineProperty2.default)(_ref, "index_lb", 0), (0, _defineProperty2.default)(_ref, "array_md", ['适用门店']), (0, _defineProperty2.default)(_ref, "array_md_cunc", []), (0, _defineProperty2.default)(_ref, "index_md", 0), (0, _defineProperty2.default)(_ref, "xzhyk", false), _ref;
  },
  onPageScroll: function onPageScroll(e) {
    var top = uni.upx2px(100);
    var scrollTop = e.scrollTop;
    var percent = scrollTop / top > 1 ? 1 : scrollTop / top;
    this.navBg = percent;
  },
  onLoad: function onLoad(option) {
    this.avatar = option.avatar;
    this.xzhyk = option.xzhyk ? true : false;
  },
  onShow: function onShow() {
    // this.isLogined = uni.getStorageSync('token') ? true : false;
    if (this.isLogined) {
      // this.userData();//个人信息
    }
    this.imgbaseUrl = this.$baseUrl;
    this.page = 1;
    this.cardsLists = [];
    this.cardsData(); //我的会员卡
    this.storeData(); //门店列表
    this.userData(); //个人信息
  },

  methods: {
    //选择会员卡
    xzhykTap: function xzhykTap(item) {
      uni.setStorageSync('selectCards', item);
      uni.navigateBack({});
    },
    //个人信息
    userData: function userData() {
      uni.showLoading({
        title: '加载中'
      });
      var that = this;
      (0, _httpAchieve.userInfoApi)({}).then(function (res) {
        console.log('个人中心', res);
        if (res.code == 1) {
          that.loding = true;
          that.avatar = res.data.avatar;
          uni.hideLoading();
        }
      });
    },
    //卡种类别
    bindPickerChange_lb: function bindPickerChange_lb(e) {
      console.log('picker发送选择改变，携带值为', e.detail.value);
      this.index_lb = e.detail.value;
      this.page = 1;
      this.cardsLists = [];
      this.cardsData(); //我的会员卡
    },

    //适用门店
    bindPickerChange_md: function bindPickerChange_md(e) {
      console.log('picker发送选择改变，携带值为', e.detail.value);
      this.index_md = e.detail.value;
      this.page = 1;
      this.cardsLists = [];
      this.cardsData(); //我的会员卡
    },
    //门店列表
    storeData: function storeData() {
      uni.showLoading({
        title: '加载中'
      });
      var that = this;
      (0, _httpAchieve.storeListsApi)({
        type: 1,
        longitude: uni.getStorageSync('postion').longitude,
        latitude: uni.getStorageSync('postion').latitude,
        limit: 9999
      }).then(function (res) {
        console.log('门店列表', res);
        if (res.code == 1) {
          var array_md = ['适用门店'];
          for (var i = 0; i < res.data.data.length; i++) {
            array_md.push(res.data.data[i].name);
          }
          that.array_md = array_md;
          that.array_md_cunc = res.data.data;
          uni.hideLoading();
        }
      });
    },
    //我的会员卡
    cardsData: function cardsData() {
      uni.showLoading({
        title: '加载中'
      });
      var that = this;
      (0, _httpAchieve.myCardApi)({
        page: that.page,
        size: 10,
        type: that.index_lb,
        store_id: that.index_md == 0 ? 0 : that.array_md_cunc[that.index_md - 1].id
      }).then(function (res) {
        console.log('我的会员卡2', res);
        if (res.code == 1) {
          var obj = res.data.data;
          for (var i = 0; i < obj.length; i++) {
            // console.log(obj[i].activation_time.split('-')[0] + '/' + obj[i].activation_time.split('-')[1] + '/' + obj[i].activation_time.split('-')[2],'sss')
            obj[i].activation_time = obj[i].activation_time.split('-')[0] + '/' + obj[i].activation_time.split('-')[1] + '/' + obj[i].activation_time.split('-')[2];
            obj[i].become_time = obj[i].become_time.split('-')[0] + '/' + obj[i].become_time.split('-')[1] + '/' + obj[i].become_time.split('-')[2];
          }
          that.cardsLists = that.cardsLists.concat(obj);
          that.zanwsj = that.cardsLists.length ? true : false;
          that.page++;
          // that.total_pages = Math.ceil(res.total/20);
          that.total_pages = res.data.last_page;
          if (that.page != 1) {
            if (that.total_pages >= that.page) {
              that.status = 'loading';
            } else {
              that.status = 'nomore';
            }
          }
          if (that.cardsLists.length == 0) {
            that.zanwsj = true;
          } else {
            that.zanwsj = false;
          }
          if (res.data.total * 1 <= 10) {
            that.status = 'nomore';
          }
          that.loding = true;
          uni.hideLoading();
          uni.stopPullDownRefresh();
        }
      });
    },
    onReachBottom: function onReachBottom() {
      // console.log('到底了');
      if (this.page != 1) {
        if (this.total_pages >= this.page) {
          this.cardsData();
        }
      }
    },
    onPullDownRefresh: function onPullDownRefresh() {
      // console.log('我被下拉了');
      this.page = 1;
      this.cardsLists = [];
      this.cardsData(); //我的会员卡
    },
    navTo: function navTo(url) {
      if (uni.getStorageSync('token') == '' || uni.getStorageSync('token') == undefined || !uni.getStorageSync('token')) {
        uni.showToast({
          icon: 'none',
          title: '请先登录'
        });
        setTimeout(function () {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }, 1000);
      } else {
        uni.navigateTo({
          url: url
        });
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ })

},[[170,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mine/memberCard/myMemberCard.js.map