<view class="feedback" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="fee_one fee_one_yi"><view class="fee_one_t">反馈类型</view><view class="fee_one_b"><block wx:for="{{feedLists}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tabTap',[index]]]]]}}" class="{{['fee_one_b_li',type==index?'fee_one_b_li_ac':'']}}" bindtap="__e">{{''+item+''}}</view></block></view></view><view class="fee_one fee_two"><view class="fee_one_t">反馈内容</view><textarea placeholder="问题描述的越详细，有助于我们更快的解决问题" placeholder-style="color: #999999;" data-event-opts="{{[['input',[['__set_model',['','contents','$event',[]]]]]]}}" value="{{contents}}" bindinput="__e"></textarea><view class="fee_two_tp"><block wx:for="{{uploadimgs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="fee_two_tp_li"><image class="fee_two_tp_li_tp" src="{{imgbaseUrl+item}}" mode="aspectFill"></image><image class="fee_two_tp_li_gb" src="/static/images/icon49-1.png" data-event-opts="{{[['tap',[['uploadImgDel',[index]]]]]}}" bindtap="__e"></image></view></block><view data-event-opts="{{[['tap',[['uploadImgTap',['$event']]]]]}}" class="fee_two_tp_li" bindtap="__e"><image class="fee_two_tp_li_tp" src="/static/images/icon50.png"></image></view></view></view><view class="fee_one fee_thr"><view class="fee_one_t">请留下您的联系方式</view><input type="text" placeholder="手机号/邮箱/QQ号" placeholder-style="color: #999999;" data-event-opts="{{[['input',[['__set_model',['','contact_mode','$event',[]]]]]]}}" value="{{contact_mode}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['fkSubTap',['$event']]]]]}}" class="lea_two_sub" bindtap="__e">提交</view><view class="aqjlViw"></view></view>