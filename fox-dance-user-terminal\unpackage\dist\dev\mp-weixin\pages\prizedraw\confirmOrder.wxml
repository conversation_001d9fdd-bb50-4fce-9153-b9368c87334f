<block wx:if="{{productxq.jpid}}"><view class="confirmOrder jpdh" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><block wx:if="{{shouAddr.area==''}}"><view data-event-opts="{{[['tap',[['goToAddr',['diancan']]]]]}}" class="qrdd_a" bindtap="__e"><image src="/static/images/icon33.png"></image>添加收货地址</view></block><block wx:else><view data-event-opts="{{[['tap',[['goToAddr',['diancan']]]]]}}" class="qrdd_b" bindtap="__e"><view class="qrdd_b_a"><image src="/static/images/icon38.png"></image>{{shouAddr.name+" "+shouAddr.phone}}</view><view class="qrdd_b_b">{{shouAddr.area+shouAddr.detail}}</view><image class="qrdd_b_jt" src="/static/images/index_shop_more.png"></image></view></block><view class="qrdd_c"><view class="qrdd_c_li"><image class="qrdd_c_li_l" src="{{imgbaseUrl+productxq.image}}" mode="aspectFill"></image><view class="qrdd_c_li_r"><view class="qrdd_c_li_r_a">{{productxq.name}}</view><view class="qrdd_c_li_r_c"><view></view><text>x1</text></view></view></view></view><view class="qrdd_d"><view>运费</view><input type="text" disabled="{{true}}" value="包邮" placeholder-style="color: #999999;"/></view><view class="qrdd_d qrdd_bz"><view>备注</view><input type="text" placeholder="如有特殊要求，请填写" placeholder-style="color: #999999;" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"/></view><view class="aqjlViw"></view><view class="peodex_foo"><view class="peodex_foo_l">共1件<text></text></view><view data-event-opts="{{[['tap',[['dhSubTap',['$event']]]]]}}" class="peodex_foo_r" bindtap="__e">兑换</view></view></view></block>