<block wx:if="{{productxq.id}}"><view class="confirmOrder edit_skxx" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="hbdhCon_a"><image class="hbdhCon_a_bj" src="/static/images/icon62.png"></image><view data-event-opts="{{[['tap',[['UploadImg',['$event']]]]]}}" class="hbdhCon_a_n" bindtap="__e"><image class="hbdhCon_a_l" src="{{payment_code==null||payment_code==''?'/static/images/icon70.png':imgbaseUrl+payment_code}}" mode="aspectFill"></image><view class="hbdhCon_a_r">{{payment_code==null||payment_code==''?'点击添加微信收款码':'点击更换微信收款码'}}</view></view></view><view class="hbdhCon_b"><view class="hbdhCon_b_t"><view>设为默认收款账户</view><text>每次兑换会默认使用该账户</text></view><u-switch vue-id="3406e95b-1" active-color="{{qjbutton}}" size="40" value="{{switchVal}}" data-event-opts="{{[['^change',[['change']]],['^input',[['__set_model',['','switchVal','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-switch></view><view class="aqjlViw"></view><view class="peodex_foo"><view data-event-opts="{{[['tap',[['bcSubTap',['$event']]]]]}}" class="peodex_foo_r" bindtap="__e">保存</view></view></view></block>