<block wx:if="{{goodsDetail.id}}"><view class="productDetails winningrecordxq" style="{{'--qjbutton-color:'+(qjbutton)+';'}}"><view class="pro_ban"><swiper class="swiper" circular="{{true}}" autoplay="{{true}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{goodsDetail.images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><image src="{{imgbaseUrl+item}}" mode="aspectFill"></image></swiper-item></block></swiper><view class="pro_ban_xf"><text>{{swiperIndex+1+"/"+$root.g0}}</text></view></view><view class="pro_one"><view class="pro_one_b"><view class="pro_one_b_t">{{goodsDetail.name}}</view></view></view><view class="pro_thr" style="margin-bottom:0;"><block wx:if="{{$root.g1>0}}"><view class="pro_thr_c"><block wx:for="{{goodsDetail.parameter}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pro_thr_c_li"><view>{{item.key}}</view><text>{{item.value}}</text></view></block></view></block></view><view class="pro_fou" style="{{(status!=0?'margin-bottom:20rpx':'')}}"><rich-text nodes="{{kcjsDetail}}"></rich-text></view><block wx:if="{{status==0}}"><view class="peode_foo"><view data-event-opts="{{[['tap',[['dhTap']]]]}}" bindtap="__e">立即兑换</view></view></block><view class="aqjlViw"></view></view></block>