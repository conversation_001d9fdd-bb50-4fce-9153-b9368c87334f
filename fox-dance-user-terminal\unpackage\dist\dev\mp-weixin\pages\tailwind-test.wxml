<view class="container data-v-4be4f1be"><view class="header data-v-4be4f1be"><text class="title data-v-4be4f1be">TailwindCSS 测试页面</text></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">基础样式测试</text><view class="test-item bg-red-500 text-white p-4 m-2 rounded-lg data-v-4be4f1be">红色背景，白色文字，内边距，外边距，圆角</view><view class="test-item bg-blue-500 text-white p-4 m-2 rounded-lg data-v-4be4f1be">蓝色背景，白色文字</view><view class="test-item bg-green-500 text-white p-4 m-2 rounded-lg data-v-4be4f1be">绿色背景，白色文字</view></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">布局测试</text><view class="flex justify-between items-center bg-gray-200 p-4 m-2 rounded-lg data-v-4be4f1be"><text class="text-sm data-v-4be4f1be">左侧文字</text><text class="text-lg font-bold data-v-4be4f1be">中间粗体</text><text class="text-xs data-v-4be4f1be">右侧小字</text></view><view class="grid grid-cols-2 gap-4 p-4 m-2 data-v-4be4f1be"><view class="bg-purple-500 text-white p-4 rounded text-center data-v-4be4f1be">网格1</view><view class="bg-pink-500 text-white p-4 rounded text-center data-v-4be4f1be">网格2</view><view class="bg-indigo-500 text-white p-4 rounded text-center data-v-4be4f1be">网格3</view><view class="bg-yellow-500 text-black p-4 rounded text-center data-v-4be4f1be">网格4</view></view></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">响应式测试</text><view class="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 bg-teal-500 text-white p-4 m-2 rounded-lg data-v-4be4f1be">响应式宽度测试</view></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">动画测试</text><view class="transform hover:scale-105 transition-transform duration-300 bg-orange-500 text-white p-4 m-2 rounded-lg data-v-4be4f1be">悬停缩放动画</view></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">rpx单位转换测试</text><view class="test-rpx data-v-4be4f1be"><text class="text-base data-v-4be4f1be">这应该是32rpx字体大小（1rem = 32rpx）</text></view><view class="w-32 h-32 bg-cyan-500 rounded-lg m-4 data-v-4be4f1be">128rpx x 128rpx 的正方形</view></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">按钮样式测试</text><view class="flex flex-col space-y-4 p-4 data-v-4be4f1be"><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded data-v-4be4f1be">主要按钮</button><button class="bg-transparent hover:bg-blue-500 text-blue-700 font-semibold hover:text-white py-2 px-4 border border-blue-500 hover:border-transparent rounded data-v-4be4f1be">次要按钮</button><button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-full data-v-4be4f1be">圆角按钮</button></view></view><view class="section data-v-4be4f1be"><text class="section-title data-v-4be4f1be">卡片样式测试</text><view class="max-w-sm rounded overflow-hidden shadow-lg bg-white m-4 data-v-4be4f1be"><view class="px-6 py-4 data-v-4be4f1be"><view class="font-bold text-xl mb-2 data-v-4be4f1be">卡片标题</view><text class="text-gray-700 text-base data-v-4be4f1be">这是一个使用TailwindCSS样式的卡片组件，包含标题、内容和标签。</text></view><view class="px-6 pt-4 pb-2 data-v-4be4f1be"><view class="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2 data-v-4be4f1be">#标签1</view><view class="inline-block bg-gray-200 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2 data-v-4be4f1be">#标签2</view></view></view></view></view>