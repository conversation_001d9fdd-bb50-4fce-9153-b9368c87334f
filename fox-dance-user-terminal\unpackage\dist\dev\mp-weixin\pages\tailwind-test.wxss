/* 这里可以添加一些自定义样式来补充TailwindCSS */
.container.data-v-4be4f1be {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header.data-v-4be4f1be {
  padding-top: 2rem;
  padding-bottom: 2rem;
  text-align: center;
}
.title.data-v-4be4f1be {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.section.data-v-4be4f1be {
  margin-left: 1rem;
  margin-right: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
  padding: 1rem;
}
.section-title.data-v-4be4f1be {
  margin-bottom: 1rem;
  display: block;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgba(31, 41, 55, var(--tw-text-opacity));
}
.test-item.data-v-4be4f1be {
  text-align: center;
}
.test-rpx.data-v-4be4f1be {
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
  padding: 1rem;
}

/* 测试自定义CSS与TailwindCSS的结合 */
.custom-gradient.data-v-4be4f1be {
  background: linear-gradient(45deg, #ff6b87, #ff8e53);
}

