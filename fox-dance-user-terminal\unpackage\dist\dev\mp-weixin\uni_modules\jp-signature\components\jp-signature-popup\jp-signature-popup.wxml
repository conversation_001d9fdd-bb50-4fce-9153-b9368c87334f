<view class="signature _div data-v-1756e4df"><block wx:if="{{!popup}}"><view class="inputs _div data-v-1756e4df"><view class="{{['label','_div','data-v-1756e4df',required?'labelqr':'']}}">{{label}}</view><view class="_div data-v-1756e4df"><block wx:if="{{value}}"><view class="images _div data-v-1756e4df"><image class="images data-v-1756e4df" mode="aspectFit" src="{{value}}" data-event-opts="{{[['tap',[['toImg',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{!readonly}}"><view data-event-opts="{{[['tap',[['toDeleteImg',['$event']]]]]}}" class="icons data-v-1756e4df" bindtap="__e"><view class="Deletes data-v-1756e4df">×</view></view></block></view></block><block wx:if="{{!value&&!readonly}}"><view data-event-opts="{{[['tap',[['toPop',['$event']]]]]}}" class="explain _div data-v-1756e4df" bindtap="__e">{{''+(placeholder?placeholder:'点击签名')+''}}</view></block></view></view></block><block wx:if="{{showPopup}}"><view data-event-opts="{{[['touchmove',[['moveHandle',['$event']]]]]}}" class="bottomPopup data-v-1756e4df" catchtouchmove="__e"><transition vue-id="58f38ff2-1" name="slide-up" appear="{{true}}" class="data-v-1756e4df" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-1756e4df"><view class="popup data-v-1756e4df"><block wx:if="{{!isHeight}}"><view class="hader _div data-v-1756e4df"><view data-event-opts="{{[['tap',[['toclear',['$event']]]]]}}" style="opacity:0;position:relative;z-index:-1;" bindtap="__e" class="_div data-v-1756e4df">取消</view><view class="text _div data-v-1756e4df">{{label}}</view><view data-event-opts="{{[['tap',[['toclear',['$event']]]]]}}" bindtap="__e" class="_div data-v-1756e4df">取消</view></view></block><view class="{{['_div','data-v-1756e4df',isHeight?'wgSignatureq':'wgSignature']}}"><block wx:if="{{isHeight}}"><view style="width:750rpx;height:100vh;" class="_div data-v-1756e4df"><jp-signature vue-id="{{('58f38ff2-2')+','+('58f38ff2-1')}}" beforeDelay="{{200}}" landscape="{{true}}" disableScroll="{{true}}" openSmooth="{{openSmooth}}" penSize="{{6}}" bounding-box="{{boundingBox}}" data-ref="signatureRef" class="data-v-1756e4df vue-ref" bind:__l="__l"></jp-signature></view></block><block wx:else><view style="width:750rpx;height:35vh;" class="_div data-v-1756e4df"><jp-signature vue-id="{{('58f38ff2-3')+','+('58f38ff2-1')}}" beforeDelay="{{200}}" disableScroll="{{true}}" openSmooth="{{openSmooth}}" bounding-box="{{boundingBox}}" penSize="{{3}}" data-ref="signatureRef" class="data-v-1756e4df vue-ref" bind:__l="__l"></jp-signature></view></block><block wx:if="{{!isHeight}}"><view class="appBut _div data-v-1756e4df"><view data-event-opts="{{[['tap',[['deleteImg',['$event']]]]]}}" class="buts _div data-v-1756e4df" bindtap="__e">清除</view><view data-event-opts="{{[['tap',[['Tomagnify',['$event']]]]]}}" class="buts _div data-v-1756e4df" bindtap="__e">全屏</view><view data-event-opts="{{[['tap',[['isEmpty',['$event']]]]]}}" class="buts _div data-v-1756e4df" style="background-color:#55aaff;color:#fff;" bindtap="__e">确定</view></view></block><block wx:else><view class="appBut _div data-v-1756e4df" style="height:80px;"><view data-event-opts="{{[['tap',[['undo',['$event']]]]]}}" class="butx _div data-v-1756e4df" bindtap="__e">撤销</view><view data-event-opts="{{[['tap',[['deleteImg',['$event']]]]]}}" class="butx _div data-v-1756e4df" bindtap="__e">清除</view><view data-event-opts="{{[['tap',[['Tomagnify',['$event']]]]]}}" class="butx _div data-v-1756e4df" style="background-color:#55aaff;color:#fff;" bindtap="__e">小屏</view><view data-event-opts="{{[['tap',[['toclear',['$event']]]]]}}" class="butx _div data-v-1756e4df" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['isEmpty',['$event']]]]]}}" class="butx _div data-v-1756e4df" style="background-color:#E59C36;color:#fff;" bindtap="__e">完成</view></view></block></view><view class="aqjlViw data-v-1756e4df"></view></view></view></transition></view></block></view>