@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.appBut.data-v-1756e4df {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  height: 50px;
  line-height: 35px;
}
.appBut .buts.data-v-1756e4df {
  color: #333;
  flex: 1;
  margin: 0 15px;
  background-color: #ccc;
  border-radius: 5px;
  height: 35px;
}
.appBut .butx.data-v-1756e4df {
  color: #333;
  flex: 1;
  margin: 0 5px;
  background-color: #ccc;
  border-radius: 5px;
  height: 35px;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.bottomPopup.data-v-1756e4df {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
}
.bottomPopup .popup-content.data-v-1756e4df {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
}
.bottomPopup .slide-up-enter-active.data-v-1756e4df,
.bottomPopup .slide-up-leave-active.data-v-1756e4df {
  transition: all .3s ease;
}
.bottomPopup .slide-up-enter.data-v-1756e4df,
.bottomPopup .slide-up-leave-to.data-v-1756e4df {
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
}
.signature .inputs.data-v-1756e4df {
  background-color: #fff;
  padding: 10px 16px;
}
.signature .inputs .label.data-v-1756e4df {
  line-height: 35px;
  position: relative;
}
.signature .inputs .labelqr.data-v-1756e4df:before {
  content: "*";
  color: #f00;
}
.signature .inputs .explain.data-v-1756e4df {
  width: 100%;
  background-color: #f1f1f1;
  text-align: center;
  line-height: 40px;
  border: 1px dotted #ccc;
  color: #999;
}
.signature .inputs .Deletes.data-v-1756e4df {
  border: 1px solid #f00;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  color: #f00;
  text-align: center;
  font-size: 30rpx;
  line-height: 30rpx;
}
.signature .images.data-v-1756e4df {
  width: 300rpx;
  height: 150rpx;
  position: relative;
}
.signature .images .icons.data-v-1756e4df {
  position: absolute;
  top: 0;
  right: 0;
}
.popup.data-v-1756e4df {
  background-color: #fff;
}
.hader.data-v-1756e4df {
  display: flex;
  justify-content: center;
  text-align: center;
  height: 45px;
  border-bottom: 1px solid #f5f5f5;
  align-items: center;
}
.hader ._div.data-v-1756e4df {
  text-align: center;
  width: 80px;
  color: #E59C36;
}
.hader .text.data-v-1756e4df {
  color: #333;
  flex: 1;
}

