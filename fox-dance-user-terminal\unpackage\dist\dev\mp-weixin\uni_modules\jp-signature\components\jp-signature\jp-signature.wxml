<block wx:if="{{show}}"><view data-ref="limeSignature" class="lime-signature vue-ref" style="{{$root.s0}}"><block wx:if="{{useCanvas2d}}"><canvas class="lime-signature__canvas" id="{{canvasId}}" type="2d" disableScroll="{{disableScroll}}" data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e"></canvas></block><block wx:else><canvas class="lime-signature__canvas" disableScroll="{{disableScroll}}" canvas-id="{{canvasId}}" id="{{canvasId}}" width="{{canvasWidth}}" height="{{canvasHeight}}" data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]],['mousedown',[['touchStart',['$event']]]],['mousemove',[['touchMove',['$event']]]],['mouseup',[['touchEnd',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindmousedown="__e" bindmousemove="__e" bindmouseup="__e"></canvas></block><canvas class="offscreen" style="{{('width:'+offscreenSize[0]+'px;height:'+offscreenSize[1]+'px')}}" canvas-id="offscreen" id="offscreen" width="{{offscreenSize[0]}}" height="{{offscreenSize[1]}}"></canvas><block wx:if="{{showMask}}"><view data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" class="mask" bindtouchstart="__e" catchtouchmove="__e" bindtouchend="__e"></view></block></view></block>