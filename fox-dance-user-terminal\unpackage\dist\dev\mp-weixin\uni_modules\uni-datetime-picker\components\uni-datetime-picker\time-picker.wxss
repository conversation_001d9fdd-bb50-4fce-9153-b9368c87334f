@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.uni-datetime-picker {
  /* width: 100%; */
}
.uni-datetime-picker-view {
  height: 130px;
  width: 270px;
  cursor: pointer;
}
.uni-datetime-picker-item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 14px;
}
.uni-datetime-picker-btn {
  margin-top: 60px;
  display: flex;
  cursor: pointer;
  flex-direction: row;
  justify-content: space-between;
}
.uni-datetime-picker-btn-text {
  font-size: 14px;
  color: #007aff;
}
.uni-datetime-picker-btn-group {
  display: flex;
  flex-direction: row;
}
.uni-datetime-picker-cancel {
  margin-right: 30px;
}
.uni-datetime-picker-mask {
  position: fixed;
  bottom: 0px;
  top: 0px;
  left: 0px;
  right: 0px;
  background-color: rgba(0, 0, 0, 0.4);
  transition-duration: 0.3s;
  z-index: 998;
}
.uni-datetime-picker-popup {
  border-radius: 8px;
  padding: 30px;
  width: 270px;
  background-color: #fff;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  transition-duration: 0.3s;
  z-index: 999;
}
.uni-datetime-picker-time {
  color: grey;
}
.uni-datetime-picker-column {
  height: 50px;
}
.uni-datetime-picker-timebox {
  border: 1px solid #E5E5E5;
  border-radius: 5px;
  padding: 7px 10px;
  box-sizing: border-box;
  cursor: pointer;
}
.uni-datetime-picker-timebox-pointer {
  cursor: pointer;
}
.uni-datetime-picker-disabled {
  opacity: 0.4;
}
.uni-datetime-picker-text {
  font-size: 14px;
  line-height: 50px;
}
.uni-datetime-picker-sign {
  position: absolute;
  top: 53px;
  /* 减掉 10px 的元素高度，兼容nvue */
  color: #999;
}
.sign-left {
  left: 86px;
}
.sign-right {
  right: 86px;
}
.sign-center {
  left: 135px;
}
.uni-datetime-picker__container-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
}
.time-hide-second {
  width: 180px;
}

