@charset "UTF-8";

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
.uni-noticebar.data-v-a1596656 {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 10px;
}
.uni-noticebar-close.data-v-a1596656 {
  margin-left: 8px;
  margin-right: 5px;
}
.uni-noticebar-icon.data-v-a1596656 {
  margin-right: 5px;
}
.uni-noticebar__content-wrapper.data-v-a1596656 {
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}
.uni-noticebar__content-wrapper--single.data-v-a1596656 {
  line-height: 18px;
}
.uni-noticebar__content-wrapper--single.data-v-a1596656,
.uni-noticebar__content-wrapper--scrollable.data-v-a1596656 {
  flex-direction: row;
}
.uni-noticebar__content-wrapper--scrollable.data-v-a1596656 {
  position: relative;
}
.uni-noticebar__content--scrollable.data-v-a1596656 {
  flex: 1;
  display: block;
  overflow: hidden;
}
.uni-noticebar__content--single.data-v-a1596656 {
  display: flex;
  flex: none;
  width: 100%;
  justify-content: center;
}
.uni-noticebar__content-text.data-v-a1596656 {
  font-size: 14px;
  line-height: 18px;
  word-break: break-all;
}
.uni-noticebar__content-text--single.data-v-a1596656 {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.uni-noticebar__content-text--scrollable.data-v-a1596656 {
  position: absolute;
  display: block;
  height: 18px;
  line-height: 18px;
  white-space: nowrap;
  padding-left: 100%;
  -webkit-animation: notice-data-v-a1596656 10s 0s linear infinite both;
          animation: notice-data-v-a1596656 10s 0s linear infinite both;
  -webkit-animation-play-state: paused;
          animation-play-state: paused;
}
.uni-noticebar__more.data-v-a1596656 {
  display: inline-flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  padding-left: 5px;
}
@-webkit-keyframes notice-data-v-a1596656 {
100% {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
}
}
@keyframes notice-data-v-a1596656 {
100% {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
}
}

