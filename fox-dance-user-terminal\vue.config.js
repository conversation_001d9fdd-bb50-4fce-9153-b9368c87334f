// vue.config.js - uni-app TailwindCSS 配置
const { UnifiedWebpackPluginV5 } = require('weapp-tailwindcss-webpack-plugin')

/**
 * @type {import('@vue/cli-service').ProjectOptions}
 */
const config = {
  configureWebpack: (config) => {
    // 添加 weapp-tailwindcss-webpack-plugin 插件
    config.plugins.push(
      new UnifiedWebpackPluginV5({
        appType: 'uni-app'
      })
    )
  },
  
  // 其他可能的配置
  transpileDependencies: [],
  
  // 如果需要的话，可以添加其他webpack配置
  chainWebpack: (config) => {
    // 可以在这里添加其他webpack链式配置
  }
}

module.exports = config
