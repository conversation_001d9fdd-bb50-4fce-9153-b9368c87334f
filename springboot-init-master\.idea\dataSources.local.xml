<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-241.19416.15">
    <data-source name="@localhost" uuid="73245ab5-7bd8-4242-a4cf-374b8097c583">
      <database-info product="MySQL" version="8.0.34" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.34" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="test" />
            <name qname="@" />
            <name qname="my_db" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="my_db@localhost" uuid="912cf3b9-0842-43ac-a30e-d59112f9c6f7">
      <database-info product="MySQL" version="8.0.34" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.34" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="@************" uuid="dd8ba793-bd06-44d6-958c-80f3483be866">
      <database-info product="MySQL" version="5.7.40-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.40" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>my_db_root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="my_db" />
            <name qname="@" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="@localhost [2]" uuid="ea209a86-a9e1-4022-bdfd-c413b38a4329">
      <database-info product="" version="" jdbc-version="" driver-name="" driver-version="" dbms="MYSQL" />
      <secret-storage>master_key</secret-storage>
      <schema-mapping />
    </data-source>
    <data-source name="@extend.foxdance.com.cn" uuid="0782b7a8-1b05-4586-90ff-6f787964536c">
      <database-info product="MySQL" version="5.7.44-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.44" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>vote_foxdance</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="vote_foxdance" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="@120.53.240.108" uuid="97269e7a-3002-48af-a7e9-1af540c6b387">
      <database-info product="MySQL" version="5.7.44-log" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="5.7.44" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>admin_foxdance_c</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="admin_foxdance_c" />
            <name qname="information_schema" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="@rm-2zeg25227e8xz8bjz4o.mysql.rds.aliyuncs.com" uuid="03d62e69-8a2e-4988-9366-ee50b8ce05f5">
      <database-info product="MySQL" version="8.0.36" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.36" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>admin_foxdance_c</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="admin_foxdance_c" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="<EMAIL>" uuid="a7e5f657-a31b-4faa-8e7b-71d0ef99bfbd">
      <database-info product="MySQL" version="8.0.36" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.36" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <user-name>admin_foxdance_c</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>