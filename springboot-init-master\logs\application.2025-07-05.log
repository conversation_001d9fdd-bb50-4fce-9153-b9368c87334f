2025-07-05 09:10:13.267 [HikariPool-9 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Thread starvation or clock leap detected (housekeeper delta=15h8m15s900ms369µs400ns).
2025-07-05 14:10:49.621 [HikariPool-9 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Thread starvation or clock leap detected (housekeeper delta=1h34m33s212ms680µs900ns).
2025-07-05 14:17:22.474 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: c16588c6-421f-4cd2-b7ab-aea34ddddd8f, path: /api/topic/list/page, ip: 0:0:0:0:0:0:0:1, params: [TopicQueryRequest(id=null, title=null, description=null, sortField=new)]
2025-07-05 14:17:22.489 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.TopicController - 🎯 获取话题列表请求（带缓存） - 查询条件: TopicQueryRequest(id=null, title=null, description=null, sortField=new)
2025-07-05 14:17:22.490 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.TopicController - 📋 话题列表查询参数详情 - 页码: 1, 页大小: 10, 搜索关键词: 'null'
2025-07-05 14:17:22.492 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TopicServiceImpl - 🔍 获取话题列表（带缓存） - 查询条件: TopicQueryRequest(id=null, title=null, description=null, sortField=new)
2025-07-05 14:17:22.493 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.service.impl.TopicServiceImpl - 🔍 构建查询条件 - id: null, title: 'null', description: 'null', sortField: new, sortOrder: descend
2025-07-05 14:17:22.493 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.service.impl.TopicServiceImpl - 🔍 查询条件构建完成 - 包含title搜索: false, 逻辑删除由@TableLogic自动处理
2025-07-05 14:17:22.493 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.utils.QueryWrapperDebugUtil - 🔍 QueryWrapper调试 - 话题搜索查询条件
2025-07-05 14:17:22.494 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.utils.QueryWrapperDebugUtil - 📝 生成的SQL条件: 
2025-07-05 14:17:22.494 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.utils.QueryWrapperDebugUtil - 📋 查询参数:
2025-07-05 14:17:22.494 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.utils.QueryWrapperDebugUtil -   - 无查询参数
2025-07-05 14:17:22.494 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.utils.QueryWrapperDebugUtil - 🎯 完整SQL语句: SELECT * FROM topics WHERE 
2025-07-05 14:17:22.619 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM topics WHERE is_delete = 0
2025-07-05 14:17:22.623 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.selectPage_mpCount - ==> Parameters: 
2025-07-05 14:17:22.675 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.selectPage_mpCount - <==      Total: 1
2025-07-05 14:17:22.677 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectPage - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE is_delete=0 ORDER BY create_time DESC LIMIT ?
2025-07-05 14:17:22.678 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectPage - ==> Parameters: 10(Long)
2025-07-05 14:17:22.722 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=["https://file.foxdance.com.cn/uploads/2025/06/28/1d3d3b88cfa448d7b4dc7d47635bc137.jpg"]
2025-07-05 14:17:22.723 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/1d3d3b88cfa448d7b4dc7d47635bc137.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/1d3d3b88cfa448d7b4dc7d47635bc137.jpg]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=[]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=[]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg"]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=["https://file.foxdance.com.cn/uploads/2025/06/24/930114a577f44296845fc00f1c17ebd1.jpg", "https://file.foxdance.com.cn/uploads/2025/06/24/9a620217e2bf4d669c29e1816b29a63a.png"]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/24/930114a577f44296845fc00f1c17ebd1.jpg", "https://file.foxdance.com.cn/uploads/2025/06/24/9a620217e2bf4d669c29e1816b29a63a.png"] -> [https://file.foxdance.com.cn/uploads/2025/06/24/930114a577f44296845fc00f1c17ebd1.jpg, https://file.foxdance.com.cn/uploads/2025/06/24/9a620217e2bf4d669c29e1816b29a63a.png]
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=null
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON字符串为空，返回null
2025-07-05 14:17:22.726 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectPage - <==      Total: 6
2025-07-05 14:17:22.727 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.service.impl.TopicServiceImpl - 📊 从数据库查询到话题列表 - 总数: 6, 当前页: 1, 返回数量: 6, 搜索关键词: null
2025-07-05 14:17:22.727 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.TopicController - 📊 缓存查询结果 - 总数: 6, 当前页: 1, 返回记录数: 6
2025-07-05 14:17:22.728 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-05 14:17:22.730 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 8(String)
2025-07-05 14:17:22.770 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-05 14:17:22.774 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-05 14:17:22.774 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 1(String), 8(String)
2025-07-05 14:17:22.853 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-05 14:17:22.853 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-05 14:17:22.853 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 7(String)
2025-07-05 14:17:22.893 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-05 14:17:22.893 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-05 14:17:22.893 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 0(String), 7(String)
2025-07-05 14:17:22.972 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-05 14:17:22.972 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-05 14:17:22.972 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 6(String)
2025-07-05 14:17:23.012 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-05 14:17:23.013 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-05 14:17:23.013 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 0(String), 6(String)
2025-07-05 14:17:23.092 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-05 14:17:23.092 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-05 14:17:23.092 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 5(String)
2025-07-05 14:17:23.131 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-05 14:17:23.132 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-05 14:17:23.132 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 0(String), 5(String)
2025-07-05 14:17:23.211 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-05 14:17:23.211 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-05 14:17:23.211 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 3(String)
2025-07-05 14:17:23.251 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-05 14:17:23.251 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-05 14:17:23.252 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 2(String), 3(String)
2025-07-05 14:17:23.330 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-05 14:17:23.330 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-05 14:17:23.331 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 2(String)
2025-07-05 14:17:23.373 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-05 14:17:23.373 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-05 14:17:23.373 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 3(String), 2(String)
2025-07-05 14:17:23.452 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-05 14:17:23.453 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-05 14:17:23.453 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 8(String)
2025-07-05 14:17:23.492 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=["https://file.foxdance.com.cn/uploads/2025/06/28/1d3d3b88cfa448d7b4dc7d47635bc137.jpg"]
2025-07-05 14:17:23.493 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/1d3d3b88cfa448d7b4dc7d47635bc137.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/1d3d3b88cfa448d7b4dc7d47635bc137.jpg]
2025-07-05 14:17:23.493 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-05 14:17:23.493 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-05 14:17:23.493 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 7(String)
2025-07-05 14:17:23.533 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=[]
2025-07-05 14:17:23.533 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-05 14:17:23.533 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-05 14:17:23.534 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-05 14:17:23.534 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 6(String)
2025-07-05 14:17:23.573 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=[]
2025-07-05 14:17:23.574 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: [] -> []
2025-07-05 14:17:23.574 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-05 14:17:23.575 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-05 14:17:23.575 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 5(String)
2025-07-05 14:17:23.615 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg"]
2025-07-05 14:17:23.615 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg"] -> [https://file.foxdance.com.cn/uploads/2025/06/28/c97442f4e2c94dafbdd8fa3b3da84693.jpg]
2025-07-05 14:17:23.615 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-05 14:17:23.615 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-05 14:17:23.616 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 3(String)
2025-07-05 14:17:23.655 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=["https://file.foxdance.com.cn/uploads/2025/06/24/930114a577f44296845fc00f1c17ebd1.jpg", "https://file.foxdance.com.cn/uploads/2025/06/24/9a620217e2bf4d669c29e1816b29a63a.png"]
2025-07-05 14:17:23.655 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/uploads/2025/06/24/930114a577f44296845fc00f1c17ebd1.jpg", "https://file.foxdance.com.cn/uploads/2025/06/24/9a620217e2bf4d669c29e1816b29a63a.png"] -> [https://file.foxdance.com.cn/uploads/2025/06/24/930114a577f44296845fc00f1c17ebd1.jpg, https://file.foxdance.com.cn/uploads/2025/06/24/9a620217e2bf4d669c29e1816b29a63a.png]
2025-07-05 14:17:23.655 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-05 14:17:23.656 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-05 14:17:23.656 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 2(String)
2025-07-05 14:17:23.696 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=topicImages, value=null
2025-07-05 14:17:23.696 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON字符串为空，返回null
2025-07-05 14:17:23.696 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-05 14:17:23.697 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.TopicController - 📊 VO处理后结果 - 总数: 6, 当前页: 1, 返回记录数: 6
2025-07-05 14:17:23.697 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: c16588c6-421f-4cd2-b7ab-aea34ddddd8f, cost: 1227ms
2025-07-05 16:41:56.644 [scheduling-1] INFO  c.y.s.s.i.VipMemberScheduleServiceImpl - 开始执行VIP会员状态更新定时任务
2025-07-05 16:41:56.686 [scheduling-1] DEBUG c.y.s.m.B.selectList - ==>  Preparing: SELECT id,out_trade_no,uid,number,surplus_frequency FROM ba_card_record WHERE (surplus_frequency > ?)
2025-07-05 16:41:56.688 [scheduling-1] DEBUG c.y.s.m.B.selectList - ==> Parameters: 0(String)
2025-07-05 16:41:57.174 [scheduling-1] DEBUG c.y.s.m.B.selectList - <==      Total: 27710
2025-07-05 16:41:57.179 [scheduling-1] INFO  c.y.s.s.i.VipMemberScheduleServiceImpl - 找到19602个应该是VIP的用户
2025-07-05 16:41:57.182 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectList - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE (is_member = ?)
2025-07-05 16:41:57.182 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectList - ==> Parameters: 1(String)
2025-07-05 16:41:57.418 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectList - <==      Total: 19424
2025-07-05 16:41:57.421 [scheduling-1] INFO  c.y.s.s.i.VipMemberScheduleServiceImpl - 找到19424个当前是VIP的用户
2025-07-05 16:41:57.424 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:57.424 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 1363(String)
2025-07-05 16:41:57.474 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:57.477 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:57.478 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "彬子"(String), "15251979289"(String), "/storage/default/20250523/tmp_85c2a83e90e1634c438fce11dd5e4e405a54f4fd647d730d12f.jpg"(String), 0(String), 1(String), 2(String), 1363(String)
2025-07-05 16:41:57.568 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:57.568 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:57.568 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 6855(String)
2025-07-05 16:41:57.613 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:57.613 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:57.614 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "蔡蔡"(String), "18566071041"(String), "/storage/default/20250628/tmp_b9b131bec718f27750d9bb19d4a460d4ff359058525a6638a8c.jpeg"(String), 11(String), 1(String), 2(String), 6855(String)
2025-07-05 16:41:57.704 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:57.705 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:57.705 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 7375(String)
2025-07-05 16:41:57.750 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:57.750 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:57.751 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "朵朵"(String), "14739553689"(String), "/storage/default/20250404/tmp_bb772c76a8a1bebdf142ce0a9de312d8a262d8fa54db532be90.jpeg"(String), 4(String), 1(String), 2(String), 7375(String)
2025-07-05 16:41:57.842 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:57.843 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:57.844 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 7791(String)
2025-07-05 16:41:57.890 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:57.890 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:57.890 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "石玲"(String), "19830709226"(String), ""(String), 0(String), 1(String), 2(String), 7791(String)
2025-07-05 16:41:57.980 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:57.980 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:57.981 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 8576(String)
2025-07-05 16:41:58.026 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.028 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.028 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "添添"(String), "13392642880"(String), "/storage/default/20250331/tmp_1eea86447bd8fee040109dc0d37b652f7c96b70375d7a5b67c4.jpg"(String), 2(String), 1(String), 2(String), 8576(String)
2025-07-05 16:41:58.119 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.120 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.120 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 15018(String)
2025-07-05 16:41:58.166 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.167 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.167 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "辣辣"(String), "15017747094"(String), ""(String), 3(String), 1(String), 2(String), 15018(String)
2025-07-05 16:41:58.263 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.263 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.263 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 15908(String)
2025-07-05 16:41:58.309 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.310 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.311 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "朱朱"(String), "14739675847"(String), "/storage/default/20250331/tmp_8cf71bd2715f24021f59de88e35e17b758b221bf104c67e1c40.jpg"(String), 11(String), 1(String), 2(String), 15908(String)
2025-07-05 16:41:58.402 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.403 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.404 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 18699(String)
2025-07-05 16:41:58.450 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.451 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.451 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "张张"(String), "13652822348"(String), ""(String), 0(String), 1(String), 2(String), 18699(String)
2025-07-05 16:41:58.544 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.544 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.544 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 20943(String)
2025-07-05 16:41:58.591 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.591 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.592 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "李飞飞"(String), "18291353195"(String), "/storage/default/20250331/tmp_0f22f4c6f6938e3039dbcffe18a58294e438231c4da8d663ca3.jpeg"(String), 2(String), 1(String), 2(String), 20943(String)
2025-07-05 16:41:58.681 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.681 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.681 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 21199(String)
2025-07-05 16:41:58.726 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.726 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.726 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "陶一丹"(String), "18780907028"(String), "/storage/default/20250401/tmp_97a9ae8b7bfcd5eff1ec938b6b52731197934cb21f8c98e890e.jpg"(String), 0(String), 1(String), 2(String), 21199(String)
2025-07-05 16:41:58.818 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.818 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.818 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 22941(String)
2025-07-05 16:41:58.863 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:58.863 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:58.863 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: "4.20体验"(String), "苏暮梨"(String), "13729758869"(String), ""(String), 1(String), 1(String), 2(String), 22941(String)
2025-07-05 16:41:58.953 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:58.953 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:58.953 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 22959(String)
2025-07-05 16:41:58.999 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 0
2025-07-05 16:41:59.000 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.000 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 23761(String)
2025-07-05 16:41:59.045 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.045 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.046 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "陈越"(String), "13353060771"(String), ""(String), 0(String), 1(String), 2(String), 23761(String)
2025-07-05 16:41:59.135 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:59.135 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.135 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 23783(String)
2025-07-05 16:41:59.181 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.182 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.183 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "何巧婷"(String), "18218325458"(String), ""(String), 0(String), 1(String), 2(String), 23783(String)
2025-07-05 16:41:59.273 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:59.274 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.274 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24158(String)
2025-07-05 16:41:59.320 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 0
2025-07-05 16:41:59.320 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.320 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24425(String)
2025-07-05 16:41:59.369 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.370 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.371 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "田伟婷"(String), "18650800335"(String), ""(String), 0(String), 1(String), 2(String), 24425(String)
2025-07-05 16:41:59.461 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:59.461 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.461 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24561(String)
2025-07-05 16:41:59.507 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.507 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.508 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "嗯嗯"(String), "13560493218"(String), ""(String), 0(String), 1(String), 2(String), 24561(String)
2025-07-05 16:41:59.597 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:59.597 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.597 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24641(String)
2025-07-05 16:41:59.644 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.647 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.649 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "寻寻"(String), "18377120273"(String), ""(String), 0(String), 1(String), 2(String), 24641(String)
2025-07-05 16:41:59.740 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:59.741 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.741 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 25172(String)
2025-07-05 16:41:59.785 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.786 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.786 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "李如娇"(String), "13421251049"(String), "/storage/default/20250703/tmp_a76dc880dee7b52e8d0799207d46a1361581a3acb5c740b2957.jpg"(String), 0(String), 1(String), 2(String), 25172(String)
2025-07-05 16:41:59.877 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:41:59.878 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:41:59.878 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 25662(String)
2025-07-05 16:41:59.924 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:41:59.926 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:41:59.927 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "卷卷"(String), "17688460735"(String), ""(String), 0(String), 1(String), 2(String), 25662(String)
2025-07-05 16:42:00.020 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:42:00.020 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:42:00.020 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 25846(String)
2025-07-05 16:42:00.066 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:42:00.066 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:42:00.066 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: "熊浩丽 已体验"(String), "熊浩丽"(String), "15170354151"(String), ""(String), 0(String), 1(String), 2(String), 25846(String)
2025-07-05 16:42:00.157 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:42:00.157 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:42:00.158 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 25923(String)
2025-07-05 16:42:00.204 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:42:00.206 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:42:00.207 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "张张"(String), "18719003172"(String), ""(String), 0(String), 1(String), 1(String), 25923(String)
2025-07-05 16:42:00.296 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:42:00.296 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:42:00.296 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 26316(String)
2025-07-05 16:42:00.342 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:42:00.342 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:42:00.342 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: ""(String), "怀山"(String), "18535243918"(String), ""(String), 0(String), 1(String), 1(String), 26316(String)
2025-07-05 16:42:00.434 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:42:00.434 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:42:00.435 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 26343(String)
2025-07-05 16:42:00.479 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:42:00.479 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:42:00.480 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: "小涵 已交定金"(String), "小涵"(String), "18938301316"(String), "/storage/default/20250704/tmp_d94b81a71dda789d8f8ac0f24a8f2fe8ba0972cd72854d28142.jpg"(String), 0(String), 1(String), 2(String), 26343(String)
2025-07-05 16:42:00.570 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - <==    Updates: 1
2025-07-05 16:42:00.570 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-05 16:42:00.570 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 26374(String)
2025-07-05 16:42:00.616 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-05 16:42:00.617 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==>  Preparing: UPDATE ba_user SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=? WHERE id=?
2025-07-05 16:42:00.618 [scheduling-1] DEBUG c.y.s.mapper.BaUserMapper.updateById - ==> Parameters: "\"\""(String), "\"邹杨风吟\""(String), "\"17728167624\""(String), "\"\""(String), 0(String), 1(String), 2(String), 26374(String)
2025-07-05 16:42:00.815 [scheduling-1] ERROR c.y.s.s.i.VipMemberScheduleServiceImpl - VIP会员状态更新定时任务执行失败
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
### The error may exist in com/yupi/springbootinit/mapper/BaUserMapper.java (best guess)
### The error may involve com.yupi.springbootinit.mapper.BaUserMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE ba_user  SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=?  WHERE id=?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
; Data truncation: Data too long for column 'mobile' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy18/jdk.proxy18.$Proxy426.updateById(Unknown Source)
	at com.yupi.springbootinit.service.impl.VipMemberScheduleServiceImpl.updateVipMemberStatus(VipMemberScheduleServiceImpl.java:88)
	at com.yupi.springbootinit.service.impl.VipMemberScheduleServiceImpl$$FastClassBySpringCGLIB$$36e893e9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.yupi.springbootinit.service.impl.VipMemberScheduleServiceImpl$$EnhancerBySpringCGLIB$$ce84cedd.updateVipMemberStatus(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor110.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy4/jdk.proxy4.$Proxy164.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at jdk.internal.reflect.GeneratedMethodAccessor407.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at jdk.proxy2/jdk.proxy2.$Proxy162.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor406.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at jdk.proxy2/jdk.proxy2.$Proxy161.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at jdk.internal.reflect.GeneratedMethodAccessor405.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 32 common frames omitted
2025-07-05 16:42:00.919 [scheduling-1] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
### The error may exist in com/yupi/springbootinit/mapper/BaUserMapper.java (best guess)
### The error may involve com.yupi.springbootinit.mapper.BaUserMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE ba_user  SET username=?, nickname=?, mobile=?, avatar=?, level=?, is_member=?, remaining_votes=?  WHERE id=?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
; Data truncation: Data too long for column 'mobile' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy18/jdk.proxy18.$Proxy426.updateById(Unknown Source)
	at com.yupi.springbootinit.service.impl.VipMemberScheduleServiceImpl.updateVipMemberStatus(VipMemberScheduleServiceImpl.java:88)
	at com.yupi.springbootinit.service.impl.VipMemberScheduleServiceImpl$$FastClassBySpringCGLIB$$36e893e9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.yupi.springbootinit.service.impl.VipMemberScheduleServiceImpl$$EnhancerBySpringCGLIB$$ce84cedd.updateVipMemberStatus(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'mobile' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor110.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy4/jdk.proxy4.$Proxy164.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at jdk.internal.reflect.GeneratedMethodAccessor407.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at jdk.proxy2/jdk.proxy2.$Proxy162.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor406.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at jdk.proxy2/jdk.proxy2.$Proxy161.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at jdk.internal.reflect.GeneratedMethodAccessor405.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 32 common frames omitted
