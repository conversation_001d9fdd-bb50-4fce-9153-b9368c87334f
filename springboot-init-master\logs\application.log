2025-07-08 09:09:08.719 [HikariPool-9 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Thread starvation or clock leap detected (housekeeper delta=2d15h8m15s504ms921µs300ns).
2025-07-08 09:42:14.832 [HikariPool-9 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-9 - Thread starvation or clock leap detected (housekeeper delta=3m5s732ms965µs600ns).
2025-07-08 09:50:37.171 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Shutdown initiated...
2025-07-08 09:50:37.173 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-9 - Shutdown completed.
2025-07-08 09:50:43.538 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 09:50:43.539 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 09:50:43.569 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-08 09:50:43.569 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-08 09:50:44.771 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.781 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 09:50:44.782 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 09:50:45.348 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 09:50:45.353 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 09:50:45.354 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 09:50:45.354 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 09:50:45.398 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 09:50:45.398 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1829 ms
2025-07-08 09:50:45.623 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@82aad7f'
2025-07-08 09:50:45.730 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 09:50:45.752 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 09:50:45.773 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 09:50:45.794 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 09:50:45.813 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 09:50:45.825 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 09:50:45.830 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 09:50:45.834 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 09:50:45.835 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 09:50:45.836 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 09:50:45.837 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 09:50:45.837 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 09:50:45.856 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 09:50:45.879 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 09:50:45.895 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 09:50:45.913 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 09:50:46.007 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 09:50:46.012 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 09:50:46.013 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 09:50:46.069 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 09:50:46.080 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 09:50:46.303 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 09:50:46.579 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 09:50:46.657 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 09:50:46.723 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 09:50:46.816 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 09:50:46.876 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 09:50:46.886 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 09:50:46.886 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 09:50:46.887 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 09:50:46.910 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 09:50:46.978 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 09:50:47.053 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.808 seconds (JVM running for 7.861)
2025-07-08 10:59:56.971 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 10:59:56.974 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.061 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 10:59:58.062 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 10:59:58.260 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 10:59:58.261 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 10:59:58.261 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 10:59:58.261 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 10:59:58.277 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 10:59:58.278 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1293 ms
2025-07-08 10:59:58.333 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@79077791'
2025-07-08 10:59:58.365 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 10:59:58.384 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 10:59:58.408 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 10:59:58.423 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 10:59:58.439 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 10:59:58.444 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 10:59:58.446 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 10:59:58.449 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 10:59:58.450 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 10:59:58.451 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 10:59:58.451 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 10:59:58.452 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 10:59:58.477 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 10:59:58.501 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 10:59:58.528 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 10:59:58.554 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 10:59:58.630 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 10:59:58.634 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 10:59:58.636 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 10:59:58.689 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 10:59:58.697 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 10:59:58.801 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 10:59:59.122 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 10:59:59.196 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 10:59:59.256 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 10:59:59.315 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 10:59:59.366 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 10:59:59.375 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 10:59:59.376 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 10:59:59.376 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 10:59:59.391 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 10:59:59.439 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 10:59:59.520 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.676 seconds (JVM running for 4160.328)
2025-07-08 10:59:59.522 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:00:10.057 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:00:10.058 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.213 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.214 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.214 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.214 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.214 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.214 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:10.214 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:00:10.270 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:00:10.270 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:00:10.271 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:00:10.271 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:00:10.283 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:00:10.283 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 224 ms
2025-07-08 11:00:10.325 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1f8fd0e9'
2025-07-08 11:00:10.334 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:00:10.342 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:00:10.349 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:00:10.356 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:00:10.368 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:00:10.371 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:00:10.372 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:00:10.376 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:00:10.376 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:00:10.377 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:00:10.377 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:00:10.377 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:00:10.386 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:00:10.393 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:00:10.400 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:00:10.407 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:00:10.455 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:00:10.458 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:00:10.459 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:00:10.501 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:00:10.507 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:00:10.560 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:00:10.748 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:00:10.794 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:00:10.878 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:00:10.916 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:00:10.965 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:00:10.971 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:00:10.972 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:00:10.972 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:00:10.979 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:00:10.997 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:00:11.049 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.011 seconds (JVM running for 4171.857)
2025-07-08 11:00:11.050 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:00:25.685 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:00:25.685 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:25.816 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:00:25.884 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:00:25.884 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:00:25.885 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:00:25.885 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:00:25.898 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:00:25.898 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 212 ms
2025-07-08 11:00:25.966 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@40449365'
2025-07-08 11:00:25.976 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:00:25.984 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:00:25.991 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:00:25.999 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:00:26.006 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:00:26.011 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:00:26.012 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:00:26.014 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:00:26.015 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:00:26.015 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:00:26.015 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:00:26.016 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:00:26.022 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:00:26.030 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:00:26.036 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:00:26.045 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:00:26.100 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:00:26.105 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:00:26.106 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:00:26.193 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:00:26.202 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:00:26.299 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:00:26.521 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:00:26.586 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:00:26.632 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:00:26.668 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:00:26.728 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:00:26.729 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:00:26.730 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:00:26.730 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:00:26.738 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:00:26.761 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:00:26.822 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.154 seconds (JVM running for 4187.63)
2025-07-08 11:00:26.823 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:00:43.512 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:00:43.512 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:00:43.661 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:00:43.713 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:00:43.714 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:00:43.714 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:00:43.714 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:00:43.724 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:00:43.724 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 211 ms
2025-07-08 11:00:43.767 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1afb26aa'
2025-07-08 11:00:43.774 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:00:43.783 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:00:43.789 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:00:43.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:00:43.804 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:00:43.808 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:00:43.809 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:00:43.811 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:00:43.812 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:00:43.813 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:00:43.813 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:00:43.813 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:00:43.819 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:00:43.825 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:00:43.830 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:00:43.836 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:00:43.885 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:00:43.887 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:00:43.888 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:00:43.928 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:00:43.933 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:00:43.987 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:00:44.169 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:00:44.210 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:00:44.238 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:00:44.262 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:00:44.295 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:00:44.297 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:00:44.297 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:00:44.297 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:00:44.307 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:00:44.323 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:00:44.362 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 0.866 seconds (JVM running for 4205.17)
2025-07-08 11:00:44.363 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:01:05.156 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:01:05.156 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:01:05.393 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:01:05.480 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:01:05.480 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:01:05.480 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:01:05.480 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:01:05.493 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:01:05.493 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 334 ms
2025-07-08 11:01:05.548 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@767bda55'
2025-07-08 11:01:05.562 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:01:05.569 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:01:05.576 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:01:05.585 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:01:05.592 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:01:05.596 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:01:05.596 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:01:05.599 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:01:05.599 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:01:05.600 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:01:05.600 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:01:05.600 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:01:05.610 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:01:05.617 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:01:05.625 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:01:05.633 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:01:05.683 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:01:05.685 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:01:05.686 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:01:05.727 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:01:05.732 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:01:05.784 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:01:05.977 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:01:06.016 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:01:06.042 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:01:06.064 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:01:06.096 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:01:06.097 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:01:06.098 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:01:06.098 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:01:06.108 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:01:06.122 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:01:06.172 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.036 seconds (JVM running for 4226.98)
2025-07-08 11:01:06.173 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:07:34.090 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:07:34.090 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:34.411 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:07:34.529 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:07:34.530 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:07:34.530 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:07:34.530 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:07:34.547 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:07:34.547 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 454 ms
2025-07-08 11:07:34.592 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@2ced37d2'
2025-07-08 11:07:34.601 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:07:34.608 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:07:34.622 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:07:34.646 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:07:34.654 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:07:34.659 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:07:34.660 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:07:34.664 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:07:34.664 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:07:34.665 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:07:34.665 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:07:34.665 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:07:34.672 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:07:34.682 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:07:34.689 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:07:34.697 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:07:34.749 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:07:34.752 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:07:34.753 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:07:34.799 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:07:34.805 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:07:34.929 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:07:35.129 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:07:35.178 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:07:35.237 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:07:35.265 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:07:35.299 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:07:35.302 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:07:35.302 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:07:35.303 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:07:35.308 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:07:35.324 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:07:35.366 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.333 seconds (JVM running for 4616.174)
2025-07-08 11:07:35.367 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:07:54.216 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 12372 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:07:54.216 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:07:54.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:07:54.613 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:07:54.614 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:07:54.614 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:07:54.614 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:07:54.634 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:07:54.635 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 417 ms
2025-07-08 11:07:54.715 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4eb91d17'
2025-07-08 11:07:54.731 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:07:54.743 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:07:54.759 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:07:54.773 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:07:54.787 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:07:54.792 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:07:54.793 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:07:54.798 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:07:54.799 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:07:54.800 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:07:54.801 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:07:54.801 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:07:54.813 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:07:54.825 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:07:54.836 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:07:54.848 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:07:54.945 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:07:54.951 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:07:54.953 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:07:55.052 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:07:55.064 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:07:55.164 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:07:55.563 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:07:55.677 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:07:55.758 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:07:55.837 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:07:55.927 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:07:55.930 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:07:55.931 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:07:55.931 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:07:55.947 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:07:56.006 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:07:56.097 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.908 seconds (JVM running for 4636.905)
2025-07-08 11:07:56.099 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-08 11:28:16.154 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 11:28:16.165 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-08 11:28:16.267 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 102 ms
2025-07-08 11:28:16.538 [http-nio-0.0.0.0-8101-exec-1] WARN  o.a.c.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [190] milliseconds.
2025-07-08 11:28:50.208 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17252 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-08 11:28:50.214 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-08 11:28:50.286 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-08 11:28:50.286 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-08 11:28:51.755 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-08 11:28:51.770 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.770 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.770 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.770 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.770 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-08 11:28:51.771 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-08 11:28:52.618 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-08 11:28:52.627 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:28:52.628 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 11:28:52.628 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-08 11:28:52.690 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-08 11:28:52.691 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2404 ms
2025-07-08 11:28:52.940 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@315df117'
2025-07-08 11:28:53.070 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-08 11:28:53.088 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-08 11:28:53.112 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-08 11:28:53.130 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-08 11:28:53.146 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-08 11:28:53.157 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-08 11:28:53.164 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-08 11:28:53.169 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-08 11:28:53.171 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-08 11:28:53.172 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-08 11:28:53.173 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-08 11:28:53.176 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-08 11:28:53.198 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-08 11:28:53.214 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-08 11:28:53.226 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-08 11:28:53.238 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-08 11:28:53.372 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-08 11:28:53.378 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-08 11:28:53.380 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-08 11:28:53.467 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-08 11:28:53.478 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-08 11:28:53.766 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-08 11:28:54.186 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-08 11:28:54.294 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-08 11:28:54.385 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 11:28:54.498 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-08 11:28:54.592 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-08 11:28:54.605 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-08 11:28:54.606 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-08 11:28:54.608 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-08 11:28:54.635 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-08 11:28:54.736 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-08 11:28:54.844 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 5.013 seconds (JVM running for 5.765)
2025-07-08 11:31:01.731 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 11:31:01.731 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-08 11:31:01.733 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-08 11:31:01.784 [http-nio-0.0.0.0-8101-exec-1] ERROR c.y.s.e.GlobalExceptionHandler - RuntimeException
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:473)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1264)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1046)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-08 11:31:01.826 [http-nio-0.0.0.0-8101-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.]
2025-07-08 11:31:36.371 [http-nio-0.0.0.0-8101-exec-3] ERROR c.y.s.e.GlobalExceptionHandler - RuntimeException
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:473)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1264)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1046)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-08 11:31:36.375 [http-nio-0.0.0.0-8101-exec-3] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.]
2025-07-08 11:36:09.089 [http-nio-0.0.0.0-8101-exec-10] ERROR c.y.s.e.GlobalExceptionHandler - RuntimeException
java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.
	at org.springframework.web.cors.CorsConfiguration.validateAllowCredentials(CorsConfiguration.java:473)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:532)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1264)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1046)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-08 11:36:09.090 [http-nio-0.0.0.0-8101-exec-10] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Resolved [java.lang.IllegalArgumentException: When allowCredentials is true, allowedOrigins cannot contain the special value "*" since that cannot be set on the "Access-Control-Allow-Origin" response header. To allow credentials to a set of origins, list them explicitly or consider using "allowedOriginPatterns" instead.]
