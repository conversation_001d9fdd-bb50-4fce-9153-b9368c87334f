2025-07-09 09:39:28.910 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 1948 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:39:28.915 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:39:30.053 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. <PERSON> already defined with the same name!
2025-07-09 09:39:30.053 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUser<PERSON><PERSON>per' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. <PERSON> already defined with the same name!
2025-07-09 09:39:30.053 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.053 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:30.054 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:39:30.273 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:39:30.274 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:39:30.274 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:39:30.274 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:39:30.303 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:39:30.303 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1384 ms
2025-07-09 09:39:30.402 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@414c269e'
2025-07-09 09:39:30.453 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:39:30.490 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:39:30.627 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:39:30.660 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:39:30.680 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:39:30.691 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:39:30.694 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:39:30.698 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:39:30.699 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:39:30.699 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:39:30.699 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:39:30.700 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:39:30.717 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:39:30.736 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:39:30.754 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:39:30.773 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:39:30.868 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:39:30.875 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:39:30.876 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:39:30.983 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:39:30.997 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:39:31.148 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:39:31.534 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:39:31.639 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:39:31.710 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:39:31.773 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:39:31.851 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:39:31.856 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:39:31.857 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:39:31.858 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:39:31.876 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:39:31.919 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:39:32.009 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.16 seconds (JVM running for 59891.398)
2025-07-09 09:39:32.010 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 09:39:45.879 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 1948 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:39:45.879 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:39:46.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:39:46.428 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:39:46.428 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:39:46.429 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:39:46.429 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:39:46.448 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:39:46.448 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 566 ms
2025-07-09 09:39:46.517 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@589d6582'
2025-07-09 09:39:46.545 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:39:46.574 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:39:46.595 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:39:46.613 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:39:46.625 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:39:46.630 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:39:46.632 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:39:46.636 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:39:46.637 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:39:46.639 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:39:46.639 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:39:46.639 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:39:46.651 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:39:46.666 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:39:46.678 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:39:46.692 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:39:46.980 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:39:46.987 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:39:46.989 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:39:47.078 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:39:47.089 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:39:47.213 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:39:47.663 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:39:47.762 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:39:47.827 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:39:47.881 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:39:47.959 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:39:47.962 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:39:47.962 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:39:47.962 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:39:47.976 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:39:48.014 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:39:48.098 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.265 seconds (JVM running for 59907.487)
2025-07-09 09:39:48.099 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 09:40:04.927 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 1948 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:40:04.928 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.931 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:40:05.932 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:40:06.045 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:40:06.046 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:40:06.046 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:40:06.046 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:40:06.064 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:40:06.064 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1134 ms
2025-07-09 09:40:06.147 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@28f49907'
2025-07-09 09:40:06.164 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:40:06.177 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:40:06.187 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:40:06.272 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:40:06.286 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:40:06.291 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:40:06.292 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:40:06.297 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:40:06.298 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:40:06.299 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:40:06.300 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:40:06.300 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:40:06.314 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:40:06.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:40:06.340 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:40:06.355 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:40:06.453 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:40:06.460 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:40:06.461 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:40:06.555 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:40:06.567 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:40:06.665 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:40:07.035 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:40:07.125 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:40:07.189 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:40:07.245 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:40:07.329 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:40:07.332 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:40:07.332 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:40:07.333 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:40:07.348 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:40:07.397 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:40:07.477 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.591 seconds (JVM running for 59926.866)
2025-07-09 09:40:07.478 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 09:42:47.356 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:42:47.357 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:42:47.386 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-09 09:42:47.387 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-09 09:42:48.030 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:42:48.045 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:42:48.801 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:42:48.807 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:42:48.808 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:42:48.808 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:42:48.866 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:42:48.866 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1479 ms
2025-07-09 09:42:49.040 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@8d958c3'
2025-07-09 09:42:49.985 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:42:50.081 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:42:50.120 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:42:50.311 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:42:50.434 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:42:50.486 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:42:50.550 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:42:50.554 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:42:50.556 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:42:50.556 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:42:50.557 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:42:50.557 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:42:50.570 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:42:50.590 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:42:50.678 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:42:50.806 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:42:53.362 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:42:53.372 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:42:53.374 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:42:53.457 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:42:53.466 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:42:53.696 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:42:54.001 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:42:54.101 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:42:54.313 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:42:54.403 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:42:54.480 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:42:54.490 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:42:54.491 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:42:54.492 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:42:54.527 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:42:54.603 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:42:54.705 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 7.725 seconds (JVM running for 8.292)
2025-07-09 09:43:25.505 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:43:25.506 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:43:25.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:43:25.729 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:43:25.797 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:43:25.798 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:43:25.799 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:43:25.799 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:43:25.815 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:43:25.815 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 307 ms
2025-07-09 09:43:25.857 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@573490c3'
2025-07-09 09:43:25.869 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:43:25.881 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:43:25.902 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:43:25.918 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:43:25.929 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:43:25.934 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:43:25.935 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:43:25.939 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:43:25.941 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:43:25.942 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:43:25.942 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:43:25.943 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:43:25.953 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:43:25.965 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:43:25.976 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:43:25.985 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:43:26.044 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:43:26.047 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:43:26.048 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:43:26.110 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:43:26.115 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:43:26.186 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:43:26.409 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:43:26.456 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:43:26.490 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:43:26.515 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:43:26.555 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:43:26.557 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:43:26.557 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:43:26.558 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:43:26.565 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:43:26.590 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:43:26.657 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.19 seconds (JVM running for 40.244)
2025-07-09 09:43:26.658 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 09:52:57.493 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:52:57.493 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:52:57.640 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:52:57.698 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:52:57.699 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:52:57.699 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:52:57.699 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:52:57.711 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:52:57.711 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 216 ms
2025-07-09 09:52:57.752 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@cebe1d3'
2025-07-09 09:52:57.760 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:52:57.765 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:52:57.772 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:52:57.778 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:52:57.785 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:52:57.787 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:52:57.788 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:52:57.791 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:52:57.794 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:52:57.795 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:52:57.795 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:52:57.795 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:52:57.802 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:52:57.808 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:52:57.813 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:52:57.820 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:52:57.864 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:52:57.866 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:52:57.867 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:52:57.906 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:52:57.912 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:52:57.995 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:52:58.174 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:52:58.220 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:52:58.258 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:52:58.284 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:52:58.319 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:52:58.320 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:52:58.321 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:52:58.321 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:52:58.326 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:52:58.347 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:52:58.426 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 0.95 seconds (JVM running for 612.013)
2025-07-09 09:52:58.427 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 09:53:18.339 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:53:18.339 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:53:18.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:18.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:53:18.600 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:53:18.600 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:53:18.601 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:53:18.601 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:53:18.616 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:53:18.616 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 275 ms
2025-07-09 09:53:18.663 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@44b35f75'
2025-07-09 09:53:18.675 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:53:18.681 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:53:18.688 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:53:18.697 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:53:18.705 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:53:18.709 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:53:18.710 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:53:18.712 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:53:18.713 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:53:18.714 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:53:18.714 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:53:18.714 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:53:18.721 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:53:18.727 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:53:18.733 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:53:18.743 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:53:18.788 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'commentController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.yupi.springbootinit.service.CommentService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup="", name="", description="", authenticationType=CONTAINER, type=java.lang.Object.class, mappedName="")}
2025-07-09 09:53:18.789 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-09 09:53:18.794 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-09 09:53:18.811 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'com.yupi.springbootinit.service.CommentService' that could not be found.


Action:

Consider defining a bean of type 'com.yupi.springbootinit.service.CommentService' in your configuration.

2025-07-09 09:53:22.327 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 09:53:22.327 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 09:53:22.630 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 09:53:22.631 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 09:53:22.729 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 09:53:22.729 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:53:22.729 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:53:22.730 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 09:53:22.745 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:53:22.745 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 416 ms
2025-07-09 09:53:22.809 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@3c326326'
2025-07-09 09:53:22.947 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 09:53:22.957 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 09:53:22.968 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 09:53:22.981 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 09:53:22.997 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 09:53:23.002 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 09:53:23.003 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 09:53:23.007 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 09:53:23.008 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 09:53:23.009 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 09:53:23.009 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 09:53:23.009 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 09:53:23.019 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 09:53:23.029 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 09:53:23.038 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 09:53:23.049 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 09:53:23.129 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 09:53:23.135 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 09:53:23.136 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 09:53:23.225 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 09:53:23.235 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 09:53:23.320 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 09:53:23.687 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 09:53:23.772 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 09:53:23.832 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 09:53:23.878 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 09:53:23.944 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 09:53:23.947 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 09:53:23.947 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 09:53:23.948 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 09:53:23.963 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 09:53:24.005 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 09:53:24.096 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 1.799 seconds (JVM running for 637.683)
2025-07-09 09:53:24.097 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 10:00:42.031 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 10:00:42.032 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 10:00:42.034 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 10:00:42.384 [http-nio-0.0.0.0-8101-exec-1] WARN  o.a.c.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [184] milliseconds.
2025-07-09 10:00:42.503 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: f3cfbdb6-4df1-4aaf-92bb-37cccd6c867f, path: /api/topic/list/page, ip: 0:0:0:0:0:0:0:1, params: [TopicQueryRequest(id=null, title=null, description=null, sortField=new)]
2025-07-09 10:00:42.510 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TopicController - 🎯 获取话题列表请求（带缓存） - 查询条件: TopicQueryRequest(id=null, title=null, description=null, sortField=new)
2025-07-09 10:00:42.511 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TopicController - 📋 话题列表查询参数详情 - 页码: 1, 页大小: 10, 搜索关键词: 'null'
2025-07-09 10:00:42.518 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TopicServiceImpl - 🔍 获取话题列表（带缓存） - 查询条件: TopicQueryRequest(id=null, title=null, description=null, sortField=new)
2025-07-09 10:00:42.527 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.service.impl.TopicServiceImpl - 🔍 构建查询条件 - id: null, title: 'null', description: 'null', sortField: new, sortOrder: descend
2025-07-09 10:00:42.530 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.service.impl.TopicServiceImpl - 🔍 查询条件构建完成 - 包含title搜索: false, 逻辑删除由@TableLogic自动处理
2025-07-09 10:00:42.530 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.utils.QueryWrapperDebugUtil - 🔍 QueryWrapper调试 - 话题搜索查询条件
2025-07-09 10:00:42.530 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.utils.QueryWrapperDebugUtil - 📝 生成的SQL条件: 
2025-07-09 10:00:42.530 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.utils.QueryWrapperDebugUtil - 📋 查询参数:
2025-07-09 10:00:42.530 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.utils.QueryWrapperDebugUtil -   - 无查询参数
2025-07-09 10:00:42.530 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.utils.QueryWrapperDebugUtil - 🎯 完整SQL语句: SELECT * FROM topics WHERE 
2025-07-09 10:00:42.951 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 10:00:43.726 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 10:00:43.734 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM topics WHERE is_delete = 0
2025-07-09 10:00:43.762 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.selectPage_mpCount - ==> Parameters: 
2025-07-09 10:00:43.840 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.selectPage_mpCount - <==      Total: 1
2025-07-09 10:00:43.849 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectPage - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE is_delete=0 ORDER BY create_time DESC LIMIT ?
2025-07-09 10:00:43.850 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectPage - ==> Parameters: 10(Long)
2025-07-09 10:00:43.907 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectPage - <==      Total: 3
2025-07-09 10:00:43.911 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.service.impl.TopicServiceImpl - 📊 从数据库查询到话题列表 - 总数: 3, 当前页: 1, 返回数量: 3, 搜索关键词: null
2025-07-09 10:00:43.912 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TopicController - 📊 缓存查询结果 - 总数: 3, 当前页: 1, 返回记录数: 3
2025-07-09 10:00:43.913 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-09 10:00:43.921 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 9(String)
2025-07-09 10:00:43.967 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-09 10:00:43.971 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-09 10:00:43.973 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 0(String), 9(String)
2025-07-09 10:00:44.062 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-09 10:00:44.063 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-09 10:00:44.063 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 5(String)
2025-07-09 10:00:44.109 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-09 10:00:44.110 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-09 10:00:44.111 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 1(String), 5(String)
2025-07-09 10:00:44.202 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-09 10:00:44.202 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-09 10:00:44.202 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 2(String)
2025-07-09 10:00:44.247 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-09 10:00:44.249 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-09 10:00:44.249 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 3(String), 2(String)
2025-07-09 10:00:44.341 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-09 10:00:44.342 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-09 10:00:44.342 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 9(String)
2025-07-09 10:00:44.388 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-09 10:00:44.390 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-09 10:00:44.391 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 5(String)
2025-07-09 10:00:44.436 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-09 10:00:44.436 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-09 10:00:44.438 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 2(String)
2025-07-09 10:00:44.483 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-09 10:00:44.484 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TopicController - 📊 VO处理后结果 - 总数: 3, 当前页: 1, 返回记录数: 3
2025-07-09 10:00:44.486 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: f3cfbdb6-4df1-4aaf-92bb-37cccd6c867f, cost: 1996ms
2025-07-09 10:00:46.332 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 3922a06f-2bac-46e6-9140-b2340967c115, path: /api/store/list, ip: 0:0:0:0:0:0:0:1, params: []
2025-07-09 10:00:46.336 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.StoreController - 🏪 接收获取店铺列表请求
2025-07-09 10:00:46.345 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.StoreServiceImpl - 🏪 获取所有有效店铺信息
2025-07-09 10:00:46.392 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.S.selectActiveStores - ==>  Preparing: SELECT id, name FROM ba_store WHERE status = 1 ORDER BY create_time DESC
2025-07-09 10:00:46.393 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.S.selectActiveStores - ==> Parameters: 
2025-07-09 10:00:46.439 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.S.selectActiveStores - <==      Total: 12
2025-07-09 10:00:46.440 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.StoreServiceImpl - ✅ 成功获取店铺信息 - 数量: 12
2025-07-09 10:00:46.440 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.StoreController - ✅ 成功返回店铺列表 - 数量: 12
2025-07-09 10:00:46.440 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 3922a06f-2bac-46e6-9140-b2340967c115, cost: 107ms
2025-07-09 10:00:49.525 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 3ef9c0a7-3678-4745-81a2-1bc487747296, path: /api/comments/store/9/stats, ip: 0:0:0:0:0:0:0:1, params: [9, 222, org.apache.catalina.connector.RequestFacade@6b57947a]
2025-07-09 10:00:49.533 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.CommentController - 🔢 获取店铺评论统计 - storeId: 9, userId: 222
2025-07-09 10:00:49.535 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: b5b16306-56ce-42e5-8768-ebb77764bb21, path: /api/comments/store/9, ip: 0:0:0:0:0:0:0:1, params: [9, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:00:49.536 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 9, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:00:49.536 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:00:49.547 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.CommentServiceImpl - 获取店铺评论统计 - storeId: 9, userId: 222
2025-07-09 10:00:49.547 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 9, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:00:49.548 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 9, filter: hot
2025-07-09 10:00:49.554 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:00:49.559 [http-nio-0.0.0.0-8101-exec-3] WARN  c.b.m.c.t.support.ReflectLambdaMeta - Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not "opens java.lang.invoke" to unnamed module @298a5e20
2025-07-09 10:00:49.609 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:00:49.611 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:00:49.613 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:00:49.614 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:00:49.659 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:00:49.659 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:00:49.660 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 0, 当前页: 1, 返回数量: 0
2025-07-09 10:00:49.661 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 9, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-09 10:00:49.662 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: b5b16306-56ce-42e5-8768-ebb77764bb21, cost: 126ms
2025-07-09 10:00:49.665 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND user_id = ? AND is_delete = ?)
2025-07-09 10:00:49.666 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 9(String), 222(String), 0(String)
2025-07-09 10:00:49.682 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 3403b568-8376-40d9-ad41-a90e840e9b63, path: /api/comments/store/9, ip: 0:0:0:0:0:0:0:1, params: [9, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:00:49.682 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 9, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:00:49.682 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:00:49.682 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 9, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:00:49.682 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 9, filter: hot
2025-07-09 10:00:49.682 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:00:49.695 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:00:49.698 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:00:49.712 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:00:49.713 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论统计结果 - storeId: 9, 热门: 0, 最新: 0, 我的: 0
2025-07-09 10:00:49.714 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.CommentController - 🔢 店铺评论统计获取成功 - storeId: 9, 热门: 0, 最新: 0, 我的: 0
2025-07-09 10:00:49.714 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 3ef9c0a7-3678-4745-81a2-1bc487747296, cost: 188ms
2025-07-09 10:00:49.743 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:00:49.745 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 0, 当前页: 1, 返回数量: 0
2025-07-09 10:00:49.745 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 9, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-09 10:00:49.745 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 3403b568-8376-40d9-ad41-a90e840e9b63, cost: 63ms
2025-07-09 10:00:56.066 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: c0b29659-4f1e-42b0-b6bc-f2ee975148e5, path: /api/comments/store, ip: 0:0:0:0:0:0:0:1, params: [{userId=222, storeId=9, content=123}, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:00:56.066 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 发表店铺评论请求开始 - 请求参数: {userId=222, storeId=9, content=123}
2025-07-09 10:00:56.066 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 发表店铺评论参数解析 - userId: 222, storeId: 9, content: 123
2025-07-09 10:00:56.067 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 当前发表店铺评论的用户ID: 222
2025-07-09 10:00:57.557 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - ✅ 店铺评论内容敏感词检测通过 - userId: 222
2025-07-09 10:00:57.656 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.CommentServiceImpl - 创建店铺评论服务 - storeId: 9, content: 123, userId: 222
2025-07-09 10:00:57.661 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-09 10:00:57.662 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 222(String)
2025-07-09 10:00:57.709 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-09 10:00:57.713 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论数据准备完成 - contentId: store_9_1752026457710, storeId: 9, userId: 222
2025-07-09 10:00:57.720 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.CommentMapper.insert - ==>  Preparing: INSERT INTO comments ( content_id, topic_id, store_id, user_id, content, likes, reply_count, nickname, avatar, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-09 10:00:57.721 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.CommentMapper.insert - ==> Parameters: store_9_1752026457710(String), 9(Long), 9(Long), 222(Long), 123(String), 0(Integer), 0(Integer), (String), /storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg(String), 2025-07-09 10:00:57.716(Timestamp), 2025-07-09 10:00:57.719(Timestamp)
2025-07-09 10:00:57.813 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.CommentMapper.insert - <==    Updates: 1
2025-07-09 10:00:57.825 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论创建成功 - commentId: 56, contentId: store_9_1752026457710, storeId: 9, userId: 222
2025-07-09 10:00:57.920 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 发表店铺评论请求成功 - 新评论ID: 56, 用户ID: 222, 店铺ID: 9
2025-07-09 10:00:57.921 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: c0b29659-4f1e-42b0-b6bc-f2ee975148e5, cost: 1854ms
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: 5e9c60e0-a01a-4227-8a81-f35d81554245, path: /api/comments/store/9/stats, ip: 0:0:0:0:0:0:0:1, params: [9, 222, org.apache.catalina.connector.RequestFacade@6b57947a]
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.CommentController - 🔢 获取店铺评论统计 - storeId: 9, userId: 222
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 2beddb6f-6879-4140-8f4a-ffba59040586, path: /api/comments/store/9, ip: 0:0:0:0:0:0:0:1, params: [9, 222, new, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 9, userId: 222, filter: new, current: 1, pageSize: 10
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 9, filter: new, userId: 222, current: 1, pageSize: 10
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 9, filter: new
2025-07-09 10:00:57.997 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.s.impl.CommentServiceImpl - 获取店铺评论统计 - storeId: 9, userId: 222
2025-07-09 10:00:57.999 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:00:57.999 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 按最新排序（二级排序：ID）
2025-07-09 10:00:57.999 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:00:58.044 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:00:58.045 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:00:58.047 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:00:58.049 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND user_id = ? AND is_delete = ?)
2025-07-09 10:00:58.051 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 9(String), 222(String), 0(String)
2025-07-09 10:00:58.091 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:00:58.094 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.CommentMapper.selectPage - ==>  Preparing: SELECT id,content_id AS contentId,topic_id AS topicId,store_id AS storeId,user_id AS userId,content,likes,reply_count AS replyCount,nickname,avatar,created_at AS createdAt,updated_at AS updatedAt,is_delete AS isDelete FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?) ORDER BY created_at DESC,id DESC LIMIT ?
2025-07-09 10:00:58.095 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:00:58.095 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.CommentMapper.selectPage - ==> Parameters: 9(String), 0(String), 10(Long)
2025-07-09 10:00:58.096 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论统计结果 - storeId: 9, 热门: 1, 最新: 1, 我的: 1
2025-07-09 10:00:58.096 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.CommentController - 🔢 店铺评论统计获取成功 - storeId: 9, 热门: 1, 最新: 1, 我的: 1
2025-07-09 10:00:58.096 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: 5e9c60e0-a01a-4227-8a81-f35d81554245, cost: 99ms
2025-07-09 10:00:58.142 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.CommentMapper.selectPage - <==      Total: 1
2025-07-09 10:00:58.143 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 1, 当前页: 1, 返回数量: 1
2025-07-09 10:00:58.143 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 转换评论实体到DTO - commentId: 56
2025-07-09 10:00:58.154 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-09 10:00:58.156 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 222(String)
2025-07-09 10:00:58.197 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-09 10:00:58.197 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 获取到评论用户信息 - userId: 222, userName: 
2025-07-09 10:00:58.198 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 处理用户头像URL - 原始URL: /storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg, 处理后URL: https://file.foxdance.com.cn/storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg
2025-07-09 10:00:58.198 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.CommentServiceImpl - 用户等级: 0
2025-07-09 10:00:58.199 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comment_likes WHERE is_delete=0 AND (comment_id = ? AND user_id = ?)
2025-07-09 10:00:58.199 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectCount - ==> Parameters: 56(String), 222(String)
2025-07-09 10:00:58.242 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectCount - <==      Total: 1
2025-07-09 10:00:58.243 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 查询用户是否点赞 - commentId: 56, userId: 222, isLiked: false
2025-07-09 10:00:58.243 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 9, 总数: 1, 当前页: 1/1, 返回数: 1, hasMore: false
2025-07-09 10:00:58.243 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 2beddb6f-6879-4140-8f4a-ffba59040586, cost: 246ms
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: de54d70f-8232-4009-ace0-b0166b88efe1, path: /api/topic/list/page, ip: 0:0:0:0:0:0:0:1, params: [TopicQueryRequest(id=null, title=null, description=null, sortField=new)]
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.TopicController - 🎯 获取话题列表请求（带缓存） - 查询条件: TopicQueryRequest(id=null, title=null, description=null, sortField=new)
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.TopicController - 📋 话题列表查询参数详情 - 页码: 1, 页大小: 10, 搜索关键词: 'null'
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.service.impl.TopicServiceImpl - 🔍 获取话题列表（带缓存） - 查询条件: TopicQueryRequest(id=null, title=null, description=null, sortField=new)
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.service.impl.TopicServiceImpl - 🔍 构建查询条件 - id: null, title: 'null', description: 'null', sortField: new, sortOrder: descend
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.service.impl.TopicServiceImpl - 🔍 查询条件构建完成 - 包含title搜索: false, 逻辑删除由@TableLogic自动处理
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.utils.QueryWrapperDebugUtil - 🔍 QueryWrapper调试 - 话题搜索查询条件
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.utils.QueryWrapperDebugUtil - 📝 生成的SQL条件: 
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.utils.QueryWrapperDebugUtil - 📋 查询参数:
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.utils.QueryWrapperDebugUtil -   - 无查询参数
2025-07-09 10:01:02.385 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.utils.QueryWrapperDebugUtil - 🎯 完整SQL语句: SELECT * FROM topics WHERE 
2025-07-09 10:01:02.386 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.TopicController - 📊 缓存查询结果 - 总数: 3, 当前页: 1, 返回记录数: 3
2025-07-09 10:01:02.432 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-09 10:01:02.432 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 9(String)
2025-07-09 10:01:02.479 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-09 10:01:02.480 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-09 10:01:02.480 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 1(String), 9(String)
2025-07-09 10:01:02.569 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-09 10:01:02.569 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-09 10:01:02.569 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 5(String)
2025-07-09 10:01:02.612 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-09 10:01:02.613 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-09 10:01:02.614 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 1(String), 5(String)
2025-07-09 10:01:02.704 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-09 10:01:02.707 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - ==>  Preparing: SELECT COUNT(DISTINCT user_id) FROM comments WHERE topic_id = ? AND is_delete = 0
2025-07-09 10:01:02.708 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - ==> Parameters: 2(String)
2025-07-09 10:01:02.752 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.T.getCommentUserCount - <==      Total: 1
2025-07-09 10:01:02.752 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - ==>  Preparing: UPDATE topics SET comment_user_count=? WHERE id=? AND is_delete=0
2025-07-09 10:01:02.752 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - ==> Parameters: 3(String), 2(String)
2025-07-09 10:01:02.841 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.updateById - <==    Updates: 1
2025-07-09 10:01:02.842 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-09 10:01:02.842 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 9(String)
2025-07-09 10:01:02.888 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-09 10:01:02.890 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-09 10:01:02.891 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 5(String)
2025-07-09 10:01:02.935 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-09 10:01:02.935 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - ==>  Preparing: SELECT id,title,description,user_id AS userId,comment_user_count AS commentUserCount,cover_image AS coverImage,topic_images AS topicImages,create_time AS createTime,update_time AS updateTime,is_delete AS isDelete FROM topics WHERE id=? AND is_delete=0
2025-07-09 10:01:02.935 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - ==> Parameters: 2(String)
2025-07-09 10:01:02.980 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.TopicMapper.selectById - <==      Total: 1
2025-07-09 10:01:02.981 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.TopicController - 📊 VO处理后结果 - 总数: 3, 当前页: 1, 返回记录数: 3
2025-07-09 10:01:02.981 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: de54d70f-8232-4009-ace0-b0166b88efe1, cost: 596ms
2025-07-09 10:01:04.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 7ffb9e13-390f-464e-ae87-c67e8d990dfc, path: /api/store/list, ip: 0:0:0:0:0:0:0:1, params: []
2025-07-09 10:01:04.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.StoreController - 🏪 接收获取店铺列表请求
2025-07-09 10:01:04.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.StoreServiceImpl - 🏪 获取所有有效店铺信息
2025-07-09 10:01:04.330 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.S.selectActiveStores - ==>  Preparing: SELECT id, name FROM ba_store WHERE status = 1 ORDER BY create_time DESC
2025-07-09 10:01:04.330 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.S.selectActiveStores - ==> Parameters: 
2025-07-09 10:01:04.378 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.S.selectActiveStores - <==      Total: 12
2025-07-09 10:01:04.378 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.StoreServiceImpl - ✅ 成功获取店铺信息 - 数量: 12
2025-07-09 10:01:04.378 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.StoreController - ✅ 成功返回店铺列表 - 数量: 12
2025-07-09 10:01:04.378 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 7ffb9e13-390f-464e-ae87-c67e8d990dfc, cost: 103ms
2025-07-09 10:01:07.615 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 7d3a4aae-fba3-44c5-bcce-3cab770579d2, path: /api/comments/store/7/stats, ip: 0:0:0:0:0:0:0:1, params: [7, 222, org.apache.catalina.connector.RequestFacade@6b57947a]
2025-07-09 10:01:07.615 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.CommentController - 🔢 获取店铺评论统计 - storeId: 7, userId: 222
2025-07-09 10:01:07.615 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.CommentServiceImpl - 获取店铺评论统计 - storeId: 7, userId: 222
2025-07-09 10:01:07.615 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: d37ce6c8-e77a-45c1-8948-04377838ea1e, path: /api/comments/store/7, ip: 0:0:0:0:0:0:0:1, params: [7, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:01:07.616 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 7, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:01:07.617 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:01:07.617 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 7, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:01:07.617 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 7, filter: hot
2025-07-09 10:01:07.617 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:01:07.661 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:07.663 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 7(String), 0(String)
2025-07-09 10:01:07.665 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:07.665 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 7(String), 0(String)
2025-07-09 10:01:07.708 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:01:07.708 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:01:07.709 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 0, 当前页: 1, 返回数量: 0
2025-07-09 10:01:07.709 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 7, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-09 10:01:07.709 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: d37ce6c8-e77a-45c1-8948-04377838ea1e, cost: 93ms
2025-07-09 10:01:07.710 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND user_id = ? AND is_delete = ?)
2025-07-09 10:01:07.711 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 7(String), 222(String), 0(String)
2025-07-09 10:01:07.715 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: d860629e-2eaa-4aaf-9aee-c61ea6606969, path: /api/comments/store/7, ip: 0:0:0:0:0:0:0:1, params: [7, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:01:07.715 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 7, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:01:07.715 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:01:07.715 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 7, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:01:07.715 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 7, filter: hot
2025-07-09 10:01:07.715 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:01:07.724 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:07.726 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 7(String), 0(String)
2025-07-09 10:01:07.757 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:01:07.758 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论统计结果 - storeId: 7, 热门: 0, 最新: 0, 我的: 0
2025-07-09 10:01:07.758 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.CommentController - 🔢 店铺评论统计获取成功 - storeId: 7, 热门: 0, 最新: 0, 我的: 0
2025-07-09 10:01:07.758 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 7d3a4aae-fba3-44c5-bcce-3cab770579d2, cost: 143ms
2025-07-09 10:01:07.768 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:01:07.769 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 0, 当前页: 1, 返回数量: 0
2025-07-09 10:01:07.769 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 7, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-09 10:01:07.770 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: d860629e-2eaa-4aaf-9aee-c61ea6606969, cost: 54ms
2025-07-09 10:01:12.652 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: e882bce7-6344-412a-804c-b6901b4d0741, path: /api/comments/store/9, ip: 0:0:0:0:0:0:0:1, params: [9, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:01:12.652 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 9, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:01:12.652 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:01:12.652 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 9, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:01:12.652 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 9, filter: hot
2025-07-09 10:01:12.652 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:01:12.655 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 29d38197-373d-4005-9270-61f020fa43fd, path: /api/comments/store/9/stats, ip: 0:0:0:0:0:0:0:1, params: [9, 222, org.apache.catalina.connector.RequestFacade@6b57947a]
2025-07-09 10:01:12.655 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.CommentController - 🔢 获取店铺评论统计 - storeId: 9, userId: 222
2025-07-09 10:01:12.655 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.s.impl.CommentServiceImpl - 获取店铺评论统计 - storeId: 9, userId: 222
2025-07-09 10:01:12.703 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:12.705 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:01:12.707 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:12.707 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:01:12.746 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:01:12.748 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND user_id = ? AND is_delete = ?)
2025-07-09 10:01:12.749 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 9(String), 222(String), 0(String)
2025-07-09 10:01:12.750 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:01:12.751 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectPage - ==>  Preparing: SELECT id,content_id AS contentId,topic_id AS topicId,store_id AS storeId,user_id AS userId,content,likes,reply_count AS replyCount,nickname,avatar,created_at AS createdAt,updated_at AS updatedAt,is_delete AS isDelete FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?) ORDER BY likes DESC,created_at DESC,id DESC LIMIT ?
2025-07-09 10:01:12.751 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectPage - ==> Parameters: 9(String), 0(String), 10(Long)
2025-07-09 10:01:12.792 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:01:12.793 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论统计结果 - storeId: 9, 热门: 1, 最新: 1, 我的: 1
2025-07-09 10:01:12.793 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.CommentController - 🔢 店铺评论统计获取成功 - storeId: 9, 热门: 1, 最新: 1, 我的: 1
2025-07-09 10:01:12.793 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 29d38197-373d-4005-9270-61f020fa43fd, cost: 138ms
2025-07-09 10:01:12.798 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.CommentMapper.selectPage - <==      Total: 1
2025-07-09 10:01:12.799 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 1, 当前页: 1, 返回数量: 1
2025-07-09 10:01:12.799 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 转换评论实体到DTO - commentId: 56
2025-07-09 10:01:12.800 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-09 10:01:12.801 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 222(String)
2025-07-09 10:01:12.855 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-09 10:01:12.856 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 获取到评论用户信息 - userId: 222, userName: 
2025-07-09 10:01:12.856 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 处理用户头像URL - 原始URL: /storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg, 处理后URL: https://file.foxdance.com.cn/storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg
2025-07-09 10:01:12.856 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.CommentServiceImpl - 用户等级: 0
2025-07-09 10:01:12.860 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.C.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comment_likes WHERE is_delete=0 AND (comment_id = ? AND user_id = ?)
2025-07-09 10:01:12.862 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.C.selectCount - ==> Parameters: 56(String), 222(String)
2025-07-09 10:01:12.908 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.C.selectCount - <==      Total: 1
2025-07-09 10:01:12.910 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.s.impl.CommentServiceImpl - 查询用户是否点赞 - commentId: 56, userId: 222, isLiked: false
2025-07-09 10:01:12.910 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 9, 总数: 1, 当前页: 1/1, 返回数: 1, hasMore: false
2025-07-09 10:01:12.910 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: e882bce7-6344-412a-804c-b6901b4d0741, cost: 258ms
2025-07-09 10:01:12.919 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 1ea40770-c2f9-473d-a8f4-722e629b5e02, path: /api/comments/store/9, ip: 0:0:0:0:0:0:0:1, params: [9, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:01:12.919 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 9, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:01:12.919 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:01:12.919 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 9, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:01:12.919 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 9, filter: hot
2025-07-09 10:01:12.919 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:01:12.929 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:12.932 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 9(String), 0(String)
2025-07-09 10:01:12.978 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:01:12.981 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.CommentMapper.selectPage - ==>  Preparing: SELECT id,content_id AS contentId,topic_id AS topicId,store_id AS storeId,user_id AS userId,content,likes,reply_count AS replyCount,nickname,avatar,created_at AS createdAt,updated_at AS updatedAt,is_delete AS isDelete FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?) ORDER BY likes DESC,created_at DESC,id DESC LIMIT ?
2025-07-09 10:01:12.982 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.CommentMapper.selectPage - ==> Parameters: 9(String), 0(String), 10(Long)
2025-07-09 10:01:13.039 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.CommentMapper.selectPage - <==      Total: 1
2025-07-09 10:01:13.040 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 1, 当前页: 1, 返回数量: 1
2025-07-09 10:01:13.040 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 转换评论实体到DTO - commentId: 56
2025-07-09 10:01:13.042 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-09 10:01:13.042 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 222(String)
2025-07-09 10:01:13.090 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-09 10:01:13.091 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 获取到评论用户信息 - userId: 222, userName: 
2025-07-09 10:01:13.091 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 处理用户头像URL - 原始URL: /storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg, 处理后URL: https://file.foxdance.com.cn/storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg
2025-07-09 10:01:13.091 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.CommentServiceImpl - 用户等级: 0
2025-07-09 10:01:13.094 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.C.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comment_likes WHERE is_delete=0 AND (comment_id = ? AND user_id = ?)
2025-07-09 10:01:13.095 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.C.selectCount - ==> Parameters: 56(String), 222(String)
2025-07-09 10:01:13.143 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.C.selectCount - <==      Total: 1
2025-07-09 10:01:13.143 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.s.impl.CommentServiceImpl - 查询用户是否点赞 - commentId: 56, userId: 222, isLiked: false
2025-07-09 10:01:13.144 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 9, 总数: 1, 当前页: 1/1, 返回数: 1, hasMore: false
2025-07-09 10:01:13.144 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 1ea40770-c2f9-473d-a8f4-722e629b5e02, cost: 224ms
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 2a33aa14-3e15-422c-9735-da7e626d27f6, path: /api/comments/store/11, ip: 0:0:0:0:0:0:0:1, params: [11, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6b57947a]
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: 5b73a773-bdd6-4e1b-8eb9-022d7f104be5, path: /api/comments/store/11/stats, ip: 0:0:0:0:0:0:0:1, params: [11, 222, org.apache.catalina.connector.RequestFacade@6882f479]
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 11, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.CommentController - 🔢 获取店铺评论统计 - storeId: 11, userId: 222
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.s.impl.CommentServiceImpl - 获取店铺评论统计 - storeId: 11, userId: 222
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 11, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:01:17.070 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 11, filter: hot
2025-07-09 10:01:17.071 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:01:17.117 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:17.117 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:17.118 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 11(String), 0(String)
2025-07-09 10:01:17.118 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 11(String), 0(String)
2025-07-09 10:01:17.162 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:01:17.162 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:01:17.164 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.CommentMapper.selectPage - ==>  Preparing: SELECT id,content_id AS contentId,topic_id AS topicId,store_id AS storeId,user_id AS userId,content,likes,reply_count AS replyCount,nickname,avatar,created_at AS createdAt,updated_at AS updatedAt,is_delete AS isDelete FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?) ORDER BY likes DESC,created_at DESC,id DESC LIMIT ?
2025-07-09 10:01:17.165 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comments WHERE is_delete=0 AND (store_id = ? AND user_id = ? AND is_delete = ?)
2025-07-09 10:01:17.165 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.CommentMapper.selectPage - ==> Parameters: 11(String), 0(String), 10(Long)
2025-07-09 10:01:17.165 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - ==> Parameters: 11(String), 222(String), 0(String)
2025-07-09 10:01:17.211 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.CommentMapper.selectCount - <==      Total: 1
2025-07-09 10:01:17.212 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.CommentMapper.selectPage - <==      Total: 1
2025-07-09 10:01:17.212 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 1, 当前页: 1, 返回数量: 1
2025-07-09 10:01:17.212 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.s.impl.CommentServiceImpl - 店铺评论统计结果 - storeId: 11, 热门: 1, 最新: 1, 我的: 1
2025-07-09 10:01:17.212 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.CommentController - 🔢 店铺评论统计获取成功 - storeId: 11, 热门: 1, 最新: 1, 我的: 1
2025-07-09 10:01:17.212 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 转换评论实体到DTO - commentId: 55
2025-07-09 10:01:17.212 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: 5b73a773-bdd6-4e1b-8eb9-022d7f104be5, cost: 142ms
2025-07-09 10:01:17.213 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-09 10:01:17.214 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 222(String)
2025-07-09 10:01:17.256 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-09 10:01:17.257 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 获取到评论用户信息 - userId: 222, userName: 
2025-07-09 10:01:17.257 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 处理用户头像URL - 原始URL: /storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg, 处理后URL: https://file.foxdance.com.cn/storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg
2025-07-09 10:01:17.257 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.CommentServiceImpl - 用户等级: 0
2025-07-09 10:01:17.258 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comment_likes WHERE is_delete=0 AND (comment_id = ? AND user_id = ?)
2025-07-09 10:01:17.259 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectCount - ==> Parameters: 55(String), 222(String)
2025-07-09 10:01:17.300 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.C.selectCount - <==      Total: 1
2025-07-09 10:01:17.301 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.s.impl.CommentServiceImpl - 查询用户是否点赞 - commentId: 55, userId: 222, isLiked: false
2025-07-09 10:01:17.301 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 11, 总数: 1, 当前页: 1/1, 返回数: 1, hasMore: false
2025-07-09 10:01:17.301 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 2a33aa14-3e15-422c-9735-da7e626d27f6, cost: 231ms
2025-07-09 10:01:17.310 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 54a88f67-ca02-4b8e-a16e-78347cea0c5d, path: /api/comments/store/11, ip: 0:0:0:0:0:0:0:1, params: [11, 222, hot, 1, 10, org.apache.catalina.connector.RequestFacade@6b57947a]
2025-07-09 10:01:17.311 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 根据店铺ID获取评论列表请求开始 - storeId: 11, userId: 222, filter: hot, current: 1, pageSize: 10
2025-07-09 10:01:17.312 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 当前登录用户ID: 222
2025-07-09 10:01:17.312 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.CommentServiceImpl - 根据店铺ID获取评论列表服务(分页) - storeId: 11, filter: hot, userId: 222, current: 1, pageSize: 10
2025-07-09 10:01:17.312 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 从数据库查询店铺评论列表 - storeId: 11, filter: hot
2025-07-09 10:01:17.313 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 按热度排序（二级排序：创建时间，三级排序：ID）
2025-07-09 10:01:17.323 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (store_id = ? AND is_delete = ?)
2025-07-09 10:01:17.325 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 11(String), 0(String)
2025-07-09 10:01:17.371 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-09 10:01:17.373 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.CommentMapper.selectPage - ==>  Preparing: SELECT id,content_id AS contentId,topic_id AS topicId,store_id AS storeId,user_id AS userId,content,likes,reply_count AS replyCount,nickname,avatar,created_at AS createdAt,updated_at AS updatedAt,is_delete AS isDelete FROM comments WHERE is_delete=0 AND (store_id = ? AND is_delete = ?) ORDER BY likes DESC,created_at DESC,id DESC LIMIT ?
2025-07-09 10:01:17.374 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.CommentMapper.selectPage - ==> Parameters: 11(String), 0(String), 10(Long)
2025-07-09 10:01:17.423 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.CommentMapper.selectPage - <==      Total: 1
2025-07-09 10:01:17.424 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 数据库查询结果 - 总数: 1, 当前页: 1, 返回数量: 1
2025-07-09 10:01:17.425 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 转换评论实体到DTO - commentId: 55
2025-07-09 10:01:17.426 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,mobile,avatar,level,is_member,remaining_votes FROM ba_user WHERE id=?
2025-07-09 10:01:17.427 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 222(String)
2025-07-09 10:01:17.475 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-09 10:01:17.476 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 获取到评论用户信息 - userId: 222, userName: 
2025-07-09 10:01:17.477 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 处理用户头像URL - 原始URL: /storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg, 处理后URL: https://file.foxdance.com.cn/storage/default/20250415/tmp_01ffbc4aac68219304bbea786a3cb871d8937240e10266f68e9.jpeg
2025-07-09 10:01:17.477 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.CommentServiceImpl - 用户等级: 0
2025-07-09 10:01:17.479 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM comment_likes WHERE is_delete=0 AND (comment_id = ? AND user_id = ?)
2025-07-09 10:01:17.481 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectCount - ==> Parameters: 55(String), 222(String)
2025-07-09 10:01:17.531 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectCount - <==      Total: 1
2025-07-09 10:01:17.532 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.s.impl.CommentServiceImpl - 查询用户是否点赞 - commentId: 55, userId: 222, isLiked: false
2025-07-09 10:01:17.532 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 🎯 店铺评论分页查询成功 - storeId: 11, 总数: 1, 当前页: 1/1, 返回数: 1, hasMore: false
2025-07-09 10:01:17.533 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 54a88f67-ca02-4b8e-a16e-78347cea0c5d, cost: 222ms
2025-07-09 10:01:52.314 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 81b58e80-9eb0-44c9-9f45-54cb04177464, path: /api/store/list, ip: 0:0:0:0:0:0:0:1, params: []
2025-07-09 10:01:52.314 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.StoreController - 🏪 接收获取店铺列表请求
2025-07-09 10:01:52.314 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.StoreServiceImpl - 🏪 获取所有有效店铺信息
2025-07-09 10:01:52.359 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.S.selectActiveStores - ==>  Preparing: SELECT id, name FROM ba_store WHERE status = 1 ORDER BY create_time DESC
2025-07-09 10:01:52.360 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.S.selectActiveStores - ==> Parameters: 
2025-07-09 10:01:52.407 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.S.selectActiveStores - <==      Total: 12
2025-07-09 10:01:52.408 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.StoreServiceImpl - ✅ 成功获取店铺信息 - 数量: 12
2025-07-09 10:01:52.408 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.StoreController - ✅ 成功返回店铺列表 - 数量: 12
2025-07-09 10:01:52.408 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 81b58e80-9eb0-44c9-9f45-54cb04177464, cost: 94ms
2025-07-09 10:27:26.744 [Thread-18] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 10:27:26.921 [Thread-18] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 10:27:27.448 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 10:27:27.448 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:27:28.715 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 10:27:28.942 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 10:27:28.945 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 10:27:28.946 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 10:27:28.946 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 10:27:28.989 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 10:27:28.990 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1540 ms
2025-07-09 10:27:29.155 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5eb919df'
2025-07-09 10:27:29.209 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 10:27:29.259 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 10:27:29.340 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 10:27:29.367 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 10:27:29.427 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 10:27:29.440 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 10:27:29.442 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 10:27:29.445 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 10:27:29.446 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 10:27:29.446 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 10:27:29.446 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 10:27:29.447 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 10:27:29.485 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 10:27:29.513 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 10:27:29.528 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 10:27:29.557 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 10:27:29.705 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 10:27:29.716 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 10:27:29.722 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 10:27:29.852 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 10:27:29.866 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 10:27:30.102 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 10:27:30.650 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 10:27:30.824 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 10:27:30.912 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 10:27:30.979 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 10:27:31.074 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 10:27:31.080 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 10:27:31.081 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 10:27:31.083 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 10:27:31.098 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 10:27:31.175 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 10:27:31.313 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.933 seconds (JVM running for 2684.9)
2025-07-09 10:27:31.317 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-09 10:28:03.563 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on DESKTOP-6QMNTV4 with PID 17700 (D:\Project\fox\用户端\springboot-init-master\target\classes started by pc in D:\Project\fox\用户端\springboot-init-master)
2025-07-09 10:28:03.564 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-09 10:28:03.918 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-09 10:28:04.100 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-09 10:28:04.105 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 10:28:04.107 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 10:28:04.107 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-09 10:28:04.132 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-09 10:28:04.132 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 567 ms
2025-07-09 10:28:04.327 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@36300c3a'
2025-07-09 10:28:04.384 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-09 10:28:04.417 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-09 10:28:04.443 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-09 10:28:04.473 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-09 10:28:04.494 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-09 10:28:04.498 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-09 10:28:04.499 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-09 10:28:04.502 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-09 10:28:04.502 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-09 10:28:04.503 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-09 10:28:04.503 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-09 10:28:04.503 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-09 10:28:04.522 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-09 10:28:04.537 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-09 10:28:04.558 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-09 10:28:04.580 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Project\fox\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-09 10:28:04.721 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-09 10:28:04.726 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-09 10:28:04.727 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-09 10:28:04.832 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-09 10:28:04.843 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-09 10:28:04.960 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-09 10:28:05.637 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-09 10:28:05.812 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-09 10:28:05.967 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-09 10:28:06.031 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-09 10:28:06.187 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-09 10:28:06.191 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-09 10:28:06.192 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-09 10:28:06.193 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-09 10:28:06.205 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-09 10:28:06.267 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-09 10:28:06.357 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.867 seconds (JVM running for 2719.944)
2025-07-09 10:28:06.360 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
