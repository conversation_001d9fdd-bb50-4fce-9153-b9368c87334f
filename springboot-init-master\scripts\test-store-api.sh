#!/bin/bash

# 店铺API测试脚本
# 用于测试店铺相关接口功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8101"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试获取店铺名称接口
test_store_names() {
    log_info "🏪 测试获取店铺名称接口"
    
    local response=$(curl -s -X GET "$BASE_URL/api/store/names" \
        -H "Content-Type: application/json")
    
    if [ $? -ne 0 ]; then
        log_error "请求发送失败"
        return 1
    fi
    
    log_info "📡 API响应: $response"
    
    # 解析响应
    local code=$(echo "$response" | jq -r '.code // "null"' 2>/dev/null)
    local message=$(echo "$response" | jq -r '.message // "null"' 2>/dev/null)
    local data_count=$(echo "$response" | jq -r '.data | length // 0' 2>/dev/null)
    
    log_info "📊 解析结果:"
    log_info "  - 响应码: $code"
    log_info "  - 响应消息: $message"
    log_info "  - 店铺数量: $data_count"
    
    # 显示店铺名称
    if [ "$data_count" -gt 0 ]; then
        log_success "✅ 成功获取 $data_count 个店铺名称:"
        echo "$response" | jq -r '.data[]?' 2>/dev/null | while read -r name; do
            echo "  🏪 $name"
        done
    else
        log_warning "⚠️ 未获取到店铺名称"
    fi
    
    return 0
}

# 测试获取店铺列表接口
test_store_list() {
    log_info "🏪 测试获取店铺列表接口"
    
    local response=$(curl -s -X GET "$BASE_URL/api/store/list" \
        -H "Content-Type: application/json")
    
    if [ $? -ne 0 ]; then
        log_error "请求发送失败"
        return 1
    fi
    
    log_info "📡 API响应: $response"
    
    # 解析响应
    local code=$(echo "$response" | jq -r '.code // "null"' 2>/dev/null)
    local data_count=$(echo "$response" | jq -r '.data | length // 0' 2>/dev/null)
    
    log_info "📊 解析结果:"
    log_info "  - 响应码: $code"
    log_info "  - 店铺数量: $data_count"
    
    # 显示店铺详情
    if [ "$data_count" -gt 0 ]; then
        log_success "✅ 成功获取 $data_count 个店铺详情:"
        echo "$response" | jq -r '.data[] | "  🏪 ID: \(.id), 名称: \(.name), 地址: \(.address // "未设置")"' 2>/dev/null
    else
        log_warning "⚠️ 未获取到店铺列表"
    fi
    
    return 0
}

# 测试获取单个店铺接口
test_store_detail() {
    local store_id=${1:-1}
    
    log_info "🏪 测试获取店铺详情接口 - ID: $store_id"
    
    local response=$(curl -s -X GET "$BASE_URL/api/store/$store_id" \
        -H "Content-Type: application/json")
    
    if [ $? -ne 0 ]; then
        log_error "请求发送失败"
        return 1
    fi
    
    log_info "📡 API响应: $response"
    
    # 解析响应
    local code=$(echo "$response" | jq -r '.code // "null"' 2>/dev/null)
    local store_name=$(echo "$response" | jq -r '.data.name // "null"' 2>/dev/null)
    
    if [ "$code" = "0" ] && [ "$store_name" != "null" ]; then
        log_success "✅ 成功获取店铺详情: $store_name"
        echo "$response" | jq -r '.data | "  📍 地址: \(.address // "未设置")\n  📞 电话: \(.phone // "未设置")\n  📝 描述: \(.description // "未设置")"' 2>/dev/null
    else
        log_warning "⚠️ 获取店铺详情失败或店铺不存在"
    fi
    
    return 0
}

# 检查服务状态
check_service_status() {
    log_info "🔍 检查后端服务状态..."
    
    local health_response=$(curl -s "$BASE_URL/api/health" 2>/dev/null || echo '{"status":"unknown"}')
    
    # 尝试简单的API调用来验证服务状态
    local test_response=$(curl -s -X GET "$BASE_URL/api/store/names" 2>/dev/null)
    
    if echo "$test_response" | grep -q '"code"' 2>/dev/null; then
        log_success "✅ 后端服务正常运行"
        return 0
    else
        log_error "❌ 后端服务不可访问，请检查服务是否启动"
        log_error "测试响应: $test_response"
        return 1
    fi
}

# 主测试流程
main() {
    echo "=================================================="
    echo "🏪 店铺API功能测试"
    echo "=================================================="
    echo ""
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 命令未找到，将使用简化输出"
    fi
    
    # 检查服务状态
    if ! check_service_status; then
        log_error "后端服务不可用，测试终止"
        exit 1
    fi
    
    echo ""
    echo "开始API测试..."
    echo ""
    
    # 测试1: 获取店铺名称
    if test_store_names; then
        log_success "✅ 测试1通过: 获取店铺名称接口正常"
    else
        log_error "❌ 测试1失败: 获取店铺名称接口异常"
    fi
    
    echo ""
    
    # 测试2: 获取店铺列表
    if test_store_list; then
        log_success "✅ 测试2通过: 获取店铺列表接口正常"
    else
        log_error "❌ 测试2失败: 获取店铺列表接口异常"
    fi
    
    echo ""
    
    # 测试3: 获取店铺详情
    if test_store_detail 1; then
        log_success "✅ 测试3通过: 获取店铺详情接口正常"
    else
        log_error "❌ 测试3失败: 获取店铺详情接口异常"
    fi
    
    echo ""
    echo "=================================================="
    echo "🎯 测试总结"
    echo "=================================================="
    echo ""
    echo "如果所有测试都通过，说明店铺API功能正常。"
    echo "前端页面访问地址: /pagesSub/store/store-list"
    echo ""
}

# 执行主函数
main "$@"
