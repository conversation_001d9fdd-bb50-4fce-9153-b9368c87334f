#!/bin/bash

# 店铺评论API测试脚本
# 用于测试店铺评论功能的完整流程

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8101"
STORE_ID=1
USER_ID=222

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试发表店铺评论
test_create_store_comment() {
    log_info "🏪 测试发表店铺评论"
    
    local comment_content="这家舞蹈工作室很不错，环境优美，教练专业！"
    
    local request_data='{
        "userId": '$USER_ID',
        "storeId": '$STORE_ID',
        "content": "'$comment_content'"
    }'
    
    log_info "发送评论请求..."
    log_info "请求数据: $request_data"
    
    local response=$(curl -s -X POST "$BASE_URL/api/comment/store" \
        -H "Content-Type: application/json" \
        -d "$request_data")
    
    if [ $? -ne 0 ]; then
        log_error "请求发送失败"
        return 1
    fi
    
    log_info "📡 API响应: $response"
    
    # 解析响应
    local code=$(echo "$response" | jq -r '.code // "null"' 2>/dev/null)
    local message=$(echo "$response" | jq -r '.message // "null"' 2>/dev/null)
    local comment_id=$(echo "$response" | jq -r '.data.comment_id // "null"' 2>/dev/null)
    
    log_info "📊 解析结果:"
    log_info "  - 响应码: $code"
    log_info "  - 响应消息: $message"
    log_info "  - 评论ID: $comment_id"
    
    if [ "$code" = "0" ] && [ "$comment_id" != "null" ]; then
        log_success "✅ 店铺评论发表成功 - 评论ID: $comment_id"
        return 0
    else
        log_error "❌ 店铺评论发表失败: $message"
        return 1
    fi
}

# 测试获取店铺评论列表
test_get_store_comments() {
    local filter=${1:-"hot"}
    
    log_info "🏪 测试获取店铺评论列表 - 筛选: $filter"
    
    local url="$BASE_URL/api/comment/store/$STORE_ID?userId=$USER_ID&filter=$filter&current=1&pageSize=10"
    
    log_info "请求URL: $url"
    
    local response=$(curl -s -X GET "$url" \
        -H "Content-Type: application/json")
    
    if [ $? -ne 0 ]; then
        log_error "请求发送失败"
        return 1
    fi
    
    log_info "📡 API响应: $response"
    
    # 解析响应
    local code=$(echo "$response" | jq -r '.code // "null"' 2>/dev/null)
    local total=$(echo "$response" | jq -r '.data.total // 0' 2>/dev/null)
    local comments_count=$(echo "$response" | jq -r '.data.comments | length // 0' 2>/dev/null)
    
    log_info "📊 解析结果:"
    log_info "  - 响应码: $code"
    log_info "  - 总评论数: $total"
    log_info "  - 返回评论数: $comments_count"
    
    if [ "$code" = "0" ]; then
        log_success "✅ 店铺评论列表获取成功 - 总数: $total, 返回: $comments_count"
        
        # 显示评论内容
        if [ "$comments_count" -gt 0 ]; then
            log_info "📝 评论内容预览:"
            echo "$response" | jq -r '.data.comments[]? | "  💬 \(.nickname): \(.content) (点赞: \(.likes))"' 2>/dev/null
        fi
        return 0
    else
        log_error "❌ 店铺评论列表获取失败"
        return 1
    fi
}

# 测试获取店铺评论统计
test_get_store_comment_stats() {
    log_info "🏪 测试获取店铺评论统计"
    
    local url="$BASE_URL/api/comment/store/$STORE_ID/stats?userId=$USER_ID"
    
    log_info "请求URL: $url"
    
    local response=$(curl -s -X GET "$url" \
        -H "Content-Type: application/json")
    
    if [ $? -ne 0 ]; then
        log_error "请求发送失败"
        return 1
    fi
    
    log_info "📡 API响应: $response"
    
    # 解析响应
    local code=$(echo "$response" | jq -r '.code // "null"' 2>/dev/null)
    local hot_total=$(echo "$response" | jq -r '.data.hotTotal // 0' 2>/dev/null)
    local new_total=$(echo "$response" | jq -r '.data.newTotal // 0' 2>/dev/null)
    local my_total=$(echo "$response" | jq -r '.data.myTotal // 0' 2>/dev/null)
    
    log_info "📊 统计结果:"
    log_info "  - 响应码: $code"
    log_info "  - 热门评论数: $hot_total"
    log_info "  - 最新评论数: $new_total"
    log_info "  - 我的评论数: $my_total"
    
    if [ "$code" = "0" ]; then
        log_success "✅ 店铺评论统计获取成功"
        return 0
    else
        log_error "❌ 店铺评论统计获取失败"
        return 1
    fi
}

# 检查服务状态
check_service_status() {
    log_info "🔍 检查后端服务状态..."
    
    local test_response=$(curl -s "$BASE_URL/api/store/names" 2>/dev/null)
    
    if echo "$test_response" | grep -q '"code"' 2>/dev/null; then
        log_success "✅ 后端服务正常运行"
        return 0
    else
        log_error "❌ 后端服务不可访问，请检查服务是否启动"
        log_error "测试响应: $test_response"
        return 1
    fi
}

# 主测试流程
main() {
    echo "=================================================="
    echo "🏪 店铺评论API功能测试"
    echo "=================================================="
    echo ""
    echo "测试配置:"
    echo "  - 基础URL: $BASE_URL"
    echo "  - 店铺ID: $STORE_ID"
    echo "  - 用户ID: $USER_ID"
    echo ""
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 命令未找到，将使用简化输出"
    fi
    
    # 检查服务状态
    if ! check_service_status; then
        log_error "后端服务不可用，测试终止"
        exit 1
    fi
    
    echo ""
    echo "开始API测试..."
    echo ""
    
    # 测试1: 发表店铺评论
    if test_create_store_comment; then
        log_success "✅ 测试1通过: 发表店铺评论功能正常"
    else
        log_error "❌ 测试1失败: 发表店铺评论功能异常"
    fi
    
    echo ""
    
    # 等待一秒确保数据已保存
    sleep 1
    
    # 测试2: 获取热门评论
    if test_get_store_comments "hot"; then
        log_success "✅ 测试2通过: 获取热门评论功能正常"
    else
        log_error "❌ 测试2失败: 获取热门评论功能异常"
    fi
    
    echo ""
    
    # 测试3: 获取最新评论
    if test_get_store_comments "new"; then
        log_success "✅ 测试3通过: 获取最新评论功能正常"
    else
        log_error "❌ 测试3失败: 获取最新评论功能异常"
    fi
    
    echo ""
    
    # 测试4: 获取我的评论
    if test_get_store_comments "my"; then
        log_success "✅ 测试4通过: 获取我的评论功能正常"
    else
        log_error "❌ 测试4失败: 获取我的评论功能异常"
    fi
    
    echo ""
    
    # 测试5: 获取评论统计
    if test_get_store_comment_stats; then
        log_success "✅ 测试5通过: 获取评论统计功能正常"
    else
        log_error "❌ 测试5失败: 获取评论统计功能异常"
    fi
    
    echo ""
    echo "=================================================="
    echo "🎯 测试总结"
    echo "=================================================="
    echo ""
    echo "如果所有测试都通过，说明店铺评论API功能正常。"
    echo "前端页面测试:"
    echo "  1. 访问店铺列表: /pagesSub/store/store-list"
    echo "  2. 点击店铺进入评论页面"
    echo "  3. 测试查看和发表评论功能"
    echo ""
}

# 执行主函数
main "$@"
