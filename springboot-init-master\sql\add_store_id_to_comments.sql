-- 为comments表添加store_id字段的数据库迁移脚本
-- 执行日期: 2025-07-08
-- 目的: 支持店铺评论功能

-- 检查表是否存在
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'comments';

-- 检查store_id字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'comments' 
  AND COLUMN_NAME = 'store_id';

-- 添加store_id字段（如果不存在）
ALTER TABLE comments 
ADD COLUMN IF NOT EXISTS store_id BIGINT(20) NULL COMMENT '店铺ID，关联ba_store表' 
AFTER topic_id;

-- 为store_id字段添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_comments_store_id ON comments(store_id);

-- 创建复合索引以优化店铺评论查询
CREATE INDEX IF NOT EXISTS idx_comments_store_filter ON comments(store_id, is_delete, likes, created_at);

-- 验证字段添加成功
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '可为空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'comments' 
  AND COLUMN_NAME = 'store_id';

-- 验证索引创建成功
SHOW INDEX FROM comments WHERE Column_name = 'store_id';

-- 查看表结构
DESCRIBE comments;

-- 统计现有数据
SELECT 
    '总评论数' as '统计项',
    COUNT(*) as '数量'
FROM comments
UNION ALL
SELECT 
    '话题评论数' as '统计项',
    COUNT(*) as '数量'
FROM comments 
WHERE topic_id IS NOT NULL
UNION ALL
SELECT 
    '店铺评论数' as '统计项',
    COUNT(*) as '数量'
FROM comments 
WHERE store_id IS NOT NULL;

-- 插入测试数据（可选，用于验证功能）
-- 注意：只有在确认需要测试数据时才执行以下语句

/*
-- 为店铺ID为1的店铺插入测试评论
INSERT INTO comments (
    content_id, 
    store_id, 
    user_id, 
    content, 
    likes, 
    reply_count, 
    nickname, 
    avatar, 
    created_at, 
    updated_at, 
    is_delete
) VALUES 
(
    'store_1_comment_1', 
    1, 
    222, 
    '这家舞蹈工作室的环境很好，教练也很专业！', 
    5, 
    0, 
    '舞蹈爱好者', 
    '/static/avatar/default.png', 
    NOW(), 
    NOW(), 
    0
),
(
    'store_1_comment_2', 
    1, 
    223, 
    '课程安排合理，适合初学者，推荐！', 
    3, 
    0, 
    '新手小白', 
    '/static/avatar/default.png', 
    NOW(), 
    NOW(), 
    0
),
(
    'store_1_comment_3', 
    1, 
    224, 
    '找到了很多志同道合的舞友，氛围很棒！', 
    8, 
    0, 
    '舞蹈达人', 
    '/static/avatar/default.png', 
    NOW(), 
    NOW(), 
    0
);

-- 验证测试数据插入成功
SELECT 
    id,
    content,
    store_id,
    user_id,
    likes,
    nickname,
    created_at
FROM comments 
WHERE store_id = 1 
ORDER BY created_at DESC;
*/

-- 执行完成提示
SELECT '✅ store_id字段添加完成！可以开始使用店铺评论功能。' as '执行结果';
