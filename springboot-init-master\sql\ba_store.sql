-- 创建ba_store表的SQL脚本
-- 用于存储店铺信息

-- 创建ba_store表
CREATE TABLE IF NOT EXISTS `ba_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '店铺名称',
  `description` varchar(500) DEFAULT NULL COMMENT '店铺描述',
  `address` varchar(200) DEFAULT NULL COMMENT '店铺地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '店铺状态：0-关闭，1-营业',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺信息表';

-- 插入测试数据
INSERT INTO `ba_store` (`name`, `description`, `address`, `phone`, `status`) VALUES
('星舞舞蹈工作室', '专业的现代舞、爵士舞培训机构', '北京市朝阳区三里屯SOHO', '010-12345678', 1),
('梦想舞蹈学院', '少儿舞蹈、成人舞蹈培训', '北京市海淀区中关村大街', '010-87654321', 1),
('炫彩舞蹈中心', '街舞、Breaking、Popping专业培训', '北京市东城区王府井大街', '010-11111111', 1),
('优雅芭蕾舞室', '古典芭蕾、现代芭蕾教学', '北京市西城区金融街', '010-22222222', 1),
('动感街舞俱乐部', '嘻哈文化、街舞竞技培训', '北京市丰台区丽泽商务区', '010-33333333', 1),
('艺术舞蹈学校', '民族舞、古典舞、现代舞', '北京市石景山区万达广场', '010-44444444', 1),
('青春舞蹈工作室', '青少年舞蹈兴趣培养', '北京市通州区万达广场', '010-55555555', 1),
('专业舞蹈培训中心', '舞蹈考级、比赛培训', '北京市昌平区回龙观', '010-66666666', 1);

-- 查询验证数据
SELECT * FROM ba_store WHERE is_delete = 0 AND status = 1 ORDER BY create_time DESC;
