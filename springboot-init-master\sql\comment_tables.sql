-- 评论表
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
    content_id VARCHAR(64) NOT NULL COMMENT '内容ID，如视频ID、文章ID等',
    content_type VARCHAR(20) NOT NULL COMMENT '内容类型，如video、article等',
    user_id BIGINT NOT NULL COMMENT '评论用户ID',
    content TEXT NOT NULL COMMENT '评论内容',
    likes INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    reply_count INT NOT NULL DEFAULT 0 COMMENT '回复数量',
    nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    INDEX idx_content(content_id, content_type),
    INDEX idx_user(user_id),
    INDEX idx_created_at(created_at)
) COMMENT='评论表' COLLATE=utf8mb4_unicode_ci;

-- 评论回复表
CREATE TABLE IF NOT EXISTS comment_replies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '回复ID',
    comment_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '回复用户ID',
    content TEXT NOT NULL COMMENT '回复内容',
    reply_to_id BIGINT COMMENT '被回复的用户ID，如果直接回复评论则为NULL',
    likes INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    INDEX idx_comment(comment_id),
    INDEX idx_user(user_id),
    INDEX idx_reply_to(reply_to_id),
    INDEX idx_created_at(created_at)
) COMMENT='评论回复表' COLLATE=utf8mb4_unicode_ci;

-- 评论点赞记录表
CREATE TABLE IF NOT EXISTS comment_likes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
    comment_id BIGINT NOT NULL COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    UNIQUE KEY uk_comment_user(comment_id, user_id),
    INDEX idx_user(user_id),
    INDEX idx_created_at(created_at)
) COMMENT='评论点赞记录表' COLLATE=utf8mb4_unicode_ci;

-- 回复点赞记录表
CREATE TABLE IF NOT EXISTS reply_likes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '点赞ID',
    reply_id BIGINT NOT NULL COMMENT '回复ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_delete TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除，0-未删除，1-已删除',
    UNIQUE KEY uk_reply_user(reply_id, user_id),
    INDEX idx_user(user_id),
    INDEX idx_created_at(created_at)
) COMMENT='回复点赞记录表' COLLATE=utf8mb4_unicode_ci;

-- 查询vote_records表中投票station_name为"嘉禾望岗"的用户id，在这个结果中，从ba_user表中通过刚刚查询到的user_id查询结果中包含is_member=1的数量
SELECT COUNT(*) FROM ba_user WHERE is_member = 1 AND id IN (
    SELECT user_id FROM vote_records WHERE station_name = '嘉禾望岗'
);

-- 删除comments表中的content_type字段
ALTER TABLE comments DROP COLUMN content_type;

-- 添加nickname和avatar字段到comments表（如果不存在）
ALTER TABLE comments ADD COLUMN IF NOT EXISTS nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称' AFTER reply_count,
ADD COLUMN IF NOT EXISTS avatar VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL' AFTER nickname;

-- 添加nickname和avatar字段到comment_replies表（如果不存在）
ALTER TABLE comment_replies ADD COLUMN IF NOT EXISTS nickname VARCHAR(64) DEFAULT NULL COMMENT '用户昵称' AFTER likes,
ADD COLUMN IF NOT EXISTS avatar VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL' AFTER nickname;

select * from ba_user where mobile='13660016882';
select * from ba_user where id=17250;
SELECT *  FROM topics WHERE is_delete = 0 AND title like '%123%';

-- comments表添加store字段
ALTER TABLE comments ADD store_id BIGINT DEFAULT NULL COMMENT '店铺ID';