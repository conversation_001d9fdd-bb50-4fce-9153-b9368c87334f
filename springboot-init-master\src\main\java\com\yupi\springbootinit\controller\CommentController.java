package com.yupi.springbootinit.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.dto.CommentDTO;
import com.yupi.springbootinit.model.dto.ReplyDTO;
import com.yupi.springbootinit.service.CacheService;
import com.yupi.springbootinit.service.CommentReplyService;
import com.yupi.springbootinit.service.CommentService;
import com.yupi.springbootinit.service.ThirdPartySensitiveWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评论接口
 */
@RestController
@RequestMapping("/comments")
@Slf4j
@Api(tags = "评论接口")
public class CommentController {

    @Resource
    private CommentService commentService;

    @Resource
    private CommentReplyService commentReplyService;

    @Resource
    private CacheService cacheService;

    @Resource
    private ThirdPartySensitiveWordService thirdPartySensitiveWordService;

    /**
     * 获取评论列表（支持分页）
     */
    @GetMapping
    @ApiOperation(value = "获取评论列表")
    public BaseResponse<Map<String, Object>> getCommentList(
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "内容ID", required = true) @RequestParam String contentId,
            @ApiParam(value = "排序方式", required = false) @RequestParam(defaultValue = "hot") String filter,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", required = false) @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {
        log.info("获取评论列表请求开始 - userId: {}, contentId: {}, filter: {}, current: {}, pageSize: {}",
                userId, contentId, filter, current, pageSize);
        
        try {
            if (userId == null || userId <= 0) {
                log.warn("获取评论列表请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (StringUtils.isBlank(contentId)) {
                log.warn("获取评论列表请求参数错误 - contentId为空");
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            // 参数验证
            if (current < 1) current = 1;
            if (pageSize < 1 || pageSize > 50) pageSize = 10; // 限制最大页面大小

            log.info("当前登录用户ID: {}", userId);

            // 获取评论列表（分页）
            Page<CommentDTO> commentPage = commentService.getCommentListWithPage(contentId, filter, userId, current, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("total", commentPage.getTotal());
            result.put("comments", commentPage.getRecords());
            result.put("current", commentPage.getCurrent());
            result.put("size", commentPage.getSize());
            result.put("pages", commentPage.getPages());
            result.put("hasMore", commentPage.getCurrent() < commentPage.getPages());

            log.info("🎯 普通评论分页查询成功 - contentId: {}, 总数: {}, 当前页: {}/{}, 返回数: {}, hasMore: {}",
                    contentId, commentPage.getTotal(), commentPage.getCurrent(), commentPage.getPages(),
                    commentPage.getRecords().size(), commentPage.getCurrent() < commentPage.getPages());
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("获取评论列表请求异常 - contentId: {}, 错误信息: {}", contentId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据话题ID获取评论列表（支持分页）
     */
    @GetMapping("/topic/{topicId}")
    @ApiOperation(value = "根据话题ID获取评论列表")
    public BaseResponse<Map<String, Object>> getCommentListByTopicId(
            @ApiParam(value = "话题ID", required = true) @PathVariable Long topicId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "排序方式", required = false) @RequestParam(defaultValue = "hot") String filter,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", required = false) @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {
        log.info("根据话题ID获取评论列表请求开始 - topicId: {}, userId: {}, filter: {}, current: {}, pageSize: {}",
                topicId, userId, filter, current, pageSize);

        try {
            if (topicId == null || topicId <= 0) {
                log.warn("获取评论列表请求参数错误 - topicId无效: {}", topicId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "话题ID不能为空");
            }

            if (userId == null || userId <= 0) {
                log.warn("获取评论列表请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }

            // 参数验证
            if (current < 1) current = 1;
            if (pageSize < 1 || pageSize > 50) pageSize = 10; // 限制最大页面大小

            log.info("当前登录用户ID: {}", userId);
            Page<CommentDTO> commentPage = commentService.getCommentListByTopicIdWithPage(topicId, filter, userId, current, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("total", commentPage.getTotal());
            result.put("comments", commentPage.getRecords());
            result.put("current", commentPage.getCurrent());
            result.put("size", commentPage.getSize());
            result.put("pages", commentPage.getPages());
            result.put("hasMore", commentPage.getCurrent() < commentPage.getPages());

            log.info("🎯 话题评论分页查询成功 - topicId: {}, 总数: {}, 当前页: {}/{}, 返回数: {}, hasMore: {}",
                    topicId, commentPage.getTotal(), commentPage.getCurrent(), commentPage.getPages(),
                    commentPage.getRecords().size(), commentPage.getCurrent() < commentPage.getPages());
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("根据话题ID获取评论列表请求异常 - topicId: {}, 错误信息: {}", topicId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取评论详情
     */
    @GetMapping("/{commentId}")
    @ApiOperation(value = "获取评论详情")
    public BaseResponse<Map<String, Object>> getCommentDetail(
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "评论ID", required = true) @PathVariable Long commentId,
            @ApiParam(value = "回复排序方式", required = false) @RequestParam(defaultValue = "hot") String sort,
            HttpServletRequest request) {
        log.info("获取评论详情请求开始 - userId: {}, commentId: {}, sort: {}", userId, commentId, sort);
        
        try {
            if (userId == null || userId <= 0) {
                log.warn("获取评论详情请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (commentId == null || commentId <= 0) {
                log.warn("获取评论详情请求参数错误 - commentId无效: {}", commentId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            log.info("当前登录用户ID: {}", userId);

            // 获取评论详情
            CommentDTO commentDetail = commentService.getCommentDetail(commentId, userId);
            if (commentDetail == null) {
                log.warn("评论不存在或已删除 - commentId: {}", commentId);
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR);
            }
            
            // 获取回复列表
            List<ReplyDTO> replyList = commentReplyService.getReplyList(commentId, sort, userId);
            
            Map<String, Object> repliesMap = new HashMap<>();
            repliesMap.put("total", replyList.size());
            repliesMap.put("items", replyList);
            
            Map<String, Object> result = new HashMap<>();
            result.put("comment", commentDetail);
            result.put("replies", repliesMap);
            
            log.info("获取评论详情请求成功 - commentId: {}, 回复数: {}", commentId, replyList.size());
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("获取评论详情请求异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论详情失败: " + e.getMessage());
        }
    }

    /**
     * 发表评论
     */
    @PostMapping
    @ApiOperation(value = "发表评论")
    public BaseResponse<Map<String, Object>> createComment(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        log.info("发表评论请求开始 - 请求参数: {}", params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            String contentId = (String) params.get("contentId");
            String content = (String) params.get("content");
            // 修复：安全的类型转换，避免ClassCastException
            Long topicId = params.get("topicId") != null ? Long.valueOf(params.get("topicId").toString()) : null;

            log.info("发表评论参数解析 - userId: {}, contentId: {}, content: {}, topicId: {}", userId, contentId, content, topicId);
            
            if (userId == null || userId <= 0) {
                log.warn("发表评论请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (StringUtils.isBlank(contentId) || StringUtils.isBlank(content)) {
                log.warn("发表评论请求参数错误 - contentId或content为空");
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            log.info("当前发表评论的用户ID: {}", userId);

            // 第三方敏感词检测
            try {
                thirdPartySensitiveWordService.validateContent(content);
                log.info("✅ 评论内容敏感词检测通过 - userId: {}", userId);
            } catch (Exception e) {
                log.warn("🚫 评论内容包含敏感词被拦截 - userId: {}, content: {}, error: {}", userId, content, e.getMessage());
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, e.getMessage());
            }

            // 创建评论
            Long commentId = commentService.createComment(contentId, content, userId, topicId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("comment_id", commentId);
            result.put("created_at", new Date());
            
            log.info("发表评论请求成功 - 新评论ID: {}, 用户ID: {}, 内容ID: {}", commentId, userId, contentId);
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("发表评论请求异常 - 错误信息: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "发表评论失败: " + e.getMessage());
        }
    }

    /**
     * 回复评论
     */
    @PostMapping("/{commentId}/replies")
    @ApiOperation(value = "回复评论")
    public BaseResponse<Map<String, Object>> createReply(
            @ApiParam(value = "评论ID", required = true) @PathVariable Long commentId,
            @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        log.info("回复评论请求开始 - commentId: {}, 请求参数: {}", commentId, params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            String content = (String) params.get("content");
            Long replyToId = null;
            if (params.get("replyToId") != null) {
                replyToId = Long.valueOf(params.get("replyToId").toString());
            }
            
            log.info("回复评论参数解析 - userId: {}, commentId: {}, content: {}, replyToId: {}", userId, commentId, content, replyToId);
            
            if (userId == null || userId <= 0) {
                log.warn("回复评论请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (commentId == null || commentId <= 0) {
                log.warn("回复评论请求参数错误 - commentId无效: {}", commentId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            if (StringUtils.isBlank(content)) {
                log.warn("回复评论请求参数错误 - 回复内容为空");
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "回复内容不能为空");
            }
            
            log.info("当前回复评论的用户ID: {}", userId);

            // 第三方敏感词检测
            try {
                thirdPartySensitiveWordService.validateContent(content);
                log.info("✅ 回复内容敏感词检测通过 - userId: {}", userId);
            } catch (Exception e) {
                log.warn("🚫 回复内容包含敏感词被拦截 - userId: {}, content: {}, error: {}", userId, content, e.getMessage());
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, e.getMessage());
            }

            // 创建回复
            Long replyId = commentReplyService.createReply(commentId, content, replyToId, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("reply_id", replyId);
            result.put("created_at", new Date());
            
            log.info("回复评论请求成功 - 新回复ID: {}, 用户ID: {}, 评论ID: {}", replyId, userId, commentId);
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("回复评论请求异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "回复评论失败: " + e.getMessage());
        }
    }

    /**
     * 点赞评论
     */
    @PostMapping("/{commentId}/like")
    @ApiOperation(value = "点赞评论")
    public BaseResponse<Map<String, Object>> likeComment(
            @ApiParam(value = "评论ID", required = true) @PathVariable Long commentId,
            @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        log.info("点赞评论请求开始 - commentId: {}, 请求参数: {}", commentId, params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            String action = (String) params.get("action");
            
            log.info("点赞评论操作类型 - userId: {}, commentId: {}, action: {}", userId, commentId, action);
            
            if (userId == null || userId <= 0) {
                log.warn("点赞评论请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (commentId == null || commentId <= 0) {
                log.warn("点赞评论请求参数错误 - commentId无效: {}", commentId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            if (StringUtils.isBlank(action) || (!action.equals("like") && !action.equals("unlike"))) {
                log.warn("点赞评论请求参数错误 - 操作类型无效: {}", action);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "操作类型错误");
            }
            
            log.info("当前点赞用户ID: {}", userId);

            // 点赞/取消点赞
            CommentService.LikeResult likeResult = commentService.likeComment(commentId, userId, action);
            
            Map<String, Object> result = new HashMap<>();
            result.put("likes", likeResult.getLikes());
            result.put("is_liked", likeResult.isLiked());
            
            log.info("点赞评论请求成功 - commentId: {}, 用户ID: {}, 操作: {}, 点赞数: {}, 是否已点赞: {}", 
                    commentId, userId, action, likeResult.getLikes(), likeResult.isLiked());
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("点赞评论请求异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "点赞评论失败: " + e.getMessage());
        }
    }

    /**
     * 点赞回复
     */
    @PostMapping("/replies/{replyId}/like")
    @ApiOperation(value = "点赞回复")
    public BaseResponse<Map<String, Object>> likeReply(
            @ApiParam(value = "回复ID", required = true) @PathVariable Long replyId,
            @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        log.info("点赞回复请求开始 - replyId: {}, 请求参数: {}", replyId, params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            String action = (String) params.get("action");
            
            log.info("点赞回复操作类型 - userId: {}, replyId: {}, action: {}", userId, replyId, action);
            
            if (userId == null || userId <= 0) {
                log.warn("点赞回复请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (replyId == null || replyId <= 0) {
                log.warn("点赞回复请求参数错误 - replyId无效: {}", replyId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            if (StringUtils.isBlank(action) || (!action.equals("like") && !action.equals("unlike"))) {
                log.warn("点赞回复请求参数错误 - 操作类型无效: {}", action);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "操作类型错误");
            }
            
            log.info("当前点赞用户ID: {}", userId);

            // 点赞/取消点赞
            CommentService.LikeResult likeResult = commentReplyService.likeReply(replyId, userId, action);
            
            Map<String, Object> result = new HashMap<>();
            result.put("likes", likeResult.getLikes());
            result.put("is_liked", likeResult.isLiked());
            
            log.info("点赞回复请求成功 - replyId: {}, 用户ID: {}, 操作: {}, 点赞数: {}, 是否已点赞: {}", 
                    replyId, userId, action, likeResult.getLikes(), likeResult.isLiked());
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("点赞回复请求异常 - replyId: {}, 错误信息: {}", replyId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "点赞回复失败: " + e.getMessage());
        }
    }

    /**
     * 删除评论
     */
    @DeleteMapping("/{commentId}")
    @ApiOperation(value = "删除评论")
    public BaseResponse<Boolean> deleteComment(
            @ApiParam(value = "评论ID", required = true) @PathVariable Long commentId,
            @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        log.info("删除评论请求开始 - commentId: {}, 请求参数: {}", commentId, params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            
            log.info("删除评论 - userId: {}, commentId: {}", userId, commentId);
            
            if (userId == null || userId <= 0) {
                log.warn("删除评论请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (commentId == null || commentId <= 0) {
                log.warn("删除评论请求参数错误 - commentId无效: {}", commentId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            log.info("当前删除评论的用户ID: {}", userId);

            // 删除评论
            boolean result = commentService.deleteComment(commentId, userId);
            if (!result) {
                log.warn("删除评论失败，可能是评论不存在或无权删除 - commentId: {}, userId: {}", commentId, userId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "删除失败，评论不存在或无权删除");
            }
            
            log.info("删除评论请求成功 - commentId: {}, userId: {}", commentId, userId);
            return ResultUtils.success(true);
        } catch (Exception e) {
            log.error("删除评论请求异常 - commentId: {}, 错误信息: {}", commentId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "删除评论失败: " + e.getMessage());
        }
    }

    /**
     * 删除回复
     */
    @DeleteMapping("/replies/{replyId}")
    @ApiOperation(value = "删除回复")
    public BaseResponse<Boolean> deleteReply(
            @ApiParam(value = "回复ID", required = true) @PathVariable Long replyId,
            @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        log.info("删除回复请求开始 - replyId: {}, 请求参数: {}", replyId, params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            
            log.info("删除回复 - userId: {}, replyId: {}", userId, replyId);
            
            if (userId == null || userId <= 0) {
                log.warn("删除回复请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (replyId == null || replyId <= 0) {
                log.warn("删除回复请求参数错误 - replyId无效: {}", replyId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR);
            }
            
            log.info("当前删除回复的用户ID: {}", userId);

            // 删除回复
            boolean result = commentReplyService.deleteReply(replyId, userId);
            if (!result) {
                log.warn("删除回复失败，可能是回复不存在或无权删除 - replyId: {}, userId: {}", replyId, userId);
                return ResultUtils.error(ErrorCode.OPERATION_ERROR, "删除失败，回复不存在或无权删除");
            }
            
            log.info("删除回复请求成功 - replyId: {}, userId: {}", replyId, userId);
            return ResultUtils.success(true);
        } catch (Exception e) {
            log.error("删除回复请求异常 - replyId: {}, 错误信息: {}", replyId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "删除回复失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺ID获取评论列表（支持分页）
     */
    @GetMapping("/store/{storeId}")
    @ApiOperation(value = "根据店铺ID获取评论列表")
    public BaseResponse<Map<String, Object>> getCommentListByStoreId(
            @ApiParam(value = "店铺ID", required = true) @PathVariable Long storeId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            @ApiParam(value = "排序方式", required = false) @RequestParam(defaultValue = "hot") String filter,
            @ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", required = false) @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {
        log.info("根据店铺ID获取评论列表请求开始 - storeId: {}, userId: {}, filter: {}, current: {}, pageSize: {}",
                storeId, userId, filter, current, pageSize);

        try {
            if (storeId == null || storeId <= 0) {
                log.warn("获取评论列表请求参数错误 - storeId无效: {}", storeId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "店铺ID不能为空");
            }

            if (userId == null || userId <= 0) {
                log.warn("获取评论列表请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }

            // 参数验证
            if (current < 1) current = 1;
            if (pageSize < 1 || pageSize > 50) pageSize = 10; // 限制最大页面大小

            log.info("当前登录用户ID: {}", userId);
            Page<CommentDTO> commentPage = commentService.getCommentListByStoreIdWithPage(storeId, filter, userId, current, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("total", commentPage.getTotal());
            result.put("comments", commentPage.getRecords());
            result.put("current", commentPage.getCurrent());
            result.put("size", commentPage.getSize());
            result.put("pages", commentPage.getPages());
            result.put("hasMore", commentPage.getCurrent() < commentPage.getPages());

            log.info("🎯 店铺评论分页查询成功 - storeId: {}, 总数: {}, 当前页: {}/{}, 返回数: {}, hasMore: {}",
                    storeId, commentPage.getTotal(), commentPage.getCurrent(), commentPage.getPages(),
                    commentPage.getRecords().size(), commentPage.getCurrent() < commentPage.getPages());
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("根据店铺ID获取评论列表请求异常 - storeId: {}, 错误信息: {}", storeId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 发表店铺评论
     */
    @PostMapping("/store")
    @ApiOperation(value = "发表店铺评论")
    public BaseResponse<Map<String, Object>> createStoreComment(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        log.info("发表店铺评论请求开始 - 请求参数: {}", params);

        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            Long storeId = params.get("storeId") != null ? Long.valueOf(params.get("storeId").toString()) : null;
            String content = (String) params.get("content");

            log.info("发表店铺评论参数解析 - userId: {}, storeId: {}, content: {}", userId, storeId, content);

            if (userId == null || userId <= 0) {
                log.warn("发表店铺评论请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }

            if (storeId == null || storeId <= 0 || StringUtils.isBlank(content)) {
                log.warn("发表店铺评论请求参数错误 - storeId无效或content为空");
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "店铺ID和评论内容不能为空");
            }

            log.info("当前发表店铺评论的用户ID: {}", userId);

            // 第三方敏感词检测
            try {
                thirdPartySensitiveWordService.validateContent(content);
                log.info("✅ 店铺评论内容敏感词检测通过 - userId: {}", userId);
            } catch (Exception e) {
                log.warn("❌ 店铺评论内容敏感词检测失败 - userId: {}, 错误: {}", userId, e.getMessage());
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "评论内容包含敏感词，请修改后重试");
            }

            // 创建店铺评论
            Long commentId = commentService.createStoreComment(storeId, content, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("comment_id", commentId);
            result.put("created_at", new Date());

            log.info("发表店铺评论请求成功 - 新评论ID: {}, 用户ID: {}, 店铺ID: {}", commentId, userId, storeId);
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("发表店铺评论请求异常 - 错误信息: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "发表店铺评论失败: " + e.getMessage());
        }
    }

    /**
     * 获取店铺评论统计信息
     */
    @GetMapping("/store/{storeId}/stats")
    @ApiOperation(value = "获取店铺评论统计信息")
    public BaseResponse<Map<String, Object>> getStoreCommentStats(
            @ApiParam(value = "店铺ID", required = true) @PathVariable Long storeId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            HttpServletRequest request) {

        log.info("🔢 获取店铺评论统计 - storeId: {}, userId: {}", storeId, userId);

        try {
            // 参数验证
            if (storeId == null || storeId <= 0) {
                log.warn("获取店铺评论统计参数错误 - storeId无效: {}", storeId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "店铺ID不能为空");
            }

            if (userId == null || userId <= 0) {
                log.warn("获取店铺评论统计参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }

            // 获取各筛选条件下的评论统计
            Map<String, Object> stats = commentService.getStoreCommentStats(storeId, userId);

            log.info("🔢 店铺评论统计获取成功 - storeId: {}, 热门: {}, 最新: {}, 我的: {}",
                    storeId, stats.get("hotTotal"), stats.get("newTotal"), stats.get("myTotal"));

            return ResultUtils.success(stats);

        } catch (Exception e) {
            log.error("获取店铺评论统计异常 - storeId: {}, userId: {}, 错误信息: {}",
                    storeId, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论统计失败: " + e.getMessage());
        }
    }

    /**
     * 发表话题评论
     */
    @PostMapping("/topic")
    @ApiOperation(value = "发表话题评论")
    public BaseResponse<Map<String, Object>> createTopicComment(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        log.info("发表话题评论请求开始 - 请求参数: {}", params);
        
        try {
            Long userId = params.get("userId") != null ? Long.valueOf(params.get("userId").toString()) : null;
            Long topicId = params.get("topicId") != null ? Long.valueOf(params.get("topicId").toString()) : null;
            String content = (String) params.get("content");
            
            log.info("发表话题评论参数解析 - userId: {}, topicId: {}, content: {}", userId, topicId, content);
            
            if (userId == null || userId <= 0) {
                log.warn("发表话题评论请求参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            if (topicId == null || topicId <= 0 || StringUtils.isBlank(content)) {
                log.warn("发表话题评论请求参数错误 - topicId无效或content为空");
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "话题ID和评论内容不能为空");
            }
            
            log.info("当前发表话题评论的用户ID: {}", userId);

            // 第三方敏感词检测
            try {
                thirdPartySensitiveWordService.validateContent(content);
                log.info("✅ 话题评论内容敏感词检测通过 - userId: {}", userId);
            } catch (Exception e) {
                log.warn("🚫 话题评论内容包含敏感词被拦截 - userId: {}, content: {}, error: {}", userId, content, e.getMessage());
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, e.getMessage());
            }

            // 创建话题评论
            Long commentId = commentService.createTopicComment(topicId, content, userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("comment_id", commentId);
            result.put("created_at", new Date());
            
            log.info("发表话题评论请求成功 - 新评论ID: {}, 用户ID: {}, 话题ID: {}", commentId, userId, topicId);
            return ResultUtils.success(result);
        } catch (Exception e) {
            log.error("发表话题评论请求异常 - 错误信息: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "发表话题评论失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    @ApiOperation(value = "获取缓存统计信息")
    public BaseResponse<String> getCacheStats() {
        try {
            String stats = cacheService.getCacheStats();
            return ResultUtils.success(stats);
        } catch (Exception e) {
            log.error("获取缓存统计信息异常: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取缓存统计信息失败");
        }
    }

    /**
     * 清除话题评论缓存
     */
    @DeleteMapping("/cache/topic/{topicId}")
    @ApiOperation(value = "清除话题评论缓存")
    public BaseResponse<String> evictTopicCache(@PathVariable Long topicId) {
        try {
            cacheService.evictTopicCommentCache(topicId);
            return ResultUtils.success("话题评论缓存已清除");
        } catch (Exception e) {
            log.error("清除话题评论缓存异常 - topicId: {}, error: {}", topicId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "清除缓存失败");
        }
    }

    /**
     * 清除所有评论缓存（谨慎使用）
     */
    @DeleteMapping("/cache/all")
    @ApiOperation(value = "清除所有评论缓存")
    public BaseResponse<String> clearAllCache() {
        try {
            cacheService.clearAllCache();
            log.warn("所有评论缓存已被清除");
            return ResultUtils.success("所有评论缓存已清除");
        } catch (Exception e) {
            log.error("清除所有缓存异常: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "清除缓存失败");
        }
    }

    /**
     * 调试接口：检查话题评论数据
     */
    @GetMapping("/debug/topic/{topicId}")
    @ApiOperation(value = "调试话题评论数据")
    public BaseResponse<Map<String, Object>> debugTopicComments(
            @ApiParam(value = "话题ID", required = true) @PathVariable Long topicId,
            @ApiParam(value = "用户ID", required = false) @RequestParam(required = false) Long userId) {

        log.info("🔍 调试话题评论数据 - topicId: {}, userId: {}", topicId, userId);

        try {
            Map<String, Object> debugInfo = new HashMap<>();

            // 1. 检查参数
            debugInfo.put("topicId", topicId);
            debugInfo.put("userId", userId);
            debugInfo.put("contentId", "topic_" + topicId);

            // 2. 直接查询数据库（使用新的基于topicId的查询）
            Page<CommentDTO> hotComments = commentService.getCommentListByTopicIdWithPage(topicId, "hot", userId != null ? userId : 1L, 1, 10);
            Page<CommentDTO> newComments = commentService.getCommentListByTopicIdWithPage(topicId, "new", userId != null ? userId : 1L, 1, 10);

            debugInfo.put("hotCommentsTotal", hotComments.getTotal());
            debugInfo.put("hotCommentsCount", hotComments.getRecords().size());
            debugInfo.put("newCommentsTotal", newComments.getTotal());
            debugInfo.put("newCommentsCount", newComments.getRecords().size());

            // 3. 检查缓存状态
            String cacheStats = cacheService.getCacheStats();
            debugInfo.put("cacheStats", cacheStats);

            // 4. 返回部分评论数据用于验证
            if (!hotComments.getRecords().isEmpty()) {
                debugInfo.put("sampleHotComment", hotComments.getRecords().get(0));
            }
            if (!newComments.getRecords().isEmpty()) {
                debugInfo.put("sampleNewComment", newComments.getRecords().get(0));
            }

            log.info("🔍 调试结果 - topicId: {}, 热门评论: {}, 最新评论: {}",
                    topicId, hotComments.getTotal(), newComments.getTotal());

            return ResultUtils.success(debugInfo);

        } catch (Exception e) {
            log.error("调试话题评论数据异常 - topicId: {}, 错误信息: {}", topicId, e.getMessage(), e);

            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            errorInfo.put("errorClass", e.getClass().getSimpleName());
            errorInfo.put("topicId", topicId);

            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "调试失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：直接查询数据库
     */
    @GetMapping("/debug/database/{contentId}")
    @ApiOperation(value = "调试数据库查询")
    public BaseResponse<Map<String, Object>> debugDatabase(
            @ApiParam(value = "内容ID", required = true) @PathVariable String contentId) {

        log.info("🔍 调试数据库查询 - contentId: {}", contentId);

        try {
            // 简化调试方法，直接返回基本信息
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("contentId", contentId);
            debugInfo.put("timestamp", System.currentTimeMillis());
            debugInfo.put("message", "数据库调试接口已简化，请使用话题调试接口");

            return ResultUtils.success(debugInfo);

        } catch (Exception e) {
            log.error("调试数据库查询异常 - contentId: {}, 错误信息: {}", contentId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "数据库调试失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试数据接口
     */
    @PostMapping("/debug/create-test-data/{topicId}")
    @ApiOperation(value = "创建测试评论数据")
    public BaseResponse<String> createTestData(@PathVariable Long topicId) {
        log.info("🔧 创建测试数据 - topicId: {}", topicId);

        try {
            // 简化创建测试数据方法
            log.info("创建测试数据请求 - topicId: {}", topicId);

            // 返回提示信息
            String message = "测试数据创建功能已简化，请手动在数据库中插入测试数据";
            return ResultUtils.success(message);

        } catch (Exception e) {
            log.error("创建测试数据异常 - topicId: {}, 错误: {}", topicId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "创建测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取话题评论统计信息
     */
    @GetMapping("/topic/{topicId}/stats")
    @ApiOperation(value = "获取话题评论统计信息")
    public BaseResponse<Map<String, Object>> getTopicCommentStats(
            @ApiParam(value = "话题ID", required = true) @PathVariable Long topicId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Long userId,
            HttpServletRequest request) {

        log.info("🔢 获取话题评论统计 - topicId: {}, userId: {}", topicId, userId);

        try {
            // 参数验证
            if (topicId == null || topicId <= 0) {
                log.warn("获取话题评论统计参数错误 - topicId无效: {}", topicId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "话题ID不能为空");
            }

            if (userId == null || userId <= 0) {
                log.warn("获取话题评论统计参数错误 - userId无效: {}", userId);
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }

            // 获取各筛选条件下的评论统计
            Map<String, Object> stats = commentService.getTopicCommentStats(topicId, userId);

            log.info("🔢 话题评论统计获取成功 - topicId: {}, 热门: {}, 最新: {}, 我的: {}",
                    topicId, stats.get("hotTotal"), stats.get("newTotal"), stats.get("myTotal"));

            return ResultUtils.success(stats);

        } catch (Exception e) {
            log.error("获取话题评论统计异常 - topicId: {}, userId: {}, 错误信息: {}",
                    topicId, userId, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取评论统计失败: " + e.getMessage());
        }
    }
}