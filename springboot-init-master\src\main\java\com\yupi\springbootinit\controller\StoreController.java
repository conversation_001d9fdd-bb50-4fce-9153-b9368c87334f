package com.yupi.springbootinit.controller;

import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.model.entity.Store;
import com.yupi.springbootinit.service.StoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 店铺Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/store")
@Api(tags = "店铺管理")
@Slf4j
public class StoreController {

    @Resource
    private StoreService storeService;

    /**
     * 获取所有店铺名称
     */
    @GetMapping("/names")
    @ApiOperation(value = "获取所有店铺名称")
    public BaseResponse<List<String>> getAllStoreNames() {
        try {
            log.info("🏪 接收获取店铺名称请求");
            
            List<String> storeNames = storeService.getAllStoreNames();
            
            log.info("✅ 成功返回店铺名称 - 数量: {}", storeNames.size());
            return ResultUtils.success(storeNames);
            
        } catch (Exception e) {
            log.error("❌ 获取店铺名称失败: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取店铺名称失败");
        }
    }

    /**
     * 获取所有有效店铺信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取所有有效店铺信息")
    public BaseResponse<List<Store>> getAllActiveStores() {
        try {
            log.info("🏪 接收获取店铺列表请求");
            
            List<Store> stores = storeService.getAllActiveStores();
            
            log.info("✅ 成功返回店铺列表 - 数量: {}", stores.size());
            return ResultUtils.success(stores);
            
        } catch (Exception e) {
            log.error("❌ 获取店铺列表失败: {}", e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取店铺列表失败");
        }
    }

    /**
     * 根据ID获取店铺信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取店铺信息")
    public BaseResponse<Store> getStoreById(@PathVariable Long id) {
        try {
            log.info("🏪 接收获取店铺详情请求 - ID: {}", id);
            
            if (id == null || id <= 0) {
                return ResultUtils.error(ErrorCode.PARAMS_ERROR, "店铺ID无效");
            }
            
            Store store = storeService.getById(id);
            if (store == null) {
                return ResultUtils.error(ErrorCode.NOT_FOUND_ERROR, "店铺不存在");
            }
            
            log.info("✅ 成功返回店铺详情 - ID: {}, 名称: {}", id, store.getName());
            return ResultUtils.success(store);
            
        } catch (Exception e) {
            log.error("❌ 获取店铺详情失败 - ID: {}, 错误: {}", id, e.getMessage(), e);
            return ResultUtils.error(ErrorCode.SYSTEM_ERROR, "获取店铺详情失败");
        }
    }
}
