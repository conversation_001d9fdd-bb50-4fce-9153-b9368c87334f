package com.yupi.springbootinit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yupi.springbootinit.model.entity.Store;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 店铺Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Mapper
public interface StoreMapper extends BaseMapper<Store> {

    /**
     * 获取所有店铺名称
     * 
     * @return 店铺名称列表
     */
    @Select("SELECT name FROM ba_store WHERE status = 1 ORDER BY create_time DESC")
    List<String> selectAllStoreNames();

    /**
     * 获取所有有效店铺的基本信息
     * @return 店铺列表
     */
    @Select("SELECT id, image, name FROM ba_store WHERE status = 1 ORDER BY create_time DESC")
    List<Store> selectActiveStores();
}
