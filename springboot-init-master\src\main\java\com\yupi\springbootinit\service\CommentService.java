package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.dto.CommentDTO;
import com.yupi.springbootinit.model.entity.Comment;

import java.util.List;
import java.util.Map;

/**
 * 评论服务接口
 */
public interface CommentService extends IService<Comment> {

    /**
     * 获取评论列表（返回全部数据）- 已废弃，建议使用分页版本
     *
     * @param contentId 内容ID
     * @param filter 排序方式，可选值：hot(最热)、new(最新)、my(我的)
     * @param userId 当前用户ID，用于检查是否点赞
     * @return 评论列表结果
     */
    @Deprecated
    List<CommentDTO> getCommentList(String contentId, String filter, Long userId);

    /**
     * 获取评论列表（分页版本）
     *
     * @param contentId 内容ID
     * @param filter 排序方式，可选值：hot(最热)、new(最新)、my(我的)
     * @param userId 当前用户ID，用于检查是否点赞
     * @param current 页码
     * @param pageSize 每页大小
     * @return 分页评论列表结果
     */
    Page<CommentDTO> getCommentListWithPage(String contentId, String filter, Long userId, Integer current, Integer pageSize);

    /**
     * 根据话题ID获取评论列表（已废弃，建议使用分页版本）
     *
     * @param topicId 话题ID
     * @param filter 排序方式，可选值：hot(最热)、new(最新)、my(我的)
     * @param userId 当前用户ID，用于检查是否点赞
     * @return 评论列表结果
     */
    @Deprecated
    List<CommentDTO> getCommentListByTopicId(Long topicId, String filter, Long userId);

    /**
     * 根据话题ID获取评论列表（分页版本）
     *
     * @param topicId 话题ID
     * @param filter 排序方式，可选值：hot(最热)、new(最新)、my(我的)
     * @param userId 当前用户ID，用于检查是否点赞
     * @param current 页码
     * @param pageSize 每页大小
     * @return 分页评论列表结果
     */
    Page<CommentDTO> getCommentListByTopicIdWithPage(Long topicId, String filter, Long userId, Integer current, Integer pageSize);

    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @param userId 当前用户ID，用于检查是否点赞
     * @return 评论详情
     */
    CommentDTO getCommentDetail(Long commentId, Long userId);

    /**
     * 发表评论
     *
     * @param contentId 内容ID
     * @param content 评论内容
     * @param userId 用户ID
     * @return 新创建的评论ID
     */
    Long createComment(String contentId, String content, Long userId, Long topicId);

    /**
     * 发表话题评论
     *
     * @param topicId 话题ID
     * @param content 评论内容
     * @param userId 用户ID
     * @return 新创建的评论ID
     */
    Long createTopicComment(Long topicId, String content, Long userId);

    /**
     * 点赞或取消点赞评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @param action 操作类型，"like"表示点赞，"unlike"表示取消点赞
     * @return 点赞结果，包含更新后的点赞数和点赞状态
     */
    LikeResult likeComment(Long commentId, Long userId, String action);
    
    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @param userId 当前用户ID，必须是评论的发布者
     * @return 是否删除成功
     */
    boolean deleteComment(Long commentId, Long userId);

    /**
     * 点赞结果类
     */
    class LikeResult {
        private int likes;
        private boolean isLiked;

        public LikeResult(int likes, boolean isLiked) {
            this.likes = likes;
            this.isLiked = isLiked;
        }

        public int getLikes() {
            return likes;
        }

        public boolean isLiked() {
            return isLiked;
        }
    }

    /**
     * 获取话题评论统计信息
     *
     * @param topicId 话题ID
     * @param userId 用户ID
     * @return 包含各筛选条件下评论总数的统计信息
     */
    Map<String, Object> getTopicCommentStats(Long topicId, Long userId);

    /**
     * 根据店铺ID获取评论列表（分页版本）
     *
     * @param storeId 店铺ID
     * @param filter 排序方式，可选值：hot(最热)、new(最新)、my(我的)
     * @param userId 当前用户ID，用于检查是否点赞
     * @param current 页码
     * @param pageSize 每页大小
     * @return 分页评论列表结果
     */
    Page<CommentDTO> getCommentListByStoreIdWithPage(Long storeId, String filter, Long userId, Integer current, Integer pageSize);

    /**
     * 发表店铺评论
     *
     * @param storeId 店铺ID
     * @param content 评论内容
     * @param userId 用户ID
     * @return 新创建的评论ID
     */
    Long createStoreComment(Long storeId, String content, Long userId);

    /**
     * 获取店铺评论统计信息
     *
     * @param storeId 店铺ID
     * @param userId 用户ID
     * @return 包含各筛选条件下评论总数的统计信息
     */
    Map<String, Object> getStoreCommentStats(Long storeId, Long userId);
}