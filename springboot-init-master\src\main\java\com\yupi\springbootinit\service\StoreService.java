package com.yupi.springbootinit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yupi.springbootinit.model.entity.Store;

import java.util.List;

/**
 * 店铺服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface StoreService extends IService<Store> {

    /**
     * 获取所有店铺名称
     * 
     * @return 店铺名称列表
     */
    List<String> getAllStoreNames();

    /**
     * 获取所有有效店铺信息
     * 
     * @return 店铺列表
     */
    List<Store> getAllActiveStores();
}
