package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.mapper.StoreMapper;
import com.yupi.springbootinit.model.entity.Store;
import com.yupi.springbootinit.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 店铺服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@Slf4j
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {

    @Resource
    private StoreMapper storeMapper;

    @Override
    public List<String> getAllStoreNames() {
        try {
            log.info("🏪 获取所有店铺名称");
            List<String> storeNames = storeMapper.selectAllStoreNames();
            log.info("✅ 成功获取店铺名称 - 数量: {}", storeNames.size());
            return storeNames;
        } catch (Exception e) {
            log.error("❌ 获取店铺名称失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取店铺名称失败", e);
        }
    }

    @Override
    public List<Store> getAllActiveStores() {
        try {
            log.info("🏪 获取所有有效店铺信息");
            List<Store> stores = storeMapper.selectActiveStores();
            log.info("✅ 成功获取店铺信息 - 数量: {}", stores.size());
            return stores;
        } catch (Exception e) {
            log.error("❌ 获取店铺信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取店铺信息失败", e);
        }
    }
}
