package com.yupi.springbootinit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.exception.BusinessException;
import com.yupi.springbootinit.mapper.TopicMapper;
import com.yupi.springbootinit.model.dto.topic.TopicQueryRequest;
import com.yupi.springbootinit.model.entity.Topic;
import com.yupi.springbootinit.service.CacheService;
import com.yupi.springbootinit.service.TopicService;
import com.yupi.springbootinit.utils.QueryWrapperDebugUtil;
import com.yupi.springbootinit.utils.SqlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 话题服务实现
 */
@Service
@Slf4j
public class TopicServiceImpl extends ServiceImpl<TopicMapper, Topic> implements TopicService {

    private static final Long AUTHORIZED_USER_ID = 24840L;

    @Resource
    private CacheService cacheService;

    @Override
    public void validTopic(Topic topic, boolean add) {
        if (topic == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR);
        }
        
        Long userId = topic.getUserId();
        
        // 创建时，必须指定用户ID
        if (add) {
            if (userId == null) {
                throw new BusinessException(ErrorCode.PARAMS_ERROR, "用户ID不能为空");
            }
            
            // 只有指定用户能创建话题
            if (!AUTHORIZED_USER_ID.equals(userId)) {
                throw new BusinessException(ErrorCode.NO_AUTH_ERROR, "只有特定用户可以创建话题");
            }
        }
        
        // 标题和描述非空
        String title = topic.getTitle();
        String description = topic.getDescription();
        
        if (add && StringUtils.isAnyBlank(title, description)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "标题或内容不能为空");
        }
        
        if (StringUtils.isNotBlank(title) && title.length() > 100) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "标题过长");
        }

        // 处理图片字段：如果有topicImages，自动设置coverImage为第一个图片
        if (!CollectionUtils.isEmpty(topic.getTopicImages())) {
            topic.setCoverImage(topic.getTopicImages().get(0));
            log.info("自动设置话题封面图: {}", topic.getCoverImage());
        }
    }

    @Override
    public QueryWrapper<Topic> getQueryWrapper(TopicQueryRequest topicQueryRequest) {
        QueryWrapper<Topic> queryWrapper = new QueryWrapper<>();
        if (topicQueryRequest == null) {
            log.debug("🔍 构建查询条件 - 请求参数为空，返回默认查询条件");
            queryWrapper.eq("is_delete", 0);
            return queryWrapper;
        }

        Long id = topicQueryRequest.getId();
        String title = topicQueryRequest.getTitle();
        String description = topicQueryRequest.getDescription();
        // Long userId = topicQueryRequest.getUserId();
        String sortField = topicQueryRequest.getSortField();
        String sortOrder = topicQueryRequest.getSortOrder();

        log.debug("🔍 构建查询条件 - id: {}, title: '{}', description: '{}', sortField: {}, sortOrder: {}",
                id, title, description, sortField, sortOrder);

        // 如果有title搜索条件，进行详细调试
        if (StringUtils.isNotBlank(title)) {
            QueryWrapperDebugUtil.debugLikeCondition(title, "title");
        }

        // 拼接查询条件
        queryWrapper.eq(ObjectUtils.isNotEmpty(id), "id", id);
        queryWrapper.like(StringUtils.isNotBlank(title), "title", title);
        //queryWrapper.like(StringUtils.isNotBlank(description), "description", description);
        //queryWrapper.eq(ObjectUtils.isNotEmpty(userId), "user_id", userId);

        // 注意：不需要手动添加is_delete条件，因为实体类使用了@TableLogic注解，MyBatis-Plus会自动处理

        log.debug("🔍 查询条件构建完成 - 包含title搜索: {}, 逻辑删除由@TableLogic自动处理", StringUtils.isNotBlank(title));

        // 如果搜索关键词不为空，记录额外调试信息
        if (StringUtils.isNotBlank(title)) {
            log.info("🔍 话题搜索关键词: '{}', 长度: {}", title, title.length());
            // 测试直接SQL查询，查看是否有数据
            String testSql = String.format("SELECT COUNT(*) FROM topics WHERE title LIKE '%%%s%%' AND is_delete = 0", title);
            log.info("🧪 推荐手动执行测试SQL: {}", testSql);
        }

        // 调试QueryWrapper生成的SQL
        QueryWrapperDebugUtil.debugQueryWrapper(queryWrapper, "话题搜索查询条件");
        
        // 根据排序字段排序
        if (StringUtils.isNotBlank(sortField)) {
            if ("hot".equals(sortField)) {
                // 按热度排序 - 在本案例中使用评论人数作为热度指标
                boolean isAsc = "ascend".equals(sortOrder);
                queryWrapper.orderBy(SqlUtils.validSortField("comment_user_count"), 
                        isAsc, 
                        "comment_user_count");
            } else if ("new".equals(sortField)) {
                // 按创建时间排序
                boolean isAsc = "ascend".equals(sortOrder);
                queryWrapper.orderBy(SqlUtils.validSortField("create_time"), 
                        isAsc, 
                        "create_time");
            }
        } else {
            // 默认按创建时间倒序排序
            queryWrapper.orderByDesc("create_time");
        }
        
        return queryWrapper;
    }

    @Override
    public Topic getTopicVO(Topic topic, TopicQueryRequest request) {
        if (topic == null) {
            return null;
        }

        log.info("🔍 TopicService.getTopicVO - 输入topic数据: topicId={}, topicImages={}",
                topic.getId(), topic.getTopicImages());

        // 更新评论人数
        updateCommentUserCount(topic.getId());

        // 重新查询最新数据，确保包含topicImages
        Topic latestTopic = this.getById(topic.getId());

        log.info("🔍 TopicService.getTopicVO - 重新查询后的数据: topicId={}, topicImages={}, topicImagesSize={}",
                latestTopic.getId(),
                latestTopic.getTopicImages(),
                latestTopic.getTopicImages() != null ? latestTopic.getTopicImages().size() : "null");

        // 如果重新查询后topicImages为null，但原始topic有数据，则使用原始数据
        if (latestTopic.getTopicImages() == null && topic.getTopicImages() != null) {
            log.warn("⚠️ 重新查询后topicImages丢失，使用原始数据: {}", topic.getTopicImages());
            latestTopic.setTopicImages(topic.getTopicImages());
        }

        return latestTopic;
    }

    @Override
    public Page<Topic> getTopicVOPage(Page<Topic> topicPage, TopicQueryRequest request) {
        if (topicPage == null || topicPage.getRecords() == null) {
            return topicPage;
        }

        // 更新评论人数，但不重新查询数据
        for (Topic topic : topicPage.getRecords()) {
            updateCommentUserCount(topic.getId());
        }

        // 重新查询每个话题的最新数据，保持原有的分页结构
        List<Topic> updatedRecords = new ArrayList<>();
        for (Topic topic : topicPage.getRecords()) {
            Topic latestTopic = this.getById(topic.getId());
            if (latestTopic != null) {
                updatedRecords.add(latestTopic);
            }
        }

        // 创建新的分页对象，保持原有的分页信息
        Page<Topic> resultPage = new Page<>();
        resultPage.setRecords(updatedRecords);
        resultPage.setTotal(topicPage.getTotal());
        resultPage.setCurrent(topicPage.getCurrent());
        resultPage.setSize(topicPage.getSize());
        resultPage.setPages(topicPage.getPages());

        return resultPage;
    }

    @Override
    public boolean updateCommentUserCount(Long topicId) {
        if (topicId == null) {
            return false;
        }
        
        // 获取评论人数
        int commentUserCount = baseMapper.getCommentUserCount(topicId);
        
        // 更新到话题表
        Topic topic = new Topic();
        topic.setId(topicId);
        topic.setCommentUserCount(commentUserCount);
        
        return this.updateById(topic);
    }

    @Override
    public boolean save(Topic entity) {
        // 在保存前处理图片字段
        if (!CollectionUtils.isEmpty(entity.getTopicImages())) {
            entity.setCoverImage(entity.getTopicImages().get(0));
            log.info("保存话题时自动设置封面图: {}", entity.getCoverImage());
        }
        return super.save(entity);
    }

    @Override
    public boolean updateById(Topic entity) {
        // 在更新前处理图片字段
        if (!CollectionUtils.isEmpty(entity.getTopicImages())) {
            entity.setCoverImage(entity.getTopicImages().get(0));
            log.info("更新话题时自动设置封面图: {}", entity.getCoverImage());
        }
        return super.updateById(entity);
    }

    // ==================== 缓存相关方法 ====================

    @Override
    public Topic getTopicByIdWithCache(Long topicId) {
        if (topicId == null || topicId <= 0) {
            log.warn("话题ID无效: {}", topicId);
            return null;
        }

        log.info("🔍 获取话题详情（带缓存） - topicId: {}", topicId);

        return cacheService.getTopicDetailWithCache(topicId, () -> {
            // 数据库查询逻辑
            Topic topic = this.getById(topicId);
            if (topic != null) {
                log.debug("📊 从数据库查询到话题 - id: {}, title: {}, 图片数量: {}",
                        topic.getId(), topic.getTitle(),
                        topic.getTopicImages() != null ? topic.getTopicImages().size() : 0);
            } else {
                log.debug("📊 数据库中未找到话题 - topicId: {}", topicId);
            }
            return topic;
        });
    }

    @Override
    public Page<Topic> getTopicListWithCache(TopicQueryRequest topicQueryRequest) {
        log.info("🔍 获取话题列表（带缓存） - 查询条件: {}", topicQueryRequest);

        // 构建查询条件
        QueryWrapper<Topic> queryWrapper = this.getQueryWrapper(topicQueryRequest);

        // 获取分页参数
        long current = topicQueryRequest.getCurrent();
        long size = topicQueryRequest.getPageSize();
        String sortField = topicQueryRequest.getSortField();
        String sortOrder = topicQueryRequest.getSortOrder();
        String title = topicQueryRequest.getTitle(); // 搜索关键词

        // 构建筛选类型标识，包含搜索条件
        String filterType = buildFilterType(sortField, sortOrder, title);

        return cacheService.getTopicListWithCache((int) current, (int) size, filterType, () -> {
            // 数据库查询逻辑
            Page<Topic> topicPage = new Page<>(current, size);
            Page<Topic> result = this.page(topicPage, queryWrapper);

            log.debug("📊 从数据库查询到话题列表 - 总数: {}, 当前页: {}, 返回数量: {}, 搜索关键词: {}",
                    result.getTotal(), result.getCurrent(), result.getRecords().size(), title);

            return result;
        });
    }

    @Override
    public void evictTopicCache(Long topicId) {
        if (topicId == null || topicId <= 0) {
            log.warn("话题ID无效，无法清除缓存: {}", topicId);
            return;
        }

        log.info("🗑️ 清除话题缓存 - topicId: {}", topicId);

        // 清除话题详情缓存
        cacheService.evictTopicCache(topicId);

        // 清除话题列表缓存（因为列表中可能包含该话题）
        cacheService.evictTopicListCache();
    }

    /**
     * 构建筛选类型标识
     */
    private String buildFilterType(String sortField, String sortOrder) {
        return buildFilterType(sortField, sortOrder, null);
    }

    /**
     * 构建筛选类型标识（包含搜索条件）
     */
    private String buildFilterType(String sortField, String sortOrder, String searchKeyword) {
        StringBuilder filterType = new StringBuilder();

        if (StringUtils.isBlank(sortField)) {
            filterType.append("default");
        } else {
            String order = StringUtils.isBlank(sortOrder) ? "desc" : sortOrder.toLowerCase();
            filterType.append(sortField).append("_").append(order);
        }

        // 添加搜索关键词到缓存键中
        if (StringUtils.isNotBlank(searchKeyword)) {
            filterType.append("_search_").append(searchKeyword.hashCode());
        }

        return filterType.toString();
    }
}