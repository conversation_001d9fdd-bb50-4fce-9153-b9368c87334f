{"groups": [{"name": "cos.client", "type": "com.yupi.springbootinit.config.CosClientConfig", "sourceType": "com.yupi.springbootinit.config.CosClientConfig"}, {"name": "tencent.captcha", "type": "com.yupi.springbootinit.config.TencentCaptchaConfig", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}], "properties": [{"name": "cos.client.base-url", "type": "java.lang.String", "description": "COS访问域名", "sourceType": "com.yupi.springbootinit.config.CosClientConfig"}, {"name": "cos.client.bucket", "type": "java.lang.String", "description": "存储桶名称", "sourceType": "com.yupi.springbootinit.config.CosClientConfig"}, {"name": "cos.client.region", "type": "java.lang.String", "description": "区域", "sourceType": "com.yupi.springbootinit.config.CosClientConfig"}, {"name": "cos.client.secret-id", "type": "java.lang.String", "description": "accessKey", "sourceType": "com.yupi.springbootinit.config.CosClientConfig"}, {"name": "cos.client.secret-key", "type": "java.lang.String", "description": "secret<PERSON>ey", "sourceType": "com.yupi.springbootinit.config.CosClientConfig"}, {"name": "tencent.captcha.app-secret-key", "type": "java.lang.String", "description": "验证码应用密钥", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}, {"name": "tencent.captcha.captcha-app-id", "type": "java.lang.String", "description": "验证码应用ID", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}, {"name": "tencent.captcha.dev", "type": "java.lang.Bo<PERSON>an", "description": "是否开发环境，开发环境下可允许验证跳过", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}, {"name": "tencent.captcha.region", "type": "java.lang.String", "description": "验证码接入区域，默认为广州区域", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}, {"name": "tencent.captcha.secret-id", "type": "java.lang.String", "description": "腾讯云验证码secretId", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}, {"name": "tencent.captcha.secret-key", "type": "java.lang.String", "description": "腾讯云验证码secretKey", "sourceType": "com.yupi.springbootinit.config.TencentCaptchaConfig"}], "hints": []}