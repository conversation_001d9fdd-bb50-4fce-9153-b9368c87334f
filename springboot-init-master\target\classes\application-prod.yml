# 线上配置文件
# <AUTHOR> href="https://github.com/liyupi">程序员鱼皮</a>
# @from <a href="https://yupi.icu">编程导航知识星球</a>
server:
  port: 8101
spring:
  # 数据库配置
  # todo 需替换配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************
    username: admin_foxdance_c
    password: btxzGpwj9kyy7CKF
  # Redis 配置
  # todo 需替换配置
#  redis:
#    database: 1
#    host: ************
#    port: 6379
#    timeout: 5000
    # password: 123456
mybatis-plus:
  configuration:
    # 生产环境关闭日志
    log-impl: ''
# 接口文档配置
knife4j:
  basic:
    enable: true
    username: foxdance
    password: 666666