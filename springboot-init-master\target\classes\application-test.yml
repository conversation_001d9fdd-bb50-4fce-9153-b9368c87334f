# 测试配置文件
# <AUTHOR> href="https://github.com/liyupi">程序员鱼皮</a>
# @from <a href="https://yupi.icu">编程导航知识星球</a>
server:
  port: 8101
spring:
  # 数据库配置
  # todo 需替换配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************
    username: root
    password: 123456
  # Redis 配置
  # todo 需替换配置
  redis:
    database: 1
    host: localhost
    port: 6379
    timeout: 5000
    password: 123456
  # Elasticsearch 配置
  # todo 需替换配置
  elasticsearch:
    uris: http://localhost:9200
    username: root
    password: 123456