# 投票系统配置文件
spring:
  application:
    name: metro-vote-system
  # 默认 dev 环境
  profiles:
    active: dev
  # 支持 swagger3
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 禁用指定的自动配置
  autoconfigure:
    exclude:
      - com.binarywang.spring.starter.wxjava.mp.config.WxMpServiceAutoConfiguration
      - com.binarywang.spring.starter.wxjava.mp.config.WxMpStorageAutoConfiguration
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************
    username: admin_foxdance_c
    password: btxzGpwj9kyy7CKF
  # 文件上传
  servlet:
    multipart:
      # 大小限制
      max-file-size: 10MB
  # Redis配置（可选，如果没有Redis服务则注释掉）
  # redis:
  #   host: localhost

# 第三方敏感词检测API配置
sensitive-word:
  api:
    # API地址
    url: https://v.api.aa1.cn/api/api-mgc/index.php
    # 是否启用敏感词检测
    enabled: true
    # API调用超时时间（毫秒）
    timeout: 5000
    # 重试次数
    retry-count: 2
  #   port: 6379
  #   password:
  #   database: 0
  #   timeout: 3000ms
  #   lettuce:
  #     pool:
  #       max-active: 8
  #       max-idle: 8
  #       min-idle: 0
  #       max-wait: -1ms
server:
  address: 0.0.0.0
  port: 8101
  servlet:
    context-path: /api
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: isDelete # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值（默认为 1）
      logic-not-delete-value: 0 # 逻辑未删除值（默认为 0）
  # 确保TypeHandler正确注册
  type-handlers-package: com.yupi.springbootinit.config

# 日志配置 - 用于调试分页查询和SQL执行
logging:
  level:
    # MyBatis SQL执行日志
    com.yupi.springbootinit.mapper: DEBUG
    # MyBatis-Plus详细日志
    com.baomidou.mybatisplus: DEBUG
    # SQL执行和参数日志
    org.apache.ibatis: DEBUG
    # 数据库连接日志
    com.mysql.cj.jdbc: DEBUG
    # 应用Service日志
    com.yupi.springbootinit.service.impl.CommentServiceImpl: DEBUG
    com.yupi.springbootinit.controller.CommentController: DEBUG
    com.yupi.springbootinit.controller.TopicController: DEBUG
    com.yupi.springbootinit.service.impl.TopicServiceImpl: DEBUG
    com.yupi.springbootinit.config.ListJsonTypeHandler: DEBUG
    # 根日志级别
    root: INFO

# 接口文档配置
knife4j:
  enable: true
  openapi:
    title: "地铁投票系统接口文档"
    version: 1.0
    group:
      default:
        api-rule: package
        api-rule-resources:
          - com.yupi.springbootinit.controller

# 添加投票限制配置
vote:
  limit:
    # 是否只允许广东IP投票(已禁用，改用精确地理位置)
    guangdong-only: false
    # 是否允许内网IP投票（开发环境使用）
    allow-internal-ip: true

# 腾讯云验证码配置
tencent:
  captcha:
    secretId: ${TENCENT_CAPTCHA_SECRET_ID}
    secretKey: ${TENCENT_CAPTCHA_SECRET_KEY}

# 腾讯云对象存储配置
cos:
  client:
    secretId: AKIDgGhwzs9MPTTJ4MTN5qlFcqvqkEl9Zqvn
    secretKey: ud9Tm052ub8bxQp9LZhjeQAmJtQ1XwWZ
    region: ap-guangzhou
    bucket: dance-1329875799
    baseUrl: https://file.foxdance.com.cn

# # 腾讯云验证码配置
# tencent:
#   captcha:
#     secretId: AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     secretKey: xxxxxxxxxxxxxxxxxxxxxxxxxxxx

# # 腾讯云对象存储配置
# cos:
#   client:
#     accessKey: AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxx  # 与腾讯云API密钥相同
#     secretKey: xxxxxxxxxxxxxxxxxxxxxxxxxxxx      # 与腾讯云API密钥相同
#     region: ap-guangzhou                         # 存储桶所在地域
#     bucket: your-bucket-name-1250000000          # 存储桶名称加APPID
#     baseUrl: https://your-bucket-name-1250000000.cos.ap-guangzhou.myqcloud.com