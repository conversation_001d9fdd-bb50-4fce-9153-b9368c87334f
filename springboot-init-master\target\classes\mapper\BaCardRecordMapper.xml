<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.BaCardRecordMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.BaCardRecord">
            <id property="id" column="id" />
            <result property="out_trade_no" column="out_trade_no" />
            <result property="uid" column="uid" />
            <result property="number" column="number" />
            <result property="surplus_frequency" column="surplus_frequency" />
    </resultMap>

    <sql id="Base_Column_List">
        id,out_trade_no,uid,sale_id,card_id,store_id,
        type,number,surplus_frequency,price,total_amount,
        payment_price,leave_frequency,leave_duration,contract_name,open_store_id,
        three_parties_order_number,payment_voucher,notes,gift_days,gift_frequency,
        create_time,payment_time,activation_time,end_time,status,
        payment_status,activation_method,payment_method,general_type,card_transfer_time,
        card_front_id,transfer_time,duration,refund_price,refund_time,
        member_card_number,sign_contract_type,delete_time
    </sql>
</mapper>
