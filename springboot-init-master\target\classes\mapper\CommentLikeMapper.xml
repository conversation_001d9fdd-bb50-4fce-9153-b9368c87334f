<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.CommentLikeMapper">

    <select id="checkUserLiked" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM comment_likes
        WHERE comment_id = #{commentId}
        AND user_id = #{userId}
        AND is_delete = 0
    </select>
    
    <update id="softDeleteByCommentIdAndUserId">
        UPDATE comment_likes
        SET is_delete = 1
        WHERE comment_id = #{commentId}
        AND user_id = #{userId}
        AND is_delete = 0
    </update>
    
    <update id="restoreByCommentIdAndUserId">
        UPDATE comment_likes
        SET is_delete = 0, created_at = NOW()
        WHERE comment_id = #{commentId}
        AND user_id = #{userId}
        AND is_delete = 1
    </update>
    
    <!-- 修改查询点赞记录的方法，确保字段名与实体类属性正确映射 -->
    <select id="findByCommentIdAndUserId" resultType="com.yupi.springbootinit.model.entity.CommentLike">
        SELECT 
            id,
            comment_id AS commentId,
            user_id AS userId,
            created_at AS createdAt,
            is_delete AS isDelete
        FROM comment_likes
        WHERE comment_id = #{commentId}
        AND user_id = #{userId}
        LIMIT 1
    </select>
</mapper> 