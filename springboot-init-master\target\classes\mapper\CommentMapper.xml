<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.CommentMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.Comment">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="contentId" column="content_id" jdbcType="VARCHAR"/>
        <result property="contentType" column="content_type" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="likes" column="likes" jdbcType="INTEGER"/>
        <result property="replyCount" column="reply_count" jdbcType="INTEGER"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,content_id,content_type,
        user_id,content,likes,
        reply_count,nickname,avatar,
        created_at,updated_at,
        is_delete
    </sql>

    <!-- 按热度查询评论 -->
    <select id="queryHotComments" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comments
        WHERE content_id = #{contentId} AND content_type = #{contentType} AND is_delete = 0
        ORDER BY likes DESC, created_at DESC
    </select>

    <!-- 按最新查询评论 -->
    <select id="queryNewComments" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comments
        WHERE content_id = #{contentId} AND content_type = #{contentType} AND is_delete = 0
        ORDER BY created_at DESC
    </select>

    <!-- 查询用户的评论 -->
    <select id="queryUserComments" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comments
        WHERE content_id = #{contentId} AND content_type = #{contentType} AND user_id = #{userId} AND is_delete = 0
        ORDER BY created_at DESC
    </select>

    <!-- 增加评论回复数 -->
    <update id="incrementReplyCount">
        UPDATE comments
        SET reply_count = reply_count + 1, updated_at = NOW()
        WHERE id = #{commentId} AND is_delete = 0
    </update>

    <!-- 增加评论点赞数 -->
    <update id="incrementLikes">
        UPDATE comments
        SET likes = likes + 1, updated_at = NOW()
        WHERE id = #{commentId} AND is_delete = 0
    </update>

    <!-- 减少评论点赞数 -->
    <update id="decrementLikes">
        UPDATE comments
        SET likes = GREATEST(likes - 1, 0), updated_at = NOW()
        WHERE id = #{commentId} AND is_delete = 0
    </update>
    
    <!-- 软删除评论 -->
    <update id="softDeleteById">
        UPDATE comments
        SET is_delete = 1, updated_at = NOW()
        WHERE id = #{commentId} AND is_delete = 0
    </update>
</mapper> 