<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.CommentReplyMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.CommentReply">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="commentId" column="comment_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="replyToId" column="reply_to_id" jdbcType="BIGINT"/>
        <result property="likes" column="likes" jdbcType="INTEGER"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,comment_id,user_id,
        content,reply_to_id,likes,
        nickname,avatar,
        created_at,updated_at,is_delete
    </sql>

    <!-- 按热度查询回复 -->
    <select id="queryHotReplies" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comment_replies
        WHERE comment_id = #{commentId} AND is_delete = 0
        ORDER BY likes DESC, created_at DESC
    </select>

    <!-- 按最新查询回复 -->
    <select id="queryNewReplies" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comment_replies
        WHERE comment_id = #{commentId} AND is_delete = 0
        ORDER BY created_at DESC
    </select>

    <!-- 查询用户的回复 -->
    <select id="queryUserReplies" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comment_replies
        WHERE comment_id = #{commentId} AND user_id = #{userId} AND is_delete = 0
        ORDER BY created_at DESC
    </select>

    <!-- 获取最新回复（用于预览） -->
    <select id="getLatestReplies" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM comment_replies
        WHERE comment_id = #{commentId} AND is_delete = 0
        ORDER BY created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 增加回复点赞数 -->
    <update id="incrementLikes">
        UPDATE comment_replies
        SET likes = likes + 1, updated_at = NOW()
        WHERE id = #{replyId} AND is_delete = 0
    </update>

    <!-- 减少回复点赞数 -->
    <update id="decrementLikes">
        UPDATE comment_replies
        SET likes = GREATEST(likes - 1, 0), updated_at = NOW()
        WHERE id = #{replyId} AND is_delete = 0
    </update>
    
    <!-- 软删除回复 -->
    <update id="softDeleteById">
        UPDATE comment_replies
        SET is_delete = 1, updated_at = NOW()
        WHERE id = #{replyId} AND is_delete = 0
    </update>
</mapper> 