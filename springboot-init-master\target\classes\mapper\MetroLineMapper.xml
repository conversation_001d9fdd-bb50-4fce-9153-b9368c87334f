<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.MetroLineMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.MetroLine">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="lineName" column="line_name" jdbcType="VARCHAR"/>
        <result property="stations" column="stations" jdbcType="VARCHAR" typeHandler="com.yupi.springbootinit.config.ListJsonTypeHandler"/>
        <result property="voteCounts" column="vote_counts" jdbcType="VARCHAR" typeHandler="com.yupi.springbootinit.config.MapJsonTypeHandler"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,line_name,stations,
        vote_counts,create_time,update_time,
        is_delete
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from metro_lines
        where is_delete = 0
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from metro_lines
        where id = #{id} and is_delete = 0
    </select>

    <select id="selectByLineName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from metro_lines
        where line_name = #{lineName} and is_delete = 0
    </select>

    <insert id="insert" parameterType="com.yupi.springbootinit.model.entity.MetroLine">
        insert into metro_lines
        (line_name, stations, vote_counts, create_time, update_time, is_delete)
        values
        (#{lineName}, 
         #{stations,typeHandler=com.yupi.springbootinit.config.ListJsonTypeHandler}, 
         #{voteCounts,typeHandler=com.yupi.springbootinit.config.MapJsonTypeHandler}, 
         now(), now(), 0)
    </insert>

    <update id="updateById" parameterType="com.yupi.springbootinit.model.entity.MetroLine">
        update metro_lines
        <set>
            <if test="lineName != null">line_name = #{lineName},</if>
            <if test="stations != null">stations = #{stations,typeHandler=com.yupi.springbootinit.config.ListJsonTypeHandler},</if>
            <if test="voteCounts != null">vote_counts = #{voteCounts,typeHandler=com.yupi.springbootinit.config.MapJsonTypeHandler},</if>
            update_time = now()
        </set>
        where id = #{id} and is_delete = 0
    </update>

    <update id="deleteById">
        update metro_lines
        set is_delete = 1, update_time = now()
        where id = #{id} and is_delete = 0
    </update>

    <update id="updateVoteCount">
        update metro_lines
        set vote_counts = JSON_SET(COALESCE(vote_counts, '{}'), CONCAT('$.', #{stationName,jdbcType=VARCHAR}), #{voteCount,jdbcType=INTEGER}),
            update_time = now()
        where id = #{id,jdbcType=BIGINT} and is_delete = 0
    </update>
    
    <update id="incrementVoteCount">
        update metro_lines
        set
            vote_counts = CASE
                WHEN vote_counts IS NULL OR vote_counts = '{}' THEN
                    JSON_OBJECT(#{stationName,jdbcType=VARCHAR}, 1)
                ELSE
                    JSON_SET(
                        COALESCE(vote_counts, '{}'),
                        CONCAT('$.', #{stationName,jdbcType=VARCHAR}),
                        COALESCE(CAST(JSON_EXTRACT(vote_counts, CONCAT('$.', #{stationName,jdbcType=VARCHAR})) AS UNSIGNED), 0) + 1
                    )
                END,
            update_time = now()
        where id = #{id,jdbcType=BIGINT} and is_delete = 0
    </update>
</mapper> 