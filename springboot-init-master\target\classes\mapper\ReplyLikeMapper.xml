<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.ReplyLikeMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.ReplyLike">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="replyId" column="reply_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,reply_id,user_id,
        created_at,is_delete
    </sql>

    <!-- 检查用户是否点赞了回复 -->
    <select id="checkUserLiked" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM reply_likes
        WHERE reply_id = #{replyId} AND user_id = #{userId} AND is_delete = 0
    </select>

    <!-- 软删除点赞记录 -->
    <update id="softDeleteByReplyIdAndUserId">
        UPDATE reply_likes
        SET is_delete = 1
        WHERE reply_id = #{replyId} AND user_id = #{userId}
    </update>

    <!-- 恢复已软删除的点赞记录 -->
    <update id="restoreByReplyIdAndUserId">
        UPDATE reply_likes
        SET is_delete = 0
        WHERE reply_id = #{replyId} AND user_id = #{userId} AND is_delete = 1
    </update>
    
    <!-- 查询点赞记录（包括已删除的） -->
    <select id="findByReplyIdAndUserId" resultType="com.yupi.springbootinit.model.entity.ReplyLike">
        SELECT 
            id,
            reply_id AS replyId,
            user_id AS userId,
            created_at AS createdAt,
            is_delete AS isDelete
        FROM reply_likes
        WHERE reply_id = #{replyId}
        AND user_id = #{userId}
        LIMIT 1
    </select>
</mapper> 