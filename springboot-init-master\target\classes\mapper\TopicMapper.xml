<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.TopicMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.Topic">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="commentUserCount" column="comment_user_count" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,description,
        user_id,comment_user_count,create_time,
        update_time,is_delete
    </sql>
    
    <!-- 获取评论人数：查询comments表中指定topic_id的不同用户数量 -->
    <select id="getCommentUserCount" resultType="int">
        SELECT COUNT(DISTINCT user_id) 
        FROM comments 
        WHERE topic_id = #{topicId} 
        AND is_delete = 0
    </select>
</mapper> 