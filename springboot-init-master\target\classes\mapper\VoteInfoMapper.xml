<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.VoteInfoMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.VoteInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="info" column="info" jdbcType="VARCHAR"/>
            <result property="tips" column="tips" jdbcType="VARCHAR"/>
            <result property="cardTip" column="card_tip" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,info,
        tips,card_tip,createTime,updateTime,
        isDelete
    </sql>
</mapper> 