<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.VoteRecordMapper">

    <resultMap id="BaseResultMap" type="com.yupi.springbootinit.model.entity.VoteRecord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="openId" column="wx_open_id" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="userAgent" column="user_agent" jdbcType="VARCHAR"/>
        <result property="metroLineId" column="metro_line_id" jdbcType="BIGINT"/>
        <result property="stationName" column="station_name" jdbcType="VARCHAR"/>
        <result property="isSuspicious" column="is_suspicious" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, open_id, ip_address, user_agent, metro_line_id, station_name, is_suspicious, create_time
    </sql>

    <select id="countRecentVotes" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM vote_records
        WHERE ip_address = #{ipAddress,jdbcType=VARCHAR}
        AND metro_line_id = #{metroLineId,jdbcType=BIGINT}
        AND station_name = #{stationName,jdbcType=VARCHAR}
        AND create_time > #{startTime,jdbcType=TIMESTAMP}
    </select>

    <select id="countRecentVotesByTimeStr" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM vote_records
        WHERE ip_address = #{ipAddress,jdbcType=VARCHAR}
        AND metro_line_id = #{metroLineId,jdbcType=BIGINT}
        AND station_name = #{stationName,jdbcType=VARCHAR}
        AND create_time > #{startTimeStr,jdbcType=VARCHAR}
    </select>
    
    <select id="getUserVoteRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM vote_records
        WHERE user_id = #{userId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>
    
</mapper> 